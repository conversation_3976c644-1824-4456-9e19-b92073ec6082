package weather.po;

public class WeatherDataVo {
    /** 地区 */
    private String region;

    /** 位置，经纬度或者其他标识 */
    private String location;

    /** 天气 */
    private String weatherJson;

    @Override
    public String toString() {
        return "WeatherDataVo{" +
                "region='" + region + '\'' +
                ", location='" + location + '\'' +
                ", weatherJson='" + weatherJson + '\'' +
                '}';
    }

    public WeatherDataVo(String region, String location, String weatherJson) {
        this.region = region;
        this.location = location;
        this.weatherJson = weatherJson;
    }
    public WeatherDataVo() {

    }


    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getWeatherJson() {
        return weatherJson;
    }

    public void setWeatherJson(String weatherJson) {
        this.weatherJson = weatherJson;
    }
}
