package weather.message.mapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import weather.message.utils.DeviceIpMonVo;
import weather.message.utils.OpoNoSendVo;

import java.util.List;

@Mapper
public interface MessageMapper {
    List<OpoNoSendVo> getNoMessageList();

    void updateStatusById(@Param("id") Integer id);

    void updateManageState(@Param("cd")String cd, @Param("state")String state);

    List<DeviceIpMonVo> findWorkByState(@Param("state")String state);

    void updateNetWork(@Param("id")Integer id, @Param("time")String time, @Param("state")String state, @Param("alarmType")String alarmType);

    void updateTopologyState(@Param("deviceName")String deviceName, @Param("state")String state);

    List<DeviceIpMonVo> findSafeByState(@Param("state")String state);

    void updateNetSafe(@Param("id")Integer id, @Param("time")String time, @Param("state")String state, @Param("alarmType")String alarmType);

    List<DeviceIpMonVo> findServerByState(@Param("state")String state);

    void updateServer(@Param("id")Integer id, @Param("time")String time, @Param("state")String state, @Param("alarmType")String alarmType);
}
