package weather.message;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import weather.po.WeatherDataVo;
import weather.message.mapper.WeatherDataMapper;
import weather.message.utils.DateUtils;
import weather.po.WeatherDataPo;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Component
public class WeatherData {

    // 全局原子计数器，用于生成唯一ID
    private static final AtomicLong ID_GENERATOR = new AtomicLong(System.currentTimeMillis());

    @Autowired
    private WeatherDataMapper baseMapper;

    // 天气API密钥，建议移到配置文件
    @Value("${weather.api.key:07db4300e23c4e50881571ea9ab8f193}")
    private String apiKey;

    @Value("${weather.api.base-url:https://devapi.qweather.com/v7/weather/7d}")
    private String apiBaseUrl;

    /**
     * 101220202 怀远县
     * 101220809 颍泉区
     * 101220808 颍东区
     * 101220904 蒙城县
     * 101220903 利辛县
     * 101220402 凤台县
     * 101220403 潘集区
     */
    // 定义地区代码列表
    private static final List<String> LOCATION_CODES =
            Arrays.asList("101220202", // 怀远县
                            "101220809", // 颍泉区
                            "101220808", // 颍东区
                            "101220904", // 蒙城县
                            "101220903", // 利辛县
                            "101220402", // 凤台县
                            "101220403"  // 潘集区
                    )
                    .stream().collect(Collectors.toList());


    @PostConstruct
    public void init() {
        try {
            getWeatherData();
        } catch (Exception e) {
            System.err.println("初始化天气数据失败: " + e.getMessage());
        }
    }

    @Scheduled(fixedDelay = 1000 * 60 * 60) // 每小时执行一次
    public void getWeatherData() {
        try {
            List<WeatherDataVo> weatherData = fetchWeatherData();
            if (weatherData.isEmpty()) {
                // 在数据获取失败时进行日志记录或其他错误处理
                System.err.println("Failed to fetch weather data.");
                return;
            }

            if (ObjectUtil.isNotEmpty(weatherData)) {
                // 所有状态改成0
                baseMapper.updateDataStatus();

                for (WeatherDataVo weather : weatherData) {
                    WeatherDataPo weatherDataPo = new WeatherDataPo();
                    // 使用原子计数器生成唯一ID
                    weatherDataPo.setId(ID_GENERATOR.incrementAndGet());
                    weatherDataPo.setCreateTime(DateUtils.getNowDate());
                    weatherDataPo.setStatus(1);
                    weatherDataPo.setWeatherJson(weather.getWeatherJson());
                    weatherDataPo.setRegion(weather.getRegion());
                    weatherDataPo.setLocation(weather.getLocation());
                    baseMapper.insert(weatherDataPo);
                }
            }
        } catch (Exception e) {
            System.err.println("获取或保存天气数据失败: " + e.getMessage());
        }
    }

    //传输数据给后端系统
    public void postData(String targetURL, List<WeatherDataVo> data) {
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("weather", data);
            HttpUtil.post(targetURL, JSONUtil.toJsonStr(paramMap));
        } catch (Exception e) {
            // 使用日志框架记录异常，而不是直接打印堆栈跟踪
            // Log.error(e.getMessage(), e);
            System.err.println("Error posting data: " + e.getMessage());
        }
    }

    //天气数据获取数据
    public List<WeatherDataVo> fetchWeatherData() {
        List<WeatherDataVo> weatherData = new ArrayList<>();
        for (String locationCode : LOCATION_CODES) {
            try {
                WeatherDataVo weatherDataVo = new WeatherDataVo();
                String weatherJson = HttpUtil.get(buildApiUrl(locationCode));
                if (StrUtil.isBlank(weatherJson)) {
                    System.err.println("获取 " + getRegionByCode(locationCode) + " 的天气数据为空");
                    continue; // 如果获取的天气数据为空，则跳过当前地区
                }
                weatherDataVo.setWeatherJson(weatherJson);
                weatherDataVo.setRegion(getRegionByCode(locationCode));
                weatherDataVo.setLocation(locationCode);
                weatherData.add(weatherDataVo);
            } catch (Exception e) {
                System.err.println("获取 " + getRegionByCode(locationCode) + " 的天气数据失败: " + e.getMessage());
            }
        }
        return weatherData;
    }

    private String buildApiUrl(String locationCode) {
        return apiBaseUrl + "?location=" + locationCode + "&key=" + apiKey;
    }

    // 用于根据地区代码返回地区名称
    private String getRegionByCode(String locationCode) {
        switch (locationCode) {
            case "101220202":
                return "怀远县";
            case "101220809":
                return "颍泉区";
            case "101220808":
                return "颍东区";
            case "101220904":
                return "蒙城县";
            case "101220903":
                return "利辛县";
            case "101220402":
                return "凤台县";
            case "101220403":
                return "潘集区";
            // 其他地区代码的处理...
            default:
                return "Unknown";
        }
    }
}
