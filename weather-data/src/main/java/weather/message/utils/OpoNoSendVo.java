package weather.message.utils;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 短信平台对象 opo_message_send
 *
 * <AUTHOR>
 * @date 2024-08-30
 */

@Data
@Accessors(chain = true)
public class OpoNoSendVo {

    /** id */
    private Integer id;

    /** 发送人 */
    private String people;

    /** 内容 */
    private String content;

    /** 接收人 */
    private String person;

    /** 接收人号码 */
    private String phone;



}
