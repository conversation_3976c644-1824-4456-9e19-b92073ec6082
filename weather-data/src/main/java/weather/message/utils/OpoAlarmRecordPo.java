package weather.message.utils;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 机房告警记录对象 opo_alarm_record
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@Accessors(chain = true)
@TableName(value ="opo_alarm_record")
@AllArgsConstructor
@NoArgsConstructor
public class OpoAlarmRecordPo implements Serializable
{


    @TableId(type = IdType.AUTO)
    private Long id;

    /** 故障编码 */
    private String alarmCode;

    /** 机名 */
    private String deviceName;

    /** 机码 */
    private String deviceCode;

    /** 设备类型 */
    private String deviceType;

    /** 告警类型 */
    private String alarmType;

    /** 告警时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 故障状态 */
    private String alarmState;

    /** 告警内容 */
    private String content;

    /** 是否删除 */
    private Integer isDelete;

    /** 位置 */
    private String site;


}
