<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="weather.message.mapper.WeatherDataMapper">

    <!-- 添加天气数据 -->
    <insert id="insert" parameterType="weather.po.WeatherDataPo">
        insert into weather_data(
            id,
            region,
            location,
            weather_json,
            status,
            create_time
        ) values (
            #{id},
            #{region},
            #{location},
            #{weatherJson},
            #{status},
            #{createTime}
        )
    </insert>

    <!-- 更新天气数据状态 -->
    <update id="updateDataStatus" >
        update weather_data set status = 0 where status = 1
    </update>

</mapper>
