<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.drought.mapper.DroughtExpertMapper">

    <resultMap type="com.jhdr.drought.entity.po.DroughtExpertPo" id="DroughtExpertResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="unit"    column="unit"    />
        <result property="position"    column="position"    />
        <result property="phone"    column="phone"    />
        <result property="officePhone"    column="office_phone"    />
        <result property="professional"    column="professional"    />
        <result property="type"    column="type"    />
        <result property="majorA"    column="major_a"    />
        <result property="majorB"    column="major_b"    />
        <result property="experience"    column="experience"    />
        <result property="time"    column="time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectDroughtExpertVo">
        select id, name, unit, position, phone, office_phone, professional, type, major_a, major_b, experience, time, update_time, url from chxh.drought_expert
    </sql>

    <select id="selectDroughtExpertList"  resultType="com.jhdr.drought.entity.vo.DroughtExpertVo">
        <include refid="selectDroughtExpertVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="phone != null "> and phone = #{phone}</if>
            <if test="officePhone != null "> and office_phone = #{officePhone}</if>
            <if test="professional != null  and professional != ''"> and professional = #{professional}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="majorA != null  and majorA != ''"> and major_a = #{majorA}</if>
            <if test="majorB != null  and majorB != ''"> and major_b = #{majorB}</if>
            <if test="experience != null  and experience != ''"> and experience = #{experience}</if>
            <if test="time != null "> and time = #{time}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
        </where>
    </select>

    <select id="selectDroughtExpertById"  resultType="com.jhdr.drought.entity.vo.DroughtExpertVo">
            <include refid="selectDroughtExpertVo"/>
            where id = #{id}
    </select>
    <select id="getList" resultType="com.jhdr.drought.entity.vo.DroughtExpertVo">
        select id, name, unit, position, phone, office_phone, professional, type, major_a, major_b, experience, time, update_time, url from chxh.drought_expert
        <where>
            <if test="name != null "> and name  like concat('%', #{name}, '%')</if>
            <if test="type != null "> and type like concat('%', #{type}, '%')</if>
            <if test="major != null "> and major_a like concat('%', #{major}, '%') or major_b like concat('%', #{major}, '%') </if>
        </where>
    </select>

    <insert id="insertDroughtExpert" parameterType="com.jhdr.drought.entity.param.DroughtExpertAddParam">
        insert into chxh.drought_expert
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="name != null">name,</if>
                    <if test="unit != null">unit,</if>
                    <if test="position != null">position,</if>
                    <if test="phone != null">phone,</if>
                    <if test="officePhone != null">office_phone,</if>
                    <if test="professional != null">professional,</if>
                    <if test="type != null">type,</if>
                    <if test="majorA != null">major_a,</if>
                    <if test="majorB != null">major_b,</if>
                    <if test="experience != null">experience,</if>
                    <if test="time != null">time,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="url != null">url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="name != null">#{name},</if>
                    <if test="unit != null">#{unit},</if>
                    <if test="position != null">#{position},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="officePhone != null">#{officePhone},</if>
                    <if test="professional != null">#{professional},</if>
                    <if test="type != null">#{type},</if>
                    <if test="majorA != null">#{majorA},</if>
                    <if test="majorB != null">#{majorB},</if>
                    <if test="experience != null">#{experience},</if>
                    <if test="time != null">#{time},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="url != null">#{url},</if>
        </trim>
    </insert>

    <update id="updateDroughtExpert" parameterType="com.jhdr.drought.entity.param.DroughtExpertEditParam">
        update chxh.drought_expert
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">name = #{name},</if>
                    <if test="unit != null">unit = #{unit},</if>
                    <if test="position != null">position = #{position},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="officePhone != null">office_phone = #{officePhone},</if>
                    <if test="professional != null">professional = #{professional},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="majorA != null">major_a = #{majorA},</if>
                    <if test="majorB != null">major_b = #{majorB},</if>
                    <if test="experience != null">experience = #{experience},</if>
                    <if test="time != null">time = #{time},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="url != null">url = #{url},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDroughtExpertById" parameterType="Long">
        delete from chxh.drought_expert where id = #{id}
    </delete>

    <delete id="deleteDroughtExpertByIds" parameterType="String">
        delete from chxh.drought_expert where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
