<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.drought.mapper.DroughtMeetingMapper">

    <resultMap type="com.jhdr.drought.entity.po.DroughtMeetingPo" id="DroughtMeetingResult">
        <result property="id"    column="id"    />
        <result property="url"    column="url"    />
        <result property="planType"    column="plan_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="idDrill"    column="id_drill"    />
        <result property="name"    column="name"    />
    </resultMap>

    <sql id="selectDroughtMeetingVo">
        select id, url,name, plan_type, create_time, update_time, id_drill from drought_meeting
    </sql>

    <select id="selectDroughtMeetingList"  resultType="com.jhdr.drought.entity.vo.DroughtMeetingVo">
        <include refid="selectDroughtMeetingVo"/>
        <where>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="idDrill != null "> and id_drill = #{idDrill}</if>
        </where>
    </select>

    <select id="selectDroughtMeetingById"  resultType="com.jhdr.drought.entity.vo.DroughtMeetingVo">
        select id, url,name, plan_type, create_time, update_time, id_drill
        from chxh.drought_meeting
            where id = #{id}
    </select>
    <select id="getDroughtList" resultType="com.jhdr.drought.entity.vo.DroughtMeetingVo">
        select id, url,name, plan_type, create_time, update_time, id_drill
        from chxh.drought_meeting
        <where>
            <if test="idDrill != null "> and id_drill = #{idDrill}</if>
            <if test="name != null "> and name like concat('%',#{name},'%')</if>
            <if test="timeA != null and timeB != null"> and create_time between #{timeA} and #{timeB}</if>
        </where>
    </select>
    <select id="download" resultType="java.lang.String">
        select  url
        from chxh.drought_meeting
        where id = #{id}
    </select>

    <insert id="insertDroughtMeeting" parameterType="com.jhdr.drought.entity.param.DroughtMeetingAddParam">
        insert into chxh.drought_meeting
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="url != null">url,</if>
                    <if test="planType != null">plan_type,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="idDrill != null">id_drill,</if>
                    <if test="name != null">name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="url != null">#{url},</if>
                    <if test="planType != null">#{planType},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="idDrill != null">#{idDrill},</if>
            <if test="name != null">#{name},</if>
        </trim>
    </insert>

    <update id="updateDroughtMeeting" parameterType="com.jhdr.drought.entity.param.DroughtMeetingEditParam">
        update drought_meeting
        <trim prefix="SET" suffixOverrides=",">
                    <if test="url != null">url = #{url},</if>
                    <if test="planType != null">plan_type = #{planType},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="idDrill != null">id_drill = #{idDrill},</if>
                    <if test="name != null">name = #{name},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDroughtMeetingById" parameterType="Long">
        delete from chxh.drought_meeting where id = #{id}
    </delete>

    <delete id="deleteDroughtMeetingByIds" parameterType="String">
        delete from chxh.drought_meeting where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
