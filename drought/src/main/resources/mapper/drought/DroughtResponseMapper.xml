<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.drought.mapper.DroughtResponseMapper">

    <resultMap type="com.jhdr.drought.entity.po.DroughtResponsePo" id="DroughtResponseResult">
        <result property="id"    column="id"    />
        <result property="emergencies"    column="emergencies"    />
        <result property="emergencyLevel"    column="emergency_level"    />
        <result property="emergencyPlan"    column="emergency_plan"    />
        <result property="personsId"    column="persons_id"    />
        <result property="deploymentProposals"    column="deployment_proposals"    />
    </resultMap>

    <sql id="selectDroughtResponseVo">
        select id, emergencies, emergency_level, emergency_plan, persons_id, deployment_proposals from drought_response
    </sql>

    <select id="selectDroughtResponseList"  resultType="com.jhdr.drought.entity.vo.DroughtResponseVo">
        <include refid="selectDroughtResponseVo"/>
        <where>
            <if test="emergencies != null  and emergencies != ''"> and emergencies = #{emergencies}</if>
            <if test="emergencyLevel != null "> and emergency_level = #{emergencyLevel}</if>
            <if test="emergencyPlan != null  and emergencyPlan != ''"> and emergency_plan = #{emergencyPlan}</if>
            <if test="personsId != null "> and persons_id = #{personsId}</if>
            <if test="deploymentProposals != null  and deploymentProposals != ''"> and deployment_proposals = #{deploymentProposals}</if>
        </where>
    </select>

    <select id="selectDroughtResponseById"  resultType="com.jhdr.drought.entity.vo.DroughtResponseVo">
            <include refid="selectDroughtResponseVo"/>
            where id = #{id}
    </select>
    <select id="selectDroughtResponseListByCOndition"
            resultType="com.jhdr.drought.entity.vo.DroughtResponseVo">
        select id, emergencies, emergency_level, emergency_plan, persons_id, deployment_proposals from drought_response
        <where>
            <if test="droughtResponse.emergencyLevel!=null">
                emergency_level = #{droughtResponse.emergencyLevel}
            </if>
        </where>
    </select>

    <insert id="insertDroughtResponse" parameterType="com.jhdr.drought.entity.param.DroughtResponseAddParam">
        insert into drought_response
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="emergencies != null">emergencies,</if>
                    <if test="emergencyLevel != null">emergency_level,</if>
                    <if test="emergencyPlan != null">emergency_plan,</if>
                    <if test="personsId != null">persons_id,</if>
                    <if test="deploymentProposals != null">deployment_proposals,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="emergencies != null">#{emergencies},</if>
                    <if test="emergencyLevel != null">#{emergencyLevel},</if>
                    <if test="emergencyPlan != null">#{emergencyPlan},</if>
                    <if test="personsId != null">#{personsId},</if>
                    <if test="deploymentProposals != null">#{deploymentProposals},</if>
        </trim>
    </insert>
    <insert id="insertOne">
        insert into drought_response (id, emergencies, emergency_level, emergency_plan, persons_id, deployment_proposals) values
                                (#{droughtResponse.id},#{droughtResponse.emergencies},#{droughtResponse.emergencyLevel},#{droughtResponse.emergencyPlan},#{droughtResponse.personsId},#{droughtResponse.deploymentProposals})
    </insert>

    <update id="updateDroughtResponse" parameterType="com.jhdr.drought.entity.param.DroughtResponseEditParam">
        update drought_response
        <trim prefix="SET" suffixOverrides=",">
                    <if test="emergencies != null">emergencies = #{emergencies},</if>
                    <if test="emergencyLevel != null">emergency_level = #{emergencyLevel},</if>
                    <if test="emergencyPlan != null">emergency_plan = #{emergencyPlan},</if>
                    <if test="personsId != null">persons_id = #{personsId},</if>
                    <if test="deploymentProposals != null">deployment_proposals = #{deploymentProposals},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDroughtResponseById" parameterType="Long">
        delete from drought_response where id = #{id}
    </delete>

    <delete id="deleteDroughtResponseByIds" parameterType="String">
        delete from drought_response where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
