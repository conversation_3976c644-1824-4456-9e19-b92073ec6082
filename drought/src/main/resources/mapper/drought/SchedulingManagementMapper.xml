<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.drought.mapper.SchedulingManagementMapper">

    <resultMap type="com.jhdr.drought.entity.po.SchedulingManagementPo" id="SchedulingManagementResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="unit"    column="unit"    />
        <result property="hairPeople"    column="hair_people"    />
        <result property="createTime"    column="create_time"    />
        <result property="status"    column="status"    />
        <result property="verifier"    column="verifier"    />
        <result property="verificationOpinions"    column="verification_opinions"    />
        <result property="reviewers"    column="reviewers"    />
        <result property="reviewOpinions"    column="review_opinions"    />
        <result property="reviewTime"    column="review_time"    />
        <result property="calibrationTime"    column="calibration_time"    />
        <result property="remark"    column="remark"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectSchedulingManagementVo">
        select id, title, content, unit, hair_people, create_time, status, verifier, verification_opinions, reviewers, review_opinions, review_time, calibration_time, remark, url from scheduling_management
    </sql>

    <select id="selectSchedulingManagementList"  resultType="com.jhdr.drought.entity.vo.SchedulingManagementVo">
        <include refid="selectSchedulingManagementVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="hairPeople != null  and hairPeople != ''"> and hair_people = #{hairPeople}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="verifier != null  and verifier != ''"> and verifier = #{verifier}</if>
            <if test="verificationOpinions != null  and verificationOpinions != ''"> and verification_opinions = #{verificationOpinions}</if>
            <if test="reviewers != null  and reviewers != ''"> and reviewers = #{reviewers}</if>
            <if test="reviewOpinions != null  and reviewOpinions != ''"> and review_opinions = #{reviewOpinions}</if>
            <if test="reviewTime != null "> and review_time = #{reviewTime}</if>
            <if test="calibrationTime != null "> and calibration_time = #{calibrationTime}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
        </where>
    </select>

    <select id="selectSchedulingManagementById"  resultType="com.jhdr.drought.entity.vo.SchedulingManagementVo">
            <include refid="selectSchedulingManagementVo"/>
            where id = #{id}
    </select>
    <select id="getAllList" resultType="com.jhdr.drought.entity.vo.SchedulingManagementVo">
        select id, title, content, unit, hair_people, create_time, status, verifier, verification_opinions, reviewers, review_opinions, review_time, calibration_time, remark, url from scheduling_management
        <where>
            <if test="startTime !=null and endTime !=null">
                create_time between #{startTime} and #{endTime}
            </if>
            <if test="title !=null">
            title = #{title}
            </if>
            <if test="status !=null">
                status = #{status}
            </if>
        </where>
    </select>
    <select id="getOneById" resultType="com.jhdr.drought.entity.vo.SchedulingManagementVo">
        select id, title, content, unit, hair_people, create_time, status, verifier, verification_opinions, reviewers, review_opinions, review_time, calibration_time, remark, url from scheduling_management
        where id = #{id}
    </select>
    <select id="getStatistics" resultType="com.jhdr.drought.entity.vo.StatisticsVo">
        select count(*) as count from scheduling_management where create_time between #{begin} and #{end}
    </select>

    <insert id="insertSchedulingManagement" parameterType="com.jhdr.drought.entity.param.SchedulingManagementAddParam">
        insert into scheduling_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="title != null">title,</if>
                    <if test="content != null">content,</if>
                    <if test="unit != null">unit,</if>
                    <if test="hairPeople != null">hair_people,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="status != null">status,</if>
                    <if test="verifier != null">verifier,</if>
                    <if test="verificationOpinions != null">verification_opinions,</if>
                    <if test="reviewers != null">reviewers,</if>
                    <if test="reviewOpinions != null">review_opinions,</if>
                    <if test="reviewTime != null">review_time,</if>
                    <if test="calibrationTime != null">calibration_time,</if>
                    <if test="remark != null">remark,</if>
                    <if test="url != null">url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="title != null">#{title},</if>
                    <if test="content != null">#{content},</if>
                    <if test="unit != null">#{unit},</if>
                    <if test="hairPeople != null">#{hairPeople},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="status != null">#{status},</if>
                    <if test="verifier != null">#{verifier},</if>
                    <if test="verificationOpinions != null">#{verificationOpinions},</if>
                    <if test="reviewers != null">#{reviewers},</if>
                    <if test="reviewOpinions != null">#{reviewOpinions},</if>
                    <if test="reviewTime != null">#{reviewTime},</if>
                    <if test="calibrationTime != null">#{calibrationTime},</if>
                    <if test="remark != null">#{remark},</if>
                    <if test="url != null">#{url},</if>
        </trim>
    </insert>

    <update id="updateSchedulingManagement" parameterType="com.jhdr.drought.entity.param.SchedulingManagementEditParam">
        update scheduling_management
        <trim prefix="SET" suffixOverrides=",">
                    <if test="title != null">title = #{title},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="unit != null">unit = #{unit},</if>
                    <if test="hairPeople != null">hair_people = #{hairPeople},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="verifier != null">verifier = #{verifier},</if>
                    <if test="verificationOpinions != null">verification_opinions = #{verificationOpinions},</if>
                    <if test="reviewers != null">reviewers = #{reviewers},</if>
                    <if test="reviewOpinions != null">review_opinions = #{reviewOpinions},</if>
                    <if test="reviewTime != null">review_time = #{reviewTime},</if>
                    <if test="calibrationTime != null">calibration_time = #{calibrationTime},</if>
                    <if test="remark != null">remark = #{remark},</if>
                    <if test="url != null">url = #{url},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSchedulingManagementById" parameterType="String">
        delete from scheduling_management where id = #{id}
    </delete>

    <delete id="deleteSchedulingManagementByIds" parameterType="String">
        delete from scheduling_management where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
