<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.drought.mapper.DroughtPersonMapper">

    <resultMap type="com.jhdr.drought.entity.po.DroughtPersonPo" id="DroughtPersonResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="relevancyId"    column="relevancy_id"    />
    </resultMap>

    <sql id="selectDroughtPersonVo">
        select id, name, phone,relevancy_id  from drought_person
    </sql>

    <select id="selectDroughtPersonList"  resultType="com.jhdr.drought.entity.vo.DroughtPersonVo">
        <include refid="selectDroughtPersonVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null "> and phone = #{phone}</if>
        </where>
    </select>

    <select id="selectDroughtPersonById"  resultType="com.jhdr.drought.entity.vo.DroughtPersonVo">
            <include refid="selectDroughtPersonVo"/>
            where id = #{id}
    </select>
    <select id="selectDroughtPersonByRelevancyId" resultType="com.jhdr.drought.entity.po.DroughtPersonPo">
        select id, name, phone,relevancy_id from drought_person where relevancy_id = #{personsId}
    </select>
    <select id="selectDroughtPersonId" resultType="com.jhdr.drought.entity.vo.DroughtPersonVo">
        select id, name, phone, <if test="phone != null">phone,</if> from drought_person where relevancy_id = #{id}
    </select>

    <insert id="insertDroughtPerson" parameterType="com.jhdr.drought.entity.param.DroughtPersonAddParam">
        insert into drought_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="name != null">name,</if>
                    <if test="phone != null">phone,</if>
                    <if test="relevancyId != null">relevancy_id,</if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="name != null">#{name},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="relevancyId != null">#{relevancy_id},</if>
        </trim>
    </insert>

    <update id="updateDroughtPerson" parameterType="com.jhdr.drought.entity.param.DroughtPersonEditParam">
        update drought_person
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">name = #{name},</if>
                    <if test="phone != null">phone = #{phone},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateByrelevancyId">
        update drought_person set name =#{droughtPersonPo.name} ,phone=#{droughtPersonPo.phone} where relevancy_id = #{droughtPersonPo.relevancyId}
    </update>

    <delete id="deleteDroughtPersonById" parameterType="Long">
        delete from drought_person where id = #{id}
    </delete>

    <delete id="deleteDroughtPersonByIds" parameterType="String">
        delete from drought_person where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
