package com.jhdr.drought.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 响应准备-人员信息对象 drought_person
 *
 * <AUTHOR>
 * @date 2024-09-14
 */

@Data
@ApiModel(description = "响应准备-人员信息")
@Accessors(chain = true)
public class DroughtPersonEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty(value = "主键id")
    @TableId
    private Long id;

    /** 姓名 */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名")
    private String name;

    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    @Excel(name = "手机号")
    private Long phone;

    @ApiModelProperty(value = "关联id")
    @Excel(name = "关联id")
    private String relevancyId;
}
