package com.jhdr.drought.entity.param;

import java.io.Serializable;
import java.util.List;

import com.jhdr.common.core.annotation.Excel;
import com.jhdr.drought.entity.po.DroughtPersonPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 响应准备对象 drought_response
 *
 * <AUTHOR>
 * @date 2024-09-14
 */

@Data
@ApiModel(description = "响应准备")
@Accessors(chain = true)
public class DroughtResponseAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;



    /** 应急险情 */
    @ApiModelProperty(value = "应急险情")
    @Excel(name = "应急险情")
    private String emergencies;

    /** 应急响应级别(1,2,3,4) */
    @ApiModelProperty(value = "应急响应级别(1,2,3,4)")
    @Excel(name = "应急响应级别(1,2,3,4)")
    private Long emergencyLevel;

    /** 应急预案 */
    @ApiModelProperty(value = "应急预案")
    @Excel(name = "应急预案")
    private String emergencyPlan;

/*    *//** 相关人员（关联drought_person的id） *//*
    @ApiModelProperty(value = "相关人员（关联drought_person的id）")
    @Excel(name = "相关人员", readConverterExp = "关=联drought_person的id")
    private Long personsId;*/


    /** 调遣建议 */
    @ApiModelProperty(value = "调遣建议")
    @Excel(name = "调遣建议")
    private String deploymentProposals;

    @ApiModelProperty(value = "新增人员列表")
    @Excel(name = "新增人员列表")
    private List<DroughtPersonPo> list;
}
