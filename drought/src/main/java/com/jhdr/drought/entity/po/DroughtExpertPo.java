package com.jhdr.drought.entity.po;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水旱防灾专家信息库对象 drought_expert
 *
 * <AUTHOR>
 * @date 2024-06-19
 */

@Data
@ApiModel(description = "水旱防灾专家信息库")
@Accessors(chain = true)
@TableName(value ="drought_expert",schema = "chxh")
public class DroughtExpertPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 专家编号 */
    @ApiModelProperty(value = "专家编号")
    @TableId
    private Long id;

    /** 姓名 */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名")
    private String name;

    /** 单位 */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String unit;

    /** 职位 */
    @ApiModelProperty(value = "职位")
    @Excel(name = "职位")
    private String position;

    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    @Excel(name = "手机号")
    private Long phone;

    /** 办公电话 */
    @ApiModelProperty(value = "办公电话")
    @Excel(name = "办公电话")
    private Long officePhone;

    /** 职称 */
    @ApiModelProperty(value = "职称")
    @Excel(name = "职称")
    private String professional;

    /** 行业类别 */
    @ApiModelProperty(value = "行业类别")
    @Excel(name = "行业类别")
    private String type;

    /** 擅长专业1 */
    @ApiModelProperty(value = "擅长专业1")
    @Excel(name = "擅长专业1")
    private String majorA;

    /** 擅长专业2 */
    @ApiModelProperty(value = "擅长专业2")
    @Excel(name = "擅长专业2")
    private String majorB;

    /** 主要经历 */
    @ApiModelProperty(value = "主要经历")
    @Excel(name = "主要经历")
    private String experience;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String time;

    /** 修改时间 */
    @ApiModelProperty(value = "修改时间")
    private String updateTime;

    /** 照片地址 */
    @ApiModelProperty(value = "照片地址")
    @Excel(name = "照片地址")
    private String url;

}
