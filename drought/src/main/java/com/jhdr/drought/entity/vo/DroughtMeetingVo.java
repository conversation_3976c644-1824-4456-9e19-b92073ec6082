package com.jhdr.drought.entity.vo;

import java.io.Serializable;
import java.util.Date;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 异地会商对象 drought_meeting
 *
 * <AUTHOR>
 * @date 2024-06-18
 */

@Data
@ApiModel(description = "异地会商")
@Accessors(chain = true)
public class DroughtMeetingVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 异地会商表id */
    @ApiModelProperty(value = "异地会商表id")
    @TableId
    private Long id;

    /** 文件id */
    @ApiModelProperty(value = "文件地址")
    @TableId
    private String url;

    /** 文件名称 */
    @ApiModelProperty(value = "文件名称")
    private String name;

    /** 预警类型 */
    @ApiModelProperty(value = "预警类型")
    @Excel(name = "预警类型")
    private String planType;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private String createTime;

    /** 修改时间 */
    @ApiModelProperty(value = "修改时间")
    private String updateTime;

    /** 是否演练 */
    @ApiModelProperty(value = "是否演练")
    @Excel(name = "是否演练")
    private Long idDrill;

}
