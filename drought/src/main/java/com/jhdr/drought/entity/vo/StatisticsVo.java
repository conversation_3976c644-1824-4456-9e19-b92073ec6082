package com.jhdr.drought.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 调度统计vo
 *
 * <AUTHOR>
 * @version 1.0
 * @project chxh_back
 * @date 2024/7/3 19:55:45
 */
@Data
@ApiModel(description = "调度统计")
@Accessors(chain = true)
public class StatisticsVo implements Serializable {

    @ApiModelProperty(value = "月份")
    @Excel(name = "月份")
    private String month;

    @ApiModelProperty(value = "月统计数")
    @Excel(name = "月统计数")
    private Integer count;

}
