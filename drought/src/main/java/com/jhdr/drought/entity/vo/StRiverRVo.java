package com.jhdr.drought.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 河道水位信息对象 st_river_r
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "河道水位信息")
@Accessors(chain = true)
public class StRiverRVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String STCD;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @Excel(name = "时间")
    private String TM;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private BigDecimal Z;

    /** 流量 */
    @ApiModelProperty(value = "流量")
    @Excel(name = "流量")
    private BigDecimal Q;

    /** 断面过水面积 */
    @ApiModelProperty(value = "断面过水面积")
    @Excel(name = "断面过水面积")
    private BigDecimal XSA;

    /** 断面平均流速 */
    @ApiModelProperty(value = "断面平均流速")
    @Excel(name = "断面平均流速")
    private BigDecimal XSAVV;

    /** 断面最大流速 */
    @ApiModelProperty(value = "断面最大流速")
    @Excel(name = "断面最大流速")
    private BigDecimal XSMXV;

    /** 河水特征码 */
    @ApiModelProperty(value = "河水特征码")
    @Excel(name = "河水特征码")
    private String FLWCHRCD;

    /** 水势 */
    @ApiModelProperty(value = "水势")
    @Excel(name = "水势")
    private String WPTN;

    /** 测流方法 */
    @ApiModelProperty(value = "测流方法")
    @Excel(name = "测流方法")
    private String MSQMT;

    /** 测积方法 */
    @ApiModelProperty(value = "测积方法")
    @Excel(name = "测积方法")
    private String MSAMT;

    /** 测速方法 */
    @ApiModelProperty(value = "测速方法")
    @Excel(name = "测速方法")
    private String MSVMT;

}
