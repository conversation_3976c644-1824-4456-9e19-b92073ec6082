package com.jhdr.drought.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 调度管理对象 scheduling_management
 *
 * <AUTHOR>
 * @date 2024-07-03
 */

@Data
@ApiModel(description = "调度管理")
@Accessors(chain = true)
public class SchedulingManagementParam implements Serializable
{
    private static final long serialVersionUID = 1L;

/*
    */
/** 主键id *//*

    @ApiModelProperty(value = "主键id")
    @TableId
    private String id;
*/

    /** 指令标题 */
    @ApiModelProperty(value = "指令标题")
    @Excel(name = "指令标题")
    private String title;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间")
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间")
    @Excel(name = "结束时间")
    private String endTime;

    /** 状态（1：提交，2：校验，3：审核，4：完成） */
    @ApiModelProperty(value = "状态（1：提交，2：校验，3：审核，4：完成）")
    @Excel(name = "状态", readConverterExp = "1=：提交，2：校验，3：审核，4：完成")
    private Long status;

   /* *//** 指令内容 *//*
    @ApiModelProperty(value = "指令内容")
    @Excel(name = "指令内容")
    private String content;

    *//** 指令单位 *//*
    @ApiModelProperty(value = "指令单位")
    @Excel(name = "指令单位")
    private String unit;

    *//** 发令人 *//*
    @ApiModelProperty(value = "发令人")
    @Excel(name = "发令人")
    private String hairPeople;

    *//** 创建时间 *//*
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    *//** 状态（1：提交，2：校验，3：审核，4：完成） *//*
    @ApiModelProperty(value = "状态（1：提交，2：校验，3：审核，4：完成）")
    @Excel(name = "状态", readConverterExp = "1=：提交，2：校验，3：审核，4：完成")
    private Long status;

    *//** 校验人 *//*
    @ApiModelProperty(value = "校验人")
    @Excel(name = "校验人")
    private String verifier;

    *//** 校验意见 *//*
    @ApiModelProperty(value = "校验意见")
    @Excel(name = "校验意见")
    private String verificationOpinions;

    *//** 审核人 *//*
    @ApiModelProperty(value = "审核人")
    @Excel(name = "审核人")
    private String reviewers;

    *//** 审核意见 *//*
    @ApiModelProperty(value = "审核意见")
    @Excel(name = "审核意见")
    private String reviewOpinions;

    *//** 审核时间 *//*
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    *//** 校验时间 *//*
    @ApiModelProperty(value = "校验时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "校验时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date calibrationTime;

    *//** 备注 *//*
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;

    *//** 附件地址 *//*
    @ApiModelProperty(value = "附件地址")
    @Excel(name = "附件地址")
    private String url;
*/
}
