package com.jhdr.drought.service;

import java.util.List;
import com.jhdr.drought.entity.po.SchedulingManagementPo;
import com.jhdr.drought.entity.param.SchedulingManagementParam;
import com.jhdr.drought.entity.param.SchedulingManagementAddParam;
import com.jhdr.drought.entity.param.SchedulingManagementEditParam;
import com.jhdr.drought.entity.vo.SchedulingManagementVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.drought.entity.vo.StatisticsVo;

/**
 * 调度管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
public interface ISchedulingManagementService extends IService<SchedulingManagementPo>
{

    /**
     * 查询调度管理列表
     *
     * @param schedulingManagement 调度管理
     * @return 调度管理集合
     */
    public List<SchedulingManagementVo> queryList(SchedulingManagementPo schedulingManagement);

    /**
     * 查询调度管理
     *
     * @param id 调度管理主键
     * @return 调度管理
     */
    public SchedulingManagementVo selectSchedulingManagementById(String id);

    /**
     * 查询调度管理列表
     *
     * @param schedulingManagement 调度管理
     * @return 调度管理集合
     */
    public List<SchedulingManagementVo> selectSchedulingManagementList(SchedulingManagementParam schedulingManagement);

    /**
     * 新增调度管理
     *
     * @param schedulingManagement 调度管理
     * @return 结果
     */
    public int insertSchedulingManagement(SchedulingManagementAddParam schedulingManagement);

    /**
     * 修改调度管理
     *
     * @param schedulingManagement 调度管理
     * @return 结果
     */
    public int updateSchedulingManagement(SchedulingManagementEditParam schedulingManagement);

    /**
     * 批量删除调度管理
     *
     * @param ids 需要删除的调度管理主键集合
     * @return 结果
     */
    public int deleteSchedulingManagementByIds(String[] ids);

    /**
     * 删除调度管理信息
     *
     * @param id 调度管理主键
     * @return 结果
     */
    public int deleteSchedulingManagementById(String id);

    List<StatisticsVo> getStatistics();
}
