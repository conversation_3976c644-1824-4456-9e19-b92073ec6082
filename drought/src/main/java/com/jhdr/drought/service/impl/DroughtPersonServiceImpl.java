package com.jhdr.drought.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.drought.entity.po.DroughtPersonPo;
import com.jhdr.drought.entity.param.DroughtPersonParam;
import com.jhdr.drought.entity.param.DroughtPersonAddParam;
import com.jhdr.drought.entity.param.DroughtPersonEditParam;
import com.jhdr.drought.entity.vo.DroughtPersonVo;
import com.jhdr.drought.mapper.DroughtPersonMapper;
import com.jhdr.drought.service.IDroughtPersonService;

import java.util.ArrayList;

import java.util.List;

/**
 * 响应准备-人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
public class DroughtPersonServiceImpl extends ServiceImpl<DroughtPersonMapper, DroughtPersonPo> implements IDroughtPersonService {

    @Override
    public List<DroughtPersonVo> queryList(DroughtPersonPo droughtPerson) {
        LambdaQueryWrapper<DroughtPersonPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(droughtPerson.getName())){
            lqw.like(DroughtPersonPo::getName ,droughtPerson.getName());
        }
        if (droughtPerson.getPhone() != null){
            lqw.eq(DroughtPersonPo::getPhone ,droughtPerson.getPhone());
        }
        List<DroughtPersonVo> droughtPersonVos= BeanUtil.copyToList(this.list(lqw), DroughtPersonVo.class);
        return droughtPersonVos;
    }
    /**
     * 查询响应准备-人员信息
     *
     * @param id 响应准备-人员信息主键
     * @return 响应准备-人员信息
     */
    @Override
    public DroughtPersonVo selectDroughtPersonById(String  id)
    {
        DroughtPersonVo vo = baseMapper.selectDroughtPersonId(id);
        return vo;
    }

    /**
     * 查询响应准备-人员信息列表
     *
     * @param droughtPerson 响应准备-人员信息
     * @return 响应准备-人员信息
     */
    @Override
    public List<DroughtPersonVo> selectDroughtPersonList(DroughtPersonParam droughtPerson)
    {
        return baseMapper.selectDroughtPersonList(droughtPerson);
    }

    /**
     * 新增响应准备-人员信息
     *
     * @param droughtPersonAddParam 响应准备-人员信息
     * @return 结果
     */
    @Override
    public int insertDroughtPerson(DroughtPersonAddParam droughtPersonAddParam)
    {

        DroughtPersonPo droughtPerson=new DroughtPersonPo();
        BeanUtil.copyProperties(droughtPersonAddParam,droughtPerson);
        return baseMapper.insert(droughtPerson);
    }

    /**
     * 修改响应准备-人员信息
     *
     * @param droughtPersonEditParam 响应准备-人员信息
     * @return 结果
     */
    @Override
    public int updateDroughtPerson(DroughtPersonEditParam droughtPersonEditParam)
    {
        DroughtPersonPo droughtPerson=new DroughtPersonPo();
        BeanUtil.copyProperties(droughtPersonEditParam,droughtPerson);
        return baseMapper.updateById(droughtPerson);
    }

    /**
     * 批量删除响应准备-人员信息
     *
     * @param ids 需要删除的响应准备-人员信息主键
     * @return 结果
     */
    @Override
    public int deleteDroughtPersonByIds(Long[] ids)
    {
        return baseMapper.deleteDroughtPersonByIds(ids);
    }

    /**
     * 删除响应准备-人员信息信息
     *
     * @param id 响应准备-人员信息主键
     * @return 结果
     */
    @Override
    public int deleteDroughtPersonById(Long id)
    {
        return baseMapper.deleteDroughtPersonById(id);
    }
}
