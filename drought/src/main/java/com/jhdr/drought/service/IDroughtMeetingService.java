package com.jhdr.drought.service;

import java.util.List;
import com.jhdr.drought.entity.po.DroughtMeetingPo;
import com.jhdr.drought.entity.param.DroughtMeetingParam;
import com.jhdr.drought.entity.param.DroughtMeetingAddParam;
import com.jhdr.drought.entity.param.DroughtMeetingEditParam;
import com.jhdr.drought.entity.vo.DroughtMeetingVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 异地会商Service接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface IDroughtMeetingService extends IService<DroughtMeetingPo>
{

    /**
     * 查询异地会商列表
     *
     * @param droughtMeeting 异地会商
     * @return 异地会商集合
     */
    public List<DroughtMeetingVo> queryList(DroughtMeetingPo droughtMeeting);

    /**
     * 查询异地会商
     *
     * @param id 异地会商主键
     * @return 异地会商
     */
    public DroughtMeetingVo selectDroughtMeetingById(Long id);

    /**
     * 查询异地会商列表
     *
     * @param droughtMeeting 异地会商
     * @return 异地会商集合
     */
    public List<DroughtMeetingVo> selectDroughtMeetingList(DroughtMeetingParam droughtMeeting);

    /**
     * 新增异地会商
     *
     * @param droughtMeeting 异地会商
     * @return 结果
     */
    public int insertDroughtMeeting(DroughtMeetingAddParam droughtMeeting);

    /**
     * 修改异地会商
     *
     * @param droughtMeeting 异地会商
     * @return 结果
     */
    public int updateDroughtMeeting(DroughtMeetingEditParam droughtMeeting);

    /**
     * 批量删除异地会商
     *
     * @param ids 需要删除的异地会商主键集合
     * @return 结果
     */
    public int deleteDroughtMeetingByIds(Long[] ids);

    /**
     * 删除异地会商信息
     *
     * @param id 异地会商主键
     * @return 结果
     */
    public int deleteDroughtMeetingById(Long id);


    /**
     * 下载异地会商信息文件
     *
     * @param id 异地会商主键
     * @return 结果
     */
    String download(Long id);
}
