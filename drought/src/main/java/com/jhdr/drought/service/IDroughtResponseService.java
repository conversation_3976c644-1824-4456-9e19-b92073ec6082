package com.jhdr.drought.service;

import java.util.List;
import com.jhdr.drought.entity.po.DroughtResponsePo;
import com.jhdr.drought.entity.param.DroughtResponseParam;
import com.jhdr.drought.entity.param.DroughtResponseAddParam;
import com.jhdr.drought.entity.param.DroughtResponseEditParam;
import com.jhdr.drought.entity.vo.DroughtResponseVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 响应准备Service接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface IDroughtResponseService extends IService<DroughtResponsePo>
{

    /**
     * 查询响应准备列表
     *
     * @param droughtResponse 响应准备
     * @return 响应准备集合
     */
    public List<DroughtResponseVo> queryList(DroughtResponsePo droughtResponse);

    /**
     * 查询响应准备
     *
     * @param id 响应准备主键
     * @return 响应准备
     */
    public DroughtResponseVo selectDroughtResponseById(Long id);

    /**
     * 查询响应准备列表
     *
     * @param droughtResponse 响应准备
     * @return 响应准备集合
     */
    public List<DroughtResponseVo> selectDroughtResponseList(DroughtResponseParam droughtResponse);

    /**
     * 新增响应准备
     *
     * @param droughtResponse 响应准备
     * @return 结果
     */
    public int insertDroughtResponse(DroughtResponseAddParam droughtResponse);

    /**
     * 修改响应准备
     *
     * @param droughtResponse 响应准备
     * @return 结果
     */
    public int updateDroughtResponse(DroughtResponseEditParam droughtResponse);

    /**
     * 批量删除响应准备
     *
     * @param ids 需要删除的响应准备主键集合
     * @return 结果
     */
    public int deleteDroughtResponseByIds(Long[] ids);

    /**
     * 删除响应准备信息
     *
     * @param id 响应准备主键
     * @return 结果
     */
    public int deleteDroughtResponseById(Long id);

}
