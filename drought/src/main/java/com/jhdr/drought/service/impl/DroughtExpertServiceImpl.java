package com.jhdr.drought.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.drought.entity.param.DroughtExpertParmExcel;
import com.jhdr.drought.mapper.DroughtExpertMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.drought.entity.po.DroughtExpertPo;
import com.jhdr.drought.entity.param.DroughtExpertParam;
import com.jhdr.drought.entity.param.DroughtExpertAddParam;
import com.jhdr.drought.entity.param.DroughtExpertEditParam;
import com.jhdr.drought.entity.vo.DroughtExpertVo;
import com.jhdr.drought.service.IDroughtExpertService;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 水旱防灾专家信息库Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Service
public class DroughtExpertServiceImpl extends ServiceImpl<DroughtExpertMapper, DroughtExpertPo> implements IDroughtExpertService {
    @Autowired
    private DroughtExpertMapper droughtExpertMapper;

    @Override
    public List<DroughtExpertVo> queryList(DroughtExpertPo droughtExpert) {
        LambdaQueryWrapper<DroughtExpertPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(droughtExpert.getName())) {
            lqw.like(DroughtExpertPo::getName, droughtExpert.getName());
        }
        if (StringUtils.isNotBlank(droughtExpert.getUnit())) {
            lqw.eq(DroughtExpertPo::getUnit, droughtExpert.getUnit());
        }
        if (StringUtils.isNotBlank(droughtExpert.getPosition())) {
            lqw.eq(DroughtExpertPo::getPosition, droughtExpert.getPosition());
        }
        if (droughtExpert.getPhone() != null) {
            lqw.eq(DroughtExpertPo::getPhone, droughtExpert.getPhone());
        }
        if (droughtExpert.getOfficePhone() != null) {
            lqw.eq(DroughtExpertPo::getOfficePhone, droughtExpert.getOfficePhone());
        }
        if (StringUtils.isNotBlank(droughtExpert.getProfessional())) {
            lqw.eq(DroughtExpertPo::getProfessional, droughtExpert.getProfessional());
        }
        if (StringUtils.isNotBlank(droughtExpert.getType())) {
            lqw.eq(DroughtExpertPo::getType, droughtExpert.getType());
        }
        if (StringUtils.isNotBlank(droughtExpert.getMajorA())) {
            lqw.eq(DroughtExpertPo::getMajorA, droughtExpert.getMajorA());
        }
        if (StringUtils.isNotBlank(droughtExpert.getMajorB())) {
            lqw.eq(DroughtExpertPo::getMajorB, droughtExpert.getMajorB());
        }
        if (StringUtils.isNotBlank(droughtExpert.getExperience())) {
            lqw.eq(DroughtExpertPo::getExperience, droughtExpert.getExperience());
        }
        if (droughtExpert.getTime() != null) {
            lqw.eq(DroughtExpertPo::getTime, droughtExpert.getTime());
        }
        if (StringUtils.isNotBlank(droughtExpert.getUrl())) {
            lqw.eq(DroughtExpertPo::getUrl, droughtExpert.getUrl());
        }
        List<DroughtExpertVo> droughtExpertVos = BeanUtil.copyToList(this.list(lqw), DroughtExpertVo.class);
        return droughtExpertVos;
    }

    /**
     * 查询水旱防灾专家信息库
     *
     * @param id 水旱防灾专家信息库主键
     * @return 水旱防灾专家信息库
     */
    @Override
    public DroughtExpertVo selectDroughtExpertById(Long id) {
        return droughtExpertMapper.selectDroughtExpertById(id);
    }

    /**
     * 查询水旱防灾专家信息库列表
     *
     * @param droughtExpert 水旱防灾专家信息库
     * @return 水旱防灾专家信息库
     */
    @Override
    public List<DroughtExpertVo> selectDroughtExpertList(DroughtExpertParam droughtExpert) {

        List<DroughtExpertVo> list = droughtExpertMapper.getList(droughtExpert.getName(),
                droughtExpert.getType(), droughtExpert.getMajor());
        return list;
        // return droughtExpertMapper.selectDroughtExpertList(droughtExpert);
    }

    /**
     * 新增水旱防灾专家信息库
     *
     * @param droughtExpertAddParam 水旱防灾专家信息库
     * @return 结果
     */
    @Override
    public int insertDroughtExpert(DroughtExpertAddParam droughtExpertAddParam) {
        Long phone = droughtExpertAddParam.getPhone();
        if (Long.toString(phone).length() != 11) {
            return 0;
        }
        DroughtExpertPo droughtExpert = new DroughtExpertPo();
        BeanUtil.copyProperties(droughtExpertAddParam, droughtExpert);
/*        TimeZone time = TimeZone.getTimeZone("Etc/GMT-8");  //转换为中国时区
        TimeZone.setDefault(time);
        droughtExpert.setUpdateTime(time.toString());新增失败
        droughtExpert.setTime(time.toString());*/
        droughtExpert.setUpdateTime(new Date().toString());
        droughtExpert.setTime(new Date().toString());
        return droughtExpertMapper.insert(droughtExpert);
    }

    /**
     * 修改水旱防灾专家信息库
     *
     * @param droughtExpertEditParam 水旱防灾专家信息库
     * @return 结果
     */
    @Override
    public int updateDroughtExpert(DroughtExpertEditParam droughtExpertEditParam) {
        DroughtExpertPo droughtExpert = new DroughtExpertPo();

  /*      SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        if (time == null || time.isEmpty()) {
            // 设置默认日期或者提示用户输入正确的日期格式
            throw new IllegalArgumentException("日期不能为空");
        }
        //将日期转换为yyyy-MM-dd格式
        try {
            date = format.parse(time);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeBegin = format.format(date);
        droughtExpert.setTime(timeBegin);*/
        droughtExpert.setUpdateTime(new Date().toString());
        BeanUtil.copyProperties(droughtExpertEditParam, droughtExpert);
        System.out.println(1);
        return droughtExpertMapper.updateById(droughtExpert);

    }

    /**
     * 批量删除水旱防灾专家信息库
     *
     * @param ids 需要删除的水旱防灾专家信息库主键
     * @return 结果
     */
    @Override
    public int deleteDroughtExpertByIds(Long[] ids) {
        return droughtExpertMapper.deleteDroughtExpertByIds(ids);
    }

    /**
     * 删除水旱防灾专家信息库信息
     *
     * @param id 水旱防灾专家信息库主键
     * @return 结果
     */
    @Override
    public int deleteDroughtExpertById(Long id) {
        return droughtExpertMapper.deleteDroughtExpertById(id);
    }

    @Override
    public String importUser(List<DroughtExpertParmExcel> userList) {
        if (userList == null) {
            return "导入失败";
        } else {
            for (DroughtExpertParmExcel droughtExpertParmExcel : userList) {
                // String uuid = UUID.randomUUID().toString();
                DroughtExpertPo droughtExpertPo = new DroughtExpertPo();
                BeanUtil.copyProperties(droughtExpertParmExcel, droughtExpertPo);
                droughtExpertPo.setTime(new Date().toString());
                droughtExpertPo.setUpdateTime(new Date().toString());
                droughtExpertMapper.insert(droughtExpertPo);
            }
            return "导入成功";
        }
    }
}
