package com.jhdr.drought.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.drought.entity.param.DroughtPersonAddParam;
import com.jhdr.drought.entity.po.DroughtPersonPo;
import com.jhdr.drought.mapper.DroughtPersonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.drought.entity.po.DroughtResponsePo;
import com.jhdr.drought.entity.param.DroughtResponseParam;
import com.jhdr.drought.entity.param.DroughtResponseAddParam;
import com.jhdr.drought.entity.param.DroughtResponseEditParam;
import com.jhdr.drought.entity.vo.DroughtResponseVo;
import com.jhdr.drought.mapper.DroughtResponseMapper;
import com.jhdr.drought.service.IDroughtResponseService;

import java.util.ArrayList;

import java.util.List;
import java.util.UUID;

/**
 * 响应准备Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Service
public class DroughtResponseServiceImpl extends ServiceImpl<DroughtResponseMapper, DroughtResponsePo> implements IDroughtResponseService {
@Autowired
private DroughtResponseMapper droughtResponseMapper;
@Autowired
private DroughtPersonMapper droughtPersonMapper;
    @Override
    public List<DroughtResponseVo> queryList(DroughtResponsePo droughtResponse) {
        LambdaQueryWrapper<DroughtResponsePo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(droughtResponse.getEmergencies())){
            lqw.eq(DroughtResponsePo::getEmergencies ,droughtResponse.getEmergencies());
        }
        if (droughtResponse.getEmergencyLevel() != null){
            lqw.eq(DroughtResponsePo::getEmergencyLevel ,droughtResponse.getEmergencyLevel());
        }
        if (StringUtils.isNotBlank(droughtResponse.getEmergencyPlan())){
            lqw.eq(DroughtResponsePo::getEmergencyPlan ,droughtResponse.getEmergencyPlan());
        }
        if (droughtResponse.getPersonsId() != null){
            lqw.eq(DroughtResponsePo::getPersonsId ,droughtResponse.getPersonsId());
        }
        if (StringUtils.isNotBlank(droughtResponse.getDeploymentProposals())){
            lqw.eq(DroughtResponsePo::getDeploymentProposals ,droughtResponse.getDeploymentProposals());
        }
        List<DroughtResponseVo> droughtResponseVos= BeanUtil.copyToList(this.list(lqw), DroughtResponseVo.class);
        return droughtResponseVos;
    }
    /**
     * 查询响应准备
     *
     * @param id 响应准备主键
     * @return 响应准备
     */
    @Override
    public DroughtResponseVo selectDroughtResponseById(Long id)
    {
        DroughtResponseVo droughtResponseVo = baseMapper.selectDroughtResponseById(id);
        String personsId = droughtResponseVo.getPersonsId();
        List<DroughtPersonPo> droughtPersonPoList = droughtPersonMapper.selectDroughtPersonByRelevancyId(personsId);
        droughtResponseVo.setList(droughtPersonPoList);
        return droughtResponseVo;
    }

    /**
     * 查询响应准备列表
     *
     * @param droughtResponse 响应准备
     * @return 响应准备
     */
    @Override
    public List<DroughtResponseVo> selectDroughtResponseList(DroughtResponseParam droughtResponse)
    {
        List<DroughtResponseVo> list = droughtResponseMapper.selectDroughtResponseListByCOndition(droughtResponse);
        for (DroughtResponseVo droughtResponseVo : list) {
            String personsId = droughtResponseVo.getPersonsId();
            List<DroughtPersonPo> droughtPersonPoList = droughtPersonMapper.selectDroughtPersonByRelevancyId(personsId);
            droughtResponseVo.setList(droughtPersonPoList);
        }
        return list;
    }

    /**
     * 新增响应准备
     *
     * @param droughtResponseAddParam 响应准备
     * @return 结果
     */
    @Override
    public int insertDroughtResponse(DroughtResponseAddParam droughtResponseAddParam)
    {
        DroughtResponsePo droughtResponse=new DroughtResponsePo();
        BeanUtil.copyProperties(droughtResponseAddParam,droughtResponse);
        String uuid = UUID.randomUUID().toString();
        droughtResponse.setPersonsId(uuid);
        List<DroughtPersonPo> list = droughtResponseAddParam.getList();
        for (DroughtPersonPo droughtPersonPo : list) {
            //droughtPersonPo.setRelevancyId(uuid);
        /*    DroughtPersonAddParam droughtPersonAddParam = new DroughtPersonAddParam();
            BeanUtil.copyProperties(droughtPersonPo,droughtPersonAddParam);
            droughtPersonAddParam.setRelevancyId(uuid);
            droughtPersonMapper.insertDroughtPerson(droughtPersonAddParam);*/
            droughtPersonPo.setRelevancyId(uuid);
            droughtPersonMapper.insert(droughtPersonPo);
        }
        return droughtResponseMapper.insert(droughtResponse);
    }

    /**
     * 修改响应准备
     *
     * @param droughtResponseEditParam 响应准备
     * @return 结果
     */
    @Override
    public int updateDroughtResponse(DroughtResponseEditParam droughtResponseEditParam)
    {
        DroughtResponsePo droughtResponse=new DroughtResponsePo();
        BeanUtil.copyProperties(droughtResponseEditParam,droughtResponse);
        String personsId = droughtResponseEditParam.getPersonsId();
        List<DroughtPersonPo> list = droughtResponseEditParam.getList();
        for (DroughtPersonPo droughtPersonPo : list) {
            droughtPersonPo.setRelevancyId(personsId);
            droughtPersonMapper.updateByrelevancyId(droughtPersonPo);
        }

        return baseMapper.updateById(droughtResponse);
    }

    /**
     * 批量删除响应准备
     *
     * @param ids 需要删除的响应准备主键
     * @return 结果
     */
    @Override
    public int deleteDroughtResponseByIds(Long[] ids)
    {
        return baseMapper.deleteDroughtResponseByIds(ids);
    }

    /**
     * 删除响应准备信息
     *
     * @param id 响应准备主键
     * @return 结果
     */
    @Override
    public int deleteDroughtResponseById(Long id)
    {
        return baseMapper.deleteDroughtResponseById(id);
    }
}
