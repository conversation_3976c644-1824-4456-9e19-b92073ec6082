package com.jhdr.drought.service;

import java.util.List;
import com.jhdr.drought.entity.po.DroughtPersonPo;
import com.jhdr.drought.entity.param.DroughtPersonParam;
import com.jhdr.drought.entity.param.DroughtPersonAddParam;
import com.jhdr.drought.entity.param.DroughtPersonEditParam;
import com.jhdr.drought.entity.vo.DroughtPersonVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 响应准备-人员信息Service接口
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
public interface IDroughtPersonService extends IService<DroughtPersonPo>
{

    /**
     * 查询响应准备-人员信息列表
     *
     * @param droughtPerson 响应准备-人员信息
     * @return 响应准备-人员信息集合
     */
    public List<DroughtPersonVo> queryList(DroughtPersonPo droughtPerson);

    /**
     * 查询响应准备-人员信息
     *
     * @param id 响应准备-人员信息主键
     * @return 响应准备-人员信息
     */
    public DroughtPersonVo selectDroughtPersonById(String id);

    /**
     * 查询响应准备-人员信息列表
     *
     * @param droughtPerson 响应准备-人员信息
     * @return 响应准备-人员信息集合
     */
    public List<DroughtPersonVo> selectDroughtPersonList(DroughtPersonParam droughtPerson);

    /**
     * 新增响应准备-人员信息
     *
     * @param droughtPerson 响应准备-人员信息
     * @return 结果
     */
    public int insertDroughtPerson(DroughtPersonAddParam droughtPerson);

    /**
     * 修改响应准备-人员信息
     *
     * @param droughtPerson 响应准备-人员信息
     * @return 结果
     */
    public int updateDroughtPerson(DroughtPersonEditParam droughtPerson);

    /**
     * 批量删除响应准备-人员信息
     *
     * @param ids 需要删除的响应准备-人员信息主键集合
     * @return 结果
     */
    public int deleteDroughtPersonByIds(Long[] ids);

    /**
     * 删除响应准备-人员信息信息
     *
     * @param id 响应准备-人员信息主键
     * @return 结果
     */
    public int deleteDroughtPersonById(Long id);

}
