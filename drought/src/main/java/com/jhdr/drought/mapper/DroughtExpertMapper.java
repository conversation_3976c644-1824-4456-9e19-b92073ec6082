package com.jhdr.drought.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.drought.entity.po.DroughtExpertPo;
import com.jhdr.drought.entity.param.DroughtExpertParam;
import com.jhdr.drought.entity.param.DroughtExpertAddParam;
import com.jhdr.drought.entity.param.DroughtExpertEditParam;
import com.jhdr.drought.entity.vo.DroughtExpertVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 水旱防灾专家信息库Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Mapper
public interface DroughtExpertMapper extends BaseMapper<DroughtExpertPo>
{
    /**
     * 查询水旱防灾专家信息库
     *
     * @param id 水旱防灾专家信息库主键
     * @return 水旱防灾专家信息库
     */
    public DroughtExpertVo selectDroughtExpertById(Long id);

    /**
     * 查询水旱防灾专家信息库列表
     *
     * @param droughtExpert 水旱防灾专家信息库
     * @return 水旱防灾专家信息库集合
     */
    public List<DroughtExpertVo> selectDroughtExpertList(DroughtExpertParam droughtExpert);

    /**
     * 新增水旱防灾专家信息库
     *
     * @param droughtExpert 水旱防灾专家信息库
     * @return 结果
     */
    public int insertDroughtExpert(DroughtExpertAddParam droughtExpert);

    /**
     * 修改水旱防灾专家信息库
     *
     * @param droughtExpert 水旱防灾专家信息库
     * @return 结果
     */
    public int updateDroughtExpert(DroughtExpertEditParam droughtExpert);

    /**
     * 删除水旱防灾专家信息库
     *
     * @param id 水旱防灾专家信息库主键
     * @return 结果
     */
    public int deleteDroughtExpertById(Long id);

    /**
     * 批量删除水旱防灾专家信息库
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDroughtExpertByIds(Long[] ids);

    List<DroughtExpertVo> getList(@Param("name") String name,
                                  @Param("type")String type,
                                  @Param("major")String major);
}
