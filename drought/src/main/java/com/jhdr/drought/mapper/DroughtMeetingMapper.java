package com.jhdr.drought.mapper;

import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.drought.entity.po.DroughtMeetingPo;
import com.jhdr.drought.entity.param.DroughtMeetingParam;
import com.jhdr.drought.entity.param.DroughtMeetingAddParam;
import com.jhdr.drought.entity.param.DroughtMeetingEditParam;
import com.jhdr.drought.entity.vo.DroughtMeetingVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 异地会商Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface DroughtMeetingMapper extends BaseMapper<DroughtMeetingPo>
{
    /**
     * 查询异地会商
     *
     * @param id 异地会商主键
     * @return 异地会商
     */
    public DroughtMeetingVo selectDroughtMeetingById(Long id);

    /**
     * 查询异地会商列表
     *
     * @param droughtMeeting 异地会商
     * @return 异地会商集合
     */
    public List<DroughtMeetingVo> selectDroughtMeetingList(DroughtMeetingParam droughtMeeting);

    /**
     * 新增异地会商
     *
     * @param droughtMeeting 异地会商
     * @return 结果
     */
    public int insertDroughtMeeting(DroughtMeetingAddParam droughtMeeting);

    /**
     * 修改异地会商
     *
     * @param droughtMeeting 异地会商
     * @return 结果
     */
    public int updateDroughtMeeting(DroughtMeetingEditParam droughtMeeting);

    /**
     * 删除异地会商
     *
     * @param id 异地会商主键
     * @return 结果
     */
    public int deleteDroughtMeetingById(Long id);

    /**
     * 批量删除异地会商
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDroughtMeetingByIds(Long[] ids);

    List<DroughtMeetingVo> getDroughtList(@Param("timeA") String timeA,
                                          @Param("timeB")String timeB,
                                          @Param("idDrill")Long idDrill,
                                          @Param("name")String name);

    String download(@Param("id") Long id);
}
