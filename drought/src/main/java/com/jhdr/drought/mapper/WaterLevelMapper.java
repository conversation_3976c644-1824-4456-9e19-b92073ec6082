package com.jhdr.drought.mapper;

import com.jhdr.drought.entity.po.StStbprpBPo;
import com.jhdr.drought.entity.vo.StRiverRVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @project chxh_back
 * @date 2024/7/2 14:00:59
 */
@Mapper
public interface WaterLevelMapper {
    StRiverRVo selectRiverById(@Param("stcd") String stcd);

    StStbprpBPo selectStbprpByName(@Param("stnm") String stnm);
}
