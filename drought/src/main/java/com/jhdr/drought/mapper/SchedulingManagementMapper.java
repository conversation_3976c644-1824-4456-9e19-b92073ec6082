package com.jhdr.drought.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.drought.entity.po.SchedulingManagementPo;
import com.jhdr.drought.entity.param.SchedulingManagementParam;
import com.jhdr.drought.entity.param.SchedulingManagementAddParam;
import com.jhdr.drought.entity.param.SchedulingManagementEditParam;
import com.jhdr.drought.entity.vo.SchedulingManagementVo;
import com.jhdr.drought.entity.vo.StatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 调度管理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Mapper
public interface SchedulingManagementMapper extends BaseMapper<SchedulingManagementPo>
{
    /**
     * 查询调度管理
     *
     * @param id 调度管理主键
     * @return 调度管理
     */
    public SchedulingManagementVo selectSchedulingManagementById(String id);

    /**
     * 查询调度管理列表
     *
     * @param schedulingManagement 调度管理
     * @return 调度管理集合
     */
    public List<SchedulingManagementVo> selectSchedulingManagementList(SchedulingManagementParam schedulingManagement);

    /**
     * 新增调度管理
     *
     * @param schedulingManagement 调度管理
     * @return 结果
     */
    public int insertSchedulingManagement(SchedulingManagementAddParam schedulingManagement);

    /**
     * 修改调度管理
     *
     * @param schedulingManagement 调度管理
     * @return 结果
     */
    public int updateSchedulingManagement(SchedulingManagementEditParam schedulingManagement);

    /**
     * 删除调度管理
     *
     * @param id 调度管理主键
     * @return 结果
     */
    public int deleteSchedulingManagementById(String id);

    /**
     * 批量删除调度管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSchedulingManagementByIds(String[] ids);

    List<SchedulingManagementVo> getAllList(@Param("startTime") String startTime,
                                            @Param("endTime")String endTime,
                                            @Param("title")String title,
                                            @Param("status")Long status);

    SchedulingManagementVo getOneById(@Param("id") String id);

    StatisticsVo getStatistics(@Param("begin") String begin,@Param("end") String end);
}
