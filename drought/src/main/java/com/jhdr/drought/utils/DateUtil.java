package com.jhdr.drought.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 版权：金海迪尔公司.大数据研发中心
 *
 * @author: 日期. 李家耀. 创建.
 * 日期. 人名.增加/修改的内容.
 * @description: 用于生成各类时间及时间转换
 */

public class DateUtil {
    //流量计算除3600
    public static String  getexcept(String h12) {
        if (!h12.equals("-")) {
            Double aDouble = Double.valueOf(h12);
            aDouble = aDouble / 3600;
            BigDecimal b = new BigDecimal(aDouble);
            double f1 = b.setScale(3, BigDecimal.ROUND_DOWN).doubleValue();
            return String.valueOf(f1);
        }else {
            return h12;
        }
    }
    //获取年
    public static String getYear(Date dateTime){
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        return format.format(dateTime);
    }
    //计算两个时间相差多少个小时
    public static int test(String time1,String time2){
        try {
            DateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
            Date star = dft.parse(time1);
            Date endDay = dft.parse(time2);//结束时间
            Long starTime = star.getTime();
            Long endTime = endDay.getTime();
            Long num = endTime - starTime;//时间戳相差的毫秒数
            int dd = Math.toIntExact(num  / 60 / 60 / 1000);
            System.out.println(dd);//结果为12
            return dd;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 32;
    }

    //将Date类型数据，转换为字符串类型日期
    //转换结果为yyyy-MM-dd HH:mm:ss，举例：2021-07-16 16:07:59
    public static String getYMDHms(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = sdf.format(date);
        return str;
    }

    //将字符串类型数据，转换为日期类型
    //转换结果为yyyy-MM-dd HH:mm:ss，举例：2021-07-16 16:07:59
    public static Date getYMDHms(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    //将字符串类型数据，转换为日期类型
    //转换结果为yyyy-MM-dd HH:mm:ss，举例：2021-07-16
    public static Date getYMD(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    //计算现在与输入的日期之间，相差多少分钟
    public static long diffNowMinute(Date date) {
        Calendar calendarNow = Calendar.getInstance();
        Date dateNow = calendarNow.getTime();
        long longNow = dateNow.getTime();
        long time = date.getTime();
        long diff;
        if (longNow < time) {
            diff = time - longNow;
        } else {
            diff = longNow - time;
        }
        long longMinute = (long) (diff / (60 * 1000));
        return longMinute;
    }

    //返回现在的时间
    //结果为yyyy-MM-dd HH:mm:ss，举例：2021-07-16 16:07:59
    public static String getNowYMDHms() {
        Calendar calendarNow = Calendar.getInstance();
        Date dateNow = calendarNow.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = sdf.format(dateNow);
        return str;
    }

    //返回现在的时间
    //结果为yyyy-MM-dd，举例：2021-07-16
    public static String getNowYMD() {
        Calendar calendarNow = Calendar.getInstance();
        Date dateNow = calendarNow.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String str = sdf.format(dateNow);
        return str;
    }

    //计算输入的日期，diffDay后的日期，正值为之后的日期，负值为之前的日期
    //结果为yyyy-MM-dd，举例：2021-07-16
    public static String getDayDiffYMD(String yyyyMMdd, int diffDay) {
        Calendar calendarNow = DateUtil.getCalendarYMD(yyyyMMdd);
        calendarNow.add(Calendar.DATE, diffDay);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd ").format(calendarNow.getTime());
        return yesterday;
    }

    //计算输入的时间，diffHour后的时间，正值为之后的时间，负值为之前的时间
    //结果为yyyy-MM-dd HH:mm:ss，举例：2021-07-16 16:07:59
    public static String getHourDiffYMDHms(String yyyyMMddHHmmss, int diffHour) {
        Calendar calendar = DateUtil.getCalendarYMDHms(yyyyMMddHHmmss);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //计算1小时前的时间
        calendar.add(Calendar.HOUR, diffHour);
        String str = sdf.format(calendar.getTime());
        return str;
    }

    //计算输入的时间，diffHour后的时间天数，正值为之后的时间，负值为之前的时间
    public static String getHourDiffYM(String yyyyMMddHHmmss, int diffHour) {
        Calendar calendar = DateUtil.getCalendarYMDHms(yyyyMMddHHmmss);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //计算1小时前的时间
        calendar.add(Calendar.DAY_OF_MONTH, diffHour);
        String str = sdf.format(calendar.getTime());
        return str;
    }

    //计算当月第1天的时间
    public static String getMonthStarDayYMD(String yyyyMMddHHmmss) {
        Calendar calendar = DateUtil.getCalendarYMDHms(yyyyMMddHHmmss);
        //计算当月第1天的时间
        int month = calendar.get(Calendar.MONTH) + 1;
        int year = calendar.get(Calendar.YEAR);
        String MonthStar = String.valueOf(year) + "-" + String.valueOf(month) + "-01";
        return MonthStar;
    }

    //将给定的时间转换为Calendar类型，输入类型为年-月-日
    public static Calendar getCalendarYMD(String yyyyMMdd) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        try {
            Date date = sdf.parse(yyyyMMdd);
            calendar.setTime(date);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return calendar;
    }

    //将给定的时间转换为Calendar类型，输入类型为年-月
    public static Calendar getCalendarYM(String yyyyMM) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        try {
            Date date = sdf.parse(yyyyMM);
            calendar.setTime(date);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return calendar;
    }

    //将给定的时间转换为Calendar类型，输入类型为年-月-日 时:分:秒
    //输入类型为年-月-日
    public static Calendar getCalendarYMDHms(String yyyyMMddHHmmss) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        try {
            Date date = sdf.parse(yyyyMMddHHmmss);
            calendar.setTime(date);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return calendar;
    }

    //返回字符串列表，根据开始时间、结束时间，计算从开始时间到结束时间的每一天
    public static List<String> getEveryDay(String starDate, String endDate) throws ParseException {
        List<String> listReturn = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(starDate);
        long longStarTime = date.getTime();
        date = sdf.parse(endDate);
        long longEndTime = date.getTime();
        long oneDay = 1000 * 60 * 60 * 24l;
        long longTime = longStarTime;
        while (longTime <= longEndTime) {
            Date d = new Date(longTime);
            String str = sdf.format(d);
            //System.out.println(str);
            listReturn.add(str);
            longTime += oneDay;
        }
        return listReturn;
    }

    //返回字符串列表，根据年-月，计算当月的每一天
    public static List<String> getEveryDayForMonth(String yyyyMM) {
        Calendar calendar = DateUtil.getCalendarYM(yyyyMM);
        List<String> listReturn = new ArrayList<String>();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        for (int i = 1; i <= lastDay; i++) {
            String strDay = String.valueOf(year) + "-" + String.valueOf(month) + "-";
            if (i < 10) {
                strDay = strDay + "0" + i;
            } else {
                strDay = strDay + i;
            }
            listReturn.add(strDay);
        }
        return listReturn;
    }

    //获取当前时间几小时前的时间
    public static String getBeforeHousTime(int hour) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, cal.get(Calendar.HOUR_OF_DAY) - hour);
        String beforeTime = format.format(cal.getTime());
        return beforeTime;
    }

    //获取指定时间几小时前的时间
    public static String getBeforeHousTime(String dateStr, int hour) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, cal.get(Calendar.HOUR_OF_DAY) - hour);
        String beforeTime = format.format(cal.getTime());
        return beforeTime;
    }

    //获取指定日期零点时间
    public static String getDate0H(String dateStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date time = cal.getTime();
        String date0H = DateUtil.getYMDHms(time);
        return date0H;
    }

    //获取指定日期月份的第一天时间
    public static String getMonthDay1(String dateStr) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        //获取前月的第一天
        Calendar cal = Calendar.getInstance();//获取当前日期
        cal.setTime(date);
        cal.add(Calendar.MONTH, 0);
        cal.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date time = cal.getTime();
        String day1 = DateUtil.getYMDHms(time);
        return day1;
    }

    //获取指定月份的最后一天
    public static String getLastDay(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int lastDay = calendar.getActualMaximum(Calendar.DATE);
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);
        return sdf.format(calendar.getTime());
    }

    //根据输入年份返回整年每个月的第一天字符串集合
    public static List<String> getFirstDayList(Integer yyyy) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ArrayList<String> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < 12; i++) {
            calendar.clear();
            calendar.set(Calendar.YEAR, yyyy);
            calendar.set(Calendar.MONTH, i);
            int day = calendar.getActualMinimum(Calendar.DATE);
            calendar.set(Calendar.DAY_OF_MONTH, day);
            Date date = calendar.getTime();
            String dateStr = sdf.format(date);
            list.add(dateStr);
        }
        return list;
    }

    //根据输入年份返回整年每个月的最后一天字符串集合
    public static List<String> getLastDayList(Integer yyyy) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ArrayList<String> list = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < 12; i++) {
            calendar.clear();
            calendar.set(Calendar.YEAR, yyyy);
            calendar.set(Calendar.MONTH, i);
            int day = calendar.getActualMaximum(calendar.DAY_OF_MONTH);
            calendar.set(Calendar.DAY_OF_MONTH, day);
            Date date = calendar.getTime();
            String dateStr = sdf.format(date);
            list.add(dateStr);
        }
        return list;
    }
    //获取当前时间向前推十五天的月日
    public static List<String> getSW() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        List<String> list =new ArrayList<>();
        for (int i=15 ;i>=1;i--) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, -i);
            Date time = calendar.getTime();
            String format = df.format(time);
            String[] split = format.split("-");
            String dateList = split[0]+"-"+ split[1]+"-"+split[2];
            list.add(dateList);
        }
        return list;
    }

    //获取当前时间向前推七天的月日
    public static List<String> getQT() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        List<String> list =new ArrayList<>();
        for (int i=7 ;i>=1;i--) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, -i);
            Date time = calendar.getTime();
            String format = df.format(time);
            String[] split = format.split("-");
            String dateList = split[0]+"-"+ split[1]+"-"+split[2];
            list.add(dateList);
        }
        return list;
    }
    public static List<String> getDateYMD(String startDate, String endDate) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> list = new ArrayList<String>(); //保存日期的集合
        Date date_start = sdf.parse(startDate);
        Date date_end = sdf.parse(endDate);
        Date date =date_start;
        Calendar cd = Calendar.getInstance();//用Calendar 进行日期比较判断
        while (date.getTime() <= date_end.getTime()){
            list.add(sdf.format(date));
            cd.setTime(date);
            cd.add(Calendar.DATE, 1);//增加一天 放入集合
            date=cd.getTime();
        }

        String strList = StringUtils.strip(list.toString(),"[]");//去掉符号[]

        return list ;

    }
    // 获取任意两个日期中的所有日期-月天
    public static List<String> getDates(String startDate, String endDate) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> list = new ArrayList<String>(); //保存日期的集合
        Date date_start = sdf.parse(startDate);
        Date date_end = sdf.parse(endDate);
        Date date =date_start;
        Calendar cd = Calendar.getInstance();//用Calendar 进行日期比较判断
        while (date.getTime() <= date_end.getTime()){
            String format = sdf.format(date);
            String[] split = format.split("-");
            list.add(split[0]+"-"+split[1]+"-"+split[2]);
            cd.setTime(date);
            cd.add(Calendar.DATE, 1);//增加一天 放入集合
            date=cd.getTime();
        }

//        String strList = StringUtils.strip(list.toString(),"[]");//去掉符号[]

        return list ;

    }
    public static SimpleDateFormat formatTemp = new SimpleDateFormat("yyyy-MM-dd");
    //获取指定年月最后一天的日期 yyyy-MM
    public static String getMonthEndDay(String yyyyMM) {
        String[] ymArr = yyyyMM.split("-");
        Integer year = Integer.valueOf(ymArr[0]);
        Integer month = Integer.valueOf(ymArr[1]) - 1;
        final Calendar cal = Calendar.getInstance();
        cal.set(year,month,1);
        final int last = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        cal.set(Calendar.DAY_OF_MONTH, last);
        String endDay = formatTemp.format(cal.getTime());
        return endDay;
    }
    //获取后一天时间
    public static String getNexteDay(String dateTime) throws ParseException {
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;

            date=simpleDateFormat.parse(dateTime);

        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        //往后一天
        calendar.add(Calendar.DAY_OF_MONTH,1);
        return simpleDateFormat.format(calendar.getTime());

    }
    public static final String YEARMONTH_PATTERN = "yyyy-MM";
    //获取年月
    public static String getYearMonth(Date dateTime){
        SimpleDateFormat format = new SimpleDateFormat(YEARMONTH_PATTERN);
        return format.format(dateTime);
    }
    public static final String YEARMONTHDAY_PATTERN = "yyyy-MM-dd";
    //获取年月日
    public static String getYearMonthDay(Date dateTime){
        SimpleDateFormat format = new SimpleDateFormat(YEARMONTHDAY_PATTERN);
        return format.format(dateTime);
    }
    //获取前三十天那一天的时间
    public static String getFormerDay(String dateTime) throws ParseException {
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;

        date=simpleDateFormat.parse(dateTime);

        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        //往后一天
        calendar.add(Calendar.DAY_OF_MONTH,-30);
        return simpleDateFormat.format(calendar.getTime());

    }
}
