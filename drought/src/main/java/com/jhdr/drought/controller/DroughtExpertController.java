package com.jhdr.drought.controller;

import java.util.List;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.drought.entity.param.DroughtExpertParmExcel;
import com.jhdr.drought.entity.vo.DroughtExpertTemplateVo;
import com.jhdr.drought.utils.ReadExcel;
import com.jhdr.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.drought.entity.po.DroughtExpertPo;
import com.jhdr.drought.entity.param.DroughtExpertParam;
import com.jhdr.drought.entity.param.DroughtExpertAddParam;
import com.jhdr.drought.entity.param.DroughtExpertEditParam;
import com.jhdr.drought.entity.vo.DroughtExpertVo;
import com.jhdr.drought.service.IDroughtExpertService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 水旱防灾专家信息库
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(tags = "水旱防灾专家信息库")
@RestController
@RequestMapping("/expert")
public class DroughtExpertController extends BaseController
{
    @Autowired
    private IDroughtExpertService droughtExpertService;

    /**
     * 查询水旱防灾专家信息库列表
     */
    @ApiOperation(value = "查询水旱防灾专家信息库列表",notes="drought:expert:list")
    @RequiresPermissions("drought:expert:list")
    @GetMapping("/list")
    public TableDataInfo<List<DroughtExpertVo>> list(DroughtExpertParam droughtExpertParam)
    {
        startPage();
        List<DroughtExpertVo> list = droughtExpertService.selectDroughtExpertList(droughtExpertParam);
        return getDataTable(list);
    }

    /**
     * 导出水旱防灾专家信息库列表
     */
    @ApiOperation(value = "导出水旱防灾专家信息库列表",notes="drought:expert:export")
    @RequiresPermissions("drought:expert:export")
    @Log(title = "水旱防灾专家信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DroughtExpertParam droughtExpert)
    {
        List<DroughtExpertVo> list = droughtExpertService.selectDroughtExpertList(droughtExpert);
        ExcelUtil<DroughtExpertVo> util = new ExcelUtil<DroughtExpertVo>(DroughtExpertVo.class);
        util.exportExcel(response, list, "水旱防灾专家信息库数据");
    }

    /**
     * 获取水旱防灾专家信息库详细信息
     */
    @ApiOperation(value = "获取水旱防灾专家信息库详细信息",notes="drought:expert:query")
    @RequiresPermissions("drought:expert:query")
    @GetMapping(value = "/{id}")
    public R<DroughtExpertVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(droughtExpertService.selectDroughtExpertById(id));
    }

    /**
     * 新增水旱防灾专家信息库
     */
    @ApiOperation(value = "新增水旱防灾专家信息库",notes="drought:expert:add")
    @RequiresPermissions("drought:expert:add")
    @Log(title = "水旱防灾专家信息库", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody DroughtExpertAddParam droughtExpert)
    {
        int i = droughtExpertService.insertDroughtExpert(droughtExpert);
        if (i==0){
            return R.fail("手机号格式不对");
        }
        return toAjaxR(i,"新增");
    }

    /**
     * 修改水旱防灾专家信息库
     */
    @ApiOperation(value = "修改水旱防灾专家信息库",notes="drought:expert:edit")
    @RequiresPermissions("drought:expert:edit")
    @Log(title = "水旱防灾专家信息库", businessType = BusinessType.UPDATE)
    @PutMapping("/update")
    public R edit(@RequestBody DroughtExpertEditParam droughtExpert)
    {
        return toAjaxR(droughtExpertService.updateDroughtExpert(droughtExpert),"修改");
    }

    /**
     * 删除水旱防灾专家信息库
     */
    @ApiOperation(value = "删除水旱防灾专家信息库",notes="drought:expert:remove")
    @RequiresPermissions("drought:expert:remove")
    @Log(title = "水旱防灾专家信息库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(droughtExpertService.deleteDroughtExpertByIds(ids),"删除");
    }



    @ApiOperation(value = "导入水旱防灾专家信息表格",notes="drought:expert:formExle")
    @PostMapping(value = "formExle")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        System.out.println(file.getName());
        ExcelUtil<DroughtExpertParmExcel> util = new ExcelUtil<DroughtExpertParmExcel>(DroughtExpertParmExcel.class);
        List<DroughtExpertParmExcel> userList = util.importExcel(file.getInputStream());
        if (userList==null){
            return error("文件导入失败");
        }
      //  String operName = SecurityUtils.getUsername();
        String message = droughtExpertService.importUser(userList);
        return success(message);
    }


    /**
     * 导出水旱防灾专家信息库模板
     */
    @ApiOperation(value = "导出水旱防灾专家信息模板",notes="drought:expert:exportTemplate")
    @RequiresPermissions("drought:expert:exportTemplate")
    @Log(title = "水旱防灾专家信息库", businessType = BusinessType.EXPORT)
    @PostMapping("/exportTemplate")
    public void exportTemplate(HttpServletResponse response)
    {
       // List<DroughtExpertVo> list = droughtExpertService.selectDroughtExpertListTemplate();
        ExcelUtil<DroughtExpertTemplateVo> util = new ExcelUtil<DroughtExpertTemplateVo>(DroughtExpertTemplateVo.class);
        util.exportExcel(response, null, "水旱防灾专家信息库数据");
    }

}
