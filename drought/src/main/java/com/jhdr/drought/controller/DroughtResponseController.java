package com.jhdr.drought.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.drought.entity.po.DroughtResponsePo;
import com.jhdr.drought.entity.param.DroughtResponseParam;
import com.jhdr.drought.entity.param.DroughtResponseAddParam;
import com.jhdr.drought.entity.param.DroughtResponseEditParam;
import com.jhdr.drought.entity.vo.DroughtResponseVo;
import com.jhdr.drought.service.IDroughtResponseService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 响应准备
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Api(tags = "响应准备")
@RestController
@RequestMapping("/response")
public class DroughtResponseController extends BaseController
{
    @Autowired
    private IDroughtResponseService droughtResponseService;

    /**
     * 查询响应准备列表
     */
    @ApiOperation(value = "查询响应准备列表",notes="drought:response:list")
    @RequiresPermissions("drought:response:list")
    @GetMapping("/list")
    public TableDataInfo<List<DroughtResponseVo>> list(DroughtResponseParam droughtResponseParam)
    {
        startPage();
        List<DroughtResponseVo> list = droughtResponseService.selectDroughtResponseList(droughtResponseParam);
        return getDataTable(list);
    }

    /**
     * 导出响应准备列表
     */
  /*  @ApiOperation(value = "导出响应准备列表",notes="drought:response:export")
    @RequiresPermissions("drought:response:export")
    @Log(title = "响应准备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DroughtResponseParam droughtResponse)
    {
        List<DroughtResponseVo> list = droughtResponseService.selectDroughtResponseList(droughtResponse);
        ExcelUtil<DroughtResponseVo> util = new ExcelUtil<DroughtResponseVo>(DroughtResponseVo.class);
        util.exportExcel(response, list, "响应准备数据");
    }*/

    /**
     * 获取响应准备详细信息
     */
    @ApiOperation(value = "获取响应准备详细信息",notes="drought:response:query")
    @RequiresPermissions("drought:response:query")
    @GetMapping(value = "/{id}")
    public R<DroughtResponseVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(droughtResponseService.selectDroughtResponseById(id));
    }

    /**
     * 新增响应准备
     */
    @ApiOperation(value = "新增响应准备",notes="drought:response:add")
    @RequiresPermissions("drought:response:add")
    @Log(title = "响应准备", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody DroughtResponseAddParam droughtResponse)
    {
        return toAjaxR(droughtResponseService.insertDroughtResponse(droughtResponse),"新增");
    }

    /**
     * 修改响应准备
     */
    @ApiOperation(value = "修改响应准备",notes="drought:response:edit")
    @RequiresPermissions("drought:response:edit")
    @Log(title = "响应准备", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody DroughtResponseEditParam droughtResponse)
    {
        return toAjaxR(droughtResponseService.updateDroughtResponse(droughtResponse),"修改");
    }

    /**
     * 删除响应准备
     */
    @ApiOperation(value = "删除响应准备",notes="drought:response:remove")
    @RequiresPermissions("drought:response:remove")
    @Log(title = "响应准备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(droughtResponseService.deleteDroughtResponseByIds(ids),"删除");
    }
}
