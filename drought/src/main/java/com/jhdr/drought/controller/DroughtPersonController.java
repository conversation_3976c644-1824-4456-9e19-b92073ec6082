package com.jhdr.drought.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.drought.entity.po.DroughtPersonPo;
import com.jhdr.drought.entity.param.DroughtPersonParam;
import com.jhdr.drought.entity.param.DroughtPersonAddParam;
import com.jhdr.drought.entity.param.DroughtPersonEditParam;
import com.jhdr.drought.entity.vo.DroughtPersonVo;
import com.jhdr.drought.service.IDroughtPersonService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 响应准备-人员信息
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Api(tags = "响应准备-人员信息")
@RestController
@RequestMapping("/person")
public class DroughtPersonController extends BaseController
{
    @Autowired
    private IDroughtPersonService droughtPersonService;

    /**
     * 查询响应准备-人员信息列表
     */
    @ApiOperation(value = "查询响应准备-人员信息列表",notes="drought:person:list")
    @RequiresPermissions("drought:person:list")
    @GetMapping("/list")
    public TableDataInfo<List<DroughtPersonVo>> list(DroughtPersonParam droughtPersonParam)
    {
        startPage();
        List<DroughtPersonVo> list = droughtPersonService.selectDroughtPersonList(droughtPersonParam);
        return getDataTable(list);
    }

    /**
     * 导出响应准备-人员信息列表
     */
    /*@ApiOperation(value = "导出响应准备-人员信息列表",notes="drought:person:export")
    @RequiresPermissions("drought:person:export")
    @Log(title = "响应准备-人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DroughtPersonParam droughtPerson)
    {
        List<DroughtPersonVo> list = droughtPersonService.selectDroughtPersonList(droughtPerson);
        ExcelUtil<DroughtPersonVo> util = new ExcelUtil<DroughtPersonVo>(DroughtPersonVo.class);
        util.exportExcel(response, list, "响应准备-人员信息数据");
    }*/

    /**
     * 获取响应准备-人员信息详细信息
     */
    @ApiOperation(value = "获取响应准备-人员信息详细信息",notes="drought:person:query")
    @RequiresPermissions("drought:person:query")
    @GetMapping(value = "/{id}")
    public R<DroughtPersonVo> getInfo(@PathVariable("id") String id)
    {

        return R.ok(droughtPersonService.selectDroughtPersonById(id));
    }

    /**
     * 新增响应准备-人员信息
     */
    @ApiOperation(value = "新增响应准备-人员信息",notes="drought:person:add")
    @RequiresPermissions("drought:person:add")
    @Log(title = "响应准备-人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody DroughtPersonAddParam droughtPerson)
    {
        return toAjaxR(droughtPersonService.insertDroughtPerson(droughtPerson),"新增");
    }

    /**
     * 修改响应准备-人员信息
     */
    @ApiOperation(value = "修改响应准备-人员信息",notes="drought:person:edit")
    @RequiresPermissions("drought:person:edit")
    @Log(title = "响应准备-人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody DroughtPersonEditParam droughtPerson)
    {
        return toAjaxR(droughtPersonService.updateDroughtPerson(droughtPerson),"修改");
    }

    /**
     * 删除响应准备-人员信息
     */
    @ApiOperation(value = "删除响应准备-人员信息",notes="drought:person:remove")
    @RequiresPermissions("drought:person:remove")
    @Log(title = "响应准备-人员信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(droughtPersonService.deleteDroughtPersonByIds(ids),"删除");
    }
}
