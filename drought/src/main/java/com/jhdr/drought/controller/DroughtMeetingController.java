package com.jhdr.drought.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.drought.entity.po.DroughtMeetingPo;
import com.jhdr.drought.entity.param.DroughtMeetingParam;
import com.jhdr.drought.entity.param.DroughtMeetingAddParam;
import com.jhdr.drought.entity.param.DroughtMeetingEditParam;
import com.jhdr.drought.entity.vo.DroughtMeetingVo;
import com.jhdr.drought.service.IDroughtMeetingService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 异地会商
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Api(tags = "异地会商")
@RestController
@RequestMapping("/meeting")
public class DroughtMeetingController extends BaseController
{
    @Autowired
    private IDroughtMeetingService droughtMeetingService;

    /**
     * 查询异地会商列表
     */
    @ApiOperation(value = "查询异地会商列表",notes="drought:meeting:list")
    @RequiresPermissions("drought:meeting:list")
    @GetMapping("/list")
    public TableDataInfo<List<DroughtMeetingVo>> list(DroughtMeetingParam droughtMeetingParam)
    {
        startPage();
        List<DroughtMeetingVo> list = droughtMeetingService.selectDroughtMeetingList(droughtMeetingParam);
        return getDataTable(list);
    }

    /**
     * 导出异地会商列表
     */
  /*  @ApiOperation(value = "导出异地会商列表",notes="drought:meeting:export")
    @RequiresPermissions("drought:meeting:export")
    @Log(title = "异地会商", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, DroughtMeetingParam droughtMeeting)
    {
        List<DroughtMeetingVo> list = droughtMeetingService.selectDroughtMeetingList(droughtMeeting);
        ExcelUtil<DroughtMeetingVo> util = new ExcelUtil<DroughtMeetingVo>(DroughtMeetingVo.class);
        util.exportExcel(response, list, "异地会商数据");
    }

    /**
     * 获取异地会商详细信息
     */
    @ApiOperation(value = "获取异地会商详细信息",notes="drought:meeting:query")
    @RequiresPermissions("drought:meeting:query")
    @GetMapping(value = "/{id}")
    public R<DroughtMeetingVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(droughtMeetingService.selectDroughtMeetingById(id));
    }

    /**
     * 新增异地会商
     */
    @ApiOperation(value = "新增异地会商",notes="drought:meeting:add")
    @RequiresPermissions("drought:meeting:add")
    @Log(title = "异地会商", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody DroughtMeetingAddParam droughtMeeting)
    {
        return toAjaxR(droughtMeetingService.insertDroughtMeeting(droughtMeeting),"新增");
    }

    /**
     * 修改异地会商
     */
    @ApiOperation(value = "修改异地会商",notes="drought:meeting:edit")
    @RequiresPermissions("drought:meeting:edit")
    @Log(title = "异地会商", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody DroughtMeetingEditParam droughtMeeting)
    {
        return toAjaxR(droughtMeetingService.updateDroughtMeeting(droughtMeeting),"修改");
    }

    /**
     * 删除异地会商
     */
    @ApiOperation(value = "删除异地会商",notes="drought:meeting:remove")
    @RequiresPermissions("drought:meeting:remove")
    @Log(title = "异地会商", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(droughtMeetingService.deleteDroughtMeetingByIds(ids),"删除");
    }

    /**
     * 下载文件
     */
    @ApiOperation(value = "下载异地会商文件",notes="drought:meeting:query")
    @RequiresPermissions("drought:meeting:query")
    @GetMapping("/download/{id}")
    public R download(@PathVariable("id") Long id)
    {
        String url = droughtMeetingService.download(id);
        return R.ok(url);
    }
}
