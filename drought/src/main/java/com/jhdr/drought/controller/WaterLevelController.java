package com.jhdr.drought.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.drought.entity.vo.StRiverRVo;
import com.jhdr.drought.service.IWaterLevelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 水位信息controller层处理
 *
 * <AUTHOR>
 * @version 1.0
 * @project chxh_back
 * @date 2024/7/2 13:44:54
 */
@RestController
@RequestMapping("/river")
@Api(tags = "水位信息")
public class WaterLevelController {
@Autowired
private IWaterLevelService iWaterLevelService;


    /**
     * 获取枢纽基础信息详细信息
     */
    @ApiOperation(value = "获取河道基础信息水位信息",notes="drought:river:query")
    @RequiresPermissions("drought:river:query")
    @GetMapping(value = "/{stnm}")
    public R<StRiverRVo> getInfo(@PathVariable("stnm") String stnm)
    {
        return R.ok(iWaterLevelService.selectStRiverRByName(stnm));
    }
}
