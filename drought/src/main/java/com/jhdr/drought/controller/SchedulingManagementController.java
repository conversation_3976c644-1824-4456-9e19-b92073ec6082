package com.jhdr.drought.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.drought.entity.vo.StatisticsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.drought.entity.po.SchedulingManagementPo;
import com.jhdr.drought.entity.param.SchedulingManagementParam;
import com.jhdr.drought.entity.param.SchedulingManagementAddParam;
import com.jhdr.drought.entity.param.SchedulingManagementEditParam;
import com.jhdr.drought.entity.vo.SchedulingManagementVo;
import com.jhdr.drought.service.ISchedulingManagementService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 调度管理
 *
 * <AUTHOR>
 * @date 2024-07-03
 */
@Api(tags = "调度管理")
@RestController
@RequestMapping("/scheduling")
public class SchedulingManagementController extends BaseController
{
    @Autowired
    private ISchedulingManagementService schedulingManagementService;

    /**
     * 查询调度管理列表
     */
    @ApiOperation(value = "查询调度管理列表",notes="drought:scheduling:list")
    @RequiresPermissions("drought:scheduling:list")
    @GetMapping("/list")
    public TableDataInfo<List<SchedulingManagementVo>> list(SchedulingManagementParam schedulingManagementParam)
    {
        startPage();
        List<SchedulingManagementVo> list = schedulingManagementService.selectSchedulingManagementList(schedulingManagementParam);
        return getDataTable(list);
    }


      //导出调度管理列表

    @ApiOperation(value = "导出调度管理列表",notes="drought:scheduling:export")
    @RequiresPermissions("drought:scheduling:export")
    @Log(title = "调度管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SchedulingManagementParam schedulingManagement)
    {
        List<SchedulingManagementVo> list = schedulingManagementService.selectSchedulingManagementList(schedulingManagement);
        ExcelUtil<SchedulingManagementVo> util = new ExcelUtil<SchedulingManagementVo>(SchedulingManagementVo.class);
        util.exportExcel(response, list, "调度管理数据");
    }

    /**
     * 获取调度管理详细信息
     */
    @ApiOperation(value = "获取调度管理详细信息",notes="drought:scheduling:query")
    @RequiresPermissions("drought:scheduling:query")
    @GetMapping(value = "/{id}")
    public R<SchedulingManagementVo> getInfo(@PathVariable("id") String id)
    {

        return R.ok(schedulingManagementService.selectSchedulingManagementById(id));
    }

    /**
     * 新增调度管理
     */
    @ApiOperation(value = "新增调度管理",notes="drought:scheduling:add")
    @RequiresPermissions("drought:scheduling:add")
    @Log(title = "调度管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody SchedulingManagementAddParam schedulingManagement)
    {
        return toAjaxR(schedulingManagementService.insertSchedulingManagement(schedulingManagement),"新增");
    }

    /**
     * 修改调度管理
     */
    @ApiOperation(value = "修改调度管理",notes="drought:scheduling:edit")
    @RequiresPermissions("drought:scheduling:edit")
    @Log(title = "调度管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody SchedulingManagementEditParam schedulingManagement)
    {
        int i = schedulingManagementService.updateSchedulingManagement(schedulingManagement);
        if (i==0){
            return R.fail("修改失败");
        }
        return toAjaxR(i,"修改");
    }

/*    *//**
     * 删除调度管理
     *//*
    @ApiOperation(value = "删除调度管理",notes="drought:scheduling:remove")
    @RequiresPermissions("drought:scheduling:remove")
    @Log(title = "调度管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable String[] ids)
    {
        return toAjaxR(schedulingManagementService.deleteSchedulingManagementByIds(ids),"删除");
    }*/


    /**
     * 查询调度指令月统计
     */
    @ApiOperation(value = "查询调度指令月统计",notes="drought:scheduling:statistics")
    @RequiresPermissions("drought:scheduling:statistics")
    @GetMapping("/statistics")
    public R<List<StatisticsVo>> statistics()
    {
        List<StatisticsVo> list =  schedulingManagementService.getStatistics();
        return R.ok(list);
    }
}
