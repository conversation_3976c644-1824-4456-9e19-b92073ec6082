package com.jhdr.projectManagement.service;

import java.util.List;
import com.jhdr.projectManagement.entity.po.JhirStrobeBPo;
import com.jhdr.projectManagement.entity.param.JhirStrobeBParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBAddParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirStrobeBVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 闸门基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IJhirStrobeBService extends IService<JhirStrobeBPo>
{

    /**
     * 查询闸门基础信息列表
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 闸门基础信息集合
     */
    public List<JhirStrobeBVo> queryList(JhirStrobeBPo jhirStrobeB);

    /**
     * 查询闸门基础信息
     *
     * @param strobeCode 闸门基础信息主键
     * @return 闸门基础信息
     */
    public JhirStrobeBVo selectJhirStrobeBByStrobeCode(String strobeCode);

    /**
     * 查询闸门基础信息列表
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 闸门基础信息集合
     */
    public List<JhirStrobeBVo> selectJhirStrobeBList(JhirStrobeBParam jhirStrobeB);

    /**
     * 新增闸门基础信息
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 结果
     */
    public int insertJhirStrobeB(JhirStrobeBAddParam jhirStrobeB);

    /**
     * 修改闸门基础信息
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 结果
     */
    public int updateJhirStrobeB(JhirStrobeBEditParam jhirStrobeB);

    /**
     * 批量删除闸门基础信息
     *
     * @param strobeCodes 需要删除的闸门基础信息主键集合
     * @return 结果
     */
    public int deleteJhirStrobeBByStrobeCodes(String[] strobeCodes);

    /**
     * 删除闸门基础信息信息
     *
     * @param strobeCode 闸门基础信息主键
     * @return 结果
     */
    public int deleteJhirStrobeBByStrobeCode(String strobeCode);

}
