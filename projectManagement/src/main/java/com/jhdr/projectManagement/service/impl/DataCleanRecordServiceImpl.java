package com.jhdr.projectManagement.service.impl;


import com.jhdr.projectManagement.domain.DataCleanRecord;
import com.jhdr.projectManagement.mapper.DataCleanRecordMapper;
import com.jhdr.projectManagement.service.IDataCleanRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据清洗记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class DataCleanRecordServiceImpl implements IDataCleanRecordService
{
    @Autowired
    private DataCleanRecordMapper dataCleanRecordMapper;

    /**
     * 查询数据清洗记录
     * 
     * @param id 数据清洗记录主键
     * @return 数据清洗记录
     */
    @Override
    public DataCleanRecord selectDataCleanRecordById(Integer id)
    {
        return dataCleanRecordMapper.selectDataCleanRecordById(id);
    }

    /**
     * 查询数据清洗记录列表
     * 
     * @param dataCleanRecord 数据清洗记录
     * @return 数据清洗记录
     */
    @Override
    public List<DataCleanRecord> selectDataCleanRecordList(DataCleanRecord dataCleanRecord)
    {
        return dataCleanRecordMapper.selectDataCleanRecordList(dataCleanRecord);
    }

    /**
     * 新增数据清洗记录
     * 
     * @param dataCleanRecord 数据清洗记录
     * @return 结果
     */
    @Override
    public int insertDataCleanRecord(DataCleanRecord dataCleanRecord)
    {
        return dataCleanRecordMapper.insertDataCleanRecord(dataCleanRecord);
    }

    /**
     * 修改数据清洗记录
     * 
     * @param dataCleanRecord 数据清洗记录
     * @return 结果
     */
    @Override
    public int updateDataCleanRecord(DataCleanRecord dataCleanRecord)
    {
        return dataCleanRecordMapper.updateDataCleanRecord(dataCleanRecord);
    }

    /**
     * 批量删除数据清洗记录
     * 
     * @param ids 需要删除的数据清洗记录主键
     * @return 结果
     */
    @Override
    public int deleteDataCleanRecordByIds(Integer[] ids)
    {
        return dataCleanRecordMapper.deleteDataCleanRecordByIds(ids);
    }

    /**
     * 删除数据清洗记录信息
     * 
     * @param id 数据清洗记录主键
     * @return 结果
     */
    @Override
    public int deleteDataCleanRecordById(Integer id)
    {
        return dataCleanRecordMapper.deleteDataCleanRecordById(id);
    }
}
