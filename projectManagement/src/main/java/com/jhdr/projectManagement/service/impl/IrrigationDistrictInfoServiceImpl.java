package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.projectManagement.entity.po.IrrigationDistrictInfoPo;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoParam;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoAddParam;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoEditParam;
import com.jhdr.projectManagement.entity.vo.IrrigationDistrictInfoVo;
import com.jhdr.projectManagement.mapper.IrrigationDistrictInfoMapper;
import com.jhdr.projectManagement.service.IIrrigationDistrictInfoService;

import java.util.ArrayList;

import java.util.List;

/**
 * 灌区基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class IrrigationDistrictInfoServiceImpl extends ServiceImpl<IrrigationDistrictInfoMapper, IrrigationDistrictInfoPo> implements IIrrigationDistrictInfoService {
@Autowired
private IrrigationDistrictInfoMapper irrigationDistrictInfoMapper;
    @Override
    public List<IrrigationDistrictInfoVo> queryList(IrrigationDistrictInfoPo irrigationDistrictInfo) {
        LambdaQueryWrapper<IrrigationDistrictInfoPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(irrigationDistrictInfo.getIrrigationCode())){
            lqw.eq(IrrigationDistrictInfoPo::getIrrigationCode ,irrigationDistrictInfo.getIrrigationCode());
        }
        if (StringUtils.isNotBlank(irrigationDistrictInfo.getAdminArea())){
            lqw.eq(IrrigationDistrictInfoPo::getAdminArea ,irrigationDistrictInfo.getAdminArea());
        }
        if (irrigationDistrictInfo.getDesignedIrrigationArea() != null){
            lqw.eq(IrrigationDistrictInfoPo::getDesignedIrrigationArea ,irrigationDistrictInfo.getDesignedIrrigationArea());
        }
        if (irrigationDistrictInfo.getEffectiveIrrigationArea() != null){
            lqw.eq(IrrigationDistrictInfoPo::getEffectiveIrrigationArea ,irrigationDistrictInfo.getEffectiveIrrigationArea());
        }
        if (irrigationDistrictInfo.getCroplandArea() != null){
            lqw.eq(IrrigationDistrictInfoPo::getCroplandArea ,irrigationDistrictInfo.getCroplandArea());
        }
        if (StringUtils.isNotBlank(irrigationDistrictInfo.getProfile())){
            lqw.eq(IrrigationDistrictInfoPo::getProfile ,irrigationDistrictInfo.getProfile());
        }
        if (StringUtils.isNotBlank(irrigationDistrictInfo.getName())){
            lqw.like(IrrigationDistrictInfoPo::getName ,irrigationDistrictInfo.getName());
        }
        List<IrrigationDistrictInfoVo> irrigationDistrictInfoVos= BeanUtil.copyToList(this.list(lqw), IrrigationDistrictInfoVo.class);
        return irrigationDistrictInfoVos;
    }
    /**
     * 查询灌区基本信息
     *
     * @param id 灌区基本信息主键
     * @return 灌区基本信息
     */
    @Override
    public IrrigationDistrictInfoVo selectIrrigationDistrictInfoById(Long id)
    {
        return baseMapper.selectIrrigationDistrictInfoById(id);
    }

    /**
     * 查询灌区基本信息列表
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 灌区基本信息
     */
    @Override
    public List<IrrigationDistrictInfoVo> selectIrrigationDistrictInfoList(IrrigationDistrictInfoParam irrigationDistrictInfo)
    {
        return irrigationDistrictInfoMapper.selectIrrigationAllList(irrigationDistrictInfo.getName(),irrigationDistrictInfo.getIrrigationCode());
    }

    /**
     * 新增灌区基本信息
     *
     * @param irrigationDistrictInfoAddParam 灌区基本信息
     * @return 结果
     */
    @Override
    public int insertIrrigationDistrictInfo(IrrigationDistrictInfoAddParam irrigationDistrictInfoAddParam)
    {

        IrrigationDistrictInfoPo irrigationDistrictInfo=new IrrigationDistrictInfoPo();
        BeanUtil.copyProperties(irrigationDistrictInfoAddParam,irrigationDistrictInfo);
        return baseMapper.insert(irrigationDistrictInfo);
    }

    /**
     * 修改灌区基本信息
     *
     * @param irrigationDistrictInfoEditParam 灌区基本信息
     * @return 结果
     */
    @Override
    public int updateIrrigationDistrictInfo(IrrigationDistrictInfoEditParam irrigationDistrictInfoEditParam)
    {
        IrrigationDistrictInfoPo irrigationDistrictInfo=new IrrigationDistrictInfoPo();
        BeanUtil.copyProperties(irrigationDistrictInfoEditParam,irrigationDistrictInfo);
        return baseMapper.updateById(irrigationDistrictInfo);
    }

    /**
     * 批量删除灌区基本信息
     *
     * @param ids 需要删除的灌区基本信息主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationDistrictInfoByIds(Long[] ids)
    {
        return baseMapper.deleteIrrigationDistrictInfoByIds(ids);
    }

    /**
     * 删除灌区基本信息信息
     *
     * @param id 灌区基本信息主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationDistrictInfoById(Long id)
    {
        return baseMapper.deleteIrrigationDistrictInfoById(id);
    }
}
