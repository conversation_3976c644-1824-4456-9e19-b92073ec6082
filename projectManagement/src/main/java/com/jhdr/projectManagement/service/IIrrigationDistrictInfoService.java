package com.jhdr.projectManagement.service;

import java.util.List;
import com.jhdr.projectManagement.entity.po.IrrigationDistrictInfoPo;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoParam;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoAddParam;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoEditParam;
import com.jhdr.projectManagement.entity.vo.IrrigationDistrictInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 灌区基本信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IIrrigationDistrictInfoService extends IService<IrrigationDistrictInfoPo>
{

    /**
     * 查询灌区基本信息列表
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 灌区基本信息集合
     */
    public List<IrrigationDistrictInfoVo> queryList(IrrigationDistrictInfoPo irrigationDistrictInfo);

    /**
     * 查询灌区基本信息
     *
     * @param id 灌区基本信息主键
     * @return 灌区基本信息
     */
    public IrrigationDistrictInfoVo selectIrrigationDistrictInfoById(Long id);

    /**
     * 查询灌区基本信息列表
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 灌区基本信息集合
     */
    public List<IrrigationDistrictInfoVo> selectIrrigationDistrictInfoList(IrrigationDistrictInfoParam irrigationDistrictInfo);

    /**
     * 新增灌区基本信息
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 结果
     */
    public int insertIrrigationDistrictInfo(IrrigationDistrictInfoAddParam irrigationDistrictInfo);

    /**
     * 修改灌区基本信息
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 结果
     */
    public int updateIrrigationDistrictInfo(IrrigationDistrictInfoEditParam irrigationDistrictInfo);

    /**
     * 批量删除灌区基本信息
     *
     * @param ids 需要删除的灌区基本信息主键集合
     * @return 结果
     */
    public int deleteIrrigationDistrictInfoByIds(Long[] ids);

    /**
     * 删除灌区基本信息信息
     *
     * @param id 灌区基本信息主键
     * @return 结果
     */
    public int deleteIrrigationDistrictInfoById(Long id);

}
