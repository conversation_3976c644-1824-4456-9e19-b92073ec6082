package com.jhdr.projectManagement.service;

import java.util.List;
import com.jhdr.projectManagement.entity.po.JhirPumpBPo;
import com.jhdr.projectManagement.entity.param.JhirPumpBParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBAddParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirPumpBVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 水泵基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface IJhirPumpBService extends IService<JhirPumpBPo>
{

    /**
     * 查询水泵基础信息列表
     *
     * @param jhirPumpB 水泵基础信息
     * @return 水泵基础信息集合
     */
    public List<JhirPumpBVo> queryList(JhirPumpBPo jhirPumpB);

    /**
     * 查询水泵基础信息
     *
     * @param pumpCode 水泵基础信息主键
     * @return 水泵基础信息
     */
    public JhirPumpBVo selectJhirPumpBByPumpCode(String pumpCode);

    /**
     * 查询水泵基础信息列表
     *
     * @param jhirPumpB 水泵基础信息
     * @return 水泵基础信息集合
     */
    public List<JhirPumpBVo> selectJhirPumpBList(JhirPumpBParam jhirPumpB);

    /**
     * 新增水泵基础信息
     *
     * @param jhirPumpB 水泵基础信息
     * @return 结果
     */
    public int insertJhirPumpB(JhirPumpBAddParam jhirPumpB);

    /**
     * 修改水泵基础信息
     *
     * @param jhirPumpB 水泵基础信息
     * @return 结果
     */
    public int updateJhirPumpB(JhirPumpBEditParam jhirPumpB);

    /**
     * 批量删除水泵基础信息
     *
     * @param pumpCodess 需要删除的水泵基础信息主键集合
     * @return 结果
     */
    public int deleteJhirPumpBByPumpCodes(String[] pumpCodess);

    /**
     * 删除水泵基础信息信息
     *
     * @param pumpCode 水泵基础信息主键
     * @return 结果
     */
    public int deleteJhirPumpBByPumpCode(String pumpCode);

}
