package com.jhdr.projectManagement.service.impl;

import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.projectManagement.domain.*;
import com.jhdr.projectManagement.mapper.MiddleTaskMapper;
import com.jhdr.projectManagement.service.IMiddleTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 可视化设计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class MiddleTaskServiceImpl implements IMiddleTaskService
{
    @Autowired
    private MiddleTaskMapper middleTaskMapper;

    /**
     * 查询可视化设计
     * 
     * @param id 可视化设计主键
     * @return 可视化设计
     */
    @Override
    public MiddleTask selectMiddleTaskById(Integer id)
    {
        return middleTaskMapper.selectMiddleTaskById(id);
    }

    /**
     * 查询可视化设计列表
     * 
     * @param middleTask 可视化设计
     * @return 可视化设计
     */
    @Override
    public List<MiddleTask> selectMiddleTaskList(MiddleTask middleTask)
    {
        return middleTaskMapper.selectMiddleTaskList(middleTask);
    }

    /**
     * 新增可视化设计
     * 
     * @param middleTask 可视化设计
     * @return 结果
     */
    @Override
    public int insertMiddleTask(MiddleTask middleTask)
    {
        return middleTaskMapper.insertMiddleTask(middleTask);
    }

    /**
     * 修改可视化设计
     * 
     * @param middleTask 可视化设计
     * @return 结果
     */
    @Override
    public int updateMiddleTask(MiddleTask middleTask)
    {
        middleTask.setUpdateTime(DateUtils.getNowDate());
        return middleTaskMapper.updateMiddleTask(middleTask);
    }

    /**
     * 批量删除可视化设计
     * 
     * @param ids 需要删除的可视化设计主键
     * @return 结果
     */
    @Override
    public int deleteMiddleTaskByIds(Integer[] ids)
    {
        return middleTaskMapper.deleteMiddleTaskByIds(ids);
    }

    /**
     * 删除可视化设计信息
     * 
     * @param id 可视化设计主键
     * @return 结果
     */
    @Override
    public int deleteMiddleTaskById(Integer id)
    {
        return middleTaskMapper.deleteMiddleTaskById(id);
    }
}
