package com.jhdr.projectManagement.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jhdr.projectManagement.domain.DataCleanPlan;
import com.jhdr.projectManagement.entity.po.DataCleanPlanPo;
import com.jhdr.projectManagement.entity.po.JhirPustBPo;
import com.jhdr.projectManagement.mapper.DataCleanPlanMapper;
import com.jhdr.projectManagement.mapper.JhirPustBMapper;
import com.jhdr.projectManagement.service.IDataCleanPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据清洗方案编制Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class DataCleanPlanServiceImpl extends ServiceImpl<DataCleanPlanMapper, DataCleanPlanPo> implements IDataCleanPlanService
{
    @Autowired
    private DataCleanPlanMapper dataCleanPlanMapper;

    /**
     * 查询数据清洗方案编制
     * 
     * @param id 数据清洗方案编制主键
     * @return 数据清洗方案编制
     */
    @Override
    public DataCleanPlan selectDataCleanPlanById(Integer id)
    {
        return dataCleanPlanMapper.selectDataCleanPlanById(id);
    }

    /**
     * 查询数据清洗方案编制列表
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 数据清洗方案编制
     */
    @Override
    public List<DataCleanPlan> selectDataCleanPlanList(DataCleanPlan dataCleanPlan)
    {
        return dataCleanPlanMapper.selectDataCleanPlanList(dataCleanPlan);
    }

    /**
     * 新增数据清洗方案编制
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 结果
     */
    @Override
    public int insertDataCleanPlan(DataCleanPlan dataCleanPlan) {
        DataCleanPlanPo po=new DataCleanPlanPo();
        BeanUtil.copyProperties(dataCleanPlan,po);
        po.setIsUse(1);
        return dataCleanPlanMapper.insert(po);
    }

    /**
     * 修改数据清洗方案编制
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 结果
     */
    @Override
    public int updateDataCleanPlan(DataCleanPlan dataCleanPlan)
    {
        return dataCleanPlanMapper.updateDataCleanPlan(dataCleanPlan);
    }

    /**
     * 批量删除数据清洗方案编制
     * 
     * @param ids 需要删除的数据清洗方案编制主键
     * @return 结果
     */
    @Override
    public int deleteDataCleanPlanByIds(Integer[] ids)
    {
        return dataCleanPlanMapper.deleteDataCleanPlanByIds(ids);
    }

    /**
     * 删除数据清洗方案编制信息
     * 
     * @param id 数据清洗方案编制主键
     * @return 结果
     */
    @Override
    public int deleteDataCleanPlanById(Integer id)
    {
        return dataCleanPlanMapper.deleteDataCleanPlanById(id);
    }
}
