package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.projectManagement.entity.param.JhirWagaBAddParam;
import com.jhdr.projectManagement.entity.param.JhirWagaBEditParam;
import com.jhdr.projectManagement.entity.param.JhirWagaBParam;
import com.jhdr.projectManagement.entity.po.JhirStrobeBPo;
import com.jhdr.projectManagement.entity.po.JhirWagaBPo;
import com.jhdr.projectManagement.entity.vo.JhirStrobeBVo;
import com.jhdr.projectManagement.entity.vo.JhirWagaBVo;
import com.jhdr.projectManagement.mapper.JhirStrobeBMapper;
import com.jhdr.projectManagement.mapper.JhirWagaBMapper;
import com.jhdr.projectManagement.service.IJhirWagaBService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;


import java.util.ArrayList;

import java.util.List;

/**
 * 闸站基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class JhirWagaBServiceImpl extends ServiceImpl<JhirWagaBMapper, JhirWagaBPo> implements IJhirWagaBService {
    @Autowired
    private JhirWagaBMapper jhirWagaBMapper;
    @Autowired
    private JhirStrobeBMapper jhirStrobeBMapper;

    @Override
    public List<JhirWagaBVo> queryList(JhirWagaBPo jhirWagaB) {
        LambdaQueryWrapper<JhirWagaBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhirWagaB.getStrName())){
            lqw.like(JhirWagaBPo::getStrName ,jhirWagaB.getStrName());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getAddress())){
            lqw.eq(JhirWagaBPo::getAddress ,jhirWagaB.getAddress());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getWagaType())){
            lqw.eq(JhirWagaBPo::getWagaType ,jhirWagaB.getWagaType());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getEngWait())){
            lqw.eq(JhirWagaBPo::getEngWait ,jhirWagaB.getEngWait());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getMajorType())){
            lqw.eq(JhirWagaBPo::getMajorType ,jhirWagaB.getMajorType());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getCompleteTime())){
            lqw.eq(JhirWagaBPo::getCompleteTime ,jhirWagaB.getCompleteTime());
        }
        if (jhirWagaB.getGaorNum() != null){
            lqw.eq(JhirWagaBPo::getGaorNum ,jhirWagaB.getGaorNum());
        }
        if (jhirWagaB.getDesLockDisc() != null){
            lqw.eq(JhirWagaBPo::getDesLockDisc ,jhirWagaB.getDesLockDisc());
        }
        if (jhirWagaB.getGaorTotNetWid() != null){
            lqw.eq(JhirWagaBPo::getGaorTotNetWid ,jhirWagaB.getGaorTotNetWid());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getNote())){
            lqw.eq(JhirWagaBPo::getNote ,jhirWagaB.getNote());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getUrl())){
            lqw.eq(JhirWagaBPo::getUrl ,jhirWagaB.getUrl());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getLatd())){
            lqw.eq(JhirWagaBPo::getLatd ,jhirWagaB.getLatd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getLgtd())){
            lqw.eq(JhirWagaBPo::getLgtd ,jhirWagaB.getLgtd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getGcgm())){
            lqw.eq(JhirWagaBPo::getGcgm ,jhirWagaB.getGcgm());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getZya())){
            lqw.eq(JhirWagaBPo::getZya ,jhirWagaB.getZya());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getZpll())){
            lqw.eq(JhirWagaBPo::getZpll ,jhirWagaB.getZpll());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getZgll())){
            lqw.eq(JhirWagaBPo::getZgll ,jhirWagaB.getZgll());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getAddvcd())){
            lqw.eq(JhirWagaBPo::getAddvcd ,jhirWagaB.getAddvcd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getWupcd())){
            lqw.eq(JhirWagaBPo::getWupcd ,jhirWagaB.getWupcd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getWdwcd())){
            lqw.eq(JhirWagaBPo::getWdwcd ,jhirWagaB.getWdwcd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getRaincd())){
            lqw.eq(JhirWagaBPo::getRaincd ,jhirWagaB.getRaincd());
        }
        if (jhirWagaB.getWwZ() != null){
            lqw.eq(JhirWagaBPo::getWwZ ,jhirWagaB.getWwZ());
        }
        if (jhirWagaB.getSwZ() != null){
            lqw.eq(JhirWagaBPo::getSwZ ,jhirWagaB.getSwZ());
        }
        if (jhirWagaB.getHisMaxZ() != null){
            lqw.eq(JhirWagaBPo::getHisMaxZ ,jhirWagaB.getHisMaxZ());
        }
        if (jhirWagaB.getAllAvgZ() != null){
            lqw.eq(JhirWagaBPo::getAllAvgZ ,jhirWagaB.getAllAvgZ());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getVmBCd())){
            lqw.eq(JhirWagaBPo::getVmBCd ,jhirWagaB.getVmBCd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getStDesc())){
            lqw.eq(JhirWagaBPo::getStDesc ,jhirWagaB.getStDesc());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getPidCode())){
            lqw.eq(JhirWagaBPo::getPidCode ,jhirWagaB.getPidCode());
        }
        if (jhirWagaB.getHisRt() != null){
            lqw.eq(JhirWagaBPo::getHisRt ,jhirWagaB.getHisRt());
        }
        if (jhirWagaB.getSumRt() != null){
            lqw.eq(JhirWagaBPo::getSumRt ,jhirWagaB.getSumRt());
        }
        if (jhirWagaB.getNowRt() != null){
            lqw.eq(JhirWagaBPo::getNowRt ,jhirWagaB.getNowRt());
        }
        if (jhirWagaB.getHisFw() != null){
            lqw.eq(JhirWagaBPo::getHisFw ,jhirWagaB.getHisFw());
        }
        if (jhirWagaB.getSumFw() != null){
            lqw.eq(JhirWagaBPo::getSumFw ,jhirWagaB.getSumFw());
        }
        if (jhirWagaB.getNowFw() != null){
            lqw.eq(JhirWagaBPo::getNowFw ,jhirWagaB.getNowFw());
        }
        if (jhirWagaB.getStatus() != null){
            lqw.eq(JhirWagaBPo::getStatus ,jhirWagaB.getStatus());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getWascd())){
            lqw.eq(JhirWagaBPo::getWascd ,jhirWagaB.getWascd());
        }
        if (StringUtils.isNotBlank(jhirWagaB.getSource())){
            lqw.eq(JhirWagaBPo::getSource ,jhirWagaB.getSource());
        }
        List<JhirWagaBVo> jhirWagaBVos= BeanUtil.copyToList(this.list(lqw), JhirWagaBVo.class);
        return jhirWagaBVos;
    }

    /**
     * 查询闸站基础信息
     *
     * @param strCode 闸站基础信息主键
     * @return 闸站基础信息
     */
    @Override
    public JhirWagaBVo selectJhirWagaBByStrCode(String strCode) {
        JhirWagaBVo jhirWagaBVo = baseMapper.selectJhirWagaBByStrCode(strCode);
        List<JhirStrobeBPo> strobeBList = jhirWagaBMapper.getStrobeBList(strCode);
        jhirWagaBVo.setStrobeBPoList(strobeBList);
        return jhirWagaBVo;
    }

    /**
     * 查询闸站基础信息列表
     *
     * @param jhirWagaB 闸站基础信息
     * @return 闸站基础信息
     */
    @Override
    public List<JhirWagaBVo> selectJhirWagaBList(JhirWagaBParam jhirWagaB) {
        List<JhirWagaBVo> jhirWagaBVos = jhirWagaBMapper.selectJhirWagaBAllList(jhirWagaB.getStrName(),jhirWagaB.getStrCode());
        for (JhirWagaBVo jhirWagaBVo : jhirWagaBVos) {
            String strCode = jhirWagaBVo.getStrCode();
            List<JhirStrobeBPo> list = jhirWagaBMapper.getStrobeBList(strCode);
            jhirWagaBVo.setStrobeBPoList(list);
        }
        return jhirWagaBVos;
    }

    /**
     * 新增闸站基础信息
     *
     * @param jhirWagaBAddParam 闸站基础信息
     * @return 结果
     */
    @Override
    public int insertJhirWagaB(JhirWagaBAddParam jhirWagaBAddParam) {

        JhirWagaBPo jhirWagaB = new JhirWagaBPo();
        BeanUtil.copyProperties(jhirWagaBAddParam, jhirWagaB);
        return baseMapper.insert(jhirWagaB);
    }

    /**
     * 修改闸站基础信息
     *
     * @param jhirWagaBEditParam 闸站基础信息
     * @return 结果
     */
    @Override
    public int updateJhirWagaB(JhirWagaBEditParam jhirWagaBEditParam) {
        JhirWagaBPo jhirWagaB = new JhirWagaBPo();
        BeanUtil.copyProperties(jhirWagaBEditParam, jhirWagaB);
        return baseMapper.updateById(jhirWagaB);
    }

    /**
     * 批量删除闸站基础信息
     *
     * @param strCodes 需要删除的闸站基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirWagaBByStrCodes(String[] strCodes) {
        int i = baseMapper.deleteJhirWagaBByStrCodes(strCodes);
        //删除子集闸门的信息
        for (String strCode : strCodes) {
            jhirStrobeBMapper.deleteJhirStrobeBByStrobeCode(strCode);
        }
        return i;
    }

    /**
     * 删除闸站基础信息信息
     *
     * @param strCode 闸站基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirWagaBByStrCode(String strCode) {
        //进行逻辑删除，讲status修改为9
        JhirWagaBPo jhirWagaBPo = jhirWagaBMapper.selectoneByStrCode(strCode);
        jhirWagaBPo.setStatus(9L);
        return baseMapper.updateById(jhirWagaBPo);
/*        int i = baseMapper.deleteJhirWagaBByStrCode(strCode);
        //删除子集闸门的信息
        jhirStrobeBMapper.deleteJhirStrobeBByStrobeCode(strCode);*/

    }
}
