package com.jhdr.projectManagement.service.impl;


import com.jhdr.projectManagement.domain.DataApiMount;
import com.jhdr.projectManagement.mapper.DataApiMountMapper;
import com.jhdr.projectManagement.service.IDataApiMountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据资源api挂载Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class DataApiMountServiceImpl implements IDataApiMountService
{
    @Autowired
    private DataApiMountMapper dataApiMountMapper;

    /**
     * 查询数据资源api挂载
     * 
     * @param id 数据资源api挂载主键
     * @return 数据资源api挂载
     */
    @Override
    public DataApiMount selectDataApiMountById(Integer id)
    {
        return dataApiMountMapper.selectDataApiMountById(id);
    }

    /**
     * 查询数据资源api挂载列表
     * 
     * @param dataApiMount 数据资源api挂载
     * @return 数据资源api挂载
     */
    @Override
    public List<DataApiMount> selectDataApiMountList(DataApiMount dataApiMount)
    {
        return dataApiMountMapper.selectDataApiMountList(dataApiMount);
    }

    /**
     * 新增数据资源api挂载
     * 
     * @param dataApiMount 数据资源api挂载
     * @return 结果
     */
    @Override
    public int insertDataApiMount(DataApiMount dataApiMount)
    {
        return dataApiMountMapper.insertDataApiMount(dataApiMount);
    }

    /**
     * 修改数据资源api挂载
     * 
     * @param dataApiMount 数据资源api挂载
     * @return 结果
     */
    @Override
    public int updateDataApiMount(DataApiMount dataApiMount)
    {
        return dataApiMountMapper.updateDataApiMount(dataApiMount);
    }

    /**
     * 批量删除数据资源api挂载
     * 
     * @param ids 需要删除的数据资源api挂载主键
     * @return 结果
     */
    @Override
    public int deleteDataApiMountByIds(Integer[] ids)
    {
        return dataApiMountMapper.deleteDataApiMountByIds(ids);
    }

    /**
     * 删除数据资源api挂载信息
     * 
     * @param id 数据资源api挂载主键
     * @return 结果
     */
    @Override
    public int deleteDataApiMountById(Integer id)
    {
        return dataApiMountMapper.deleteDataApiMountById(id);
    }
}
