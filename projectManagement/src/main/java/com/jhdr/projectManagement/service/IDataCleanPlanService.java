package com.jhdr.projectManagement.service;
import com.jhdr.projectManagement.domain.*;

import java.util.List;

/**
 * 数据清洗方案编制Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface IDataCleanPlanService 
{
    /**
     * 查询数据清洗方案编制
     * 
     * @param id 数据清洗方案编制主键
     * @return 数据清洗方案编制
     */
    public DataCleanPlan selectDataCleanPlanById(Integer id);

    /**
     * 查询数据清洗方案编制列表
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 数据清洗方案编制集合
     */
    public List<DataCleanPlan> selectDataCleanPlanList(DataCleanPlan dataCleanPlan);

    /**
     * 新增数据清洗方案编制
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 结果
     */
    public int insertDataCleanPlan(DataCleanPlan dataCleanPlan);

    /**
     * 修改数据清洗方案编制
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 结果
     */
    public int updateDataCleanPlan(DataCleanPlan dataCleanPlan);

    /**
     * 批量删除数据清洗方案编制
     * 
     * @param ids 需要删除的数据清洗方案编制主键集合
     * @return 结果
     */
    public int deleteDataCleanPlanByIds(Integer[] ids);

    /**
     * 删除数据清洗方案编制信息
     * 
     * @param id 数据清洗方案编制主键
     * @return 结果
     */
    public int deleteDataCleanPlanById(Integer id);
}
