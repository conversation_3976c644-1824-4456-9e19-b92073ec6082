package com.jhdr.projectManagement.service;

import java.util.List;
import com.jhdr.projectManagement.entity.po.StStbprpBPo;
import com.jhdr.projectManagement.entity.param.StStbprpBParam;
import com.jhdr.projectManagement.entity.param.StStbprpBAddParam;
import com.jhdr.projectManagement.entity.param.StStbprpBEditParam;
import com.jhdr.projectManagement.entity.vo.StStbprpBVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 测站基本属性Service接口
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
public interface IStStbprpBService extends IService<StStbprpBPo>
{

    /**
     * 查询测站基本属性列表
     *
     * @param stStbprpB 测站基本属性
     * @return 测站基本属性集合
     */
    public List<StStbprpBVo> queryList(StStbprpBPo stStbprpB);

    /**
     * 查询测站基本属性
     *
     * @param STCD 测站基本属性主键
     * @return 测站基本属性
     */
    public StStbprpBVo selectStStbprpBBySTCD(String STCD);

    /**
     * 查询测站基本属性列表
     *
     * @param stStbprpB 测站基本属性
     * @return 测站基本属性集合
     */
    public List<StStbprpBVo> selectStStbprpBList(StStbprpBParam stStbprpB);

    /**
     * 新增测站基本属性
     *
     * @param stStbprpB 测站基本属性
     * @return 结果
     */
    public int insertStStbprpB(StStbprpBAddParam stStbprpB);

    /**
     * 修改测站基本属性
     *
     * @param stStbprpB 测站基本属性
     * @return 结果
     */
    public int updateStStbprpB(StStbprpBEditParam stStbprpB);

    /**
     * 批量删除测站基本属性
     *
     * @param STCDs 需要删除的测站基本属性主键集合
     * @return 结果
     */
    public int deleteStStbprpBBySTCDs(String[] STCDs);

    /**
     * 删除测站基本属性信息
     *
     * @param STCD 测站基本属性主键
     * @return 结果
     */
    public int deleteStStbprpBBySTCD(String STCD);

}
