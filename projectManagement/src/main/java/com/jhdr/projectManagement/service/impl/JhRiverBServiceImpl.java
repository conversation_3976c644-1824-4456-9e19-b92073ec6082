package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.projectManagement.entity.po.JhRiverBPo;
import com.jhdr.projectManagement.entity.param.JhRiverBParam;
import com.jhdr.projectManagement.entity.param.JhRiverBAddParam;
import com.jhdr.projectManagement.entity.param.JhRiverBEditParam;
import com.jhdr.projectManagement.entity.vo.JhRiverBVo;
import com.jhdr.projectManagement.mapper.JhRiverBMapper;
import com.jhdr.projectManagement.service.IJhRiverBService;

import java.math.BigDecimal;
import java.util.ArrayList;

import java.util.List;

/**
 * 河流信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class JhRiverBServiceImpl extends ServiceImpl<JhRiverBMapper, JhRiverBPo> implements IJhRiverBService {
    @Autowired
    private JhRiverBMapper jhRiverBMapper;

    @Override
    public List<JhRiverBVo> queryList(JhRiverBPo jhRiverB) {
        LambdaQueryWrapper<JhRiverBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhRiverB.getRvnm())) {
            lqw.eq(JhRiverBPo::getRvnm, jhRiverB.getRvnm());
        }
        if (StringUtils.isNotBlank(jhRiverB.getRvtp())) {
            lqw.eq(JhRiverBPo::getRvtp, jhRiverB.getRvtp());
        }
        if (StringUtils.isNotBlank(jhRiverB.getBelws())) {
            lqw.eq(JhRiverBPo::getBelws, jhRiverB.getBelws());
        }
        if (StringUtils.isNotBlank(jhRiverB.getRvpl())) {
            lqw.eq(JhRiverBPo::getRvpl, jhRiverB.getRvpl());
        }
        if (StringUtils.isNotBlank(jhRiverB.getRved())) {
            lqw.eq(JhRiverBPo::getRved, jhRiverB.getRved());
        }
        if (jhRiverB.getMnstln() != null) {
            lqw.eq(JhRiverBPo::getMnstln, jhRiverB.getMnstln());
        }
        if (jhRiverB.getTtdrbsar() != null) {
            lqw.eq(JhRiverBPo::getTtdrbsar, jhRiverB.getTtdrbsar());
        }
        if (jhRiverB.getDrbspp() != null) {
            lqw.eq(JhRiverBPo::getDrbspp, jhRiverB.getDrbspp());
        }
        if (jhRiverB.getIrrigatedarea() != null) {
            lqw.eq(JhRiverBPo::getIrrigatedarea, jhRiverB.getIrrigatedarea());
        }
        if (StringUtils.isNotBlank(jhRiverB.getMunit())) {
            lqw.eq(JhRiverBPo::getMunit, jhRiverB.getMunit());
        }
        if (StringUtils.isNotBlank(jhRiverB.getRvchief())) {
            lqw.eq(JhRiverBPo::getRvchief, jhRiverB.getRvchief());
        }
        if (StringUtils.isNotBlank(jhRiverB.getRm())) {
            lqw.eq(JhRiverBPo::getRm, jhRiverB.getRm());
        }
        List<JhRiverBVo> jhRiverBVos = BeanUtil.copyToList(this.list(lqw), JhRiverBVo.class);
        return jhRiverBVos;
    }

    /**
     * 查询河流信息
     *
     * @param ennmcd 河流信息主键
     * @return 河流信息
     */
    @Override
    public JhRiverBVo selectJhRiverBByEnnmcd(String ennmcd) {
        return baseMapper.selectJhRiverBByEnnmcd(ennmcd);
    }

    /**
     * 查询河流信息列表
     *
     * @param jhRiverB 河流信息
     * @return 河流信息
     */
    @Override
    public List<JhRiverBVo> selectJhRiverBList(JhRiverBParam jhRiverB) {
        return jhRiverBMapper.selectJhRiverAllList(jhRiverB.getRvnm(),jhRiverB.getEnnmcd());
    }

    /**
     * 新增河流信息
     *
     * @param jhRiverBAddParam 河流信息
     * @return 结果
     */
    @Override
    public int insertJhRiverB(JhRiverBAddParam jhRiverBAddParam) {

        JhRiverBPo jhRiverB = new JhRiverBPo();
        BeanUtil.copyProperties(jhRiverBAddParam, jhRiverB);
        return baseMapper.insert(jhRiverB);
    }

    /**
     * 修改河流信息
     *
     * @param jhRiverBEditParam 河流信息
     * @return 结果
     */
    @Override
    public int updateJhRiverB(JhRiverBEditParam jhRiverBEditParam) {
        JhRiverBPo jhRiverB = new JhRiverBPo();
        BeanUtil.copyProperties(jhRiverBEditParam, jhRiverB);
        return baseMapper.updateById(jhRiverB);
    }

    /**
     * 批量删除河流信息
     *
     * @param ennmcds 需要删除的河流信息主键
     * @return 结果
     */
    @Override
    public int deleteJhRiverBByEnnmcds(String[] ennmcds) {
        return baseMapper.deleteJhRiverBByEnnmcds(ennmcds);
    }

    /**
     * 删除河流信息信息
     *
     * @param ennmcd 河流信息主键
     * @return 结果
     */
    @Override
    public int deleteJhRiverBByEnnmcd(String ennmcd) {
        JhRiverBVo jhRiverBVo = jhRiverBMapper.selectJhRiverBByEnnmcd(ennmcd);
        JhRiverBPo jhRiverB = new JhRiverBPo();
        BeanUtil.copyProperties(jhRiverBVo, jhRiverB);
        jhRiverB.setStatus(BigDecimal.valueOf(9));
        return baseMapper.updateById(jhRiverB);
    }
}
