package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.projectManagement.entity.param.JhirWagaBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirWagaBVo;
import com.jhdr.projectManagement.mapper.JhirWagaBMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.projectManagement.entity.po.JhirStrobeBPo;
import com.jhdr.projectManagement.entity.param.JhirStrobeBParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBAddParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirStrobeBVo;
import com.jhdr.projectManagement.mapper.JhirStrobeBMapper;
import com.jhdr.projectManagement.service.IJhirStrobeBService;

import java.math.BigDecimal;
import java.util.*;

/**
 * 闸门基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class JhirStrobeBServiceImpl extends ServiceImpl<JhirStrobeBMapper, JhirStrobeBPo> implements IJhirStrobeBService {
    @Autowired
    private JhirStrobeBMapper jhirStrobeBMapper;
    @Autowired
    private JhirWagaBMapper jhirWagaBMapper;

    @Override
    public List<JhirStrobeBVo> queryList(JhirStrobeBPo jhirStrobeB) {
        LambdaQueryWrapper<JhirStrobeBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhirStrobeB.getStrCode())){
            lqw.eq(JhirStrobeBPo::getStrCode ,jhirStrobeB.getStrCode());
        }
        if (StringUtils.isNotBlank(jhirStrobeB.getStrobeType())){
            lqw.eq(JhirStrobeBPo::getStrobeType ,jhirStrobeB.getStrobeType());
        }
        if (StringUtils.isNotBlank(jhirStrobeB.getStrobeMtr())){
            lqw.eq(JhirStrobeBPo::getStrobeMtr ,jhirStrobeB.getStrobeMtr());
        }
        if (jhirStrobeB.getStrobeH() != null){
            lqw.eq(JhirStrobeBPo::getStrobeH ,jhirStrobeB.getStrobeH());
        }
        if (jhirStrobeB.getStrobeW() != null){
            lqw.eq(JhirStrobeBPo::getStrobeW ,jhirStrobeB.getStrobeW());
        }
        if (jhirStrobeB.getStrobeWt() != null){
            lqw.eq(JhirStrobeBPo::getStrobeWt ,jhirStrobeB.getStrobeWt());
        }
        if (StringUtils.isNotBlank(jhirStrobeB.getFty())){
            lqw.eq(JhirStrobeBPo::getFty ,jhirStrobeB.getFty());
        }
        if (jhirStrobeB.getStrobeBsH() != null){
            lqw.eq(JhirStrobeBPo::getStrobeBsH ,jhirStrobeB.getStrobeBsH());
        }
        if (jhirStrobeB.getGpcd() != null){
            lqw.eq(JhirStrobeBPo::getGpcd ,jhirStrobeB.getGpcd());
        }
        List<JhirStrobeBVo> jhirStrobeBVos= BeanUtil.copyToList(this.list(lqw), JhirStrobeBVo.class);
        return jhirStrobeBVos;
    }

    /**
     * 查询闸门基础信息
     *
     * @param strobeCode 闸门基础信息主键
     * @return 闸门基础信息
     */
    @Override
    public JhirStrobeBVo selectJhirStrobeBByStrobeCode(String strobeCode) {
        return baseMapper.selectJhirStrobeBByStrobeCode(strobeCode);
    }

    /**
     * 查询闸门基础信息列表
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 闸门基础信息
     */
    @Override
    public List<JhirStrobeBVo> selectJhirStrobeBList(JhirStrobeBParam jhirStrobeB) {
        return jhirStrobeBMapper.selectJhirStrobeBAllList(jhirStrobeB.getStrCode(),jhirStrobeB.getStrobeCode());
    }

    /**
     * 新增闸门基础信息
     *
     * @param jhirStrobeBAddParam 闸门基础信息
     * @return 结果
     */
    @Override
    public int insertJhirStrobeB(JhirStrobeBAddParam jhirStrobeBAddParam) {
       // BigDecimal gpcd = jhirStrobeBAddParam.getGpcd();
        List<JhirStrobeBVo> jhirStrobeBVos = jhirStrobeBMapper.selectJhirStrobeBAllListByStrCode(jhirStrobeBAddParam.getStrCode());
        List<Integer> list = new ArrayList<>();
        //获取闸机下面最大的机组号
        for (JhirStrobeBVo jhirStrobeBVo : jhirStrobeBVos) {
            BigDecimal voGpcd = jhirStrobeBVo.getGpcd();
            int gpcd = voGpcd.intValue();
            list.add(gpcd);
        }
        int max;
        if (list.isEmpty()) {
            max = 0;
        } else {
            max = Collections.max(list);
        }
        int gpcdNew =max +1;
        int insert;
        JhirStrobeBPo jhirStrobeBPo = new JhirStrobeBPo();
        //当前没有闸机
        if (jhirStrobeBVos.size()==0){
            JhirStrobeBVo jhirStrobeBVo1 = new JhirStrobeBVo();
            jhirStrobeBVo1.setStrCode(jhirStrobeBAddParam.getStrCode());
            jhirStrobeBVo1.setStrobeCode(UUID.randomUUID().toString());
            BeanUtil.copyProperties(jhirStrobeBVo1, jhirStrobeBPo);
            jhirStrobeBPo.setGpcd(BigDecimal.valueOf(gpcdNew));
             insert = baseMapper.insert(jhirStrobeBPo);
        }else {//当前有闸机
            JhirStrobeBVo jhirStrobeBVo = jhirStrobeBVos.get(0);
            BeanUtil.copyProperties(jhirStrobeBVo, jhirStrobeBPo);
            jhirStrobeBPo.setGpcd(BigDecimal.valueOf(gpcdNew));
            jhirStrobeBPo.setStrCode(jhirStrobeBAddParam.getStrCode());
            jhirStrobeBPo.setStrobeCode(UUID.randomUUID().toString());
             insert = baseMapper.insert(jhirStrobeBPo);
        }
        return insert;
    }

    /**
     * 修改闸门基础信息
     *
     * @param jhirStrobeBEditParam 闸门基础信息
     * @return 结果
     */
    @Override
    public int updateJhirStrobeB(JhirStrobeBEditParam jhirStrobeBEditParam) {
        JhirStrobeBPo jhirStrobeB = new JhirStrobeBPo();
        BeanUtil.copyProperties(jhirStrobeBEditParam, jhirStrobeB);
        return baseMapper.updateById(jhirStrobeB);
    }

    /**
     * 批量删除闸门基础信息
     *
     * @param strobeCodes 需要删除的闸门基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirStrobeBByStrobeCodes(String[] strobeCodes) {
        return baseMapper.deleteJhirStrobeBByStrobeCodes(strobeCodes);
    }

    /**
     * 删除闸门基础信息信息
     *
     * @param strobeCode 闸门基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirStrobeBByStrobeCode(String strobeCode) {
        int i = baseMapper.deleteJhirStrobeBByStrobeCode(strobeCode);
        //修改子集的闸门数量
   /*     JhirStrobeBVo jhirStrobeBVo = jhirStrobeBMapper.selectJhirStrobeBByStrobeCode(strobeCode);
        String strCode = jhirStrobeBVo.getStrCode();
        JhirWagaBVo jhirWagaBVo = jhirWagaBMapper.selectJhirWagaBByStrCode(strCode);
        BigDecimal gaorNum = jhirWagaBVo.getGaorNum();
        BigDecimal a = new BigDecimal(1);
        BigDecimal b = new BigDecimal(0);
        if (gaorNum.compareTo(b) == 1) {
            gaorNum = gaorNum.subtract(a);
            jhirWagaBVo.setGaorNum(gaorNum);
            JhirWagaBEditParam jhirWagaBEditParam = new JhirWagaBEditParam();
            BeanUtils.copyProperties(jhirWagaBEditParam, jhirWagaBVo);
            jhirWagaBMapper.updateJhirWagaB(jhirWagaBEditParam);
        }*/
        return i;
    }
}
