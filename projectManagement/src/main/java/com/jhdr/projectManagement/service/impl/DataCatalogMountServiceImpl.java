package com.jhdr.projectManagement.service.impl;


import com.jhdr.projectManagement.domain.DataCatalogMount;
import com.jhdr.projectManagement.mapper.DataCatalogMountMapper;
import com.jhdr.projectManagement.service.IDataCatalogMountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据资源目录挂载Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class DataCatalogMountServiceImpl implements IDataCatalogMountService
{
    @Autowired
    private DataCatalogMountMapper dataCatalogMountMapper;

    /**
     * 查询数据资源目录挂载
     * 
     * @param id 数据资源目录挂载主键
     * @return 数据资源目录挂载
     */
    @Override
    public DataCatalogMount selectDataCatalogMountById(Integer id)
    {
        return dataCatalogMountMapper.selectDataCatalogMountById(id);
    }

    /**
     * 查询数据资源目录挂载列表
     * 
     * @param dataCatalogMount 数据资源目录挂载
     * @return 数据资源目录挂载
     */
    @Override
    public List<DataCatalogMount> selectDataCatalogMountList(DataCatalogMount dataCatalogMount)
    {
        return dataCatalogMountMapper.selectDataCatalogMountList(dataCatalogMount);
    }

    /**
     * 新增数据资源目录挂载
     * 
     * @param dataCatalogMount 数据资源目录挂载
     * @return 结果
     */
    @Override
    public int insertDataCatalogMount(DataCatalogMount dataCatalogMount)
    {
        return dataCatalogMountMapper.insertDataCatalogMount(dataCatalogMount);
    }

    /**
     * 修改数据资源目录挂载
     * 
     * @param dataCatalogMount 数据资源目录挂载
     * @return 结果
     */
    @Override
    public int updateDataCatalogMount(DataCatalogMount dataCatalogMount)
    {
        return dataCatalogMountMapper.updateDataCatalogMount(dataCatalogMount);
    }

    /**
     * 批量删除数据资源目录挂载
     * 
     * @param ids 需要删除的数据资源目录挂载主键
     * @return 结果
     */
    @Override
    public int deleteDataCatalogMountByIds(Integer[] ids)
    {
        return dataCatalogMountMapper.deleteDataCatalogMountByIds(ids);
    }

    /**
     * 删除数据资源目录挂载信息
     * 
     * @param id 数据资源目录挂载主键
     * @return 结果
     */
    @Override
    public int deleteDataCatalogMountById(Integer id)
    {
        return dataCatalogMountMapper.deleteDataCatalogMountById(id);
    }
}
