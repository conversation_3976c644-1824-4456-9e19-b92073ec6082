package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.projectManagement.entity.po.HubBasicInformationPo;
import com.jhdr.projectManagement.entity.param.HubBasicInformationParam;
import com.jhdr.projectManagement.entity.param.HubBasicInformationAddParam;
import com.jhdr.projectManagement.entity.param.HubBasicInformationEditParam;
import com.jhdr.projectManagement.entity.vo.HubBasicInformationVo;
import com.jhdr.projectManagement.mapper.HubBasicInformationMapper;
import com.jhdr.projectManagement.service.IHubBasicInformationService;

import java.util.ArrayList;

import java.util.List;

/**
 * 枢纽基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class HubBasicInformationServiceImpl extends ServiceImpl<HubBasicInformationMapper, HubBasicInformationPo> implements IHubBasicInformationService {
@Autowired
private HubBasicInformationMapper hubBasicInformationMapper;
    @Override
    public List<HubBasicInformationVo> queryList(HubBasicInformationPo hubBasicInformation) {
        LambdaQueryWrapper<HubBasicInformationPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(hubBasicInformation.getHubCode())){
            lqw.eq(HubBasicInformationPo::getHubCode ,hubBasicInformation.getHubCode());
        }
        if (StringUtils.isNotBlank(hubBasicInformation.getAdminArea())){
            lqw.eq(HubBasicInformationPo::getAdminArea ,hubBasicInformation.getAdminArea());
        }
        if (StringUtils.isNotBlank(hubBasicInformation.getProfile())){
            lqw.eq(HubBasicInformationPo::getProfile ,hubBasicInformation.getProfile());
        }
        if (hubBasicInformation.getStatus() != null){
            lqw.eq(HubBasicInformationPo::getStatus ,hubBasicInformation.getStatus());
        }
        if (hubBasicInformation.getGataCode() != null){
            lqw.eq(HubBasicInformationPo::getGataCode ,hubBasicInformation.getGataCode());
        }
        if (StringUtils.isNotBlank(hubBasicInformation.getName())){
            lqw.like(HubBasicInformationPo::getName ,hubBasicInformation.getName());
        }
        if (StringUtils.isNotBlank(hubBasicInformation.getPustCode())){
            lqw.eq(HubBasicInformationPo::getPustCode ,hubBasicInformation.getPustCode());
        }
        if (hubBasicInformation.getLttd() != null){
            lqw.eq(HubBasicInformationPo::getLttd ,hubBasicInformation.getLttd());
        }
        if (hubBasicInformation.getLgtd() != null){
            lqw.eq(HubBasicInformationPo::getLgtd ,hubBasicInformation.getLgtd());
        }
        if (StringUtils.isNotBlank(hubBasicInformation.getDispatchUnit())){
            lqw.eq(HubBasicInformationPo::getDispatchUnit ,hubBasicInformation.getDispatchUnit());
        }
        if (StringUtils.isNotBlank(hubBasicInformation.getAddress())){
            lqw.eq(HubBasicInformationPo::getAddress ,hubBasicInformation.getAddress());
        }
        List<HubBasicInformationVo> hubBasicInformationVos= BeanUtil.copyToList(this.list(lqw), HubBasicInformationVo.class);
        return hubBasicInformationVos;
    }
    /**
     * 查询枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 枢纽基础信息
     */
    @Override
    public HubBasicInformationVo selectHubBasicInformationById(Long id)
    {
        return baseMapper.selectHubBasicInformationById(id);
    }

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息
     */
    @Override
    public List<HubBasicInformationVo> selectHubBasicInformationList(HubBasicInformationParam hubBasicInformation)
    {
        String name = hubBasicInformation.getName();
        List<HubBasicInformationVo> list = hubBasicInformationMapper.getAllList(name,hubBasicInformation.getHubCode());
        return list;
    }

    /**
     * 新增枢纽基础信息
     *
     * @param hubBasicInformationAddParam 枢纽基础信息
     * @return 结果
     */
    @Override
    public int insertHubBasicInformation(HubBasicInformationAddParam hubBasicInformationAddParam)
    {

        HubBasicInformationPo hubBasicInformation=new HubBasicInformationPo();
        BeanUtil.copyProperties(hubBasicInformationAddParam,hubBasicInformation);
        return baseMapper.insert(hubBasicInformation);
    }

    /**
     * 修改枢纽基础信息
     *
     * @param hubBasicInformationEditParam 枢纽基础信息
     * @return 结果
     */
    @Override
    public int updateHubBasicInformation(HubBasicInformationEditParam hubBasicInformationEditParam)
    {
        HubBasicInformationPo hubBasicInformation=new HubBasicInformationPo();
        BeanUtil.copyProperties(hubBasicInformationEditParam,hubBasicInformation);
        return baseMapper.updateById(hubBasicInformation);
    }

    /**
     * 批量删除枢纽基础信息
     *
     * @param ids 需要删除的枢纽基础信息主键
     * @return 结果
     */
    @Override
    public int deleteHubBasicInformationByIds(Long[] ids)
    {
        return baseMapper.deleteHubBasicInformationByIds(ids);
    }

    /**
     * 删除枢纽基础信息信息
     *
     * @param id 枢纽基础信息主键
     * @return 结果
     */
    @Override
    public int deleteHubBasicInformationById(Long id)
    {
        return baseMapper.deleteHubBasicInformationById(id);
    }
}
