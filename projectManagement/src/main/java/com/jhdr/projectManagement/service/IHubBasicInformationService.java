package com.jhdr.projectManagement.service;

import java.util.List;
import com.jhdr.projectManagement.entity.po.HubBasicInformationPo;
import com.jhdr.projectManagement.entity.param.HubBasicInformationParam;
import com.jhdr.projectManagement.entity.param.HubBasicInformationAddParam;
import com.jhdr.projectManagement.entity.param.HubBasicInformationEditParam;
import com.jhdr.projectManagement.entity.vo.HubBasicInformationVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 枢纽基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface IHubBasicInformationService extends IService<HubBasicInformationPo>
{

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息集合
     */
    public List<HubBasicInformationVo> queryList(HubBasicInformationPo hubBasicInformation);

    /**
     * 查询枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 枢纽基础信息
     */
    public HubBasicInformationVo selectHubBasicInformationById(Long id);

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息集合
     */
    public List<HubBasicInformationVo> selectHubBasicInformationList(HubBasicInformationParam hubBasicInformation);

    /**
     * 新增枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int insertHubBasicInformation(HubBasicInformationAddParam hubBasicInformation);

    /**
     * 修改枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int updateHubBasicInformation(HubBasicInformationEditParam hubBasicInformation);

    /**
     * 批量删除枢纽基础信息
     *
     * @param ids 需要删除的枢纽基础信息主键集合
     * @return 结果
     */
    public int deleteHubBasicInformationByIds(Long[] ids);

    /**
     * 删除枢纽基础信息信息
     *
     * @param id 枢纽基础信息主键
     * @return 结果
     */
    public int deleteHubBasicInformationById(Long id);

}
