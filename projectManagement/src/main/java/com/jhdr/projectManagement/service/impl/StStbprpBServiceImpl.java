package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.projectManagement.entity.po.StStbprpBPo;
import com.jhdr.projectManagement.entity.param.StStbprpBParam;
import com.jhdr.projectManagement.entity.param.StStbprpBAddParam;
import com.jhdr.projectManagement.entity.param.StStbprpBEditParam;
import com.jhdr.projectManagement.entity.vo.StStbprpBVo;
import com.jhdr.projectManagement.mapper.StStbprpBMapper;
import com.jhdr.projectManagement.service.IStStbprpBService;

import java.util.ArrayList;

import java.util.List;

/**
 * 测站基本属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Service
public class StStbprpBServiceImpl extends ServiceImpl<StStbprpBMapper, StStbprpBPo> implements IStStbprpBService {
@Autowired
private StStbprpBMapper stStbprpBMapper;
    @Override
    public List<StStbprpBVo> queryList(StStbprpBPo stStbprpB) {
       /* LambdaQueryWrapper<StStbprpBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(stStbprpB.getSTNM())){
            lqw.eq(StStbprpBPo::getSTNM ,stStbprpB.getSTNM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getRVNM())){
            lqw.eq(StStbprpBPo::getRVNM ,stStbprpB.getRVNM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getHNNM())){
            lqw.eq(StStbprpBPo::getHNNM ,stStbprpB.getHNNM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getBSNM())){
            lqw.eq(StStbprpBPo::getBSNM ,stStbprpB.getBSNM());
        }
        if (stStbprpB.getLGTD() != null){
            lqw.eq(StStbprpBPo::getLGTD ,stStbprpB.getLGTD());
        }
        if (stStbprpB.getLTTD() != null){
            lqw.eq(StStbprpBPo::getLTTD ,stStbprpB.getLTTD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSTLC())){
            lqw.eq(StStbprpBPo::getSTLC ,stStbprpB.getSTLC());
        }
        if (StringUtils.isNotBlank(stStbprpB.getADDVCD())){
            lqw.eq(StStbprpBPo::getADDVCD ,stStbprpB.getADDVCD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getDTMNM())){
            lqw.eq(StStbprpBPo::getDTMNM ,stStbprpB.getDTMNM());
        }
        if (stStbprpB.getDTMEL() != null){
            lqw.eq(StStbprpBPo::getDTMEL ,stStbprpB.getDTMEL());
        }
        if (stStbprpB.getDTPR() != null){
            lqw.eq(StStbprpBPo::getDTPR ,stStbprpB.getDTPR());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSTTP())){
            lqw.eq(StStbprpBPo::getSTTP ,stStbprpB.getSTTP());
        }
        if (StringUtils.isNotBlank(stStbprpB.getFRGRD())){
            lqw.eq(StStbprpBPo::getFRGRD ,stStbprpB.getFRGRD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getESSTYM())){
            lqw.eq(StStbprpBPo::getESSTYM ,stStbprpB.getESSTYM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getBGFRYM())){
            lqw.eq(StStbprpBPo::getBGFRYM ,stStbprpB.getBGFRYM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getATCUNIT())){
            lqw.eq(StStbprpBPo::getATCUNIT ,stStbprpB.getATCUNIT());
        }
        if (StringUtils.isNotBlank(stStbprpB.getADMAUTH())){
            lqw.eq(StStbprpBPo::getADMAUTH ,stStbprpB.getADMAUTH());
        }
        if (StringUtils.isNotBlank(stStbprpB.getLOCALITY())){
            lqw.eq(StStbprpBPo::getLOCALITY ,stStbprpB.getLOCALITY());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSTBK())){
            lqw.eq(StStbprpBPo::getSTBK ,stStbprpB.getSTBK());
        }
        if (stStbprpB.getSTAZT() != null){
            lqw.eq(StStbprpBPo::getSTAZT ,stStbprpB.getSTAZT());
        }
        if (stStbprpB.getDSTRVM() != null){
            lqw.eq(StStbprpBPo::getDSTRVM ,stStbprpB.getDSTRVM());
        }
        if (stStbprpB.getDRNA() != null){
            lqw.eq(StStbprpBPo::getDRNA ,stStbprpB.getDRNA());
        }
        if (StringUtils.isNotBlank(stStbprpB.getPHCD())){
            lqw.eq(StStbprpBPo::getPHCD ,stStbprpB.getPHCD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getUSFL())){
            lqw.eq(StStbprpBPo::getUSFL ,stStbprpB.getUSFL());
        }
        if (StringUtils.isNotBlank(stStbprpB.getCOMMENTS())){
            lqw.eq(StStbprpBPo::getCOMMENTS ,stStbprpB.getCOMMENTS());
        }
        if (StringUtils.isNotBlank(stStbprpB.getMODITIME())){
            lqw.eq(StStbprpBPo::getMODITIME ,stStbprpB.getMODITIME());
        }
        if (stStbprpB.getIsz() != null){
            lqw.eq(StStbprpBPo::getIsz ,stStbprpB.getIsz());
        }
        if (stStbprpB.getIswz() != null){
            lqw.eq(StStbprpBPo::getIswz ,stStbprpB.getIswz());
        }
        if (stStbprpB.getIsdsq() != null){
            lqw.eq(StStbprpBPo::getIsdsq ,stStbprpB.getIsdsq());
        }
        if (stStbprpB.getIsdrp() != null){
            lqw.eq(StStbprpBPo::getIsdrp ,stStbprpB.getIsdrp());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSource())){
            lqw.eq(StStbprpBPo::getSource ,stStbprpB.getSource());
        }
        List<StStbprpBVo> stStbprpBVos= BeanUtil.copyToList(this.list(lqw), StStbprpBVo.class);
        return stStbprpBVos;*/
        return null;
    }
    /**
     * 查询测站基本属性
     *
     * @param STCD 测站基本属性主键
     * @return 测站基本属性
     */
    @Override
    public StStbprpBVo selectStStbprpBBySTCD(String STCD)
    {
        return baseMapper.selectStStbprpBBySTCD(STCD);
    }

    /**
     * 查询测站基本属性列表
     *
     * @param stStbprpB 测站基本属性
     * @return 测站基本属性
     */
    @Override
    public List<StStbprpBVo> selectStStbprpBList(StStbprpBParam stStbprpB)
    {

        return stStbprpBMapper.getAllList(stStbprpB.getSTNM(),stStbprpB.getSTCD());
    }

    /**
     * 新增测站基本属性
     *
     * @param stStbprpBAddParam 测站基本属性
     * @return 结果
     */
    @Override
    public int insertStStbprpB(StStbprpBAddParam stStbprpBAddParam)
    {

        StStbprpBPo stStbprpB=new StStbprpBPo();
        BeanUtil.copyProperties(stStbprpBAddParam,stStbprpB);
        return baseMapper.insert(stStbprpB);
    }

    /**
     * 修改测站基本属性
     *
     * @param stStbprpBEditParam 测站基本属性
     * @return 结果
     */
    @Override
    public int updateStStbprpB(StStbprpBEditParam stStbprpBEditParam)
    {
        StStbprpBPo stStbprpB=new StStbprpBPo();
        BeanUtil.copyProperties(stStbprpBEditParam,stStbprpB);
        stStbprpB.setModitime(DateUtils.getNowDate().toString());
        return baseMapper.updateById(stStbprpB);
    }

    /**
     * 批量删除测站基本属性
     *
     * @param STCDs 需要删除的测站基本属性主键
     * @return 结果
     */
    @Override
    public int deleteStStbprpBBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteStStbprpBBySTCDs(STCDs);
    }

    /**
     * 删除测站基本属性信息
     *
     * @param STCD 测站基本属性主键
     * @return 结果
     */
    @Override
    public int deleteStStbprpBBySTCD(String STCD)
    {
        return baseMapper.deleteStStbprpBBySTCD(STCD);
    }
}
