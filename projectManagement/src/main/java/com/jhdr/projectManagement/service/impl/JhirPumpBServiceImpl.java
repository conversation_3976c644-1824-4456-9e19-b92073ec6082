package com.jhdr.projectManagement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.projectManagement.entity.po.JhirPustBPo;
import com.jhdr.projectManagement.mapper.JhirPustBMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.projectManagement.entity.po.JhirPumpBPo;
import com.jhdr.projectManagement.entity.param.JhirPumpBParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBAddParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirPumpBVo;
import com.jhdr.projectManagement.mapper.JhirPumpBMapper;
import com.jhdr.projectManagement.service.IJhirPumpBService;

import java.math.BigDecimal;
import java.util.*;

/**
 * 水泵基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service
public class JhirPumpBServiceImpl extends ServiceImpl<JhirPumpBMapper, JhirPumpBPo> implements IJhirPumpBService {
    @Autowired
    private JhirPumpBMapper jhirPumpBMapper;
    @Autowired
    private JhirPustBMapper jhirPustBMapper;

    @Override
    public List<JhirPumpBVo> queryList(JhirPumpBPo jhirPumpB) {
        LambdaQueryWrapper<JhirPumpBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhirPumpB.getStrCode())) {
            lqw.eq(JhirPumpBPo::getStrCode, jhirPumpB.getStrCode());
        }
        if (StringUtils.isNotBlank(jhirPumpB.getFty())) {
            lqw.eq(JhirPumpBPo::getFty, jhirPumpB.getFty());
        }
        if (StringUtils.isNotBlank(jhirPumpB.getPumpType())) {
            lqw.eq(JhirPumpBPo::getPumpType, jhirPumpB.getPumpType());
        }
        if (jhirPumpB.getPumpD() != null) {
            lqw.eq(JhirPumpBPo::getPumpD, jhirPumpB.getPumpD());
        }
        if (jhirPumpB.getPumpCpc() != null) {
            lqw.eq(JhirPumpBPo::getPumpCpc, jhirPumpB.getPumpCpc());
        }
        if (jhirPumpB.getPumpHead() != null) {
            lqw.eq(JhirPumpBPo::getPumpHead, jhirPumpB.getPumpHead());
        }
        if (jhirPumpB.getPumpPower() != null) {
            lqw.eq(JhirPumpBPo::getPumpPower, jhirPumpB.getPumpPower());
        }
        if (jhirPumpB.getPumpEff() != null) {
            lqw.eq(JhirPumpBPo::getPumpEff, jhirPumpB.getPumpEff());
        }
        if (jhirPumpB.getAlwSctVac() != null) {
            lqw.eq(JhirPumpBPo::getAlwSctVac, jhirPumpB.getAlwSctVac());
        }
        if (jhirPumpB.getFactHead() != null) {
            lqw.eq(JhirPumpBPo::getFactHead, jhirPumpB.getFactHead());
        }
        if (jhirPumpB.getMaxHead() != null) {
            lqw.eq(JhirPumpBPo::getMaxHead, jhirPumpB.getMaxHead());
        }
        if (jhirPumpB.getMinHrad() != null) {
            lqw.eq(JhirPumpBPo::getMinHrad, jhirPumpB.getMinHrad());
        }
        if (jhirPumpB.getPmpcd() != null) {
            lqw.eq(JhirPumpBPo::getPmpcd, jhirPumpB.getPmpcd());
        }
        if (StringUtils.isNotBlank(jhirPumpB.getWupcd())) {
            lqw.eq(JhirPumpBPo::getWupcd, jhirPumpB.getWupcd());
        }
        if (StringUtils.isNotBlank(jhirPumpB.getWdwcd())) {
            lqw.eq(JhirPumpBPo::getWdwcd, jhirPumpB.getWdwcd());
        }
        if (StringUtils.isNotBlank(jhirPumpB.getRaincd())) {
            lqw.eq(JhirPumpBPo::getRaincd, jhirPumpB.getRaincd());
        }
        if (jhirPumpB.getWw() != null) {
            lqw.eq(JhirPumpBPo::getWw, jhirPumpB.getWw());
        }
        if (jhirPumpB.getSw() != null) {
            lqw.eq(JhirPumpBPo::getSw, jhirPumpB.getSw());
        }
        List<JhirPumpBVo> jhirPumpBVos = BeanUtil.copyToList(this.list(lqw), JhirPumpBVo.class);
        return jhirPumpBVos;
    }

    /**
     * 查询水泵基础信息
     *
     * @param pumpCode 水泵基础信息主键
     * @return 水泵基础信息
     */
    @Override
    public JhirPumpBVo selectJhirPumpBByPumpCode(String pumpCode) {
        return baseMapper.selectJhirPumpBByPumpCode(pumpCode);
    }

    /**
     * 查询水泵基础信息列表
     *
     * @param jhirPumpB 水泵基础信息
     * @return 水泵基础信息
     */
    @Override
    public List<JhirPumpBVo> selectJhirPumpBList(JhirPumpBParam jhirPumpB) {
        return jhirPumpBMapper.selectJhirPumpBAllList(jhirPumpB.getStrCode(),jhirPumpB.getPumpCode());
    }

    /**
     * 新增水泵基础信息
     *
     * @param jhirPumpBAddParam 水泵基础信息
     * @return 结果
     */
    @Override
    public int insertJhirPumpB(JhirPumpBAddParam jhirPumpBAddParam) {
        //查询机组编号不能相同
        List<JhirPumpBVo> jhirPumpBPoList = jhirPumpBMapper.selectJhirPumpBAllListByStrCode(jhirPumpBAddParam.getStrCode());
        List<Integer> list = new ArrayList<>();
        //获取泵机下面最大的机组号
        for (JhirPumpBVo jhirPumpBPo : jhirPumpBPoList) {
            BigDecimal poPmpcd = jhirPumpBPo.getPmpcd();
            int pmpcd = poPmpcd.intValue();
            list.add(pmpcd);
        }
        int max;
        if (list.isEmpty()) {
             max = 0;
        } else {
             max = Collections.max(list);
        }
        int pmpcdNew = max + 1;
        JhirPumpBPo jhirPumpB = new JhirPumpBPo();
        int i ;
        //当前没有泵机
        if (jhirPumpBPoList.size()==0){
            JhirPumpBVo jhirPumpBVo1 = new JhirPumpBVo();
            jhirPumpBVo1.setStrCode(jhirPumpBAddParam.getStrCode());
            jhirPumpBVo1.setPumpCode(UUID.randomUUID().toString());
            BeanUtil.copyProperties(jhirPumpBVo1, jhirPumpB);
            jhirPumpB.setPmpcd(BigDecimal.valueOf(pmpcdNew));
             i = jhirPumpBMapper.insert(jhirPumpB);
        }else {//当前有泵机
            JhirPumpBVo jhirPumpBVo = jhirPumpBPoList.get(0);
            BeanUtil.copyProperties(jhirPumpBVo, jhirPumpB);
            jhirPumpB.setPmpcd(BigDecimal.valueOf(pmpcdNew));
            jhirPumpB.setStrCode(jhirPumpBAddParam.getStrCode());
            jhirPumpB.setPumpCode(UUID.randomUUID().toString());
            i = jhirPumpBMapper.insert(jhirPumpB);
        }
            return i;
    }

    /**
     * 修改水泵基础信息
     *
     * @param jhirPumpBEditParam 水泵基础信息
     * @return 结果
     */
    @Override
    public int updateJhirPumpB(JhirPumpBEditParam jhirPumpBEditParam) {
        JhirPumpBPo jhirPumpB = new JhirPumpBPo();
        BeanUtil.copyProperties(jhirPumpBEditParam, jhirPumpB);
        return baseMapper.updateById(jhirPumpB);
    }

    /**
     * 批量删除水泵基础信息
     *
     * @param pumpCodes 需要删除的水泵基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirPumpBByPumpCodes(String[] pumpCodes) {
        return baseMapper.deleteJhirPumpBByPumpCodes(pumpCodes);
    }

    /**
     * 删除水泵基础信息信息
     *
     * @param pumpCode 水泵基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirPumpBByPumpCode(String pumpCode) {
        int i = baseMapper.deleteJhirPumpBByPumpCode(pumpCode);
/*        JhirPumpBPo jhirPumpB = jhirPumpBMapper.getPumpByPumpCode(pumpCode);
        //获取改闸站的信息并对他经行修改
        String strCode = jhirPumpB.getStrCode();
        JhirPustBPo jhirPustBPo = jhirPustBMapper.getPumpByCode(strCode);
        String pumpNum = jhirPustBPo.getPumpNum();
        Integer i1 = Integer.valueOf(pumpNum);
        Integer i2 = 0;
        if (i1!=0){
            i2 = i1-1;
        }
        jhirPustBPo.setPumpNum(i2.toString());
        jhirPustBMapper.updateById(jhirPustBPo);*/
        return i;

    }
}
