package com.jhdr.projectManagement.service.impl;


import com.jhdr.projectManagement.domain.MiddleBase;
import com.jhdr.projectManagement.domain.*;
import com.jhdr.projectManagement.mapper.MiddleBaseMapper;
import com.jhdr.projectManagement.service.IMiddleBaseService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 中台基础Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-22
 */
@Service

public class MiddleBaseServiceImpl implements IMiddleBaseService
{
    @Autowired
    private MiddleBaseMapper middleBaseMapper;

    /**
     * 查询中台基础
     * 
     * @param id 中台基础主键
     * @return 中台基础
     */
    @Override
    public MiddleBase selectMiddleBaseById(Integer id)
    {
        return middleBaseMapper.selectMiddleBaseById(id);
    }

    /**
     * 查询中台基础列表
     * 
     * @param middleBase 中台基础
     * @return 中台基础
     */
    @Override
    public List<MiddleBase> selectMiddleBaseList(MiddleBase middleBase)
    {
        return middleBaseMapper.selectMiddleBaseList(middleBase);
    }
    @Override
    public List<MiddleBase> getDataBaseList() {
        List<MiddleBase> list=middleBaseMapper.getDataBaseList();
        for (MiddleBase vo:list){
            Integer amount=middleBaseMapper.getTableAmount(vo.getId());
            vo.setAmount(amount);
        }
        return list;
    }


    @Override
    public List<MiddleDetail> getDataDetailList(Integer id) {
        List<MiddleDetail> list=middleBaseMapper.getDataDetailList(id);
        for (MiddleDetail vo:list){
            Integer amount=middleBaseMapper.getDataCountByName(vo.getTableid());
            vo.setAmount(amount);
        }
        return list;
    }

    @Override
    public List<String> getDataColumn(String tableid) {
        return middleBaseMapper.getDataColumn(tableid);
    }

    @Override
    public List<Map<String, String>> getTableData(String tableid,String stcd) {
        return middleBaseMapper.getTableData(tableid,stcd);
    }

    @Override
    public int addTable(AddTable po) {
        return middleBaseMapper.addTable(po.getId(),po.getTableid(),po.getTabcn());
    }

    @Override
    public int addTableData(StStbprpB po) {
        StStbprpB vo=middleBaseMapper.selectTableData(po.getStcd());
        if (ObjectUtils.isEmpty(vo)){
            return middleBaseMapper.addTableData(po);
        }
      return 0;
    }

    @Override
    public List<SysJob> getDesignView(SysJob sysJob) {
        return middleBaseMapper.getDesignView(sysJob);
    }

    @Override
    public List<DataSourceVo> getDataSource(String source,String tableName)  {
        List<DataSourceVo> list=middleBaseMapper.getDataSource(source,tableName);
        for (DataSourceVo vo: list){
            Integer dayCount=middleBaseMapper.findDayCount(vo.getSource(),vo.getTableName());
            Integer monCount=middleBaseMapper.findMonCount(vo.getSource(),vo.getTableName());
            if (!ObjectUtils.isEmpty(dayCount)){
                vo.setDayCount(dayCount);
            }else {
                vo.setDayCount(0);
            }
            if (!ObjectUtils.isEmpty(monCount)){
                vo.setMonthCount(monCount);
            }else {
                vo.setMonthCount(0);
            }
        }
        return list;
    }

    @Override
    public List<DataRegionVo> getDataRegion() {
        List<DataRegionVo> list = middleBaseMapper.getDataRegion();
        List<DataRegionVo> menusList = new ArrayList<>();
        for (DataRegionVo vo : list) {
            //设置一级菜单
            if (vo.getParentId().equals("0")){
                menusList.add(vo);
            }
        }
        //设置一级菜单的子集菜单
        for (DataRegionVo menusVo : menusList) {
            menusVo.setChildrens(getChildren(menusVo.getId(),list));
        }
        return menusList;

    }



    private List<DataRegionVo> getChildren(String id, List<DataRegionVo> data) {
        //定义集合
        List<DataRegionVo> childList = new ArrayList<>();
        //遍历子集菜单
        for (DataRegionVo menu : data) {
            //父级id与子集id比较
            if (menu.getParentId().equals(id)){
                childList.add(menu);
            }
        }
        //循环子集菜单
        for (DataRegionVo menusVo : childList) {
            menusVo.setChildrens(getChildren(menusVo.getId(),data));
        }

        //递归条件退出
        if (childList.size() ==0){
            return null;
        }
        return childList;
    }

    /**
     * 新增中台基础
     * 
     * @param middleBase 中台基础
     * @return 结果
     */
    @Override
    public int insertMiddleBase(MiddleBase middleBase)
    {
        return middleBaseMapper.insertMiddleBase(middleBase);
    }

    /**
     * 修改中台基础
     * 
     * @param middleBase 中台基础
     * @return 结果
     */
    @Override
    public int updateMiddleBase(MiddleBase middleBase)
    {
        return middleBaseMapper.updateMiddleBase(middleBase);
    }

    /**
     * 批量删除中台基础
     * 
     * @param ids 需要删除的中台基础主键
     * @return 结果
     */
    @Override
    public int deleteMiddleBaseByIds(Integer[] ids)
    {
        return middleBaseMapper.deleteMiddleBaseByIds(ids);
    }

    /**
     * 删除中台基础信息
     * 
     * @param id 中台基础主键
     * @return 结果
     */
    @Override
    public int deleteMiddleBaseById(Integer id)
    {
        return middleBaseMapper.deleteMiddleBaseById(id);
    }


}
