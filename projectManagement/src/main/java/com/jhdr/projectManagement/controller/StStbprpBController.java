package com.jhdr.projectManagement.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.projectManagement.entity.po.StStbprpBPo;
import com.jhdr.projectManagement.entity.param.StStbprpBParam;
import com.jhdr.projectManagement.entity.param.StStbprpBAddParam;
import com.jhdr.projectManagement.entity.param.StStbprpBEditParam;
import com.jhdr.projectManagement.entity.vo.StStbprpBVo;
import com.jhdr.projectManagement.service.IStStbprpBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 测站基本属性
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Api(tags = "水文站基本属性")
@RestController
@RequestMapping("/stbprp")
//@ApiIgnore
public class StStbprpBController extends BaseController
{
    @Autowired
    private IStStbprpBService stStbprpBService;

    /**
     * 查询测站基本属性列表
     */
    @ApiOperation(value = "查询测站基本属性列表",notes="projectManagement:b:list")
    @RequiresPermissions("projectManagement:b:list")
    @GetMapping("/list")
    public TableDataInfo<List<StStbprpBVo>> list(StStbprpBParam stStbprpBParam)
    {
        startPage();
        List<StStbprpBVo> list = stStbprpBService.selectStStbprpBList(stStbprpBParam);
        return getDataTable(list);
    }

    /**
     * 导出测站基本属性列表
     */
/*    @ApiOperation(value = "导出测站基本属性列表",notes="projectManagement:b:export")
    @RequiresPermissions("projectManagement:b:export")
    @Log(title = "测站基本属性", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, StStbprpBParam stStbprpB)
    {
        List<StStbprpBVo> list = stStbprpBService.selectStStbprpBList(stStbprpB);
        ExcelUtil<StStbprpBVo> util = new ExcelUtil<StStbprpBVo>(StStbprpBVo.class);
        util.exportExcel(response, list, "测站基本属性数据");
    }

    /**
     * 获取测站基本属性详细信息
     */
    @ApiOperation(value = "获取测站基本属性详细信息",notes="projectManagement:b:query")
    @RequiresPermissions("projectManagement:b:query")
    @GetMapping(value = "/{STCD}")
    public R<StStbprpBVo> getInfo(@PathVariable("STCD") String STCD)
    {

        return R.ok(stStbprpBService.selectStStbprpBBySTCD(STCD));
    }

    /**
     * 新增测站基本属性
     */
/*    @ApiOperation(value = "新增测站基本属性",notes="projectManagement:b:add")
    @RequiresPermissions("projectManagement:b:add")
    @Log(title = "测站基本属性", businessType = BusinessType.INSERT)
    @PostMapping*/
    public R add(@RequestBody StStbprpBAddParam stStbprpB)
    {
        return toAjaxR(stStbprpBService.insertStStbprpB(stStbprpB),"新增");
    }

    /**
     * 修改测站基本属性
     */
    @ApiOperation(value = "修改测站基本属性",notes="projectManagement:b:edit")
    @RequiresPermissions("projectManagement:b:edit")
    @Log(title = "测站基本属性", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody StStbprpBEditParam stStbprpB)
    {
        return toAjaxR(stStbprpBService.updateStStbprpB(stStbprpB),"修改");
    }

    /**
     * 删除测站基本属性
     */
/*    @ApiOperation(value = "删除测站基本属性",notes="projectManagement:b:remove")
    @RequiresPermissions("projectManagement:b:remove")
    @Log(title = "测站基本属性", businessType = BusinessType.DELETE)
	@DeleteMapping("/{STCDs}")*/
    public R remove(@PathVariable String[] STCDs)
    {
        return toAjaxR(stStbprpBService.deleteStStbprpBBySTCDs(STCDs),"删除");
    }
}
