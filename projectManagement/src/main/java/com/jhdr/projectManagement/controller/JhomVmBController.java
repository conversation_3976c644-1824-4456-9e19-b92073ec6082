package com.jhdr.projectManagement.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.projectManagement.entity.vo.JhomNameVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.projectManagement.entity.po.JhomVmBPo;
import com.jhdr.projectManagement.entity.param.JhomVmBParam;
import com.jhdr.projectManagement.entity.param.JhomVmBAddParam;
import com.jhdr.projectManagement.entity.param.JhomVmBEditParam;
import com.jhdr.projectManagement.entity.vo.JhomVmBVo;
import com.jhdr.projectManagement.service.IJhomVmBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 视频监控站点基础信息
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@Api(tags = "视频监控站点基础信息")
@RestController
@RequestMapping("/vmb")
public class JhomVmBController extends BaseController
{
    @Autowired
    private IJhomVmBService jhomVmBService;

    /**
     * 查询视频监控站点基础信息列表
     */
    @ApiOperation(value = "查询视频监控站点基础信息列表",notes="projectManagement:vmb:list")
    @RequiresPermissions("projectManagement:vmb:list")
    @GetMapping("/list")
    public TableDataInfo<List<JhomVmBVo>> list(JhomVmBParam jhomVmBParam)
    {
        startPage();
        List<JhomVmBVo> list = jhomVmBService.selectJhomVmBList(jhomVmBParam);
        return getDataTable(list);
    }

    @ApiOperation(value = "所有站点名称列表",notes="projectManagement:vmb:StationList")
    @RequiresPermissions("projectManagement:vmb:StationList")
    @GetMapping("/StationList")
    public R<List<JhomNameVo>> StationList()
    {
        return   R.ok(jhomVmBService.selectStationList());

    }

    /**
     * 导出视频监控站点基础信息列表
     */
    @ApiOperation(value = "导出视频监控站点基础信息列表",notes="projectManagement:vmb:export")
    @RequiresPermissions("projectManagement:vmb:export")
    @Log(title = "视频监控站点基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JhomVmBParam jhomVmB)
    {
        List<JhomVmBVo> list = jhomVmBService.selectJhomVmBList(jhomVmB);
        ExcelUtil<JhomVmBVo> util = new ExcelUtil<JhomVmBVo>(JhomVmBVo.class);
        util.exportExcel(response, list, "视频监控站点基础信息数据");
    }

    /**
     * 获取视频监控站点基础信息详细信息
     */
    @ApiOperation(value = "获取视频监控站点基础信息详细信息",notes="projectManagement:vmb:query")
    @RequiresPermissions("projectManagement:vmb:query")
    @GetMapping(value = "/{cd}")
    public R<JhomVmBVo> getInfo(@PathVariable("cd") String cd)
    {

        return R.ok(jhomVmBService.selectJhomVmBByCd(cd));
    }

    /**
     * 新增视频监控站点基础信息
     */
//    @ApiOperation(value = "新增视频监控站点基础信息",notes="projectManagement:vmb:add")
//    @RequiresPermissions("projectManagement:vmb:add")
//    @Log(title = "视频监控站点基础信息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public R add(@RequestBody JhomVmBAddParam jhomVmB)
//    {
//        return toAjaxR(jhomVmBService.insertJhomVmB(jhomVmB),"新增");
//    }

    /**
     * 修改视频监控站点基础信息
     */
    @ApiOperation(value = "修改视频监控站点基础信息",notes="projectManagement:vmb:edit")
    @RequiresPermissions("projectManagement:vmb:edit")
    @Log(title = "视频监控站点基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody JhomVmBEditParam jhomVmB)
    {
        return toAjaxR(jhomVmBService.updateJhomVmB(jhomVmB),"修改");
    }

    /**
     * 删除视频监控站点基础信息
     */
//    @ApiOperation(value = "删除视频监控站点基础信息",notes="projectManagement:vmb:remove")
//    @RequiresPermissions("projectManagement:vmb:remove")
//    @Log(title = "视频监控站点基础信息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{cds}")
//    public R remove(@PathVariable String[] cds)
//    {
//        return toAjaxR(jhomVmBService.deleteJhomVmBByCds(cds),"删除");
//    }
}
