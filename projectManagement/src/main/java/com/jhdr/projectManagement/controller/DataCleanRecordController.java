package com.jhdr.projectManagement.controller;


import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.projectManagement.domain.DataCleanRecord;
import com.jhdr.projectManagement.service.IDataCleanRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.jhdr.common.core.web.controller.BaseController;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * 数据清洗记录Controller
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/middle/cleanRecord")
@Api("数据清洗记录")
public class DataCleanRecordController extends BaseController
{
    @Autowired
    private IDataCleanRecordService dataCleanRecordService;

    /**
     * 查询数据清洗记录列表
     */

    @GetMapping("/list")
    @ApiOperation("数据清洗记录-分页查询")
    public TableDataInfo list(DataCleanRecord dataCleanRecord)
    {
        startPage();
        List<DataCleanRecord> list = dataCleanRecordService.selectDataCleanRecordList(dataCleanRecord);
        return getDataTable(list);
    }

    /**
     * 导出数据清洗记录列表
     */

    @Log(title = "数据清洗记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("数据清洗记录-导出")
    public void export(HttpServletResponse response, DataCleanRecord dataCleanRecord)
    {
        List<DataCleanRecord> list = dataCleanRecordService.selectDataCleanRecordList(dataCleanRecord);
        ExcelUtil<DataCleanRecord> util = new ExcelUtil<DataCleanRecord>(DataCleanRecord.class);
        util.exportExcel(response, list, "数据清洗记录数据");
    }

    /**
     * 获取数据清洗记录详细信息
     */

    @GetMapping(value = "/{id}")
    @ApiOperation("数据清洗记录-查询详情")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(dataCleanRecordService.selectDataCleanRecordById(id));
    }

    /**
     * 新增数据清洗记录
     */

    @Log(title = "数据清洗记录", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("数据清洗记录-新增")
    public AjaxResult add(@RequestBody DataCleanRecord dataCleanRecord)
    {
        return toAjax(dataCleanRecordService.insertDataCleanRecord(dataCleanRecord));
    }

    /**
     * 修改数据清洗记录
     */

    @Log(title = "数据清洗记录", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("数据清洗记录-修改")
    public AjaxResult edit(@RequestBody DataCleanRecord dataCleanRecord)
    {
        return toAjax(dataCleanRecordService.updateDataCleanRecord(dataCleanRecord));
    }

    /**
     * 删除数据清洗记录
     */

    @Log(title = "数据清洗记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("数据清洗记录-删除")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(dataCleanRecordService.deleteDataCleanRecordByIds(ids));
    }
}
