package com.jhdr.projectManagement.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.projectManagement.entity.po.JhirPumpBPo;
import com.jhdr.projectManagement.entity.param.JhirPumpBParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBAddParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirPumpBVo;
import com.jhdr.projectManagement.service.IJhirPumpBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.core.domain.R;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 水泵基础信息
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Api(tags = "水泵基础信息(泵站子集)")
@RestController
@RequestMapping("/pump")
//@ApiIgnore
public class JhirPumpBController extends BaseController
{
    @Autowired
    private IJhirPumpBService jhirPumpBService;

    /**
     * 查询水泵基础信息列表
     */
    @ApiOperation(value = "查询水泵基础信息列表",notes="projectManagement:pump:list")
    @RequiresPermissions("projectManagement:pump:list")
    @GetMapping("/list")
    public R<List<JhirPumpBVo>> list(JhirPumpBParam jhirPumpBParam)
    {
        List<JhirPumpBVo> list = jhirPumpBService.selectJhirPumpBList(jhirPumpBParam);
        return R.ok(list);
    }

    /**
     * 导出水泵基础信息列表
     */
/*    @ApiOperation(value = "导出水泵基础信息列表",notes="projectManagement:pump:export")
    @RequiresPermissions("projectManagement:pump:export")
    @Log(title = "水泵基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, JhirPumpBParam jhirPumpB)
    {
        List<JhirPumpBVo> list = jhirPumpBService.selectJhirPumpBList(jhirPumpB);
        ExcelUtil<JhirPumpBVo> util = new ExcelUtil<JhirPumpBVo>(JhirPumpBVo.class);
        util.exportExcel(response, list, "水泵基础信息数据");
    }

    /**
     * 获取水泵基础信息详细信息
     */
    @ApiOperation(value = "获取水泵基础信息详细信息",notes="projectManagement:pump:query")
    @RequiresPermissions("projectManagement:pump:query")
    @GetMapping(value = "/{pumpCode}")
    public R<JhirPumpBVo> getInfo(@PathVariable("pumpCode") String pumpCode)
    {

        return R.ok(jhirPumpBService.selectJhirPumpBByPumpCode(pumpCode));
    }

    /**
     * 新增水泵基础信息
     */
    @ApiOperation(value = "新增水泵基础信息",notes="projectManagement:pump:add")
    @RequiresPermissions("projectManagement:pump:add")
    @Log(title = "水泵基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody JhirPumpBAddParam jhirPumpB)
    {
        int i = jhirPumpBService.insertJhirPumpB(jhirPumpB);
        if (i==0){
            return R.fail("机组编号重复，请重新添加");
        }
        return toAjaxR(i,"新增");
    }

    /**
     * 修改水泵基础信息
     */
    @ApiOperation(value = "修改水泵基础信息",notes="projectManagement:pump:edit")
    @RequiresPermissions("projectManagement:pump:edit")
    @Log(title = "水泵基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody JhirPumpBEditParam jhirPumpB)
    {
        return toAjaxR(jhirPumpBService.updateJhirPumpB(jhirPumpB),"修改");
    }

    /**
     * 删除水泵基础信息
     */
/*    @ApiOperation(value = "删除水泵基础信息",notes="projectManagement:pump:remove")
    @RequiresPermissions("projectManagement:pump:remove")
    @Log(title = "水泵基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{pumpCodes}")*/
    public R remove(@PathVariable String[] pumpCodes)
    {
        return toAjaxR(jhirPumpBService.deleteJhirPumpBByPumpCodes(pumpCodes),"删除");
    }
}
