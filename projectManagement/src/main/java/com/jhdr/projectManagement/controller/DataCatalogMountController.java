package com.jhdr.projectManagement.controller;


import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.projectManagement.domain.DataCatalogMount;
import com.jhdr.projectManagement.service.IDataCatalogMountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据资源目录挂载Controller
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/middle/catalogMount")
@Api("数据资源目录挂载")
public class DataCatalogMountController extends BaseController
{
    @Autowired
    private IDataCatalogMountService dataCatalogMountService;

    /**
     * 查询数据资源目录挂载列表
     */

    @GetMapping("/list")
    @ApiOperation("数据资源目录挂载-分页查询")
    public TableDataInfo list(DataCatalogMount dataCatalogMount)
    {
        startPage();
        List<DataCatalogMount> list = dataCatalogMountService.selectDataCatalogMountList(dataCatalogMount);
        return getDataTable(list);
    }

    /**
     * 导出数据资源目录挂载列表
     */

    @Log(title = "数据资源目录挂载", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("数据资源目录挂载-导出")
    public void export(HttpServletResponse response, DataCatalogMount dataCatalogMount)
    {
        List<DataCatalogMount> list = dataCatalogMountService.selectDataCatalogMountList(dataCatalogMount);
        ExcelUtil<DataCatalogMount> util = new ExcelUtil<DataCatalogMount>(DataCatalogMount.class);
        util.exportExcel(response, list, "数据资源目录挂载数据");
    }

    /**
     * 获取数据资源目录挂载详细信息
     */

    @GetMapping(value = "/{id}")
    @ApiOperation("数据资源目录挂载-查询详情")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(dataCatalogMountService.selectDataCatalogMountById(id));
    }

    /**
     * 新增数据资源目录挂载
     */

    @Log(title = "数据资源目录挂载", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("数据资源目录挂载-新增")
    public AjaxResult add(@RequestBody DataCatalogMount dataCatalogMount)
    {
        return toAjax(dataCatalogMountService.insertDataCatalogMount(dataCatalogMount));
    }

    /**
     * 修改数据资源目录挂载
     */
    @Log(title = "数据资源目录挂载", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("数据资源目录挂载-修改")
    public AjaxResult edit(@RequestBody DataCatalogMount dataCatalogMount)
    {
        return toAjax(dataCatalogMountService.updateDataCatalogMount(dataCatalogMount));
    }

    /**
     * 删除数据资源目录挂载
     */

    @Log(title = "数据资源目录挂载", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("数据资源目录挂载-删除")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(dataCatalogMountService.deleteDataCatalogMountByIds(ids));
    }
}
