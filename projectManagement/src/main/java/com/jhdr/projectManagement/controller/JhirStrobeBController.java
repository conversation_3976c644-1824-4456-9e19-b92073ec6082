package com.jhdr.projectManagement.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.projectManagement.entity.po.JhirStrobeBPo;
import com.jhdr.projectManagement.entity.param.JhirStrobeBParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBAddParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirStrobeBVo;
import com.jhdr.projectManagement.service.IJhirStrobeBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 闸门基础信息
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "闸门基础信息（闸站子集）")
@RestController
@RequestMapping("/strobe")
//@ApiIgnore
public class JhirStrobeBController extends BaseController
{
    @Autowired
    private IJhirStrobeBService jhirStrobeBService;

    /**
     * 查询闸门基础信息列表
     */
    @ApiOperation(value = "查询闸门基础信息列表",notes="projectManagement:strobe:list")
    @RequiresPermissions("projectManagement:strobe:list")
    @GetMapping("/list")
    public R<List<JhirStrobeBVo>> list(JhirStrobeBParam jhirStrobeBParam)
    {
        List<JhirStrobeBVo> list = jhirStrobeBService.selectJhirStrobeBList(jhirStrobeBParam);
        return R.ok(list);
    }

    /**
     * 导出闸门基础信息列表
     */
   /* @ApiOperation(value = "导出闸门基础信息列表",notes="projectManagement:strobe:export")
    @RequiresPermissions("projectManagement:strobe:export")
    @Log(title = "闸门基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, JhirStrobeBParam jhirStrobeB)
    {
        List<JhirStrobeBVo> list = jhirStrobeBService.selectJhirStrobeBList(jhirStrobeB);
        ExcelUtil<JhirStrobeBVo> util = new ExcelUtil<JhirStrobeBVo>(JhirStrobeBVo.class);
        util.exportExcel(response, list, "闸门基础信息数据");
    }

    /**
     * 获取闸门基础信息详细信息
     */
    @ApiOperation(value = "获取闸门基础信息详细信息",notes="projectManagement:strobe:query")
    @RequiresPermissions("projectManagement:strobe:query")
    @GetMapping(value = "/{strobeCode}")
    public R<JhirStrobeBVo> getInfo(@PathVariable("strobeCode") String strobeCode)
    {

        return R.ok(jhirStrobeBService.selectJhirStrobeBByStrobeCode(strobeCode));
    }

    /**
     * 新增闸门基础信息
     */
    @ApiOperation(value = "新增闸门基础信息",notes="projectManagement:strobe:add")
    @RequiresPermissions("projectManagement:strobe:add")
    @Log(title = "闸门基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody JhirStrobeBAddParam jhirStrobeB)
    {
        int i = jhirStrobeBService.insertJhirStrobeB(jhirStrobeB);
        if (i==0){
            return R.fail("闸门编号相同，重新添加");
        }
        return toAjaxR(i,"新增");
    }

    /**
     * 修改闸门基础信息
     */
    @ApiOperation(value = "修改闸门基础信息",notes="projectManagement:strobe:edit")
    @RequiresPermissions("projectManagement:strobe:edit")
    @Log(title = "闸门基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody JhirStrobeBEditParam jhirStrobeB)
    {
        return toAjaxR(jhirStrobeBService.updateJhirStrobeB(jhirStrobeB),"修改");
    }

    /**
     * 删除闸门基础信息
     */
/*    @ApiOperation(value = "删除闸门基础信息",notes="projectManagement:strobe:remove")
    @RequiresPermissions("projectManagement:strobe:remove")
    @Log(title = "闸门基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{strobeCodes}")*/
    public R remove(@PathVariable String[] strobeCodes)
    {
        return toAjaxR(jhirStrobeBService.deleteJhirStrobeBByStrobeCodes(strobeCodes),"删除");
    }
}
