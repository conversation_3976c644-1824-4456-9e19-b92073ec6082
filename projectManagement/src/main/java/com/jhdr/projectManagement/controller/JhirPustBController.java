package com.jhdr.projectManagement.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.projectManagement.entity.param.JhirPustBAddParam;
import com.jhdr.projectManagement.entity.param.JhirPustBEditParam;
import com.jhdr.projectManagement.entity.param.JhirPustBParam;
import com.jhdr.projectManagement.entity.vo.JhirPustBVo;
import com.jhdr.projectManagement.service.IJhirPustBService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 泵站基本信息
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "泵站基本信息")
@RestController
@RequestMapping("/pust")
//@ApiIgnore
public class JhirPustBController extends BaseController
{
    @Autowired
    private IJhirPustBService jhirPustBService;

    /**
     * 查询泵站基本信息列表
     */
    @ApiOperation(value = "查询泵站基本信息列表",notes="drought:b:list")
    @RequiresPermissions("drought:b:list")
    @GetMapping("/list")
    public TableDataInfo<List<JhirPustBVo>> list(JhirPustBParam jhirPustBParam)
    {
        startPage();
        List<JhirPustBVo> list = jhirPustBService.selectJhirPustBList(jhirPustBParam);
        return getDataTable(list);
    }

    /**
     * 导出泵站基本信息列表
     */
/*    @ApiOperation(value = "导出泵站基本信息列表",notes="drought:b:export")
    @RequiresPermissions("drought:b:export")
    @Log(title = "泵站基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, JhirPustBParam jhirPustB)
    {
        List<JhirPustBVo> list = jhirPustBService.selectJhirPustBList(jhirPustB);
        ExcelUtil<JhirPustBVo> util = new ExcelUtil<JhirPustBVo>(JhirPustBVo.class);
        util.exportExcel(response, list, "泵站基本信息数据");
    }

    /**
     * 获取泵站基本信息详细信息
     */
    @ApiOperation(value = "获取泵站基本信息详细信息",notes="drought:b:query")
    @RequiresPermissions("drought:b:query")
    @GetMapping(value = "/{strCode}")
    public R<JhirPustBVo> getInfo(@PathVariable("strCode") String strCode)
    {

        return R.ok(jhirPustBService.selectJhirPustBByStrCode(strCode));
    }

    /**
     * 新增泵站基本信息
     */
    @ApiOperation(value = "新增泵站基本信息",notes="drought:b:add")
    @RequiresPermissions("drought:b:add")
    @Log(title = "泵站基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody JhirPustBAddParam jhirPustB)
    {
        return toAjaxR(jhirPustBService.insertJhirPustB(jhirPustB),"新增");
    }

    /**
     * 修改泵站基本信息
     */
    @ApiOperation(value = "修改泵站基本信息",notes="drought:b:edit")
    @RequiresPermissions("drought:b:edit")
    @Log(title = "泵站基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody JhirPustBEditParam jhirPustB)
    {
        return toAjaxR(jhirPustBService.updateJhirPustB(jhirPustB),"修改");
    }

    /**
     * 删除泵站基本信息
     */
    @ApiOperation(value = "删除泵站基本信息",notes="drought:b:remove")
    @RequiresPermissions("drought:b:remove")
    @Log(title = "泵站基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{strCode}")
    public R remove(@PathVariable String strCode)
    {
        return toAjaxR(jhirPustBService.deleteJhirPustBByStrCode(strCode),"删除");
    }
}
