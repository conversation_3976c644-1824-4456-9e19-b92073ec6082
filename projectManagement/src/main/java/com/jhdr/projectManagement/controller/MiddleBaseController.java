package com.jhdr.projectManagement.controller;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.projectManagement.domain.*;
import com.jhdr.projectManagement.service.IMiddleBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 中台基础Controller
 * 
 * <AUTHOR>
 * @date 2024-10-22
 */
@RestController
@Api("中台基础")
@RequestMapping("/middle/middleBase")

public class MiddleBaseController extends BaseController
{
    @Autowired
    private IMiddleBaseService middleBaseService;

    /**
     * 查询中台基础列表
     */

    @GetMapping("/list")
    public TableDataInfo list(MiddleBase middleBase)
    {
        startPage();
        List<MiddleBase> list = middleBaseService.selectMiddleBaseList(middleBase);
        return getDataTable(list);
    }

    /**
     * 数据入库
     */
    @ApiOperation("数据入库")
    @GetMapping("/getDataBaseList")
    public TableDataInfo getDataBaseList()
    {

        List<MiddleBase> list = middleBaseService.getDataBaseList();
        return getDataTable(list);
    }



    /**
     * 数据入库/基础数据表
     */
    @ApiOperation("基础数据库")
    @GetMapping("/getTableList")
    public TableDataInfo getTableList(Integer id)
    {

        List<MiddleDetail> list = middleBaseService.getDataDetailList(id);
        return getDataTable(list);
    }




    /**
     * 数据入库/获取基础数据表所有字段
     */
    @ApiOperation("获取基础数据表所有字段")
    @GetMapping("/getDataColumn")
    public TableDataInfo getDataColumn(String tableid)
    {

        List<String> list = middleBaseService.getDataColumn(tableid);
        return getDataTable(list);
    }

    /**
     * 数据入库/基础数据表
     */
    @ApiOperation("获取基础数据表所有数据")
    @GetMapping("/getTableAllData")
    public TableDataInfo getTableAllData(String tableid,String stcd)
    {
        startPage();
        List<Map<String, String>> list = middleBaseService.getTableData(tableid,stcd);
        return getDataTable(list);
    }


    @ApiOperation("数据汇聚分页查询")
    @GetMapping("/getDataSource")
    public TableDataInfo getDataSource(String source,String tableName)
    {

        List<DataSourceVo> list = middleBaseService.getDataSource(source,tableName);
        return getDataTable(list);
    }

    @ApiOperation("数据汇聚树结构")
    @GetMapping("/getDataRegion")
    public TableDataInfo getDataRegion()
    {

        List<DataRegionVo> list = middleBaseService.getDataRegion();
        return getDataTable(list);
    }

//    /**
//     * 导出中台基础列表
//     */
//    @PreAuthorize("@ss.hasPermi('middle:middleBase:export')")
//    @Log(title = "中台基础", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, MiddleBase middleBase)
//    {
//        List<MiddleBase> list = middleBaseService.selectMiddleBaseList(middleBase);
//        ExcelUtil<MiddleBase> util = new ExcelUtil<MiddleBase>(MiddleBase.class);
//        util.exportExcel(response, list, "中台基础数据");
//    }

    /**
     * 获取中台基础详细信息
     */

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(middleBaseService.selectMiddleBaseById(id));
    }

    /**
     * 新增中台基础
     */

    @Log(title = "中台基础", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MiddleBase middleBase)
    {
        return toAjax(middleBaseService.insertMiddleBase(middleBase));
    }

    /**
     * 修改中台基础
     */

    @Log(title = "中台基础", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MiddleBase middleBase)
    {
        return toAjax(middleBaseService.updateMiddleBase(middleBase));
    }

    /**
     * 删除中台基础
     */

    @Log(title = "中台基础", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(middleBaseService.deleteMiddleBaseByIds(ids));
    }


    /**
     * 数据目录编制
     */
    @PostMapping("/addTable")
    @ApiOperation("数据目录编制")
    public AjaxResult addTable(@RequestBody AddTable po)
    {
        return toAjax(middleBaseService.addTable(po));
    }


    /**
     * 新增表数据
     */
    @PostMapping("/addTableData")
    @ApiOperation("新增表数据")
    public AjaxResult addTableData(@RequestBody StStbprpB po)
    {
        return toAjax(middleBaseService.addTableData(po));
    }

    @PostMapping("/InsertByExcel")
    @ApiOperation(value = "通过excel导入数据")
    public AjaxResult InsertByExcel(@RequestPart MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        List<List<Object>> read = reader.read();
        read.remove(0);
        for (int i = 0; i < read.size(); i++) {
            List<Object> ob=read.get(i);
            String stcd= String.valueOf(ob.get(0)) ;
            String stnm= String.valueOf(ob.get(1));
            String rvnm= String.valueOf( ob.get(2));
            String hnnm= String.valueOf( ob.get(3));
            String bsnm= String.valueOf( ob.get(4));
            Double lgtd= Double.valueOf( String.valueOf(ob.get(5)));
            Double lttd= Double.valueOf( String.valueOf(ob.get(6)));
            String stlc= String.valueOf( ob.get(7));
            String addvcd= String.valueOf( ob.get(8));
            String sttp= String.valueOf( ob.get(9));
            String locality= String.valueOf( ob.get(10));
            Integer source= Integer.valueOf(String.valueOf( ob.get(11)));
            StStbprpB po=new StStbprpB(stcd,stnm,rvnm,hnnm,bsnm,lgtd,lttd,stlc,addvcd,sttp,locality,source);
            middleBaseService.addTableData(po);
        }
        return success();
    }


    /**
     * 数据入库/基础数据表
     */
    @ApiOperation("设计可视化")
    @GetMapping("/getDesignView")
    public TableDataInfo getDesignView(SysJob sysJob)
    {
        startPage();
        List<SysJob> list = middleBaseService.getDesignView(sysJob);
        return getDataTable(list);
    }

}
