package com.jhdr.projectManagement.controller;


import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.projectManagement.domain.MiddleDataAdd;
import com.jhdr.projectManagement.service.IMiddleDataAddService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据填报Controller
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/middle/dataAdd")
@Api("数据填报")
public class MiddleDataAddController extends BaseController
{
    @Autowired
    private IMiddleDataAddService middleDataAddService;

    /**
     * 查询数据填报列表
     */

    @GetMapping("/list")
    @ApiOperation("数据填报-分页查询")
    public TableDataInfo list(MiddleDataAdd middleDataAdd)
    {
        startPage();
        List<MiddleDataAdd> list = middleDataAddService.selectMiddleDataAddList(middleDataAdd);
        return getDataTable(list);
    }

    /**
     * 导出数据填报列表
     */

    @Log(title = "数据填报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("数据填报-导出")
    public void export(HttpServletResponse response, MiddleDataAdd middleDataAdd)
    {
        List<MiddleDataAdd> list = middleDataAddService.selectMiddleDataAddList(middleDataAdd);
        ExcelUtil<MiddleDataAdd> util = new ExcelUtil<MiddleDataAdd>(MiddleDataAdd.class);
        util.exportExcel(response, list, "数据填报数据");
    }

    /**
     * 获取数据填报详细信息
     */

    @GetMapping(value = "/{id}")
    @ApiOperation("数据填报-查询详情")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(middleDataAddService.selectMiddleDataAddById(id));
    }

    /**
     * 新增数据填报
     */

    @Log(title = "数据填报", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("数据填报-新增")
    public AjaxResult add(@RequestBody MiddleDataAdd middleDataAdd) {
        return toAjax(middleDataAddService.insertMiddleDataAdd(middleDataAdd));
    }

    /**
     * 修改数据填报
     */

    @Log(title = "数据填报", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("数据填报-修改")
    public AjaxResult edit(@RequestBody MiddleDataAdd middleDataAdd)
    {
        return toAjax(middleDataAddService.updateMiddleDataAdd(middleDataAdd));
    }

    /**
     * 删除数据填报
     */

    @Log(title = "数据填报", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("数据填报-删除")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(middleDataAddService.deleteMiddleDataAddByIds(ids));
    }
}
