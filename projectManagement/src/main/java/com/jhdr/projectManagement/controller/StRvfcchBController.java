package com.jhdr.projectManagement.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.projectManagement.entity.po.StRvfcchBPo;
import com.jhdr.projectManagement.entity.param.StRvfcchBParam;
import com.jhdr.projectManagement.entity.param.StRvfcchBAddParam;
import com.jhdr.projectManagement.entity.param.StRvfcchBEditParam;
import com.jhdr.projectManagement.entity.vo.StRvfcchBVo;
import com.jhdr.projectManagement.service.IStRvfcchBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 河道防洪指标
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Api(tags = "河道防洪指标")
@RestController
@RequestMapping("/b")
//@ApiIgnore
public class StRvfcchBController extends BaseController
{
    @Autowired
    private IStRvfcchBService stRvfcchBService;

    /**
     * 查询河道防洪指标列表
     */
    @ApiOperation(value = "查询河道防洪指标列表",notes="projectManagement:b:list")
    @RequiresPermissions("projectManagement:b:list")
    @GetMapping("/list")
    public TableDataInfo<List<StRvfcchBVo>> list(StRvfcchBParam stRvfcchBParam)
    {
        startPage();
        List<StRvfcchBVo> list = stRvfcchBService.selectStRvfcchBList(stRvfcchBParam);
        return getDataTable(list);
    }

    /**
     * 导出河道防洪指标列表
     */
/*    @ApiOperation(value = "导出河道防洪指标列表",notes="projectManagement:b:export")
    @RequiresPermissions("projectManagement:b:export")
    @Log(title = "河道防洪指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, StRvfcchBParam stRvfcchB)
    {
        List<StRvfcchBVo> list = stRvfcchBService.selectStRvfcchBList(stRvfcchB);
        ExcelUtil<StRvfcchBVo> util = new ExcelUtil<StRvfcchBVo>(StRvfcchBVo.class);
        util.exportExcel(response, list, "河道防洪指标数据");
    }

    /**
     * 获取河道防洪指标详细信息
     */
    @ApiOperation(value = "获取河道防洪指标详细信息",notes="projectManagement:b:query")
    @RequiresPermissions("projectManagement:b:query")
    @GetMapping(value = "/{STCD}")
    public R<StRvfcchBVo> getInfo(@PathVariable("STCD") String STCD)
    {

        return R.ok(stRvfcchBService.selectStRvfcchBBySTCD(STCD));
    }

    /**
     * 新增河道防洪指标
     */
/*    @ApiOperation(value = "新增河道防洪指标",notes="projectManagement:b:add")
    @RequiresPermissions("projectManagement:b:add")
    @Log(title = "河道防洪指标", businessType = BusinessType.INSERT)
    @PostMapping*/
    public R add(@RequestBody StRvfcchBAddParam stRvfcchB)
    {
        return toAjaxR(stRvfcchBService.insertStRvfcchB(stRvfcchB),"新增");
    }

    /**
     * 修改河道防洪指标
     */
    @ApiOperation(value = "修改河道防洪指标",notes="projectManagement:b:edit")
    @RequiresPermissions("projectManagement:b:edit")
    @Log(title = "河道防洪指标", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody StRvfcchBEditParam stRvfcchB)
    {
        return toAjaxR(stRvfcchBService.updateStRvfcchB(stRvfcchB),"修改");
    }

    /**
     * 删除河道防洪指标
     */
/*    @ApiOperation(value = "删除河道防洪指标",notes="projectManagement:b:remove")
    @RequiresPermissions("projectManagement:b:remove")
    @Log(title = "河道防洪指标", businessType = BusinessType.DELETE)
	@DeleteMapping("/{STCDs}")*/
    public R remove(@PathVariable String[] STCDs)
    {
        return toAjaxR(stRvfcchBService.deleteStRvfcchBBySTCDs(STCDs),"删除");
    }
}
