package com.jhdr.projectManagement.controller;


import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.projectManagement.domain.DataCleanPlan;
import com.jhdr.projectManagement.service.IDataCleanPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据清洗方案编制Controller
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/middle/cleanPlan")
@Api("数据清洗方案编制")
public class DataCleanPlanController extends BaseController
{
    @Autowired
    private IDataCleanPlanService dataCleanPlanService;

    /**
     * 查询数据清洗方案编制列表
     */

    @GetMapping("/list")
    @ApiOperation("数据清洗方案编制-分页查询")
    public TableDataInfo list(DataCleanPlan dataCleanPlan)
    {
        startPage();
        List<DataCleanPlan> list = dataCleanPlanService.selectDataCleanPlanList(dataCleanPlan);
        return getDataTable(list);
    }

    /**
     * 导出数据清洗方案编制列表
     */

    @Log(title = "数据清洗方案编制", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("数据清洗方案编制-导出")
    public void export(HttpServletResponse response, DataCleanPlan dataCleanPlan)
    {
        List<DataCleanPlan> list = dataCleanPlanService.selectDataCleanPlanList(dataCleanPlan);
        ExcelUtil<DataCleanPlan> util = new ExcelUtil<DataCleanPlan>(DataCleanPlan.class);
        util.exportExcel(response, list, "数据清洗方案编制数据");
    }

    /**
     * 获取数据清洗方案编制详细信息
     */

    @GetMapping(value = "/{id}")
    @ApiOperation("数据清洗方案编制-查询详情")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(dataCleanPlanService.selectDataCleanPlanById(id));
    }

    /**
     * 新增数据清洗方案编制
     */

    @Log(title = "数据清洗方案编制", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("数据清洗方案编制-新增")
    public AjaxResult add(@RequestBody DataCleanPlan dataCleanPlan)
    {
        return toAjax(dataCleanPlanService.insertDataCleanPlan(dataCleanPlan));
    }

    /**
     * 修改数据清洗方案编制
     */

    @Log(title = "数据清洗方案编制", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("数据清洗方案编制-修改")
    public AjaxResult edit(@RequestBody DataCleanPlan dataCleanPlan)
    {
        return toAjax(dataCleanPlanService.updateDataCleanPlan(dataCleanPlan));
    }

    /**
     * 删除数据清洗方案编制
     */

    @Log(title = "数据清洗方案编制", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("数据清洗方案编制-删除")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(dataCleanPlanService.deleteDataCleanPlanByIds(ids));
    }
}
