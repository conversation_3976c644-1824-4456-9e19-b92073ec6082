package com.jhdr.projectManagement.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.projectManagement.entity.po.JhRiverBPo;
import com.jhdr.projectManagement.entity.param.JhRiverBParam;
import com.jhdr.projectManagement.entity.param.JhRiverBAddParam;
import com.jhdr.projectManagement.entity.param.JhRiverBEditParam;
import com.jhdr.projectManagement.entity.vo.JhRiverBVo;
import com.jhdr.projectManagement.service.IJhRiverBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 河流信息
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "河流信息")
@RestController
@RequestMapping("/river")
//@ApiIgnore
public class JhRiverBController extends BaseController
{
    @Autowired
    private IJhRiverBService jhRiverBService;

    /**
     * 查询河流信息列表
     */
    @ApiOperation(value = "查询河流信息列表",notes="projectManagement:river:list")
    @RequiresPermissions("projectManagement:river:list")
    @GetMapping("/list")
    public TableDataInfo<List<JhRiverBVo>> list(JhRiverBParam jhRiverBParam)
    {
        startPage();
        List<JhRiverBVo> list = jhRiverBService.selectJhRiverBList(jhRiverBParam);
        return getDataTable(list);
    }

    /**
     * 导出河流信息列表
     */
 /*   @ApiOperation(value = "导出河流信息列表",notes="projectManagement:river:export")
    @RequiresPermissions("projectManagement:river:export")
    @Log(title = "河流信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")*/
    public void export(HttpServletResponse response, JhRiverBParam jhRiverB)
    {
        List<JhRiverBVo> list = jhRiverBService.selectJhRiverBList(jhRiverB);
        ExcelUtil<JhRiverBVo> util = new ExcelUtil<JhRiverBVo>(JhRiverBVo.class);
        util.exportExcel(response, list, "河流信息数据");
    }

    /**
     * 获取河流信息详细信息
     */
    @ApiOperation(value = "获取河流信息详细信息",notes="projectManagement:river:query")
    @RequiresPermissions("projectManagement:river:query")
    @GetMapping(value = "/{ennmcd}")
    public R<JhRiverBVo> getInfo(@PathVariable("ennmcd") String ennmcd)
    {

        return R.ok(jhRiverBService.selectJhRiverBByEnnmcd(ennmcd));
    }

    /**
     * 新增河流信息
     */
/*    @ApiOperation(value = "新增河流信息",notes="projectManagement:river:add")
    @RequiresPermissions("projectManagement:river:add")
    @Log(title = "河流信息", businessType = BusinessType.INSERT)
    @PostMapping*/
    public R add(@RequestBody JhRiverBAddParam jhRiverB)
    {
        return toAjaxR(jhRiverBService.insertJhRiverB(jhRiverB),"新增");
    }

    /**
     * 修改河流信息
     */
    @ApiOperation(value = "修改河流信息",notes="projectManagement:river:edit")
    @RequiresPermissions("projectManagement:river:edit")
    @Log(title = "河流信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody JhRiverBEditParam jhRiverB)
    {
        return toAjaxR(jhRiverBService.updateJhRiverB(jhRiverB),"修改");
    }

    /**
     * 删除河流信息
     */
    @ApiOperation(value = "删除河流信息",notes="projectManagement:river:remove")
    @RequiresPermissions("projectManagement:river:remove")
    @Log(title = "河流信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ennmcd}")
    public R remove(@PathVariable String ennmcd)
    {
        return toAjaxR(jhRiverBService.deleteJhRiverBByEnnmcd(ennmcd),"删除");
    }
}
