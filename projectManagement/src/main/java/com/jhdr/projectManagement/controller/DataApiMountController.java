package com.jhdr.projectManagement.controller;


import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.domain.AjaxResult;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.projectManagement.domain.DataApiMount;
import com.jhdr.projectManagement.service.IDataApiMountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import com.jhdr.common.log.annotation.Log;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据资源api挂载Controller
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@RestController
@RequestMapping("/middle/apiMount")
@Api("数据资源api挂载")
public class DataApiMountController extends BaseController
{
    @Autowired
    private IDataApiMountService dataApiMountService;

    /**
     * 查询数据资源api挂载列表
     */
    @GetMapping("/list")
    @ApiOperation("数据资源api挂载-分页查询")
    public TableDataInfo list(DataApiMount dataApiMount)
    {
        startPage();
        List<DataApiMount> list = dataApiMountService.selectDataApiMountList(dataApiMount);
        return getDataTable(list);
    }

    /**
     * 导出数据资源api挂载列表
     */

    @Log(title = "数据资源api挂载", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("数据资源api挂载-导出")
    public void export(HttpServletResponse response, DataApiMount dataApiMount)
    {
        List<DataApiMount> list = dataApiMountService.selectDataApiMountList(dataApiMount);
        ExcelUtil<DataApiMount> util = new ExcelUtil<DataApiMount>(DataApiMount.class);
        util.exportExcel(response, list, "数据资源api挂载数据");
    }

    /**
     * 获取数据资源api挂载详细信息
     */

    @GetMapping(value = "/{id}")
    @ApiOperation("数据资源api挂载-查询详情")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(dataApiMountService.selectDataApiMountById(id));
    }

    /**
     * 新增数据资源api挂载
     */

    @Log(title = "数据资源api挂载", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("数据资源api挂载-新增")
    public AjaxResult add(@RequestBody DataApiMount dataApiMount)
    {
        return toAjax(dataApiMountService.insertDataApiMount(dataApiMount));
    }

    /**
     * 修改数据资源api挂载
     */

    @Log(title = "数据资源api挂载", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("数据资源api挂载-修改")
    public AjaxResult edit(@RequestBody DataApiMount dataApiMount)
    {
        return toAjax(dataApiMountService.updateDataApiMount(dataApiMount));
    }

    /**
     * 删除数据资源api挂载
     */

    @Log(title = "数据资源api挂载", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("数据资源api挂载-删除")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(dataApiMountService.deleteDataApiMountByIds(ids));
    }
}
