package com.jhdr.projectManagement.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水泵基础信息对象 jhir_pump_b
 *
 * <AUTHOR>
 * @date 2024-07-08
 */

@Data
@ApiModel(description = "水泵基础信息")
@Accessors(chain = true)
public class JhirPumpBAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 水泵编码 */
/*    @ApiModelProperty(value = "水泵编码")
    @TableId
    private String pumpCode;*/

    /** 所属泵站编码 */
    @ApiModelProperty(value = "所属泵站编码")
    @Excel(name = "所属泵站编码")
    private String strCode;

   /* *//** 生产厂家名称 *//*
    @ApiModelProperty(value = "生产厂家名称")
    @Excel(name = "生产厂家名称")
    private String fty;

    *//** 水泵类型 *//*
    @ApiModelProperty(value = "水泵类型")
    @Excel(name = "水泵类型")
    private String pumpType;

    *//** 口径 *//*
    @ApiModelProperty(value = "口径")
    @Excel(name = "口径")
    private BigDecimal pumpD;

    *//** 流量 *//*
    @ApiModelProperty(value = "流量")
    @Excel(name = "流量")
    private BigDecimal pumpCpc;

    *//** 扬程 *//*
    @ApiModelProperty(value = "扬程")
    @Excel(name = "扬程")
    private BigDecimal pumpHead;

    *//** 水泵功率 *//*
    @ApiModelProperty(value = "水泵功率")
    @Excel(name = "水泵功率")
    private BigDecimal pumpPower;

    *//** 效率 *//*
    @ApiModelProperty(value = "效率")
    @Excel(name = "效率")
    private BigDecimal pumpEff;

    *//** 允许吸上真空高度 *//*
    @ApiModelProperty(value = "允许吸上真空高度")
    @Excel(name = "允许吸上真空高度")
    private BigDecimal alwSctVac;

    *//** 实际扬程 *//*
    @ApiModelProperty(value = "实际扬程")
    @Excel(name = "实际扬程")
    private BigDecimal factHead;

    *//** 最高扬程 *//*
    @ApiModelProperty(value = "最高扬程")
    @Excel(name = "最高扬程")
    private BigDecimal maxHead;

    *//** 最低扬程 *//*
    @ApiModelProperty(value = "最低扬程")
    @Excel(name = "最低扬程")
    private BigDecimal minHrad;

    *//** 机组编号 *//*
    @ApiModelProperty(value = "机组编号")
    @Excel(name = "机组编号")
    private BigDecimal pmpcd;

    *//** 关联泵上编码 *//*
    @ApiModelProperty(value = "关联泵上编码")
    @Excel(name = "关联泵上编码")
    private String wupcd;

    *//** 关联泵下编码 *//*
    @ApiModelProperty(value = "关联泵下编码")
    @Excel(name = "关联泵下编码")
    private String wdwcd;

    *//** 关联雨量编码 *//*
    @ApiModelProperty(value = "关联雨量编码")
    @Excel(name = "关联雨量编码")
    private String raincd;

    *//** 警戒水位 *//*
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal ww;

    *//** 保证水位 *//*
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal sw;*/

}
