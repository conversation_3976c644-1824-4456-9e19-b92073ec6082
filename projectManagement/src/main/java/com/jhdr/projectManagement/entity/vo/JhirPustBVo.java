package com.jhdr.projectManagement.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

import com.jhdr.common.core.annotation.Excel;
import com.jhdr.projectManagement.entity.po.JhirPumpBPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 泵站基本信息对象 jhir_pust_b
 *
 * <AUTHOR>
 * @date 2024-07-08
 */

@Data
@ApiModel(description = "泵站基本信息")
@Accessors(chain = true)
public class JhirPustBVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 建筑物编码 */
    @ApiModelProperty(value = "建筑物编码")
    @TableId
    private String strCode;

    /** 测站名称 */
    @ApiModelProperty(value = "测站名称")
    @Excel(name = "测站名称")
    private String strName;

    /** 泵站类型 */
    @ApiModelProperty(value = "泵站类型")
    @Excel(name = "泵站类型")
    private String pustType;

    /** 地址 */
    @ApiModelProperty(value = "地址")
    @Excel(name = "地址")
    private String address;

    /** 工程等别 */
    @ApiModelProperty(value = "工程等别")
    @Excel(name = "工程等别")
    private String enWait;

    /** 工程规模 */
    @ApiModelProperty(value = "工程规模")
    @Excel(name = "工程规模")
    private String enSize;

    /** 主要建筑物级别 */
    @ApiModelProperty(value = "主要建筑物级别")
    @Excel(name = "主要建筑物级别")
    private String impType;

    /** 建成时间 */
    @ApiModelProperty(value = "建成时间")
    @Excel(name = "建成时间")
    private String completeTime;

    /** 装机流量 */
    @ApiModelProperty(value = "装机流量")
    @Excel(name = "装机流量")
    private String insFlow;

    /** 水泵数量 */
    @ApiModelProperty(value = "水泵数量")
    @Excel(name = "水泵数量")
    private String pumpNum;

    /** 设计流量 */
    @ApiModelProperty(value = "设计流量")
    @Excel(name = "设计流量")
    private String desFlow;

    /** 设计扬程 */
    @ApiModelProperty(value = "设计扬程")
    @Excel(name = "设计扬程")
    private String desHead;

    /** 抽水水源 */
    @ApiModelProperty(value = "抽水水源")
    @Excel(name = "抽水水源")
    private String headwaters;

    /** 设计灌溉面积 */
    @ApiModelProperty(value = "设计灌溉面积")
    @Excel(name = "设计灌溉面积")
    private String deviseIrr;

    /** 设计排涝面积 */
    @ApiModelProperty(value = "设计排涝面积")
    @Excel(name = "设计排涝面积")
    private String deviseDra;

    /** 设计灌溉流量 */
    @ApiModelProperty(value = "设计灌溉流量")
    @Excel(name = "设计灌溉流量")
    private String deviseIrrFlow;

    /** 所在岸别 */
    @ApiModelProperty(value = "所在岸别")
    @Excel(name = "所在岸别")
    private String kishibetsu;

    /** 自灌流量 */
    @ApiModelProperty(value = "自灌流量")
    @Excel(name = "自灌流量")
    private String selfIrr;

    /** 抽灌流量 */
    @ApiModelProperty(value = "抽灌流量")
    @Excel(name = "抽灌流量")
    private String pumpIrr;

    /** 灌溉方式 */
    @ApiModelProperty(value = "灌溉方式")
    @Excel(name = "灌溉方式")
    private String irrType;

    /** 装机容量泵型 */
    @ApiModelProperty(value = "装机容量泵型")
    @Excel(name = "装机容量泵型")
    private String installType;

    /** 维度 */
    @ApiModelProperty(value = "维度")
    @Excel(name = "维度")
    private String latd;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private String lgtd;

    /** 装机容量 */
    @ApiModelProperty(value = "装机容量")
    @Excel(name = "装机容量")
    private String instaCapicaty;

    /** 设计进水位高程 */
    @ApiModelProperty(value = "设计进水位高程")
    @Excel(name = "设计进水位高程")
    private BigDecimal desInEl;

    /** 最高水位高程 */
    @ApiModelProperty(value = "最高水位高程")
    @Excel(name = "最高水位高程")
    private BigDecimal topInEl;

    /** 最低水位高程 */
    @ApiModelProperty(value = "最低水位高程")
    @Excel(name = "最低水位高程")
    private BigDecimal lowInEl;

    /** 正常水位高程 */
    @ApiModelProperty(value = "正常水位高程")
    @Excel(name = "正常水位高程")
    private BigDecimal normalInEl;

    /** 设计出水水位高程 */
    @ApiModelProperty(value = "设计出水水位高程")
    @Excel(name = "设计出水水位高程")
    private BigDecimal desOutEl;

    /** 最高出水为高程 */
    @ApiModelProperty(value = "最高出水为高程")
    @Excel(name = "最高出水为高程")
    private BigDecimal topOutEl;

    /** 最低出水为高程 */
    @ApiModelProperty(value = "最低出水为高程")
    @Excel(name = "最低出水为高程")
    private BigDecimal lowOutEl;

    /** 正常出水位高程 */
    @ApiModelProperty(value = "正常出水位高程")
    @Excel(name = "正常出水位高程")
    private BigDecimal normalOutEl;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String note;

    /** 图片地址 */
    @ApiModelProperty(value = "图片地址")
    @Excel(name = "图片地址")
    private String url;

    /** 自排流量 */
    @ApiModelProperty(value = "自排流量")
    @Excel(name = "自排流量")
    private String zpll;

    /** 抽排流量 */
    @ApiModelProperty(value = "抽排流量")
    @Excel(name = "抽排流量")
    private String cpll;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划")
    @Excel(name = "行政区划")
    private String addvcd;

    /** 一期导数据编号 */
    @ApiModelProperty(value = "一期导数据编号")
    @Excel(name = "一期导数据编号")
    private String pidCode;

    /** 视频站 jhom_vm_b 关联CD */
    @ApiModelProperty(value = "视频站 jhom_vm_b 关联CD")
    @Excel(name = "视频站 jhom_vm_b 关联CD")
    private String vmBCd;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal wwZ;

    /** 保证水位 */
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal swZ;

    /** 关联雨量编码 */
    @ApiModelProperty(value = "关联雨量编码")
    @Excel(name = "关联雨量编码")
    private String raincd;

    /** 关联下游水位站编码 */
    @ApiModelProperty(value = "关联下游水位站编码")
    @Excel(name = "关联下游水位站编码")
    private String wdwcd;

    /** 关联上游水位站编码 */
    @ApiModelProperty(value = "关联上游水位站编码")
    @Excel(name = "关联上游水位站编码")
    private String wupcd;

    /** 历史最高水位 */
    @ApiModelProperty(value = "历史最高水位")
    @Excel(name = "历史最高水位")
    private String hisMaxZ;

    /** 常年平均水位 */
    @ApiModelProperty(value = "常年平均水位")
    @Excel(name = "常年平均水位")
    private String allAvgZ;

    /** 站点简介 */
    @ApiModelProperty(value = "站点简介")
    @Excel(name = "站点简介")
    private String stDesc;

    /** 历史运行时间 */
    @ApiModelProperty(value = "历史运行时间")
    @Excel(name = "历史运行时间")
    private BigDecimal hisRt;

    /** 累计运行时间 */
    @ApiModelProperty(value = "累计运行时间")
    @Excel(name = "累计运行时间")
    private BigDecimal sumRt;

    /** 本次运行时间 */
    @ApiModelProperty(value = "本次运行时间")
    @Excel(name = "本次运行时间")
    private BigDecimal nowRt;

    /** 历史运行流量 */
    @ApiModelProperty(value = "历史运行流量")
    @Excel(name = "历史运行流量")
    private BigDecimal hisFw;

    /** 累计运行流量 */
    @ApiModelProperty(value = "累计运行流量")
    @Excel(name = "累计运行流量")
    private BigDecimal sumFw;

    /** 本次运行流量 */
    @ApiModelProperty(value = "本次运行流量")
    @Excel(name = "本次运行流量")
    private BigDecimal nowFw;

    /** 状态 0关闭 1开启 */
    @ApiModelProperty(value = "状态 0关闭 1开启")
    @Excel(name = "状态 0关闭 1开启")
    private Long status;

    /** 装机功率 */
    @ApiModelProperty(value = "装机功率")
    @Excel(name = "装机功率")
    private BigDecimal insPow;

    /** 关联涵闸水位站 */
    @ApiModelProperty(value = "关联涵闸水位站")
    @Excel(name = "关联涵闸水位站")
    private String wascd;

    /** 数据来源（10共享省厅，20一期，30二期,90其他） */
    @ApiModelProperty(value = "数据来源（10共享省厅，20一期，30二期,90其他）")
    @Excel(name = "数据来源", readConverterExp = "1=0共享省厅，20一期，30二期,90其他")
    private String source;

    // 水闸的子列表
    private List<JhirPumpBPo> jhirPumpBPoList;
}
