package com.jhdr.projectManagement.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 枢纽基础信息对象 hub_basic_information
 *
 * <AUTHOR>
 * @date 2024-07-08
 */

@Data
@ApiModel(description = "枢纽基础信息")
@Accessors(chain = true)
public class HubBasicInformationEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty(value = "主键id")
    @TableId
    private Long id;

    /** 枢纽编码 */
 /*   @ApiModelProperty(value = "枢纽编码")
    @Excel(name = "枢纽编码")
    private String hubCode;*/

    /** 所属行政名称 */
    @ApiModelProperty(value = "所属行政名称")
    @Excel(name = "所属行政名称")
    private String adminArea;

    /** 灌区简要介绍 */
    @ApiModelProperty(value = "灌区简要介绍")
    @Excel(name = "灌区简要介绍")
    private String profile;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private Long status;

    /** 闸站编码 */
    @ApiModelProperty(value = "闸站编码")
    @Excel(name = "闸站编码")
    private String gataCode;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    @Excel(name = "枢纽名称")
    private String name;

    /** 泵站编码 */
    @ApiModelProperty(value = "泵站编码")
    @Excel(name = "泵站编码")
    private String pustCode;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private BigDecimal lttd;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;

    /** 调度单位 */
    @ApiModelProperty(value = "调度单位")
    @Excel(name = "调度单位")
    private String dispatchUnit;

    /** 地址 */
    @ApiModelProperty(value = "地址")
    @Excel(name = "地址")
    private String address;

    /** 图片地址 */
    @ApiModelProperty(value = "图片地址")
    @Excel(name = "图片地址")
    private String url;
}
