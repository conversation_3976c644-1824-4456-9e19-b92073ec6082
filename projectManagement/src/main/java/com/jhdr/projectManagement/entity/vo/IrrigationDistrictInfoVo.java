package com.jhdr.projectManagement.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区基本信息对象 irrigation_district_info
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "灌区基本信息")
@Accessors(chain = true)
public class IrrigationDistrictInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @TableId
    private Long id;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 所属行政区名称 */
    @ApiModelProperty(value = "所属行政区名称")
    @Excel(name = "所属行政区名称")
    private String adminArea;

    /** 设计灌溉总面积（单位：万亩） */
    @ApiModelProperty(value = "设计灌溉总面积（单位：万亩）")
    @Excel(name = "设计灌溉总面积", readConverterExp = "单=位：万亩")
    private BigDecimal designedIrrigationArea;

    /** 有效灌溉面积（单位：万亩） */
    @ApiModelProperty(value = "有效灌溉面积（单位：万亩）")
    @Excel(name = "有效灌溉面积", readConverterExp = "单=位：万亩")
    private BigDecimal effectiveIrrigationArea;

    /** 耕地总面积（单位：万亩） */
    @ApiModelProperty(value = "耕地总面积（单位：万亩）")
    @Excel(name = "耕地总面积", readConverterExp = "单=位：万亩")
    private BigDecimal croplandArea;

    /** 灌区简要介绍 */
    @ApiModelProperty(value = "灌区简要介绍")
    @Excel(name = "灌区简要介绍")
    private String profile;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String name;


    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private Integer status;

    /** 干渠总长 */
    @ApiModelProperty(value = "干渠总长")
    @Excel(name = "干渠总长")
    private BigDecimal channelLength;

    /** 自流面积 */
    @ApiModelProperty(value = "自流面积")
    @Excel(name = "自流面积")
    private BigDecimal artesianArea;

    /** 提水面积 */
    @ApiModelProperty(value = "提水面积")
    @Excel(name = "提水面积")
    private BigDecimal waterLiftingArea;

    /** 提水泵站 */
    @ApiModelProperty(value = "提水泵站")
    @Excel(name = "提水泵站")
    private Integer waterPumpStation;

    /** 涵闸 */
    @ApiModelProperty(value = "涵闸")
    @Excel(name = "涵闸")
    private Integer culvertGate;

    /** 图片地址 */
    @ApiModelProperty(value = "图片地址")
    @Excel(name = "图片地址")
    private String url;
}
