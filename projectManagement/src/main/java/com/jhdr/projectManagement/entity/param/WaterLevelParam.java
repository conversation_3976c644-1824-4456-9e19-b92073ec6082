package com.jhdr.projectManagement.entity.param;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "水位信息")
@Accessors(chain = true)
public class WaterLevelParam implements Serializable
{
    private static final long serialVersionUID = 1L;



/*    *//** 测站编码 *//*
    @ApiModelProperty(value = "测站编码")
    @Excel(name = "测站编码")
    private String STCD;*/

    /** 站点名称 */
    @ApiModelProperty(value = "站点名称")
    @Excel(name = "站点名称")
    private String STNM;

}
