package com.jhdr.projectManagement.entity.po;

import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河流信息对象 jh_river_b
 *
 * <AUTHOR>
 * @date 2024-07-17
 */

@Data
@ApiModel(description = "河流信息")
@Accessors(chain = true)
@TableName(value ="jh_river_b")
public class JhRiverBPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 河流代码 */
    @ApiModelProperty(value = "河流代码")
    @TableId
    private String ennmcd;

    /** 河流名称 */
    @ApiModelProperty(value = "河流名称")
    @Excel(name = "河流名称")
    private String rvnm;

    /** 河流类别 */
    @ApiModelProperty(value = "河流类别")
    @Excel(name = "河流类别")
    private String rvtp;

    /** 所属流域 */
    @ApiModelProperty(value = "所属流域")
    @Excel(name = "所属流域")
    private String belws;

    /** 河源位置 */
    @ApiModelProperty(value = "河源位置")
    @Excel(name = "河源位置")
    private String rvpl;

    /** 流入水系 */
    @ApiModelProperty(value = "流入水系")
    @Excel(name = "流入水系")
    private String rved;

    /** 长度(km) */
    @ApiModelProperty(value = "长度(km)")
    @Excel(name = "长度(km)")
    private BigDecimal mnstln;

    /** 流域面积(km2) */
    @ApiModelProperty(value = "流域面积(km2)")
    @Excel(name = "流域面积(km2)")
    private BigDecimal ttdrbsar;

    /** 流域内人口 */
    @ApiModelProperty(value = "流域内人口")
    @Excel(name = "流域内人口")
    private BigDecimal drbspp;

    /** 农业灌溉面积 */
    @ApiModelProperty(value = "农业灌溉面积")
    @Excel(name = "农业灌溉面积")
    private BigDecimal irrigatedarea;

    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String munit;

    /** 河长制信息 */
    @ApiModelProperty(value = "河长制信息")
    @Excel(name = "河长制信息")
    private String rvchief;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String rm;

    /** 包含河流及别称 */
    @ApiModelProperty(value = "包含河流及别称")
    @Excel(name = "包含河流及别称")
    private String bc;

    /** 省内河流起点 */
    @ApiModelProperty(value = "省内河流起点")
    @Excel(name = "省内河流起点")
    private String snqd;

    /** 上一级河流名称 */
    @ApiModelProperty(value = "上一级河流名称")
    @Excel(name = "上一级河流名称")
    private String sjhlmc;

    /** 岸别 */
    @ApiModelProperty(value = "岸别")
    @Excel(name = "岸别")
    private String ab;

    /** 河流级别 */
    @ApiModelProperty(value = "河流级别")
    @Excel(name = "河流级别")
    private String hljb;

    /** 一级水系 */
    @ApiModelProperty(value = "一级水系")
    @Excel(name = "一级水系")
    private String yjsx;

    /** 二级水系 */
    @ApiModelProperty(value = "二级水系")
    @Excel(name = "二级水系")
    private String ejsx;

    /** 省内河流长度(km) */
    @ApiModelProperty(value = "省内河流长度(km)")
    @Excel(name = "省内河流长度(km)")
    private BigDecimal snhlcd;

    /** 省内河流流域面积(km2 */
    @ApiModelProperty(value = "省内河流流域面积(km2")
    @Excel(name = "省内河流流域面积(km2")
    private BigDecimal snhlmj;

    /** 是否跨省 */
    @ApiModelProperty(value = "是否跨省")
    @Excel(name = "是否跨省")
    private String sfks;

    /** 流经市数量 */
    @ApiModelProperty(value = "流经市数量")
    @Excel(name = "流经市数量")
    private BigDecimal ljssl;

    /** 流经市 */
    @ApiModelProperty(value = "流经市")
    @Excel(name = "流经市")
    private String ljs;

    /** 流经县数量 */
    @ApiModelProperty(value = "流经县数量")
    @Excel(name = "流经县数量")
    private BigDecimal ljxsl;

    /** 流经县 */
    @ApiModelProperty(value = "流经县")
    @Excel(name = "流经县")
    private String lix;

    /** 流经乡镇 */
    @ApiModelProperty(value = "流经乡镇")
    @Excel(name = "流经乡镇")
    private String ljxz;

    /** 拦河临河天然湖泊数量 */
    @ApiModelProperty(value = "拦河临河天然湖泊数量")
    @Excel(name = "拦河临河天然湖泊数量")
    private BigDecimal ltrhpsl;

    /** 拦河临河天然湖泊名称 */
    @ApiModelProperty(value = "拦河临河天然湖泊名称")
    @Excel(name = "拦河临河天然湖泊名称")
    private String ltrhpm;

    /** 拦河水库数量 */
    @ApiModelProperty(value = "拦河水库数量")
    @Excel(name = "拦河水库数量")
    private BigDecimal lsksl;

    /** 拦河水库名称 */
    @ApiModelProperty(value = "拦河水库名称")
    @Excel(name = "拦河水库名称")
    private String lskmc;

    /** 拦河坝数量 */
    @ApiModelProperty(value = "拦河坝数量")
    @Excel(name = "拦河坝数量")
    private BigDecimal lhbsl;

    /** 拦河坝名称 */
    @ApiModelProperty(value = "拦河坝名称")
    @Excel(name = "拦河坝名称")
    private String lhbmc;

    /** 临河水电站数量 */
    @ApiModelProperty(value = "临河水电站数量")
    @Excel(name = "临河水电站数量")
    private BigDecimal lsdzsl;

    /** 临河水电站名称 */
    @ApiModelProperty(value = "临河水电站名称")
    @Excel(name = "临河水电站名称")
    private String lsdzmc;

    /** 拦河水闸数量 */
    @ApiModelProperty(value = "拦河水闸数量")
    @Excel(name = "拦河水闸数量")
    private BigDecimal lszsl;

    /** 拦河水闸名称 */
    @ApiModelProperty(value = "拦河水闸名称")
    @Excel(name = "拦河水闸名称")
    private String lszmc;

    /** 是否山区河道 */
    @ApiModelProperty(value = "是否山区河道")
    @Excel(name = "是否山区河道")
    private String sfsqhd;

    /** 是否划界 */
    @ApiModelProperty(value = "是否划界")
    @Excel(name = "是否划界")
    private String sfhj;

    /** 水普河湖管理范围面积 */
    @ApiModelProperty(value = "水普河湖管理范围面积")
    @Excel(name = "水普河湖管理范围面积")
    private String sphhfwmj;

    /** 起点经度 */
    @ApiModelProperty(value = "起点经度")
    @Excel(name = "起点经度")
    private BigDecimal startLong;

    /** 起点维度 */
    @ApiModelProperty(value = "起点维度")
    @Excel(name = "起点维度")
    private BigDecimal startLat;

    /** 终点经度 */
    @ApiModelProperty(value = "终点经度")
    @Excel(name = "终点经度")
    private BigDecimal endLong;

    /** 终点维度 */
    @ApiModelProperty(value = "终点维度")
    @Excel(name = "终点维度")
    private BigDecimal endLat;

    /** 渠(沟)道所在位置 */
    @ApiModelProperty(value = "渠(沟)道所在位置")
    @Excel(name = "渠(沟)道所在位置")
    private String chanLoc;

    /** 渠(沟)道类别 */
    @ApiModelProperty(value = "渠(沟)道类别")
    @Excel(name = "渠(沟)道类别")
    private String chanType;

    /** 设计流量 */
    @ApiModelProperty(value = "设计流量")
    @Excel(name = "设计流量")
    private BigDecimal desFlow;

    /** 有效标识 */
    @ApiModelProperty(value = "有效标识")
    @Excel(name = "有效标识")
    private BigDecimal status;

    /** 行政区划编码 */
    @ApiModelProperty(value = "行政区划编码")
    @Excel(name = "行政区划编码")
    private String xzqhCode;

    /** 行政区划名称 */
    @ApiModelProperty(value = "行政区划名称")
    @Excel(name = "行政区划名称")
    private String xzqhName;

    /** 流域编码 */
    @ApiModelProperty(value = "流域编码")
    @Excel(name = "流域编码")
    private String lyCode;

}
