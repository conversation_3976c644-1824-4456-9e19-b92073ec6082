package com.jhdr.projectManagement.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 闸门基础信息对象 jhir_strobe_b
 *
 * <AUTHOR>
 * @date 2024-07-08
 */

@Data
@ApiModel(description = "闸门基础信息")
@Accessors(chain = true)
public class JhirStrobeBEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 闸门编码 */
    @ApiModelProperty(value = "闸门编码")
    @TableId
    private String strobeCode;

    /** 所属闸站编码 */
/*    @ApiModelProperty(value = "所属闸站编码")
    @Excel(name = "所属闸站编码")
    private String strCode;*/

    /** 闸门类型 */
    @ApiModelProperty(value = "闸门类型")
    @Excel(name = "闸门类型")
    private String strobeType;

    /** 闸门材料 */
    @ApiModelProperty(value = "闸门材料")
    @Excel(name = "闸门材料")
    private String strobeMtr;

    /** 闸门高度 */
    @ApiModelProperty(value = "闸门高度")
    @Excel(name = "闸门高度")
    private BigDecimal strobeH;

    /** 闸门宽度 */
    @ApiModelProperty(value = "闸门宽度")
    @Excel(name = "闸门宽度")
    private BigDecimal strobeW;

    /** 闸门重量 */
    @ApiModelProperty(value = "闸门重量")
    @Excel(name = "闸门重量")
    private BigDecimal strobeWt;

    /** 生产厂家名称 */
    @ApiModelProperty(value = "生产厂家名称")
    @Excel(name = "生产厂家名称")
    private String fty;

    /** 闸门底坎高程 */
    @ApiModelProperty(value = "闸门底坎高程")
    @Excel(name = "闸门底坎高程")
    private BigDecimal strobeBsH;

    /** 闸门编号 */
/*    @ApiModelProperty(value = "闸门编号")
    @Excel(name = "闸门编号")
    private BigDecimal gpcd;*/

}
