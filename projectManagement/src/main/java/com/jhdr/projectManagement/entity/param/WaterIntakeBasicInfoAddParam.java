package com.jhdr.projectManagement.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 取水口基本信息对象 water_intake_basic_info
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "取水口基本信息")
@Accessors(chain = true)
public class WaterIntakeBasicInfoAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

/*    *//** 取水口id *//*
    @ApiModelProperty(value = "取水口id")
    @TableId
    private Long id;*/

    /** 取水口编码 */
    @ApiModelProperty(value = "取水口编码")
    @TableId
    private String intakeCode;

    /** 取水口名称 */
    @ApiModelProperty(value = "取水口名称")
    @Excel(name = "取水口名称")
    private String intakeName;

    /** 所属行政区 */
    @ApiModelProperty(value = "所属行政区")
    @Excel(name = "所属行政区")
    private String adminDistrict;

    /** 日取水量（单位：万立方米） */
    @ApiModelProperty(value = "日取水量（单位：万立方米）")
    @Excel(name = "日取水量", readConverterExp = "单=位：万立方米")
    private BigDecimal dailyCapacity;

    /** 年取水量（单位：万立方米） */
    @ApiModelProperty(value = "年取水量（单位：万立方米）")
    @Excel(name = "年取水量", readConverterExp = "单=位：万立方米")
    private BigDecimal annualCapacity;

    /** 所在岸别 */
    @ApiModelProperty(value = "所在岸别")
    @Excel(name = "所在岸别")
    private String bankSide;

    /** 地理位置 */
    @ApiModelProperty(value = "地理位置")
    @Excel(name = "地理位置")
    private String location;

    /** 工程图片链接 */
    @ApiModelProperty(value = "工程图片链接")
    @Excel(name = "工程图片链接")
    private String imageUrl;


    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private Integer status;
}
