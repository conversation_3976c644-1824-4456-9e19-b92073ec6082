package com.jhdr.projectManagement.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 视频监控站点基础信息对象 jhom_vm_b
 *
 * <AUTHOR>
 * @date 2025-02-18
 */

@Data
@ApiModel(description = "视频监控站点基础信息")
@Accessors(chain = true)
public class JhomVmBVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 设备编号 */
    @ApiModelProperty(value = "设备编号")
    @TableId
    private String cd;

    private String name;//当前视频所属站点位置名称

    /** 设备序列号 */
    @ApiModelProperty(value = "设备序列号")
    @Excel(name = "设备序列号")
    private String sernum;

//    /** 设备ID */
//    @ApiModelProperty(value = "设备ID")
//    @Excel(name = "设备ID")
//    private String deviceid;
//
//    /** 设备型号 */
//    @ApiModelProperty(value = "设备型号")
//    @Excel(name = "设备型号")
//    private String model;
//
//    /** 设备厂商 */
//    @ApiModelProperty(value = "设备厂商")
//    @Excel(name = "设备厂商")
//    private String oem;
//
//    /** 分辨率 */
//    @ApiModelProperty(value = "分辨率")
//    @Excel(name = "分辨率")
//    private BigDecimal resolution;
//
//    /** 视角范围 */
//    @ApiModelProperty(value = "视角范围")
//    @Excel(name = "视角范围")
//    private BigDecimal viewang;
//
//    /** 所属硬盘录像机 */
//    @ApiModelProperty(value = "所属硬盘录像机")
//    @Excel(name = "所属硬盘录像机")
//    private String nvr;

    /** 通道名称（监控设备名称） */
    @ApiModelProperty(value = "通道名称（监控设备名称）")
    @Excel(name = "通道名称", readConverterExp = "监=控设备名称")
    private String nm;

    /** 通道号 */
    @ApiModelProperty(value = "通道号")
    @Excel(name = "通道号")
    private String cn;

//    /** 协议类型 */
//    @ApiModelProperty(value = "协议类型")
//    @Excel(name = "协议类型")
//    private String protocol;

    /** IP */
    @ApiModelProperty(value = "IP")
    @Excel(name = "IP")
    private String ip;

//    /** IP端口号 */
//    @ApiModelProperty(value = "IP端口号")
//    @Excel(name = "IP端口号")
//    private BigDecimal ipport;

    /** 设备通道号 */
    @ApiModelProperty(value = "设备通道号")
    @Excel(name = "设备通道号")
    private BigDecimal sbcn;

//    /** 管理端口 */
//    @ApiModelProperty(value = "管理端口")
//    @Excel(name = "管理端口")
//    private BigDecimal mport;

    /** 设备状态 */
    @ApiModelProperty(value = "设备状态")
    @Excel(name = "设备状态")
    private String status;

//    /** 所属项目 */
//    @ApiModelProperty(value = "所属项目")
//    @Excel(name = "所属项目")
//    private String project;
//
//    /** 安装单位 */
//    @ApiModelProperty(value = "安装单位")
//    @Excel(name = "安装单位")
//    private String instUnit;
//
//    /** 安装日期 */
//    @ApiModelProperty(value = "安装日期")
//    @Excel(name = "安装日期")
//    private String instdate;
//
//    /** 管理单位 */
//    @ApiModelProperty(value = "管理单位")
//    @Excel(name = "管理单位")
//    private String mangUnit;
//
//    /** 维护单位 */
//    @ApiModelProperty(value = "维护单位")
//    @Excel(name = "维护单位")
//    private String maitUnit;
//
//    /** 维保信息 */
//    @ApiModelProperty(value = "维保信息")
//    @Excel(name = "维保信息")
//    private String maitTel;
//
//    /** 行政区划 */
//    @ApiModelProperty(value = "行政区划")
//    @Excel(name = "行政区划")
//    private String addvcd;

    /** 安装地址 */
    @ApiModelProperty(value = "安装地址")
    @Excel(name = "安装地址")
    private String stlc;

    /** 安装图片 */
    @ApiModelProperty(value = "安装图片")
    @Excel(name = "安装图片")
    private String instPic;

//    /** 用户名 */
//    @ApiModelProperty(value = "用户名")
//    @Excel(name = "用户名")
//    private String username;
//
//    /** 密码 */
//    @ApiModelProperty(value = "密码")
//    @Excel(name = "密码")
//    private String password;
//
//    /** 上次维护时间 */
//    @ApiModelProperty(value = "上次维护时间")
//    @Excel(name = "上次维护时间")
//    private String lastmaittm;
//
//    /** 下次维护时间 */
//    @ApiModelProperty(value = "下次维护时间")
//    @Excel(name = "下次维护时间")
//    private String nextmaittm;
//
//    /** 状态监测时间 */
//    @ApiModelProperty(value = "状态监测时间")
//    @Excel(name = "状态监测时间")
//    private String checktm;
//
//    /** 是否录像 */
//    @ApiModelProperty(value = "是否录像")
//    @Excel(name = "是否录像")
//    private BigDecimal recyn;
//
//    /** 最大录像时长 */
//    @ApiModelProperty(value = "最大录像时长")
//    @Excel(name = "最大录像时长")
//    private BigDecimal recmaxhours;

    /** 位置经度 */
    @ApiModelProperty(value = "位置经度")
    @Excel(name = "位置经度")
    private String lgtd;

    /** 位置纬度 */
    @ApiModelProperty(value = "位置纬度")
    @Excel(name = "位置纬度")
    private String lttd;

//    /** 主控版本 */
//    @ApiModelProperty(value = "主控版本")
//    @Excel(name = "主控版本")
//    private String masterVersion;
//
//    /** 编码版本 */
//    @ApiModelProperty(value = "编码版本")
//    @Excel(name = "编码版本")
//    private String codeVersion;
//
//    /** web版本 */
//    @ApiModelProperty(value = "web版本")
//    @Excel(name = "web版本")
//    private String webVersion;
//
//    /** plugin版本 */
//    @ApiModelProperty(value = "plugin版本")
//    @Excel(name = "plugin版本")
//    private String pluginVersion;
//
//    /** 通道个数 */
//    @ApiModelProperty(value = "通道个数")
//    @Excel(name = "通道个数")
//    private String channelNum;
//
//    /** 硬盘个数 */
//    @ApiModelProperty(value = "硬盘个数")
//    @Excel(name = "硬盘个数")
//    private Long diskNum;
//
//    /** 报警输入个数 */
//    @ApiModelProperty(value = "报警输入个数")
//    @Excel(name = "报警输入个数")
//    private String alarmEnter;
//
//    /** 报警输出个数 */
//    @ApiModelProperty(value = "报警输出个数")
//    @Excel(name = "报警输出个数")
//    private String alarmOutput;
//
//    /** 通讯方式 */
//    @ApiModelProperty(value = "通讯方式")
//    @Excel(name = "通讯方式")
//    private String method;
//
//    /** 安全性 */
//    @ApiModelProperty(value = "安全性")
//    @Excel(name = "安全性")
//    private String security;
//
//    /** 启用标志 */
//    @ApiModelProperty(value = "启用标志")
//    @Excel(name = "启用标志")
//    private BigDecimal usfl;
//
//    /** 工程类型 */
//    @ApiModelProperty(value = "工程类型")
//    @Excel(name = "工程类型")
//    private String engtp;
//
//    /** 工程编号 */
//    @ApiModelProperty(value = "工程编号")
//    @Excel(name = "工程编号")
//    private String engcd;
//
//    /** 工程名称 */
//    @ApiModelProperty(value = "工程名称")
//    @Excel(name = "工程名称")
//    private String engName;
//
//    /** 相机编码 */
//    @ApiModelProperty(value = "相机编码")
//    @Excel(name = "相机编码")
//    private String cameraCode;
//
    /** 区域编码hikvision_video_region 对应index_code */
    @ApiModelProperty(value = "区域编码hikvision_video_region 对应index_code")
    @Excel(name = "区域编码hikvision_video_region 对应index_code")
    private String regionIndexCode;
//
//    /** 所属编码设备唯一标识 */
//    @ApiModelProperty(value = "所属编码设备唯一标识")
//    @Excel(name = "所属编码设备唯一标识")
//    private String encodeDevIndexCode;
//
//    /** 告警时间 */
//    @ApiModelProperty(value = "告警时间")
//    @Excel(name = "告警时间")
//    private String alarmTime;
//
//    /** 所在河流 */
//    @ApiModelProperty(value = "所在河流")
//    @Excel(name = "所在河流")
//    private String headwaters;
//
//    /** 监测更新时间 */
//    @ApiModelProperty(value = "监测更新时间")
//    private String updateTime;
//
//    /** 所在区域（和视频服务对应） */
//    @ApiModelProperty(value = "所在区域（和视频服务对应）")
//    @Excel(name = "所在区域", readConverterExp = "和=视频服务对应")
//    private String szqy;
//
//    /** 自定义分类 */
//    @ApiModelProperty(value = "自定义分类")
//    @Excel(name = "自定义分类")
//    private String typ;

    /** 来源 */
    @ApiModelProperty(value = "来源")
    @Excel(name = "来源")
    private String source;

//    /** 视频识别内容 */
//    @ApiModelProperty(value = "视频识别内容")
//    @Excel(name = "视频识别内容")
//    private String discern;
//
//    /** 视频识别报警 */
//    @ApiModelProperty(value = "视频识别报警")
//    @Excel(name = "视频识别报警")
//    private String discernState;

    /** 站点类型1一期 2二期 */
    @ApiModelProperty(value = "站点类型1一期 2二期")
    @Excel(name = "站点类型1一期 2二期")
    private String siteType;

//    /** 二期站点视频地址 */
//    @ApiModelProperty(value = "二期站点视频地址")
//    @Excel(name = "二期站点视频地址")
//    private String siteUrl;
//
//    /** 识别通道号 */
//    @ApiModelProperty(value = "识别通道号")
//    @Excel(name = "识别通道号")
//    private Long discernChannel;
//
//    /** 识别内容 */
//    @ApiModelProperty(value = "识别内容")
//    @Excel(name = "识别内容")
//    private String discernContent;
//
//    /** 大华视频状态用 */
//    @ApiModelProperty(value = "大华视频状态用")
//    @Excel(name = "大华视频状态用")
//    private String dhChannel;
//
//    /** 承建单位 */
//    @ApiModelProperty(value = "承建单位")
//    @Excel(name = "承建单位")
//    private String construction;
//
//    /** 专题展示 */
//    @ApiModelProperty(value = "专题展示")
//    @Excel(name = "专题展示")
//    private String opoShow;
//
//    /** 是否监测 */
//    @ApiModelProperty(value = "是否监测")
//    @Excel(name = "是否监测")
//    private String isMon;
//
//    /** 告警内容 */
//    @ApiModelProperty(value = "告警内容")
//    @Excel(name = "告警内容")
//    private String alarmContent;

}
