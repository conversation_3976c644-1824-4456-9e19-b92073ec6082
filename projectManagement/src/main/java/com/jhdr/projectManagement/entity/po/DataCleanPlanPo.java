package com.jhdr.projectManagement.entity.po;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import lombok.Data;

/**
 * 数据清洗方案编制对象 data_clean_plan
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@TableName(value ="data_clean_plan")
public class DataCleanPlanPo {
    /**  */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 编号 */
    @Excel(name = "编号")
    private String code;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String name;

    /** 规则属性 */
    @Excel(name = "规则属性")
    private String ruleName;

    /** 方案规则 */
    @Excel(name = "方案规则")
    private String content;

    /** 第一个数据 */
    @Excel(name = "第一个数据")
    private String upData;

    /** 第二个数据 */
    @Excel(name = "第二个数据")
    private String dowmData;

    /** 是否启用 */
    @Excel(name = "是否启用")
    private Integer isUse;

}
