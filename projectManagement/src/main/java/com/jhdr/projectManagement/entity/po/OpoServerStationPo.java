package com.jhdr.projectManagement.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-服务器管理对象 opo_server_station
 *
 * <AUTHOR>
 * @date 2024-07-11
 */

@Data
@ApiModel(description = "运维监控-服务器管理")
@Accessors(chain = true)
@TableName(value ="opo_server_station")
public class OpoServerStationPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Long id;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String deviceName;

    /** 品牌型号 */
    @ApiModelProperty(value = "品牌型号")
    @Excel(name = "品牌型号")
    private String deviceModel;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** IP地址 */
    @ApiModelProperty(value = "IP地址")
    @Excel(name = "IP地址")
    private String ip;

    /** 网关 */
    @ApiModelProperty(value = "网关")
    @Excel(name = "网关")
    private String gateway;

    /** 子网掩码 */
    @ApiModelProperty(value = "子网掩码")
    @Excel(name = "子网掩码")
    private String mask;

    /** 首选dns */
    @ApiModelProperty(value = "首选dns")
    @Excel(name = "首选dns")
    private String dns;

    /** 磁盘使用率 */
    @ApiModelProperty(value = "磁盘使用率")
    @Excel(name = "磁盘使用率")
    private String cpUse;



    /** cpu利用率 */
    @ApiModelProperty(value = "cpu利用率")
    @Excel(name = "cpu利用率")
    private String cpuUse;

    /** 内存利用率 */
    @ApiModelProperty(value = "内存利用率")
    @Excel(name = "内存利用率")
    private String ncUse;

    /** 交换区使用率 */
    @ApiModelProperty(value = "交换区使用率")
    @Excel(name = "交换区使用率")
    private String jhqUse;

    /** 运行状态 */
    @ApiModelProperty(value = "运行状态")
    @Excel(name = "运行状态")
    private String runState;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @Excel(name = "告警时间")
    private String alarmTime;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    /** 端口数量 */
    @ApiModelProperty(value = "端口数量")
    @Excel(name = "端口数量")
    private Long portSize;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Long isDelete;

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    @Excel(name = "用户名")
    private String username;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    @Excel(name = "密码")
    private String password;

    /** 系统连接端口号 */
    @ApiModelProperty(value = "系统连接端口号")
    @Excel(name = "系统连接端口号")
    private Integer port;

}
