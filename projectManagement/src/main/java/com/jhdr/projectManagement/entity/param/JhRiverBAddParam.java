package com.jhdr.projectManagement.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河流信息对象 jh_river_b
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "河流信息")
@Accessors(chain = true)
public class JhRiverBAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 河流代码 */
    @ApiModelProperty(value = "河流代码")
    @TableId
    private String ennmcd;

    /** 河流名称 */
    @ApiModelProperty(value = "河流名称")
    @Excel(name = "河流名称")
    private String rvnm;

    /** 河流类别:河道/干渠/支渠/斗渠/农渠（毛渠 */
    @ApiModelProperty(value = "河流类别:河道/干渠/支渠/斗渠/农渠（毛渠")
    @Excel(name = "河流类别:河道/干渠/支渠/斗渠/农渠", readConverterExp = "河流类别:河道/干渠/支渠/斗渠/农渠（毛渠")
    private String rvtp;

    /** 所属流域 */
    @ApiModelProperty(value = "所属流域")
    @Excel(name = "所属流域")
    private String belws;

    /** 河源位置 */
    @ApiModelProperty(value = "河源位置")
    @Excel(name = "河源位置")
    private String rvpl;

    /** 流入水系 */
    @ApiModelProperty(value = "流入水系")
    @Excel(name = "流入水系")
    private String rved;

    /** 长度:公里 */
    @ApiModelProperty(value = "长度:公里")
    @Excel(name = "长度:公里")
    private BigDecimal mnstln;

    /** 流域面积:平方公里 */
    @ApiModelProperty(value = "流域面积:平方公里")
    @Excel(name = "流域面积:平方公里")
    private BigDecimal ttdrbsar;

    /** 流域内人口:万人 */
    @ApiModelProperty(value = "流域内人口:万人")
    @Excel(name = "流域内人口:万人")
    private BigDecimal drbspp;

    /** 农业灌溉面积:万亩 */
    @ApiModelProperty(value = "农业灌溉面积:万亩")
    @Excel(name = "农业灌溉面积:万亩")
    private BigDecimal irrigatedarea;

    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String munit;

    /** 河长制信息 */
    @ApiModelProperty(value = "河长制信息")
    @Excel(name = "河长制信息")
    private String rvchief;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String rm;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private Integer status;

    /** 图片地址 */
    @ApiModelProperty(value = "图片地址")
    @Excel(name = "图片地址")
    private String url;
}
