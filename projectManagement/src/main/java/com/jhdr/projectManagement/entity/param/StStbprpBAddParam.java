package com.jhdr.projectManagement.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-07-08
 */

@Data
@ApiModel(description = "测站基本属性")
@Accessors(chain = true)
public class StStbprpBAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 对应gis_datas_d 字段CD */
    @ApiModelProperty(value = "测站编码 对应gis_datas_d 字段CD")
    @TableId
    private String STCD;

    /** 测站名称 */
    @ApiModelProperty(value = "测站名称")
    @Excel(name = "测站名称")
    private String STNM;

    /** 河流名称 */
    @ApiModelProperty(value = "河流名称")
    @Excel(name = "河流名称")
    private String RVNM;

    /** 水系名称 */
    @ApiModelProperty(value = "水系名称")
    @Excel(name = "水系名称")
    private String HNNM;

    /** 流域名称 */
    @ApiModelProperty(value = "流域名称")
    @Excel(name = "流域名称")
    private String BSNM;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal LGTD;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private BigDecimal LTTD;

    /** 站址 */
    @ApiModelProperty(value = "站址")
    @Excel(name = "站址")
    private String STLC;

    /** 行政区划码 */
    @ApiModelProperty(value = "行政区划码")
    @Excel(name = "行政区划码")
    private String ADDVCD;

    /** 基面名称 */
    @ApiModelProperty(value = "基面名称")
    @Excel(name = "基面名称")
    private String DTMNM;

    /** 基面高程 */
    @ApiModelProperty(value = "基面高程")
    @Excel(name = "基面高程")
    private BigDecimal DTMEL;

    /** 基面修正值 */
    @ApiModelProperty(value = "基面修正值")
    @Excel(name = "基面修正值")
    private BigDecimal DTPR;

    /** 站类  */
    @ApiModelProperty(value = "站类 ")
    @Excel(name = "站类 ")
    private String STTP;

    /** 报汛等级 */
    @ApiModelProperty(value = "报汛等级")
    @Excel(name = "报汛等级")
    private String FRGRD;

    /** 建站年月 */
    @ApiModelProperty(value = "建站年月")
    @Excel(name = "建站年月")
    private String ESSTYM;

    /** 始报年月 */
    @ApiModelProperty(value = "始报年月")
    @Excel(name = "始报年月")
    private String BGFRYM;

    /** 隶属行业单位 */
    @ApiModelProperty(value = "隶属行业单位")
    @Excel(name = "隶属行业单位")
    private String ATCUNIT;

    /** 信息管理单位 */
    @ApiModelProperty(value = "信息管理单位")
    @Excel(name = "信息管理单位")
    private String ADMAUTH;

    /** 交换管理单位 */
    @ApiModelProperty(value = "交换管理单位")
    @Excel(name = "交换管理单位")
    private String LOCALITY;

    /** 测站岸别 */
    @ApiModelProperty(value = "测站岸别")
    @Excel(name = "测站岸别")
    private String STBK;

    /** 测站方位 */
    @ApiModelProperty(value = "测站方位")
    @Excel(name = "测站方位")
    private BigDecimal STAZT;

    /** 至河口距离 */
    @ApiModelProperty(value = "至河口距离")
    @Excel(name = "至河口距离")
    private BigDecimal DSTRVM;

    /** 集水面积 */
    @ApiModelProperty(value = "集水面积")
    @Excel(name = "集水面积")
    private BigDecimal DRNA;

    /** 拼音码 */
    @ApiModelProperty(value = "拼音码")
    @Excel(name = "拼音码")
    private String PHCD;

    /** 启用标志 */
    @ApiModelProperty(value = "启用标志")
    @Excel(name = "启用标志")
    private String USFL;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String COMMENTS;

    /** 时间戳 */
    @ApiModelProperty(value = "时间戳")
    @Excel(name = "时间戳")
    private String MODITIME;

    /** 有河道水位 */
    @ApiModelProperty(value = "有河道水位")
    @Excel(name = "有河道水位")
    private BigDecimal isz;

    /** 有涵闸水位 */
    @ApiModelProperty(value = "有涵闸水位")
    @Excel(name = "有涵闸水位")
    private BigDecimal iswz;

    /** 有流量 */
    @ApiModelProperty(value = "有流量")
    @Excel(name = "有流量")
    private BigDecimal isdsq;

    /** 有雨量 */
    @ApiModelProperty(value = "有雨量")
    @Excel(name = "有雨量")
    private BigDecimal isdrp;

    /** 数据来源（10共享省厅，20一期，30二期,90其他） */
    @ApiModelProperty(value = "数据来源（10共享省厅，20一期，30二期,90其他）")
    @Excel(name = "数据来源", readConverterExp = "1=0共享省厅，20一期，30二期,90其他")
    private String source;

    /** 图片地址 */
    @ApiModelProperty(value = "图片地址")
    @Excel(name = "图片地址")
    private String url;

}
