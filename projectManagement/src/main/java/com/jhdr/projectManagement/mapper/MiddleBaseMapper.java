package com.jhdr.projectManagement.mapper;

import com.jhdr.projectManagement.domain.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 中台基础Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-22
 */

public interface MiddleBaseMapper 
{
    /**
     * 查询中台基础
     * 
     * @param id 中台基础主键
     * @return 中台基础
     */

    public MiddleBase selectMiddleBaseById(Integer id);

    /**
     * 查询中台基础列表
     * 
     * @param middleBase 中台基础
     * @return 中台基础集合
     */

    public List<MiddleBase> selectMiddleBaseList(MiddleBase middleBase);

    List<MiddleBase> getDataBaseList();
    /**
     * 新增中台基础
     * 
     * @param middleBase 中台基础
     * @return 结果
     */

    public int insertMiddleBase(MiddleBase middleBase);

    /**
     * 修改中台基础
     * 
     * @param middleBase 中台基础
     * @return 结果
     */

    public int updateMiddleBase(MiddleBase middleBase);

    /**
     * 删除中台基础
     * 
     * @param id 中台基础主键
     * @return 结果
     */

    public int deleteMiddleBaseById(Integer id);

    /**
     * 批量删除中台基础
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */

    public int deleteMiddleBaseByIds(Integer[] ids);


    Integer getTableAmount(@Param("id") Integer id);


    List<MiddleDetail> getDataDetailList(@Param("id")Integer id);

    Integer getDataCountByName(@Param("tableid") String tableid);

    List<String> getDataColumn(@Param("tableid")String tableid);

    List<Map<String, String>> getTableData(@Param("tableid")String tableid, @Param("stcd")String stcd);


    int addTable(@Param("id")Integer id, @Param("tableid")String tableid, @Param("tabcn")String tabcn);

    List<DataSourceVo> getDataSource(@Param("source")String source,@Param("tableName")String tableName);

    Integer findMonCount(@Param("source")String source, @Param("tableName")String tableName);


    Integer findAllCount(@Param("source")String source, @Param("tableName")String tableName);

    Integer findDayCount(@Param("source")String source, @Param("tableName")String tableName);

    List<DataRegionVo> getDataRegion();

    StStbprpB selectTableData(@Param("stcd")String stcd);

    int addTableData(StStbprpB po);


    List<SysJob> getDesignView(SysJob sysJob);
}
