package com.jhdr.projectManagement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.entity.po.StStbprpBPo;
import com.jhdr.projectManagement.entity.param.StStbprpBParam;
import com.jhdr.projectManagement.entity.param.StStbprpBAddParam;
import com.jhdr.projectManagement.entity.param.StStbprpBEditParam;
import com.jhdr.projectManagement.entity.vo.StStbprpBVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 测站基本属性Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-08
 */
@Mapper
public interface StStbprpBMapper extends BaseMapper<StStbprpBPo>
{
    /**
     * 查询测站基本属性
     *
     * @param STCD 测站基本属性主键
     * @return 测站基本属性
     */
    public StStbprpBVo selectStStbprpBBySTCD(String STCD);

    /**
     * 查询测站基本属性列表
     *
     * @param stStbprpB 测站基本属性
     * @return 测站基本属性集合
     */
    public List<StStbprpBVo> selectStStbprpBList(StStbprpBParam stStbprpB);

    /**
     * 新增测站基本属性
     *
     * @param stStbprpB 测站基本属性
     * @return 结果
     */
    public int insertStStbprpB(StStbprpBAddParam stStbprpB);

    /**
     * 修改测站基本属性
     *
     * @param stStbprpB 测站基本属性
     * @return 结果
     */
    public int updateStStbprpB(StStbprpBEditParam stStbprpB);

    /**
     * 删除测站基本属性
     *
     * @param STCD 测站基本属性主键
     * @return 结果
     */
    public int deleteStStbprpBBySTCD(String STCD);

    /**
     * 批量删除测站基本属性
     *
     * @param STCDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStStbprpBBySTCDs(String[] STCDs);

    List<StStbprpBVo> getAllList(@Param("stnm") String stnm , @Param("stcd") String  stcd);
}
