package com.jhdr.projectManagement.mapper;



import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.domain.DataCleanPlan;
import com.jhdr.projectManagement.entity.po.DataCleanPlanPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 数据清洗方案编制Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Mapper
public interface DataCleanPlanMapper extends BaseMapper<DataCleanPlanPo>
{
    /**
     * 查询数据清洗方案编制
     * 
     * @param id 数据清洗方案编制主键
     * @return 数据清洗方案编制
     */
    public DataCleanPlan selectDataCleanPlanById(Integer id);

    /**
     * 查询数据清洗方案编制列表
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 数据清洗方案编制集合
     */
    public List<DataCleanPlan> selectDataCleanPlanList(DataCleanPlan dataCleanPlan);

    /**
     * 新增数据清洗方案编制
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 结果
     */
    public int insertDataCleanPlan(DataCleanPlan dataCleanPlan);

    /**
     * 修改数据清洗方案编制
     * 
     * @param dataCleanPlan 数据清洗方案编制
     * @return 结果
     */
    public int updateDataCleanPlan(DataCleanPlan dataCleanPlan);

    /**
     * 删除数据清洗方案编制
     * 
     * @param id 数据清洗方案编制主键
     * @return 结果
     */
    public int deleteDataCleanPlanById(Integer id);

    /**
     * 批量删除数据清洗方案编制
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataCleanPlanByIds(Integer[] ids);
}
