package com.jhdr.projectManagement.mapper;



import com.jhdr.projectManagement.domain.DataCatalogMount;

import java.util.List;

/**
 * 数据资源目录挂载Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */

public interface DataCatalogMountMapper 
{
    /**
     * 查询数据资源目录挂载
     * 
     * @param id 数据资源目录挂载主键
     * @return 数据资源目录挂载
     */
    public DataCatalogMount selectDataCatalogMountById(Integer id);

    /**
     * 查询数据资源目录挂载列表
     * 
     * @param dataCatalogMount 数据资源目录挂载
     * @return 数据资源目录挂载集合
     */
    public List<DataCatalogMount> selectDataCatalogMountList(DataCatalogMount dataCatalogMount);

    /**
     * 新增数据资源目录挂载
     * 
     * @param dataCatalogMount 数据资源目录挂载
     * @return 结果
     */
    public int insertDataCatalogMount(DataCatalogMount dataCatalogMount);

    /**
     * 修改数据资源目录挂载
     * 
     * @param dataCatalogMount 数据资源目录挂载
     * @return 结果
     */
    public int updateDataCatalogMount(DataCatalogMount dataCatalogMount);

    /**
     * 删除数据资源目录挂载
     * 
     * @param id 数据资源目录挂载主键
     * @return 结果
     */
    public int deleteDataCatalogMountById(Integer id);

    /**
     * 批量删除数据资源目录挂载
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataCatalogMountByIds(Integer[] ids);
}
