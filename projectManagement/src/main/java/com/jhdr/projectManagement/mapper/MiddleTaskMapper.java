package com.jhdr.projectManagement.mapper;

import com.jhdr.projectManagement.domain.*;

import java.util.List;

/**
 * 可视化设计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */

public interface MiddleTaskMapper 
{
    /**
     * 查询可视化设计
     * 
     * @param id 可视化设计主键
     * @return 可视化设计
     */
    public MiddleTask selectMiddleTaskById(Integer id);

    /**
     * 查询可视化设计列表
     * 
     * @param middleTask 可视化设计
     * @return 可视化设计集合
     */
    public List<MiddleTask> selectMiddleTaskList(MiddleTask middleTask);

    /**
     * 新增可视化设计
     * 
     * @param middleTask 可视化设计
     * @return 结果
     */
    public int insertMiddleTask(MiddleTask middleTask);

    /**
     * 修改可视化设计
     * 
     * @param middleTask 可视化设计
     * @return 结果
     */
    public int updateMiddleTask(MiddleTask middleTask);

    /**
     * 删除可视化设计
     * 
     * @param id 可视化设计主键
     * @return 结果
     */
    public int deleteMiddleTaskById(Integer id);

    /**
     * 批量删除可视化设计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiddleTaskByIds(Integer[] ids);
}
