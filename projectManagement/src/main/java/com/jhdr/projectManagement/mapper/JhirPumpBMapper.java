package com.jhdr.projectManagement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.entity.po.JhirPumpBPo;
import com.jhdr.projectManagement.entity.param.JhirPumpBParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBAddParam;
import com.jhdr.projectManagement.entity.param.JhirPumpBEditParam;
import com.jhdr.projectManagement.entity.vo.JhirPumpBVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 水泵基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Mapper
public interface JhirPumpBMapper extends BaseMapper<JhirPumpBPo>
{
    /**
     * 查询水泵基础信息
     *
     * @param pumpCode 水泵基础信息主键
     * @return 水泵基础信息
     */
    public JhirPumpBVo selectJhirPumpBByPumpCode(String pumpCode);

    /**
     * 查询水泵基础信息列表
     *
     * @param jhirPumpB 水泵基础信息
     * @return 水泵基础信息集合
     */
    public List<JhirPumpBVo> selectJhirPumpBList(JhirPumpBParam jhirPumpB);

    /**
     * 新增水泵基础信息
     *
     * @param jhirPumpB 水泵基础信息
     * @return 结果
     */
    public int insertJhirPumpB(JhirPumpBAddParam jhirPumpB);

    /**
     * 修改水泵基础信息
     *
     * @param jhirPumpB 水泵基础信息
     * @return 结果
     */
    public int updateJhirPumpB(JhirPumpBEditParam jhirPumpB);

    /**
     * 删除水泵基础信息
     *
     * @param pumpCode 水泵基础信息主键
     * @return 结果
     */
    public int deleteJhirPumpBByPumpCode(String pumpCode);

    /**
     * 批量删除水泵基础信息
     *
     * @param pumpCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhirPumpBByPumpCodes(String[] pumpCodes);

    void deleteJhirPumpBByStrCode(@Param("strCode") String strCode);

    List<JhirPumpBVo> selectJhirPumpBAllList(@Param("strCode")String strCode,@Param("pumpCode")String pumpCode);

    int insertOne(@Param("jhirPumpB") JhirPumpBPo jhirPumpB);

    List<JhirPumpBPo> getListByStrCode(@Param("strCode") String strCode);

    JhirPumpBPo getPumpByPumpCode(@Param("pumpCode") String pumpCode);

    List<JhirPumpBVo> selectJhirPumpBAllListByStrCode(@Param("strCode")String strCode);
}
