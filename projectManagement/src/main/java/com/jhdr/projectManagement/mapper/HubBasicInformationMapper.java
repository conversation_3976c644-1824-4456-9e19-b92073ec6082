package com.jhdr.projectManagement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.entity.po.HubBasicInformationPo;
import com.jhdr.projectManagement.entity.param.HubBasicInformationParam;
import com.jhdr.projectManagement.entity.param.HubBasicInformationAddParam;
import com.jhdr.projectManagement.entity.param.HubBasicInformationEditParam;
import com.jhdr.projectManagement.entity.vo.HubBasicInformationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 枢纽基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface HubBasicInformationMapper extends BaseMapper<HubBasicInformationPo>
{
    /**
     * 查询枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 枢纽基础信息
     */
    public HubBasicInformationVo selectHubBasicInformationById(Long id);

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息集合
     */
    public List<HubBasicInformationVo> selectHubBasicInformationList(HubBasicInformationParam hubBasicInformation);

    /**
     * 新增枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int insertHubBasicInformation(HubBasicInformationAddParam hubBasicInformation);

    /**
     * 修改枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int updateHubBasicInformation(HubBasicInformationEditParam hubBasicInformation);

    /**
     * 删除枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 结果
     */
    public int deleteHubBasicInformationById(Long id);

    /**
     * 批量删除枢纽基础信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHubBasicInformationByIds(Long[] ids);

    List<HubBasicInformationVo> getAllList(@Param("name") String name,@Param("hubCode") String hubCode);
}
