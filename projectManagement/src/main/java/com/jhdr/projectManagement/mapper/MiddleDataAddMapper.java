package com.jhdr.projectManagement.mapper;

import com.jhdr.projectManagement.domain.*;

import java.util.List;

/**
 * 数据填报Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */

public interface MiddleDataAddMapper 
{
    /**
     * 查询数据填报
     * 
     * @param id 数据填报主键
     * @return 数据填报
     */
    public MiddleDataAdd selectMiddleDataAddById(Integer id);

    /**
     * 查询数据填报列表
     * 
     * @param middleDataAdd 数据填报
     * @return 数据填报集合
     */
    public List<MiddleDataAdd> selectMiddleDataAddList(MiddleDataAdd middleDataAdd);

    /**
     * 新增数据填报
     * 
     * @param middleDataAdd 数据填报
     * @return 结果
     */
    public int insertMiddleDataAdd(MiddleDataAdd middleDataAdd);

    /**
     * 修改数据填报
     * 
     * @param middleDataAdd 数据填报
     * @return 结果
     */
    public int updateMiddleDataAdd(MiddleDataAdd middleDataAdd);

    /**
     * 删除数据填报
     * 
     * @param id 数据填报主键
     * @return 结果
     */
    public int deleteMiddleDataAddById(Integer id);

    /**
     * 批量删除数据填报
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMiddleDataAddByIds(Integer[] ids);
}
