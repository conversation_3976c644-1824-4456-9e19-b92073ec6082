package com.jhdr.projectManagement.mapper;



import com.jhdr.projectManagement.domain.DataApiMount;

import java.util.List;

/**
 * 数据资源api挂载Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */

public interface DataApiMountMapper 
{
    /**
     * 查询数据资源api挂载
     * 
     * @param id 数据资源api挂载主键
     * @return 数据资源api挂载
     */
    public DataApiMount selectDataApiMountById(Integer id);

    /**
     * 查询数据资源api挂载列表
     * 
     * @param dataApiMount 数据资源api挂载
     * @return 数据资源api挂载集合
     */
    public List<DataApiMount> selectDataApiMountList(DataApiMount dataApiMount);

    /**
     * 新增数据资源api挂载
     * 
     * @param dataApiMount 数据资源api挂载
     * @return 结果
     */
    public int insertDataApiMount(DataApiMount dataApiMount);

    /**
     * 修改数据资源api挂载
     * 
     * @param dataApiMount 数据资源api挂载
     * @return 结果
     */
    public int updateDataApiMount(DataApiMount dataApiMount);

    /**
     * 删除数据资源api挂载
     * 
     * @param id 数据资源api挂载主键
     * @return 结果
     */
    public int deleteDataApiMountById(Integer id);

    /**
     * 批量删除数据资源api挂载
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDataApiMountByIds(Integer[] ids);
}
