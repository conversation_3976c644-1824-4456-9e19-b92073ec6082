package com.jhdr.projectManagement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.entity.po.JhomVmBPo;
import com.jhdr.projectManagement.entity.param.JhomVmBParam;
import com.jhdr.projectManagement.entity.param.JhomVmBAddParam;
import com.jhdr.projectManagement.entity.param.JhomVmBEditParam;
import com.jhdr.projectManagement.entity.vo.JhomNameVo;
import com.jhdr.projectManagement.entity.vo.JhomVmBVo;
import org.apache.ibatis.annotations.Param;


/**
 * 视频监控站点基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
public interface JhomVmBMapper extends BaseMapper<JhomVmBPo>
{
    /**
     * 查询视频监控站点基础信息
     *
     * @param cd 视频监控站点基础信息主键
     * @return 视频监控站点基础信息
     */
    public JhomVmBVo selectJhomVmBByCd(String cd);

    /**
     * 查询视频监控站点基础信息列表
     *
     * @param jhomVmB 视频监控站点基础信息
     * @return 视频监控站点基础信息集合
     */
    public List<JhomVmBVo> selectJhomVmBList(JhomVmBParam jhomVmB);

    /**
     * 新增视频监控站点基础信息
     *
     * @param jhomVmB 视频监控站点基础信息
     * @return 结果
     */
    public int insertJhomVmB(JhomVmBAddParam jhomVmB);

    /**
     * 修改视频监控站点基础信息
     *
     * @param jhomVmB 视频监控站点基础信息
     * @return 结果
     */
    public int updateJhomVmB(JhomVmBEditParam jhomVmB);

    /**
     * 删除视频监控站点基础信息
     *
     * @param cd 视频监控站点基础信息主键
     * @return 结果
     */
    public int deleteJhomVmBByCd(String cd);

    /**
     * 批量删除视频监控站点基础信息
     *
     * @param cds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhomVmBByCds(String[] cds);

    List<JhomNameVo> selectStationList();

    String selectNameByCode(@Param("regionIndexCode") String regionIndexCode);

    String selectCodeByName(@Param("videoName") String videoName);

    List<JhomVmBVo> selectJhomVmBListNew(JhomVmBParam jhomVmB);
}
