package com.jhdr.projectManagement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.entity.po.JhirStrobeBPo;
import com.jhdr.projectManagement.entity.param.JhirStrobeBParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBAddParam;
import com.jhdr.projectManagement.entity.param.JhirStrobeBEditParam;
import com.jhdr.projectManagement.entity.po.JhirWagaBPo;
import com.jhdr.projectManagement.entity.vo.JhirStrobeBVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 闸门基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface JhirStrobeBMapper extends BaseMapper<JhirStrobeBPo>
{
    /**
     * 查询闸门基础信息
     *
     * @param strobeCode 闸门基础信息主键
     * @return 闸门基础信息
     */
    public JhirStrobeBVo selectJhirStrobeBByStrobeCode(String strobeCode);

    /**
     * 查询闸门基础信息列表
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 闸门基础信息集合
     */
    public List<JhirStrobeBVo> selectJhirStrobeBList(JhirStrobeBParam jhirStrobeB);

    /**
     * 新增闸门基础信息
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 结果
     */
    public int insertJhirStrobeB(JhirStrobeBAddParam jhirStrobeB);

    /**
     * 修改闸门基础信息
     *
     * @param jhirStrobeB 闸门基础信息
     * @return 结果
     */
    public int updateJhirStrobeB(JhirStrobeBEditParam jhirStrobeB);

    /**
     * 删除闸门基础信息
     *
     * @param strobeCode 闸门基础信息主键
     * @return 结果
     */
    public int deleteJhirStrobeBByStrobeCode(String strobeCode);

    /**
     * 批量删除闸门基础信息
     *
     * @param strobeCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhirStrobeBByStrobeCodes(String[] strobeCodes);

    List<JhirStrobeBVo> selectJhirStrobeBAllList(@Param("strCode") String strCode,@Param("strobeCode") String strobeCode);

    JhirWagaBPo selectOneWaga(@Param("strCode") String strCode);

    List<JhirStrobeBVo> selectJhirStrobeBAllListByStrCode(@Param("strCode") String strCode);
}
