package com.jhdr.projectManagement.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.projectManagement.entity.po.IrrigationDistrictInfoPo;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoParam;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoAddParam;
import com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoEditParam;
import com.jhdr.projectManagement.entity.vo.IrrigationDistrictInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 灌区基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Mapper
public interface IrrigationDistrictInfoMapper extends BaseMapper<IrrigationDistrictInfoPo>
{
    /**
     * 查询灌区基本信息
     *
     * @param id 灌区基本信息主键
     * @return 灌区基本信息
     */
    public IrrigationDistrictInfoVo selectIrrigationDistrictInfoById(Long id);

    /**
     * 查询灌区基本信息列表
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 灌区基本信息集合
     */
    public List<IrrigationDistrictInfoVo> selectIrrigationDistrictInfoList(IrrigationDistrictInfoParam irrigationDistrictInfo);

    /**
     * 新增灌区基本信息
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 结果
     */
    public int insertIrrigationDistrictInfo(IrrigationDistrictInfoAddParam irrigationDistrictInfo);

    /**
     * 修改灌区基本信息
     *
     * @param irrigationDistrictInfo 灌区基本信息
     * @return 结果
     */
    public int updateIrrigationDistrictInfo(IrrigationDistrictInfoEditParam irrigationDistrictInfo);

    /**
     * 删除灌区基本信息
     *
     * @param id 灌区基本信息主键
     * @return 结果
     */
    public int deleteIrrigationDistrictInfoById(Long id);

    /**
     * 批量删除灌区基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationDistrictInfoByIds(Long[] ids);

    List<IrrigationDistrictInfoVo> selectIrrigationAllList(@Param("name") String name,@Param("irrigationCode") String irrigationCode);
}
