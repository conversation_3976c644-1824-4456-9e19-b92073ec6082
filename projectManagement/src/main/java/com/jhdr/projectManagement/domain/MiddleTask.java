package com.jhdr.projectManagement.domain;

import com.jhdr.common.core.annotation.Excel;
import com.jhdr.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 可视化设计对象 middle_task
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
public class MiddleTask extends BaseEntity {


    /** $column.columnComment */
    private Integer id;

    /** 任务编号 */
    @Excel(name = "任务编号")
    private String code;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String name;

    /** 任务组名 */
    @Excel(name = "任务组名")
    private String groups;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;

    /** 间隔时间 */
    @Excel(name = "间隔时间")
    private Integer cycle;

}
