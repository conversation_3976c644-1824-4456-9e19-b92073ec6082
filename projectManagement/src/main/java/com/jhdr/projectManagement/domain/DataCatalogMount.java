package com.jhdr.projectManagement.domain;


import com.jhdr.common.core.annotation.Excel;
import com.jhdr.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 数据资源目录挂载对象 data_catalog_mount
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
public class DataCatalogMount extends BaseEntity
{
    /** $column.columnComment */
    private Integer id;

    /** 挂载路径 */
    @Excel(name = "挂载路径")
    private String path;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String name;

    /** 状态 */
    @Excel(name = "状态")
    private Integer status;


}
