package com.jhdr.projectManagement.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 中台基础对象 middle_base
 * 
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StStbprpB {
    private String stcd;//编号

    private String stnm;//名称

    private String rvnm;//河流名称

    private String hnnm;//水系名称

    private String bsnm;//流域名称

    private Double lgtd;//经度

    private Double lttd;//纬度

    private String stlc;//站址

    private String addvcd;//行政区划码

    private String sttp;//站类

    private String locality;//交换管理单位

    private Integer source;//数据来源（10共享省厅，20一期，30二期,40跨行业，90其他）




}
