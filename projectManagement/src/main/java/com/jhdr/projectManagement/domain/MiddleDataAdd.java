package com.jhdr.projectManagement.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import com.jhdr.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 数据填报对象 middle_data_add
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
public class MiddleDataAdd extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Integer id;

    /** 新增文件 */
    @Excel(name = "新增文件")
    private String name;

    /** 接收数据仓库 */
    @Excel(name = "接收数据仓库")
    private String dataHouse;

    /** 接收数据表 */
    @Excel(name = "接收数据表")
    private String tableName;

    /** 表名 */
    @Excel(name = "表名")
    private String tableId;

    /** 结果 */
    @Excel(name = "结果")
    private String result;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addTime;


}
