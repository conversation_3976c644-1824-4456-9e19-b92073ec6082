package com.jhdr.projectManagement.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import com.jhdr.common.core.annotation.Excel;
import com.jhdr.common.core.web.domain.BaseEntity;
/**
 * 数据清洗记录对象 data_clean_record
 * 
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
public class DataCleanRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Integer id;

    /** 编号 */
    @Excel(name = "编号")
    private String code;

    /** 清洗方案名称 */
    @Excel(name = "清洗方案名称")
    private String name;

    /** 清洗对象 */
    @Excel(name = "清洗对象")
    private String tableName;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;


}
