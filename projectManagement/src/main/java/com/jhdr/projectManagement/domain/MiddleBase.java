package com.jhdr.projectManagement.domain;

import com.jhdr.common.core.annotation.Excel;
import com.jhdr.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * 中台基础对象 middle_base
 * 
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
public class MiddleBase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Integer id;

    /** 表数量 */
    @Excel(name = "表数量")
    private Integer amount;

    /** 表类型 */
    @Excel(name = "表类型")
    private String dataType;

    /** 图标 */
    @Excel(name = "图标")
    private String icon;


}
