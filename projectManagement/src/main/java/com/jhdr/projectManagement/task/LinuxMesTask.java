package com.jhdr.projectManagement.task;

import cn.hutool.core.bean.BeanUtil;
import com.jcraft.jsch.*;
import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.projectManagement.entity.po.OpoServerStationPo;
import com.jhdr.projectManagement.entity.vo.OpoServerStationVo;
import com.jhdr.projectManagement.mapper.OpoServerStationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;

/**
 * 定时任务获取服务器的状态信息
 *
 * <AUTHOR>
 * @version 1.0
 * @project chxh_back
 * @date 2024/7/11 19:01:41
 */
@Component
@Slf4j
public class LinuxMesTask {
    @Autowired
    private OpoServerStationMapper opoServerStationMapper;

    @Async
    @Scheduled(cron = "0 0 */1 * * ?")
//    @Scheduled(cron = "0/10 * * * * ? ")
    public void getLinuxMes() {


            // 创建JSch对象
            JSch jsch = new JSch();

            //获取所有的服务器的数据
            List<OpoServerStationVo> list = opoServerStationMapper.getAllList();
            log.info("linux--list"+list.toString());
            for (OpoServerStationVo opoServerStationVo : list) {

                //获取服务器的host，port，username，password
                String host = opoServerStationVo.getIp();
                if (host == null){
                    continue;
                }
                Integer port = opoServerStationVo.getPort();
                if (port ==null){
                    System.out.println("没有端口号");
                    continue;
                }
                String username = opoServerStationVo.getUsername();
                if (username==null){
                    continue;
                }
                String password = opoServerStationVo.getPassword();
                if (password==null){
                    continue;
                }
           /*     if (host.equals("*************")){
                    log.info("*************");
                  continue;
                }*/
                //  String host = "*************";
             //   int port = 22;
                //       int port = 14822;
          /*      String username = "admin";
                String password = "Jhdeer@402";*/
                // 创建会话
                try {
                Session session = jsch.getSession(username, host, port);
                session.setConfig("StrictHostKeyChecking", "no");
                session.setPassword(password);

                // 连接
                session.connect();
                log.info("session"+session.toString());
                // 获取服务器信息
                String serverName = executeCommand(session, "hostname");
                String internalIP = executeCommand(session, "hostname -I | awk '{print $1}'");
                String cpuUsage = executeCommand(session, "top -bn1 | grep 'Cpu(s)' | awk '{print $2 + $4}'");
                String memoryUsage = executeCommand(session, "free | awk 'NR==2 {print $3/$2*100}'");
                String diskUsage = executeCommand(session, "df -h | awk '$NF==\"/\"{print $5}'");
                String[] disk = diskUsage.split("%");
                String diskUs = disk[0];
                double jvmUsage = getJVMUsage();
                String jvmUsageStr = String.format("%.0f", jvmUsage) + "%";
                String swag = executeCommand(session, "free -m");
                String portNum = executeCommand(session, "netstat -an | wc -l");//代表当前打开的端口数量（包括TCP和UDP端口）
                String portNum2 = executeCommand(session, "ss -tuln | wc -l");//列出所有 TCP (-t)、UDP (-u)、监听 (-l) 和不解析服务名 (-n) 的连接信息

                //分割swag信息获取有用信息
                String[] split = swag.split("Swap:");
                String s = split[1];
                String[] split1 = s.split("\\s+");

                long total = Long.parseLong(split1[1]); // 总和 swap 空间
                long used = Long.parseLong(split1[2]);  // 使用 swap 空间
                long free = Long.parseLong(split1[3]);  // 空闲 swap 空间
                // swag使用率
                double usagePercentage = (double) used / total * 100.0;


                //修改数据库更新最新的数据
                opoServerStationVo.setCpuUse(cpuUsage + "%");/** cpu利用率 */
                opoServerStationVo.setJhqUse(usagePercentage + "%");  /** 交换区使用率 */
                opoServerStationVo.setCpUse(diskUsage);/** 磁盘使用率 */
                opoServerStationVo.setNcUse(memoryUsage + "%"); /** 内存利用率 */
                opoServerStationVo.setPortSize(Long.valueOf(portNum2));//端口数量
                opoServerStationVo.setUpdateTime(DateUtils.getNowDate().toString());

                //判断状态是否异常
                if (Double.valueOf(cpuUsage) > 95) {
                    opoServerStationVo.setAlarmType("cpu使用率过高");
                    opoServerStationVo.setAlarmTime(DateUtils.getNowDate().toString());
                }
                if (Double.valueOf(memoryUsage) > 90) {
                    opoServerStationVo.setAlarmType("内存使用率过高");
                    opoServerStationVo.setAlarmTime(DateUtils.getNowDate().toString());
                }
                if (Double.valueOf(diskUs) > 90) {
                    opoServerStationVo.setAlarmType("磁盘使用率过高");
                    opoServerStationVo.setAlarmTime(DateUtils.getNowDate().toString());
                }
                OpoServerStationPo opoServerStationPo = new OpoServerStationPo();
                BeanUtil.copyProperties(opoServerStationVo, opoServerStationPo);
                opoServerStationMapper.updateById(opoServerStationPo);
                System.out.println("修改成功！");
                // 打印结果
                System.out.printf("Swap Total: %d MB\n", total);
                System.out.printf("Swap Used: %d MB\n", used);
                System.out.printf("Swap Free: %d MB\n", free);
                System.out.printf("Swap Usage: %.2f%%\n", usagePercentage);
                System.out.println("Server Name: " + serverName);
                System.out.println("Internal IP: " + internalIP);
                System.out.println("CPU Usage: " + cpuUsage + "%");
                System.out.println("Memory Usage: " + memoryUsage + "%");
                System.out.println("Disk Usage: " + diskUsage);
                System.out.println("JVM Usage: " + jvmUsageStr);
                System.out.println("portNum:" + portNum);
                System.out.println("portNum2:" + portNum2);
                log.info("1");
                    // 关闭会话
                    session.disconnect();
                } catch (JSchException e) {
//                    e.printStackTrace();
                    System.out.println("连接失败");
                    continue;
                }

            }


    }


    // 执行Shell命令并获取输出
    private static String executeCommand(Session session, String command) throws JSchException {
        StringBuilder output = new StringBuilder();

        try {
            // 创建通道
            Channel channel = session.openChannel("exec");
            ((ChannelExec) channel).setCommand(command);

            // 获取输入流
            InputStream inputStream = channel.getInputStream();

            // 连接通道
            channel.connect();

            // 读取命令输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            String line;

            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // 关闭通道
            channel.disconnect();

        } catch (IOException e) {
            e.printStackTrace();
        }

        return output.toString().trim();
    }

    // 获取JVM使用率
    private static double getJVMUsage() {
        Runtime runtime = Runtime.getRuntime();
        double totalMemory = runtime.totalMemory();
        double freeMemory = runtime.freeMemory();
        double maxMemory = runtime.maxMemory();

        double jvmUsage = ((totalMemory - freeMemory) / maxMemory) * 100;
        return jvmUsage;
    }
}
