<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.JhirStrobeBMapper">

    <resultMap type="com.jhdr.projectManagement.entity.po.JhirStrobeBPo" id="JhirStrobeBResult">
        <result property="strobeCode"    column="strobe_code"    />
        <result property="strCode"    column="str_code"    />
        <result property="strobeType"    column="strobe_type"    />
        <result property="strobeMtr"    column="strobe_mtr"    />
        <result property="strobeH"    column="strobe_h"    />
        <result property="strobeW"    column="strobe_w"    />
        <result property="strobeWt"    column="strobe_wt"    />
        <result property="fty"    column="fty"    />
        <result property="strobeBsH"    column="strobe_bs_h"    />
        <result property="gpcd"    column="gpcd"    />
    </resultMap>

    <sql id="selectJhirStrobeBVo">
        select strobe_code, str_code, strobe_type, strobe_mtr, strobe_h, strobe_w, strobe_wt, fty, strobe_bs_h, gpcd from jhir_strobe_b
    </sql>

    <select id="selectJhirStrobeBList"  resultType="com.jhdr.projectManagement.entity.vo.JhirStrobeBVo">
        <include refid="selectJhirStrobeBVo"/>
        <where>
            <if test="strCode != null  and strCode != ''"> and str_code = #{strCode}</if>
            <if test="strobeType != null  and strobeType != ''"> and strobe_type = #{strobeType}</if>
            <if test="strobeMtr != null  and strobeMtr != ''"> and strobe_mtr = #{strobeMtr}</if>
            <if test="strobeH != null "> and strobe_h = #{strobeH}</if>
            <if test="strobeW != null "> and strobe_w = #{strobeW}</if>
            <if test="strobeWt != null "> and strobe_wt = #{strobeWt}</if>
            <if test="fty != null  and fty != ''"> and fty = #{fty}</if>
            <if test="strobeBsH != null "> and strobe_bs_h = #{strobeBsH}</if>
            <if test="gpcd != null "> and gpcd = #{gpcd}</if>
        </where>
    </select>

    <select id="selectJhirStrobeBByStrobeCode"  resultType="com.jhdr.projectManagement.entity.vo.JhirStrobeBVo">
            <include refid="selectJhirStrobeBVo"/>
            where strobe_code = #{strobeCode}
    </select>

    <select id="selectJhirStrobeBAllList" resultType="com.jhdr.projectManagement.entity.vo.JhirStrobeBVo">
        select strobe_code, str_code, strobe_type, strobe_mtr, strobe_h, strobe_w, strobe_wt, fty, strobe_bs_h, gpcd from jhir_strobe_b
        <where>
            <if test="strCode !=null">
              and  str_code = #{strCode}
            </if>
            <if test="strobeCode !=null">
              and  strobe_code = #{strobeCode}
            </if>
        </where>
        order by strobe_code desc,gpcd asc
    </select>
    <select id="selectOneWaga" resultType="com.jhdr.projectManagement.entity.po.JhirWagaBPo">

    </select>
    <select id="selectJhirStrobeBAllListByStrCode"
            resultType="com.jhdr.projectManagement.entity.vo.JhirStrobeBVo">
        select strobe_code, str_code, strobe_type, strobe_mtr, strobe_h, strobe_w, strobe_wt, fty, strobe_bs_h, gpcd from jhir_strobe_b
        <where>
            <if test="strCode !=null">
                str_code = #{strCode}
            </if>
        </where>
    </select>

    <insert id="insertJhirStrobeB" parameterType="com.jhdr.projectManagement.entity.param.JhirStrobeBAddParam">
        insert into jhir_strobe_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="strobeCode != null">strobe_code,</if>
                    <if test="strCode != null and strCode != ''">str_code,</if>
                    <if test="strobeType != null">strobe_type,</if>
                    <if test="strobeMtr != null">strobe_mtr,</if>
                    <if test="strobeH != null">strobe_h,</if>
                    <if test="strobeW != null">strobe_w,</if>
                    <if test="strobeWt != null">strobe_wt,</if>
                    <if test="fty != null">fty,</if>
                    <if test="strobeBsH != null">strobe_bs_h,</if>
                    <if test="gpcd != null">gpcd,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="strobeCode != null">#{strobeCode},</if>
                    <if test="strCode != null and strCode != ''">#{strCode},</if>
                    <if test="strobeType != null">#{strobeType},</if>
                    <if test="strobeMtr != null">#{strobeMtr},</if>
                    <if test="strobeH != null">#{strobeH},</if>
                    <if test="strobeW != null">#{strobeW},</if>
                    <if test="strobeWt != null">#{strobeWt},</if>
                    <if test="fty != null">#{fty},</if>
                    <if test="strobeBsH != null">#{strobeBsH},</if>
                    <if test="gpcd != null">#{gpcd},</if>
        </trim>
    </insert>

    <update id="updateJhirStrobeB" parameterType="com.jhdr.projectManagement.entity.param.JhirStrobeBEditParam">
        update jhir_strobe_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="strCode != null and strCode != ''">str_code = #{strCode},</if>
                    <if test="strobeType != null">strobe_type = #{strobeType},</if>
                    <if test="strobeMtr != null">strobe_mtr = #{strobeMtr},</if>
                    <if test="strobeH != null">strobe_h = #{strobeH},</if>
                    <if test="strobeW != null">strobe_w = #{strobeW},</if>
                    <if test="strobeWt != null">strobe_wt = #{strobeWt},</if>
                    <if test="fty != null">fty = #{fty},</if>
                    <if test="strobeBsH != null">strobe_bs_h = #{strobeBsH},</if>
                    <if test="gpcd != null">gpcd = #{gpcd},</if>
        </trim>
        where strobe_code = #{strobeCode}
    </update>

    <delete id="deleteJhirStrobeBByStrobeCode" parameterType="String">
        delete from jhir_strobe_b where strobe_code = #{strobeCode}
    </delete>

    <delete id="deleteJhirStrobeBByStrobeCodes" parameterType="String">
        delete from jhir_strobe_b where strobe_code in
        <foreach item="strobeCode" collection="array" open="(" separator="," close=")">
            #{strobeCode}
        </foreach>
    </delete>
</mapper>
