<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.DataCleanRecordMapper">
    
    <resultMap type="com.jhdr.projectManagement.domain.DataCleanRecord" id="DataCleanRecordResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="tableName"    column="table_name"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
    </resultMap>

    <sql id="selectDataCleanRecordVo">
        select id, code, name, table_name, start_time, end_time from data_clean_record
    </sql>

    <select id="selectDataCleanRecordList" parameterType="com.jhdr.projectManagement.domain.DataCleanRecord" resultMap="DataCleanRecordResult">
        <include refid="selectDataCleanRecordVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectDataCleanRecordById" parameterType="Integer" resultMap="DataCleanRecordResult">
        <include refid="selectDataCleanRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataCleanRecord" parameterType="com.jhdr.projectManagement.domain.DataCleanRecord" useGeneratedKeys="true" keyProperty="id">
        insert into data_clean_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="tableName != null">table_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
         </trim>
    </insert>

    <update id="updateDataCleanRecord" parameterType="com.jhdr.projectManagement.domain.DataCleanRecord">
        update data_clean_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataCleanRecordById" parameterType="Integer">
        delete from data_clean_record where id = #{id}
    </delete>

    <delete id="deleteDataCleanRecordByIds" parameterType="String">
        delete from data_clean_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
