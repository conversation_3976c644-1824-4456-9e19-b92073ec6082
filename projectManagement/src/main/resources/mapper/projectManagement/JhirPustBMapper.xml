<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.JhirPustBMapper">

    <resultMap type="com.jhdr.projectManagement.entity.po.JhirPustBPo" id="JhirPustBResult">
        <result property="strCode"    column="str_code"    />
        <result property="strName"    column="str_name"    />
        <result property="pustType"    column="pust_type"    />
        <result property="address"    column="address"    />
        <result property="enWait"    column="en_wait"    />
        <result property="enSize"    column="en_size"    />
        <result property="impType"    column="imp_type"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="insFlow"    column="ins_flow"    />
        <result property="pumpNum"    column="pump_num"    />
        <result property="desFlow"    column="des_flow"    />
        <result property="desHead"    column="des_head"    />
        <result property="headwaters"    column="headwaters"    />
        <result property="deviseIrr"    column="devise_irr"    />
        <result property="deviseDra"    column="devise_dra"    />
        <result property="deviseIrrFlow"    column="devise_irr_flow"    />
        <result property="kishibetsu"    column="kishibetsu"    />
        <result property="selfIrr"    column="self_irr"    />
        <result property="pumpIrr"    column="pump_irr"    />
        <result property="irrType"    column="irr_type"    />
        <result property="installType"    column="install_type"    />
        <result property="latd"    column="latd"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="instaCapicaty"    column="insta_capicaty"    />
        <result property="desInEl"    column="des_in_el"    />
        <result property="topInEl"    column="top_in_el"    />
        <result property="lowInEl"    column="low_in_el"    />
        <result property="normalInEl"    column="normal_in_el"    />
        <result property="desOutEl"    column="des_out_el"    />
        <result property="topOutEl"    column="top_out_el"    />
        <result property="lowOutEl"    column="low_out_el"    />
        <result property="normalOutEl"    column="normal_out_el"    />
        <result property="note"    column="note"    />
        <result property="url"    column="url"    />
        <result property="zpll"    column="zpll"    />
        <result property="cpll"    column="cpll"    />
        <result property="addvcd"    column="addvcd"    />
        <result property="pidCode"    column="pid_code"    />
        <result property="vmBCd"    column="vm_b_cd"    />
        <result property="wwZ"    column="ww_z"    />
        <result property="swZ"    column="sw_z"    />
        <result property="raincd"    column="raincd"    />
        <result property="wdwcd"    column="wdwcd"    />
        <result property="wupcd"    column="wupcd"    />
        <result property="hisMaxZ"    column="his_max_z"    />
        <result property="allAvgZ"    column="all_avg_z"    />
        <result property="stDesc"    column="st_desc"    />
        <result property="hisRt"    column="his_rt"    />
        <result property="sumRt"    column="sum_rt"    />
        <result property="nowRt"    column="now_rt"    />
        <result property="hisFw"    column="his_fw"    />
        <result property="sumFw"    column="sum_fw"    />
        <result property="nowFw"    column="now_fw"    />
        <result property="status"    column="status"    />
        <result property="insPow"    column="ins_pow"    />
        <result property="wascd"    column="wascd"    />
        <result property="source"    column="source"    />
    </resultMap>

    <sql id="selectJhirPustBVo">
        select str_code, str_name, pust_type, address, en_wait, en_size, imp_type, complete_time, ins_flow, pump_num, des_flow, des_head, headwaters, devise_irr, devise_dra, devise_irr_flow, kishibetsu, self_irr, pump_irr, irr_type, install_type, latd, lgtd, insta_capicaty, des_in_el, top_in_el, low_in_el, normal_in_el, des_out_el, top_out_el, low_out_el, normal_out_el, note, url, zpll, cpll, addvcd, pid_code, vm_b_cd, ww_z, sw_z, raincd, wdwcd, wupcd, his_max_z, all_avg_z, st_desc, his_rt, sum_rt, now_rt, his_fw, sum_fw, now_fw, status, ins_pow, wascd, source from jhir_pust_b
    </sql>

    <select id="selectJhirPustBList"  resultType="com.jhdr.projectManagement.entity.vo.JhirPustBVo">
        <include refid="selectJhirPustBVo"/>
        <where>
            <if test="strName != null  and strName != ''"> and str_name like concat('%', #{strName}, '%')</if>
            <if test="pustType != null  and pustType != ''"> and pust_type = #{pustType}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="enWait != null  and enWait != ''"> and en_wait = #{enWait}</if>
            <if test="enSize != null  and enSize != ''"> and en_size = #{enSize}</if>
            <if test="impType != null  and impType != ''"> and imp_type = #{impType}</if>
            <if test="completeTime != null  and completeTime != ''"> and complete_time = #{completeTime}</if>
            <if test="insFlow != null  and insFlow != ''"> and ins_flow = #{insFlow}</if>
            <if test="pumpNum != null  and pumpNum != ''"> and pump_num = #{pumpNum}</if>
            <if test="desFlow != null  and desFlow != ''"> and des_flow = #{desFlow}</if>
            <if test="desHead != null  and desHead != ''"> and des_head = #{desHead}</if>
            <if test="headwaters != null  and headwaters != ''"> and headwaters = #{headwaters}</if>
            <if test="deviseIrr != null  and deviseIrr != ''"> and devise_irr = #{deviseIrr}</if>
            <if test="deviseDra != null  and deviseDra != ''"> and devise_dra = #{deviseDra}</if>
            <if test="deviseIrrFlow != null  and deviseIrrFlow != ''"> and devise_irr_flow = #{deviseIrrFlow}</if>
            <if test="kishibetsu != null  and kishibetsu != ''"> and kishibetsu = #{kishibetsu}</if>
            <if test="selfIrr != null  and selfIrr != ''"> and self_irr = #{selfIrr}</if>
            <if test="pumpIrr != null  and pumpIrr != ''"> and pump_irr = #{pumpIrr}</if>
            <if test="irrType != null  and irrType != ''"> and irr_type = #{irrType}</if>
            <if test="installType != null  and installType != ''"> and install_type = #{installType}</if>
            <if test="latd != null  and latd != ''"> and latd = #{latd}</if>
            <if test="lgtd != null  and lgtd != ''"> and lgtd = #{lgtd}</if>
            <if test="instaCapicaty != null  and instaCapicaty != ''"> and insta_capicaty = #{instaCapicaty}</if>
            <if test="desInEl != null "> and des_in_el = #{desInEl}</if>
            <if test="topInEl != null "> and top_in_el = #{topInEl}</if>
            <if test="lowInEl != null "> and low_in_el = #{lowInEl}</if>
            <if test="normalInEl != null "> and normal_in_el = #{normalInEl}</if>
            <if test="desOutEl != null "> and des_out_el = #{desOutEl}</if>
            <if test="topOutEl != null "> and top_out_el = #{topOutEl}</if>
            <if test="lowOutEl != null "> and low_out_el = #{lowOutEl}</if>
            <if test="normalOutEl != null "> and normal_out_el = #{normalOutEl}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="zpll != null  and zpll != ''"> and zpll = #{zpll}</if>
            <if test="cpll != null  and cpll != ''"> and cpll = #{cpll}</if>
            <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
            <if test="pidCode != null  and pidCode != ''"> and pid_code = #{pidCode}</if>
            <if test="vmBCd != null  and vmBCd != ''"> and vm_b_cd = #{vmBCd}</if>
            <if test="wwZ != null "> and ww_z = #{wwZ}</if>
            <if test="swZ != null "> and sw_z = #{swZ}</if>
            <if test="raincd != null  and raincd != ''"> and raincd = #{raincd}</if>
            <if test="wdwcd != null  and wdwcd != ''"> and wdwcd = #{wdwcd}</if>
            <if test="wupcd != null  and wupcd != ''"> and wupcd = #{wupcd}</if>
            <if test="hisMaxZ != null  and hisMaxZ != ''"> and his_max_z = #{hisMaxZ}</if>
            <if test="allAvgZ != null  and allAvgZ != ''"> and all_avg_z = #{allAvgZ}</if>
            <if test="stDesc != null  and stDesc != ''"> and st_desc = #{stDesc}</if>
            <if test="hisRt != null "> and his_rt = #{hisRt}</if>
            <if test="sumRt != null "> and sum_rt = #{sumRt}</if>
            <if test="nowRt != null "> and now_rt = #{nowRt}</if>
            <if test="hisFw != null "> and his_fw = #{hisFw}</if>
            <if test="sumFw != null "> and sum_fw = #{sumFw}</if>
            <if test="nowFw != null "> and now_fw = #{nowFw}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="insPow != null "> and ins_pow = #{insPow}</if>
            <if test="wascd != null  and wascd != ''"> and wascd = #{wascd}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
        </where>
    </select>

    <select id="selectJhirPustBByStrCode"  resultType="com.jhdr.projectManagement.entity.vo.JhirPustBVo">
            <include refid="selectJhirPustBVo"/>
            where str_code = #{strCode}
    </select>
    <select id="selectAllList" resultType="com.jhdr.projectManagement.entity.vo.JhirPustBVo">
        select str_code, str_name, pust_type, address, en_wait, en_size, imp_type, complete_time, ins_flow, pump_num, des_flow, des_head, headwaters, devise_irr, devise_dra, devise_irr_flow, kishibetsu, self_irr, pump_irr, irr_type, install_type, latd, lgtd, insta_capicaty, des_in_el, top_in_el, low_in_el, normal_in_el, des_out_el, top_out_el, low_out_el, normal_out_el, note, url, zpll, cpll, addvcd, pid_code, vm_b_cd, ww_z, sw_z, raincd, wdwcd, wupcd, his_max_z, all_avg_z, st_desc, his_rt, sum_rt, now_rt, his_fw, sum_fw, now_fw, status, ins_pow, wascd, source from jhir_pust_b
        <where>
            <if test="strName !=null ">
             and   str_name like concat('%',#{strName},'%')
            </if>
            <if test="strCode !=null ">
             and   str_code = #{strCode}
            </if>
            and status != 9
        </where>
        ORDER BY addvcd DESC,str_name DESC
    </select>
    <select id="getPumpList" resultType="com.jhdr.projectManagement.entity.po.JhirPumpBPo">
        select pump_code, str_code, fty, pump_type, pump_d, pump_cpc, pump_head, pump_power, pump_eff, alw_sct_vac, fact_head, max_head, min_hrad, pmpcd, wupcd, wdwcd, raincd, ww, sw from jhir_pump_b
        where str_code = #{strCode}
    </select>
    <select id="getPumpByCode" resultType="com.jhdr.projectManagement.entity.po.JhirPustBPo">
        select pump_code, str_code, fty, pump_type, pump_d, pump_cpc, pump_head, pump_power, pump_eff, alw_sct_vac, fact_head, max_head, min_hrad, pmpcd, wupcd, wdwcd, raincd, ww, sw from jhir_pump_b
        where str_code = #{strCode}
    </select>

    <insert id="insertJhirPustB" parameterType="com.jhdr.projectManagement.entity.param.JhirPustBAddParam">
        insert into jhir_pust_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="strCode != null">str_code,</if>
                    <if test="strName != null">str_name,</if>
                    <if test="pustType != null">pust_type,</if>
                    <if test="address != null">address,</if>
                    <if test="enWait != null">en_wait,</if>
                    <if test="enSize != null">en_size,</if>
                    <if test="impType != null">imp_type,</if>
                    <if test="completeTime != null">complete_time,</if>
                    <if test="insFlow != null">ins_flow,</if>
                    <if test="pumpNum != null">pump_num,</if>
                    <if test="desFlow != null">des_flow,</if>
                    <if test="desHead != null">des_head,</if>
                    <if test="headwaters != null">headwaters,</if>
                    <if test="deviseIrr != null">devise_irr,</if>
                    <if test="deviseDra != null">devise_dra,</if>
                    <if test="deviseIrrFlow != null">devise_irr_flow,</if>
                    <if test="kishibetsu != null">kishibetsu,</if>
                    <if test="selfIrr != null">self_irr,</if>
                    <if test="pumpIrr != null">pump_irr,</if>
                    <if test="irrType != null">irr_type,</if>
                    <if test="installType != null">install_type,</if>
                    <if test="latd != null">latd,</if>
                    <if test="lgtd != null">lgtd,</if>
                    <if test="instaCapicaty != null">insta_capicaty,</if>
                    <if test="desInEl != null">des_in_el,</if>
                    <if test="topInEl != null">top_in_el,</if>
                    <if test="lowInEl != null">low_in_el,</if>
                    <if test="normalInEl != null">normal_in_el,</if>
                    <if test="desOutEl != null">des_out_el,</if>
                    <if test="topOutEl != null">top_out_el,</if>
                    <if test="lowOutEl != null">low_out_el,</if>
                    <if test="normalOutEl != null">normal_out_el,</if>
                    <if test="note != null">note,</if>
                    <if test="url != null">url,</if>
                    <if test="zpll != null">zpll,</if>
                    <if test="cpll != null">cpll,</if>
                    <if test="addvcd != null">addvcd,</if>
                    <if test="pidCode != null">pid_code,</if>
                    <if test="vmBCd != null">vm_b_cd,</if>
                    <if test="wwZ != null">ww_z,</if>
                    <if test="swZ != null">sw_z,</if>
                    <if test="raincd != null">raincd,</if>
                    <if test="wdwcd != null">wdwcd,</if>
                    <if test="wupcd != null">wupcd,</if>
                    <if test="hisMaxZ != null">his_max_z,</if>
                    <if test="allAvgZ != null">all_avg_z,</if>
                    <if test="stDesc != null">st_desc,</if>
                    <if test="hisRt != null">his_rt,</if>
                    <if test="sumRt != null">sum_rt,</if>
                    <if test="nowRt != null">now_rt,</if>
                    <if test="hisFw != null">his_fw,</if>
                    <if test="sumFw != null">sum_fw,</if>
                    <if test="nowFw != null">now_fw,</if>
                    <if test="status != null">status,</if>
                    <if test="insPow != null">ins_pow,</if>
                    <if test="wascd != null">wascd,</if>
                    <if test="source != null">source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="strCode != null">#{strCode},</if>
                    <if test="strName != null">#{strName},</if>
                    <if test="pustType != null">#{pustType},</if>
                    <if test="address != null">#{address},</if>
                    <if test="enWait != null">#{enWait},</if>
                    <if test="enSize != null">#{enSize},</if>
                    <if test="impType != null">#{impType},</if>
                    <if test="completeTime != null">#{completeTime},</if>
                    <if test="insFlow != null">#{insFlow},</if>
                    <if test="pumpNum != null">#{pumpNum},</if>
                    <if test="desFlow != null">#{desFlow},</if>
                    <if test="desHead != null">#{desHead},</if>
                    <if test="headwaters != null">#{headwaters},</if>
                    <if test="deviseIrr != null">#{deviseIrr},</if>
                    <if test="deviseDra != null">#{deviseDra},</if>
                    <if test="deviseIrrFlow != null">#{deviseIrrFlow},</if>
                    <if test="kishibetsu != null">#{kishibetsu},</if>
                    <if test="selfIrr != null">#{selfIrr},</if>
                    <if test="pumpIrr != null">#{pumpIrr},</if>
                    <if test="irrType != null">#{irrType},</if>
                    <if test="installType != null">#{installType},</if>
                    <if test="latd != null">#{latd},</if>
                    <if test="lgtd != null">#{lgtd},</if>
                    <if test="instaCapicaty != null">#{instaCapicaty},</if>
                    <if test="desInEl != null">#{desInEl},</if>
                    <if test="topInEl != null">#{topInEl},</if>
                    <if test="lowInEl != null">#{lowInEl},</if>
                    <if test="normalInEl != null">#{normalInEl},</if>
                    <if test="desOutEl != null">#{desOutEl},</if>
                    <if test="topOutEl != null">#{topOutEl},</if>
                    <if test="lowOutEl != null">#{lowOutEl},</if>
                    <if test="normalOutEl != null">#{normalOutEl},</if>
                    <if test="note != null">#{note},</if>
                    <if test="url != null">#{url},</if>
                    <if test="zpll != null">#{zpll},</if>
                    <if test="cpll != null">#{cpll},</if>
                    <if test="addvcd != null">#{addvcd},</if>
                    <if test="pidCode != null">#{pidCode},</if>
                    <if test="vmBCd != null">#{vmBCd},</if>
                    <if test="wwZ != null">#{wwZ},</if>
                    <if test="swZ != null">#{swZ},</if>
                    <if test="raincd != null">#{raincd},</if>
                    <if test="wdwcd != null">#{wdwcd},</if>
                    <if test="wupcd != null">#{wupcd},</if>
                    <if test="hisMaxZ != null">#{hisMaxZ},</if>
                    <if test="allAvgZ != null">#{allAvgZ},</if>
                    <if test="stDesc != null">#{stDesc},</if>
                    <if test="hisRt != null">#{hisRt},</if>
                    <if test="sumRt != null">#{sumRt},</if>
                    <if test="nowRt != null">#{nowRt},</if>
                    <if test="hisFw != null">#{hisFw},</if>
                    <if test="sumFw != null">#{sumFw},</if>
                    <if test="nowFw != null">#{nowFw},</if>
                    <if test="status != null">#{status},</if>
                    <if test="insPow != null">#{insPow},</if>
                    <if test="wascd != null">#{wascd},</if>
                    <if test="source != null">#{source},</if>
        </trim>
    </insert>

    <update id="updateJhirPustB" parameterType="com.jhdr.projectManagement.entity.param.JhirPustBEditParam">
        update jhir_pust_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="strName != null">str_name = #{strName},</if>
                    <if test="pustType != null">pust_type = #{pustType},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="enWait != null">en_wait = #{enWait},</if>
                    <if test="enSize != null">en_size = #{enSize},</if>
                    <if test="impType != null">imp_type = #{impType},</if>
                    <if test="completeTime != null">complete_time = #{completeTime},</if>
                    <if test="insFlow != null">ins_flow = #{insFlow},</if>
                    <if test="pumpNum != null">pump_num = #{pumpNum},</if>
                    <if test="desFlow != null">des_flow = #{desFlow},</if>
                    <if test="desHead != null">des_head = #{desHead},</if>
                    <if test="headwaters != null">headwaters = #{headwaters},</if>
                    <if test="deviseIrr != null">devise_irr = #{deviseIrr},</if>
                    <if test="deviseDra != null">devise_dra = #{deviseDra},</if>
                    <if test="deviseIrrFlow != null">devise_irr_flow = #{deviseIrrFlow},</if>
                    <if test="kishibetsu != null">kishibetsu = #{kishibetsu},</if>
                    <if test="selfIrr != null">self_irr = #{selfIrr},</if>
                    <if test="pumpIrr != null">pump_irr = #{pumpIrr},</if>
                    <if test="irrType != null">irr_type = #{irrType},</if>
                    <if test="installType != null">install_type = #{installType},</if>
                    <if test="latd != null">latd = #{latd},</if>
                    <if test="lgtd != null">lgtd = #{lgtd},</if>
                    <if test="instaCapicaty != null">insta_capicaty = #{instaCapicaty},</if>
                    <if test="desInEl != null">des_in_el = #{desInEl},</if>
                    <if test="topInEl != null">top_in_el = #{topInEl},</if>
                    <if test="lowInEl != null">low_in_el = #{lowInEl},</if>
                    <if test="normalInEl != null">normal_in_el = #{normalInEl},</if>
                    <if test="desOutEl != null">des_out_el = #{desOutEl},</if>
                    <if test="topOutEl != null">top_out_el = #{topOutEl},</if>
                    <if test="lowOutEl != null">low_out_el = #{lowOutEl},</if>
                    <if test="normalOutEl != null">normal_out_el = #{normalOutEl},</if>
                    <if test="note != null">note = #{note},</if>
                    <if test="url != null">url = #{url},</if>
                    <if test="zpll != null">zpll = #{zpll},</if>
                    <if test="cpll != null">cpll = #{cpll},</if>
                    <if test="addvcd != null">addvcd = #{addvcd},</if>
                    <if test="pidCode != null">pid_code = #{pidCode},</if>
                    <if test="vmBCd != null">vm_b_cd = #{vmBCd},</if>
                    <if test="wwZ != null">ww_z = #{wwZ},</if>
                    <if test="swZ != null">sw_z = #{swZ},</if>
                    <if test="raincd != null">raincd = #{raincd},</if>
                    <if test="wdwcd != null">wdwcd = #{wdwcd},</if>
                    <if test="wupcd != null">wupcd = #{wupcd},</if>
                    <if test="hisMaxZ != null">his_max_z = #{hisMaxZ},</if>
                    <if test="allAvgZ != null">all_avg_z = #{allAvgZ},</if>
                    <if test="stDesc != null">st_desc = #{stDesc},</if>
                    <if test="hisRt != null">his_rt = #{hisRt},</if>
                    <if test="sumRt != null">sum_rt = #{sumRt},</if>
                    <if test="nowRt != null">now_rt = #{nowRt},</if>
                    <if test="hisFw != null">his_fw = #{hisFw},</if>
                    <if test="sumFw != null">sum_fw = #{sumFw},</if>
                    <if test="nowFw != null">now_fw = #{nowFw},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="insPow != null">ins_pow = #{insPow},</if>
                    <if test="wascd != null">wascd = #{wascd},</if>
                    <if test="source != null">source = #{source},</if>
        </trim>
        where str_code = #{strCode}
    </update>

    <delete id="deleteJhirPustBByStrCode" parameterType="String">
        delete from jhir_pust_b where str_code = #{strCode}
    </delete>

    <delete id="deleteJhirPustBByStrCodes" parameterType="String">
        delete from jhir_pust_b where str_code in
        <foreach item="strCode" collection="array" open="(" separator="," close=")">
            #{strCode}
        </foreach>
    </delete>
</mapper>
