<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.DataApiMountMapper">
    
    <resultMap type="com.jhdr.projectManagement.domain.DataApiMount" id="DataApiMountResult">
        <result property="id"    column="id"    />
        <result property="path"    column="path"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectDataApiMountVo">
        select id, path, name, status from data_api_mount
    </sql>

    <select id="selectDataApiMountList" parameterType="com.jhdr.projectManagement.domain.DataApiMount" resultMap="DataApiMountResult">
        <include refid="selectDataApiMountVo"/>
        <where>  
        </where>
    </select>

    <select id="selectDataApiMountById" parameterType="Integer" resultMap="DataApiMountResult">
        <include refid="selectDataApiMountVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataApiMount" parameterType="com.jhdr.projectManagement.domain.DataApiMount" useGeneratedKeys="true" keyProperty="id">
        insert into data_api_mount
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="path != null">path,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="path != null">#{path},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateDataApiMount" parameterType="com.jhdr.projectManagement.domain.DataApiMount">
        update data_api_mount
        <trim prefix="SET" suffixOverrides=",">
            <if test="path != null">path = #{path},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataApiMountById" parameterType="Integer">
        delete from data_api_mount where id = #{id}
    </delete>

    <delete id="deleteDataApiMountByIds" parameterType="String">
        delete from data_api_mount where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
