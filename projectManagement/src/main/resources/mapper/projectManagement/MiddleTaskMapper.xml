<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.MiddleTaskMapper">
    
    <resultMap type="com.jhdr.projectManagement.domain.MiddleTask" id="MiddleTaskResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="groups"    column="groups"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="cycle"    column="cycle"    />
    </resultMap>

    <sql id="selectMiddleTaskVo">
        select id, code, name, groups, update_time, status, cycle from middle_task
    </sql>

    <select id="selectMiddleTaskList" resultMap="MiddleTaskResult">
        <include refid="selectMiddleTaskVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="groups != null  and groups != ''"> and groups = #{groups}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectMiddleTaskById" parameterType="Integer" resultMap="MiddleTaskResult">
        <include refid="selectMiddleTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertMiddleTask" parameterType="com.jhdr.projectManagement.domain.MiddleTask" useGeneratedKeys="true" keyProperty="id">
        insert into middle_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="groups != null">groups,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="cycle != null">cycle,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="groups != null">#{groups},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="cycle != null">#{cycle},</if>
         </trim>
    </insert>

    <update id="updateMiddleTask" parameterType="com.jhdr.projectManagement.domain.MiddleTask">
        update middle_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="groups != null">groups = #{groups},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMiddleTaskById" parameterType="Integer">
        delete from middle_task where id = #{id}
    </delete>

    <delete id="deleteMiddleTaskByIds" parameterType="String">
        delete from middle_task where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
