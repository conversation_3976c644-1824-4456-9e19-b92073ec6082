<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.DataCleanPlanMapper">
    
    <resultMap type="com.jhdr.projectManagement.domain.DataCleanPlan" id="DataCleanPlanResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="ruleName"    column="rule_name"    />
        <result property="content"    column="content"    />
        <result property="upData"    column="up_data"    />
        <result property="dowmData"    column="dowm_data"    />
        <result property="isUse"    column="is_use"    />
    </resultMap>

    <sql id="selectDataCleanPlanVo">
        select id, code, name, rule_name, content, up_data, dowm_data, is_use from data_clean_plan
    </sql>

    <select id="selectDataCleanPlanList" parameterType="com.jhdr.projectManagement.domain.DataCleanPlan" resultMap="DataCleanPlanResult">
        <include refid="selectDataCleanPlanVo"/>
        <where>  
        </where>
    </select>
    
    <select id="selectDataCleanPlanById" parameterType="Integer" resultMap="DataCleanPlanResult">
        <include refid="selectDataCleanPlanVo"/>
        where id = #{id}
    </select>

    <insert id="insertDataCleanPlan" parameterType="com.jhdr.projectManagement.domain.DataCleanPlan" useGeneratedKeys="true" keyProperty="id">
        insert into data_clean_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">code,</if>
            <if test="name != null">name,</if>
            <if test="ruleName != null">rule_name,</if>
            <if test="content != null">content,</if>
            <if test="upData != null">up_data,</if>
            <if test="dowmData != null">dowm_data,</if>
            <if test="isUse != null">is_use,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="ruleName != null">#{ruleName},</if>
            <if test="content != null">#{content},</if>
            <if test="upData != null">#{upData},</if>
            <if test="dowmData != null">#{dowmData},</if>
            <if test="isUse != null">#{isUse},</if>
         </trim>
    </insert>

    <update id="updateDataCleanPlan" parameterType="com.jhdr.projectManagement.domain.DataCleanPlan">
        update data_clean_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="content != null">content = #{content},</if>
            <if test="upData != null">up_data = #{upData},</if>
            <if test="dowmData != null">dowm_data = #{dowmData},</if>
            <if test="isUse != null">is_use = #{isUse},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDataCleanPlanById" parameterType="Integer">
        delete from data_clean_plan where id = #{id}
    </delete>

    <delete id="deleteDataCleanPlanByIds" parameterType="String">
        delete from data_clean_plan where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
