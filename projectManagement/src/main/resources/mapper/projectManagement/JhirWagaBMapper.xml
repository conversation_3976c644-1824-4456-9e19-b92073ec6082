<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.JhirWagaBMapper">

    <resultMap type="com.jhdr.projectManagement.entity.po.JhirWagaBPo" id="JhirWagaBResult">
        <result property="strCode"    column="str_code"    />
        <result property="strName"    column="str_name"    />
        <result property="address"    column="address"    />
        <result property="wagaType"    column="waga_type"    />
        <result property="engWait"    column="eng_wait"    />
        <result property="majorType"    column="major_type"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="gaorNum"    column="gaor_num"    />
        <result property="desLockDisc"    column="des_lock_disc"    />
        <result property="gaorTotNetWid"    column="gaor_tot_net_wid"    />
        <result property="note"    column="note"    />
        <result property="url"    column="url"    />
        <result property="latd"    column="latd"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="gcgm"    column="gcgm"    />
        <result property="zya"    column="zya"    />
        <result property="zpll"    column="zpll"    />
        <result property="zgll"    column="zgll"    />
        <result property="addvcd"    column="addvcd"    />
        <result property="wupcd"    column="wupcd"    />
        <result property="wdwcd"    column="wdwcd"    />
        <result property="raincd"    column="raincd"    />
        <result property="wwZ"    column="ww_z"    />
        <result property="swZ"    column="sw_z"    />
        <result property="hisMaxZ"    column="his_max_z"    />
        <result property="allAvgZ"    column="all_avg_z"    />
        <result property="vmBCd"    column="vm_b_cd"    />
        <result property="stDesc"    column="st_desc"    />
        <result property="pidCode"    column="pid_code"    />
        <result property="hisRt"    column="his_rt"    />
        <result property="sumRt"    column="sum_rt"    />
        <result property="nowRt"    column="now_rt"    />
        <result property="hisFw"    column="his_fw"    />
        <result property="sumFw"    column="sum_fw"    />
        <result property="nowFw"    column="now_fw"    />
        <result property="status"    column="status"    />
        <result property="wascd"    column="wascd"    />
        <result property="source"    column="source"    />
    </resultMap>

    <sql id="selectJhirWagaBVo">
        select str_code, str_name, address, waga_type, eng_wait, major_type, complete_time, gaor_num, des_lock_disc, gaor_tot_net_wid, note, url, latd, lgtd, gcgm, zya, zpll, zgll, addvcd, wupcd, wdwcd, raincd, ww_z, sw_z, his_max_z, all_avg_z, vm_b_cd, st_desc, pid_code, his_rt, sum_rt, now_rt, his_fw, sum_fw, now_fw, status, wascd, source from jhir_waga_b
    </sql>

    <select id="selectJhirWagaBList"  resultType="com.jhdr.projectManagement.entity.vo.JhirWagaBVo">
        <include refid="selectJhirWagaBVo"/>
        <where>
            <if test="strName != null  and strName != ''"> and str_name like concat('%', #{strName}, '%')</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="wagaType != null  and wagaType != ''"> and waga_type = #{wagaType}</if>
            <if test="engWait != null  and engWait != ''"> and eng_wait = #{engWait}</if>
            <if test="majorType != null  and majorType != ''"> and major_type = #{majorType}</if>
            <if test="completeTime != null  and completeTime != ''"> and complete_time = #{completeTime}</if>
            <if test="gaorNum != null "> and gaor_num = #{gaorNum}</if>
            <if test="desLockDisc != null "> and des_lock_disc = #{desLockDisc}</if>
            <if test="gaorTotNetWid != null "> and gaor_tot_net_wid = #{gaorTotNetWid}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
            <if test="latd != null  and latd != ''"> and latd = #{latd}</if>
            <if test="lgtd != null  and lgtd != ''"> and lgtd = #{lgtd}</if>
            <if test="gcgm != null  and gcgm != ''"> and gcgm = #{gcgm}</if>
            <if test="zya != null  and zya != ''"> and zya = #{zya}</if>
            <if test="zpll != null  and zpll != ''"> and zpll = #{zpll}</if>
            <if test="zgll != null  and zgll != ''"> and zgll = #{zgll}</if>
            <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
            <if test="wupcd != null  and wupcd != ''"> and wupcd = #{wupcd}</if>
            <if test="wdwcd != null  and wdwcd != ''"> and wdwcd = #{wdwcd}</if>
            <if test="raincd != null  and raincd != ''"> and raincd = #{raincd}</if>
            <if test="wwZ != null "> and ww_z = #{wwZ}</if>
            <if test="swZ != null "> and sw_z = #{swZ}</if>
            <if test="hisMaxZ != null "> and his_max_z = #{hisMaxZ}</if>
            <if test="allAvgZ != null "> and all_avg_z = #{allAvgZ}</if>
            <if test="vmBCd != null  and vmBCd != ''"> and vm_b_cd = #{vmBCd}</if>
            <if test="stDesc != null  and stDesc != ''"> and st_desc = #{stDesc}</if>
            <if test="pidCode != null  and pidCode != ''"> and pid_code = #{pidCode}</if>
            <if test="hisRt != null "> and his_rt = #{hisRt}</if>
            <if test="sumRt != null "> and sum_rt = #{sumRt}</if>
            <if test="nowRt != null "> and now_rt = #{nowRt}</if>
            <if test="hisFw != null "> and his_fw = #{hisFw}</if>
            <if test="sumFw != null "> and sum_fw = #{sumFw}</if>
            <if test="nowFw != null "> and now_fw = #{nowFw}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="wascd != null  and wascd != ''"> and wascd = #{wascd}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
        </where>
    </select>

    <select id="selectJhirWagaBByStrCode"  resultType="com.jhdr.projectManagement.entity.vo.JhirWagaBVo">
            <include refid="selectJhirWagaBVo"/>
            where str_code = #{strCode}
    </select>

    <select id="selectJhirWagaBAllList" resultType="com.jhdr.projectManagement.entity.vo.JhirWagaBVo">
        select str_code, str_name, address, waga_type, eng_wait, major_type, complete_time, gaor_num, des_lock_disc, gaor_tot_net_wid, note, url, latd, lgtd, gcgm, zya, zpll, zgll, addvcd, wupcd, wdwcd, raincd, ww_z, sw_z, his_max_z, all_avg_z, vm_b_cd, st_desc, pid_code, his_rt, sum_rt, now_rt, his_fw, sum_fw, now_fw, status, wascd, source from jhir_waga_b
        <where>
            <if test="strName !=null ">
              and  str_name like concat('%',#{strName},'%')
            </if>
            <if test="strCode !=null ">
              and  str_code = #{strCode}
            </if>
            and status !=9
        </where>
         order by addvcd DESC,str_name DESC
    </select>
    <select id="getStrobeBList" resultType="com.jhdr.projectManagement.entity.po.JhirStrobeBPo">
        select strobe_code, str_code, strobe_type, strobe_mtr, strobe_h, strobe_w, strobe_wt, fty, strobe_bs_h, gpcd from jhir_strobe_b
        where strobe_code = #{strCode}
    </select>
    <select id="selectoneByStrCode" resultType="com.jhdr.projectManagement.entity.po.JhirWagaBPo">
        select str_code, str_name, address, waga_type, eng_wait, major_type, complete_time, gaor_num, des_lock_disc, gaor_tot_net_wid, note, url, latd, lgtd, gcgm, zya, zpll, zgll, addvcd, wupcd, wdwcd, raincd, ww_z, sw_z, his_max_z, all_avg_z, vm_b_cd, st_desc, pid_code, his_rt, sum_rt, now_rt, his_fw, sum_fw, now_fw, status, wascd, source from jhir_waga_b
        where str_code = #{strCode}
    </select>

    <insert id="insertJhirWagaB" parameterType="com.jhdr.projectManagement.entity.param.JhirWagaBAddParam">
        insert into jhir_waga_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="strCode != null">str_code,</if>
                    <if test="strName != null">str_name,</if>
                    <if test="address != null">address,</if>
                    <if test="wagaType != null">waga_type,</if>
                    <if test="engWait != null">eng_wait,</if>
                    <if test="majorType != null">major_type,</if>
                    <if test="completeTime != null">complete_time,</if>
                    <if test="gaorNum != null">gaor_num,</if>
                    <if test="desLockDisc != null">des_lock_disc,</if>
                    <if test="gaorTotNetWid != null">gaor_tot_net_wid,</if>
                    <if test="note != null">note,</if>
                    <if test="url != null">url,</if>
                    <if test="latd != null">latd,</if>
                    <if test="lgtd != null">lgtd,</if>
                    <if test="gcgm != null">gcgm,</if>
                    <if test="zya != null">zya,</if>
                    <if test="zpll != null">zpll,</if>
                    <if test="zgll != null">zgll,</if>
                    <if test="addvcd != null">addvcd,</if>
                    <if test="wupcd != null">wupcd,</if>
                    <if test="wdwcd != null">wdwcd,</if>
                    <if test="raincd != null">raincd,</if>
                    <if test="wwZ != null">ww_z,</if>
                    <if test="swZ != null">sw_z,</if>
                    <if test="hisMaxZ != null">his_max_z,</if>
                    <if test="allAvgZ != null">all_avg_z,</if>
                    <if test="vmBCd != null">vm_b_cd,</if>
                    <if test="stDesc != null">st_desc,</if>
                    <if test="pidCode != null">pid_code,</if>
                    <if test="hisRt != null">his_rt,</if>
                    <if test="sumRt != null">sum_rt,</if>
                    <if test="nowRt != null">now_rt,</if>
                    <if test="hisFw != null">his_fw,</if>
                    <if test="sumFw != null">sum_fw,</if>
                    <if test="nowFw != null">now_fw,</if>
                    <if test="status != null">status,</if>
                    <if test="wascd != null">wascd,</if>
                    <if test="source != null">source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="strCode != null">#{strCode},</if>
                    <if test="strName != null">#{strName},</if>
                    <if test="address != null">#{address},</if>
                    <if test="wagaType != null">#{wagaType},</if>
                    <if test="engWait != null">#{engWait},</if>
                    <if test="majorType != null">#{majorType},</if>
                    <if test="completeTime != null">#{completeTime},</if>
                    <if test="gaorNum != null">#{gaorNum},</if>
                    <if test="desLockDisc != null">#{desLockDisc},</if>
                    <if test="gaorTotNetWid != null">#{gaorTotNetWid},</if>
                    <if test="note != null">#{note},</if>
                    <if test="url != null">#{url},</if>
                    <if test="latd != null">#{latd},</if>
                    <if test="lgtd != null">#{lgtd},</if>
                    <if test="gcgm != null">#{gcgm},</if>
                    <if test="zya != null">#{zya},</if>
                    <if test="zpll != null">#{zpll},</if>
                    <if test="zgll != null">#{zgll},</if>
                    <if test="addvcd != null">#{addvcd},</if>
                    <if test="wupcd != null">#{wupcd},</if>
                    <if test="wdwcd != null">#{wdwcd},</if>
                    <if test="raincd != null">#{raincd},</if>
                    <if test="wwZ != null">#{wwZ},</if>
                    <if test="swZ != null">#{swZ},</if>
                    <if test="hisMaxZ != null">#{hisMaxZ},</if>
                    <if test="allAvgZ != null">#{allAvgZ},</if>
                    <if test="vmBCd != null">#{vmBCd},</if>
                    <if test="stDesc != null">#{stDesc},</if>
                    <if test="pidCode != null">#{pidCode},</if>
                    <if test="hisRt != null">#{hisRt},</if>
                    <if test="sumRt != null">#{sumRt},</if>
                    <if test="nowRt != null">#{nowRt},</if>
                    <if test="hisFw != null">#{hisFw},</if>
                    <if test="sumFw != null">#{sumFw},</if>
                    <if test="nowFw != null">#{nowFw},</if>
                    <if test="status != null">#{status},</if>
                    <if test="wascd != null">#{wascd},</if>
                    <if test="source != null">#{source},</if>
        </trim>
    </insert>

    <update id="updateJhirWagaB" parameterType="com.jhdr.projectManagement.entity.param.JhirWagaBEditParam">
        update jhir_waga_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="strName != null">str_name = #{strName},</if>
                    <if test="address != null">address = #{address},</if>
                    <if test="wagaType != null">waga_type = #{wagaType},</if>
                    <if test="engWait != null">eng_wait = #{engWait},</if>
                    <if test="majorType != null">major_type = #{majorType},</if>
                    <if test="completeTime != null">complete_time = #{completeTime},</if>
                    <if test="gaorNum != null">gaor_num = #{gaorNum},</if>
                    <if test="desLockDisc != null">des_lock_disc = #{desLockDisc},</if>
                    <if test="gaorTotNetWid != null">gaor_tot_net_wid = #{gaorTotNetWid},</if>
                    <if test="note != null">note = #{note},</if>
                    <if test="url != null">url = #{url},</if>
                    <if test="latd != null">latd = #{latd},</if>
                    <if test="lgtd != null">lgtd = #{lgtd},</if>
                    <if test="gcgm != null">gcgm = #{gcgm},</if>
                    <if test="zya != null">zya = #{zya},</if>
                    <if test="zpll != null">zpll = #{zpll},</if>
                    <if test="zgll != null">zgll = #{zgll},</if>
                    <if test="addvcd != null">addvcd = #{addvcd},</if>
                    <if test="wupcd != null">wupcd = #{wupcd},</if>
                    <if test="wdwcd != null">wdwcd = #{wdwcd},</if>
                    <if test="raincd != null">raincd = #{raincd},</if>
                    <if test="wwZ != null">ww_z = #{wwZ},</if>
                    <if test="swZ != null">sw_z = #{swZ},</if>
                    <if test="hisMaxZ != null">his_max_z = #{hisMaxZ},</if>
                    <if test="allAvgZ != null">all_avg_z = #{allAvgZ},</if>
                    <if test="vmBCd != null">vm_b_cd = #{vmBCd},</if>
                    <if test="stDesc != null">st_desc = #{stDesc},</if>
                    <if test="pidCode != null">pid_code = #{pidCode},</if>
                    <if test="hisRt != null">his_rt = #{hisRt},</if>
                    <if test="sumRt != null">sum_rt = #{sumRt},</if>
                    <if test="nowRt != null">now_rt = #{nowRt},</if>
                    <if test="hisFw != null">his_fw = #{hisFw},</if>
                    <if test="sumFw != null">sum_fw = #{sumFw},</if>
                    <if test="nowFw != null">now_fw = #{nowFw},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="wascd != null">wascd = #{wascd},</if>
                    <if test="source != null">source = #{source},</if>
        </trim>
        where str_code = #{strCode}
    </update>

    <delete id="deleteJhirWagaBByStrCode" parameterType="String">
        delete from jhir_waga_b where str_code = #{strCode}
    </delete>

    <delete id="deleteJhirWagaBByStrCodes" parameterType="String">
        delete from jhir_waga_b where str_code in
        <foreach item="strCode" collection="array" open="(" separator="," close=")">
            #{strCode}
        </foreach>
    </delete>
</mapper>
