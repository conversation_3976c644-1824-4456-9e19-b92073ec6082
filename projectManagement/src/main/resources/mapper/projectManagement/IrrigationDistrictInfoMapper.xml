<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.projectManagement.mapper.IrrigationDistrictInfoMapper">

    <resultMap type="com.jhdr.projectManagement.entity.po.IrrigationDistrictInfoPo" id="IrrigationDistrictInfoResult">
        <result property="id"    column="id"    />
        <result property="irrigationCode"    column="irrigation_code"    />
        <result property="adminArea"    column="admin_area"    />
        <result property="designedIrrigationArea"    column="designed_irrigation_area"    />
        <result property="effectiveIrrigationArea"    column="effective_irrigation_area"    />
        <result property="croplandArea"    column="cropland_area"    />
        <result property="profile"    column="profile"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="channelLength"    column="channel_length"    />
        <result property="artesianArea"    column="artesian_area"    />
        <result property="waterLiftingArea"    column="water_lifting_area"    />
        <result property="waterPumpStation"    column="water_pump_station"    />
        <result property="culvertGate"    column="culvert_gate"    />
        <result property="url"    column="url"    />
    </resultMap>

    <sql id="selectIrrigationDistrictInfoVo">
        select id, irrigation_code, admin_area, designed_irrigation_area, effective_irrigation_area, cropland_area, profile, name,status,channel_length,artesian_area,water_lifting_area,water_pump_station,culvert_gate,url from irrigation_district_info
    </sql>

    <select id="selectIrrigationDistrictInfoList"  resultType="com.jhdr.projectManagement.entity.vo.IrrigationDistrictInfoVo">
        <include refid="selectIrrigationDistrictInfoVo"/>
        <where>
            <if test="irrigationCode != null  and irrigationCode != ''"> and irrigation_code = #{irrigationCode}</if>
            <if test="adminArea != null  and adminArea != ''"> and admin_area = #{adminArea}</if>
            <if test="designedIrrigationArea != null "> and designed_irrigation_area = #{designedIrrigationArea}</if>
            <if test="effectiveIrrigationArea != null "> and effective_irrigation_area = #{effectiveIrrigationArea}</if>
            <if test="croplandArea != null "> and cropland_area = #{croplandArea}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="channelLength != null  and channelLength != ''"> and channel_length = #{channelLength}</if>
            <if test="artesianArea != null  and artesianArea != ''"> and artesian_area = #{artesianArea}</if>
            <if test="waterLiftingArea != null  and waterLiftingArea != ''"> and water_lifting_area = #{waterLiftingArea}</if>
            <if test="waterPumpStation != null  and waterPumpStation != ''"> and water_pump_station = #{waterPumpStation}</if>
            <if test="culvertGate != null  and culvertGate != ''"> and culvert_gate = #{culvertGate}</if>
            <if test="url != null  and url != ''"> and url = #{url}</if>
        </where>
    </select>

    <select id="selectIrrigationDistrictInfoById"  resultType="com.jhdr.projectManagement.entity.vo.IrrigationDistrictInfoVo">
            <include refid="selectIrrigationDistrictInfoVo"/>
            where id = #{id}
    </select>
    <select id="selectIrrigationAllList"
            resultType="com.jhdr.projectManagement.entity.vo.IrrigationDistrictInfoVo">
        select id, irrigation_code, admin_area, designed_irrigation_area, effective_irrigation_area, cropland_area, profile, name,status,channel_length,artesian_area,water_lifting_area,water_pump_station,culvert_gate,url from irrigation_district_info
        <where>
            <if test="name != null">
             and   name like concat('%',#{name},'%')
            </if>
            <if test="irrigationCode != null">
              and  irrigation_code =#{irrigationCode}
            </if>
            and status !=9
        </where>
        order by admin_area desc
    </select>

    <insert id="insertIrrigationDistrictInfo" parameterType="com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoAddParam">
        insert into irrigation_district_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="irrigationCode != null">irrigation_code,</if>
                    <if test="adminArea != null">admin_area,</if>
                    <if test="designedIrrigationArea != null">designed_irrigation_area,</if>
                    <if test="effectiveIrrigationArea != null">effective_irrigation_area,</if>
                    <if test="croplandArea != null">cropland_area,</if>
                    <if test="profile != null">profile,</if>
                    <if test="name != null">name,</if>
                    <if test="status != null">status,</if>
                    <if test="channelLength != null">channel_length,</if>
                    <if test="artesianArea != null">artesian_area,</if>
                    <if test="waterLiftingArea != null">water_lifting_area,</if>
                    <if test="waterPumpStation != null">water_pump_station,</if>
                    <if test="culvertGate != null">culvert_gate,</if>
                    <if test="url != null">url,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="irrigationCode != null">#{irrigationCode},</if>
                    <if test="adminArea != null">#{adminArea},</if>
                    <if test="designedIrrigationArea != null">#{designedIrrigationArea},</if>
                    <if test="effectiveIrrigationArea != null">#{effectiveIrrigationArea},</if>
                    <if test="croplandArea != null">#{croplandArea},</if>
                    <if test="profile != null">#{profile},</if>
                    <if test="name != null">#{name},</if>
                    <if test="status != null">#{status},</if>
                    <if test="channelLength != null">#{channelLength},</if>
                    <if test="artesianArea != null">#{artesianArea},</if>
                    <if test="waterLiftingArea != null">#{waterLiftingArea},</if>
                    <if test="waterPumpStation != null">#{waterPumpStation},</if>
                    <if test="culvertGate != null">#{culvertGate},</if>
                    <if test="url != null">#{url},</if>


        </trim>
    </insert>

    <update id="updateIrrigationDistrictInfo" parameterType="com.jhdr.projectManagement.entity.param.IrrigationDistrictInfoEditParam">
        update irrigation_district_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="irrigationCode != null">irrigation_code = #{irrigationCode},</if>
                    <if test="adminArea != null">admin_area = #{adminArea},</if>
                    <if test="designedIrrigationArea != null">designed_irrigation_area = #{designedIrrigationArea},</if>
                    <if test="effectiveIrrigationArea != null">effective_irrigation_area = #{effectiveIrrigationArea},</if>
                    <if test="croplandArea != null">cropland_area = #{croplandArea},</if>
                    <if test="profile != null">profile = #{profile},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="channelLength != null">channel_length = #{channelLength},</if>
                    <if test="artesianArea != null">artesian_area = #{artesianArea},</if>
                    <if test="waterLiftingArea != null">water_lifting_area = #{waterLiftingArea},</if>
                    <if test="waterPumpStation != null">water_pump_station = #{waterPumpStation},</if>
                    <if test="culvertGate != null">culvert_gate = #{culvertGate},</if>
                    <if test="url != null">url = #{url},</if>

        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIrrigationDistrictInfoById" parameterType="Long">
        delete from irrigation_district_info where id = #{id}
    </delete>

    <delete id="deleteIrrigationDistrictInfoByIds" parameterType="String">
        delete from irrigation_district_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
