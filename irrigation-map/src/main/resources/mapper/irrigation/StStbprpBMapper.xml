<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.StStbprpBMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.StStbprpBPo" id="StStbprpBResult">
        <result property="STCD"    column="STCD"    />
        <result property="STNM"    column="STNM"    />
        <result property="RVNM"    column="RVNM"    />
        <result property="HNNM"    column="HNNM"    />
        <result property="BSNM"    column="BSNM"    />
        <result property="LGTD"    column="LGTD"    />
        <result property="LTTD"    column="LTTD"    />
        <result property="STLC"    column="STLC"    />
        <result property="ADDVCD"    column="ADDVCD"    />
        <result property="DTMNM"    column="DTMNM"    />
        <result property="DTMEL"    column="DTMEL"    />
        <result property="DTPR"    column="DTPR"    />
        <result property="STTP"    column="STTP"    />
        <result property="FRGRD"    column="FRGRD"    />
        <result property="ESSTYM"    column="ESSTYM"    />
        <result property="BGFRYM"    column="BGFRYM"    />
        <result property="ATCUNIT"    column="ATCUNIT"    />
        <result property="ADMAUTH"    column="ADMAUTH"    />
        <result property="LOCALITY"    column="LOCALITY"    />
        <result property="STBK"    column="STBK"    />
        <result property="STAZT"    column="STAZT"    />
        <result property="DSTRVM"    column="DSTRVM"    />
        <result property="DRNA"    column="DRNA"    />
        <result property="PHCD"    column="PHCD"    />
        <result property="USFL"    column="USFL"    />
        <result property="COMMENTS"    column="COMMENTS"    />
        <result property="MODITIME"    column="MODITIME"    />
    </resultMap>

    <sql id="selectStStbprpBVo">
        select STCD, STNM, RVNM, HNNM, BSNM, LGTD, LTTD, STLC, ADDVCD, DTMNM, DTMEL, DTPR, STTP, FRGRD, ESSTYM, BGFRYM, ATCUNIT, ADMAUTH, LOCALITY, STBK, STAZT, DSTRVM, DRNA, PHCD, USFL, COMMENTS, MODITIME from st_stbprp_b
    </sql>

    <select id="selectStStbprpBList"  resultType="com.jhdr.irrigation.entity.vo.StStbprpBVo">
        <include refid="selectStStbprpBVo"/>
        <where>
            <if test="STNM != null  and STNM != ''"> and STNM = #{STNM}</if>
            <if test="RVNM != null  and RVNM != ''"> and RVNM = #{RVNM}</if>
            <if test="HNNM != null  and HNNM != ''"> and HNNM = #{HNNM}</if>
            <if test="BSNM != null  and BSNM != ''"> and BSNM = #{BSNM}</if>
            <if test="LGTD != null "> and LGTD = #{LGTD}</if>
            <if test="LTTD != null "> and LTTD = #{LTTD}</if>
            <if test="STLC != null  and STLC != ''"> and STLC = #{STLC}</if>
            <if test="ADDVCD != null  and ADDVCD != ''"> and ADDVCD = #{ADDVCD}</if>
            <if test="DTMNM != null  and DTMNM != ''"> and DTMNM = #{DTMNM}</if>
            <if test="DTMEL != null "> and DTMEL = #{DTMEL}</if>
            <if test="DTPR != null "> and DTPR = #{DTPR}</if>
            <if test="STTP != null  and STTP != ''"> and STTP = #{STTP}</if>
            <if test="FRGRD != null  and FRGRD != ''"> and FRGRD = #{FRGRD}</if>
            <if test="ESSTYM != null  and ESSTYM != ''"> and ESSTYM = #{ESSTYM}</if>
            <if test="BGFRYM != null  and BGFRYM != ''"> and BGFRYM = #{BGFRYM}</if>
            <if test="ATCUNIT != null  and ATCUNIT != ''"> and ATCUNIT = #{ATCUNIT}</if>
            <if test="ADMAUTH != null  and ADMAUTH != ''"> and ADMAUTH = #{ADMAUTH}</if>
            <if test="LOCALITY != null  and LOCALITY != ''"> and LOCALITY = #{LOCALITY}</if>
            <if test="STBK != null  and STBK != ''"> and STBK = #{STBK}</if>
            <if test="STAZT != null "> and STAZT = #{STAZT}</if>
            <if test="DSTRVM != null "> and DSTRVM = #{DSTRVM}</if>
            <if test="DRNA != null "> and DRNA = #{DRNA}</if>
            <if test="PHCD != null  and PHCD != ''"> and PHCD = #{PHCD}</if>
            <if test="USFL != null  and USFL != ''"> and USFL = #{USFL}</if>
            <if test="COMMENTS != null  and COMMENTS != ''"> and COMMENTS = #{COMMENTS}</if>
            <if test="MODITIME != null  and MODITIME != ''"> and MODITIME = #{MODITIME}</if>
        </where>
    </select>

    <select id="selectStStbprpBBySTCD"  resultType="com.jhdr.irrigation.entity.vo.StStbprpBVo">
        select STCD, STNM, RVNM, HNNM, BSNM, LGTD, LTTD, STLC,
               ADDVCD, DTMNM, DTMEL, DTPR, STTP, FRGRD, ESSTYM,
               BGFRYM, ATCUNIT, ADMAUTH, LOCALITY, STBK, STAZT,
               DSTRVM, DRNA, PHCD, USFL, COMMENTS, MODITIME,isz,iswz from st_stbprp_b

        where STCD = #{STCD}
    </select>
    <!---->
    <select id="selectRegionTreeList" resultType="com.jhdr.irrigation.entity.vo.StRiverRegionDataVo">
        SELECT DISTINCT
            sad."ADDVCD" AS stcd,
            '0' AS "addvcd",
            sad."ADDVNM" AS "stnm",
            '' "LGTD",
            '' "LTTD"
        FROM
            st_addvcd_d sad
                LEFT JOIN st_stbprp_b ssb ON ssb.addvcd = sad."ADDVCD"
                AND ssb."STTP" = #{sttp}
        WHERE
            ssb."ADDVCD" IS NOT NULL UNION ALL
        SELECT
            ssb.stcd,
            ssb.addvcd,
            ssb.stnm,
            gdd."LGTD",
            gdd."LTTD"
        FROM
            st_stbprp_b ssb
                LEFT JOIN gis_datas_d gdd ON ssb."STCD" = gdd."CD"
        WHERE
            ssb."ADDVCD" IS NOT NULL
          AND ssb."ADDVCD" != ''
	AND gdd."LTTD" IS NOT NULL
	AND ssb."STTP" = #{sttp}
    </select>
    <select id="selectRegionWaterTreeList" resultType="com.jhdr.irrigation.entity.vo.StRiverRegionDataVo">
        SELECT DISTINCT
            sad."ADDVCD" AS stcd,
            '0' AS "addvcd",
            sad."ADDVNM" AS "stnm",
            '' "LGTD",
            '' "LTTD"
        FROM
            st_addvcd_d sad
                LEFT JOIN st_stbprp_b ssb ON ssb.addvcd = sad."ADDVCD"
                AND  ( ssb.isz=1 or ssb.iswz =1)
        WHERE
            ssb."ADDVCD" IS NOT NULL UNION ALL
        SELECT
            ssb.stcd,
            ssb.addvcd,
            ssb.stnm,
            gdd."LGTD",
            gdd."LTTD"
        FROM
            st_stbprp_b ssb
                LEFT JOIN gis_datas_d gdd ON ssb."STCD" = gdd."CD"
        WHERE
            ssb."ADDVCD" IS NOT NULL
          AND ssb."ADDVCD" != ''
	AND gdd."LTTD" IS NOT NULL and gdd."GISSIGN" = 'SW'
	AND ( ssb.isz=1 or ssb.iswz =1)
    </select>

    <insert id="insertStStbprpB" parameterType="com.jhdr.irrigation.entity.param.StStbprpBAddParam">
        insert into st_stbprp_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="STCD != null">STCD,</if>
                    <if test="STNM != null">STNM,</if>
                    <if test="RVNM != null">RVNM,</if>
                    <if test="HNNM != null">HNNM,</if>
                    <if test="BSNM != null">BSNM,</if>
                    <if test="LGTD != null">LGTD,</if>
                    <if test="LTTD != null">LTTD,</if>
                    <if test="STLC != null">STLC,</if>
                    <if test="ADDVCD != null">ADDVCD,</if>
                    <if test="DTMNM != null">DTMNM,</if>
                    <if test="DTMEL != null">DTMEL,</if>
                    <if test="DTPR != null">DTPR,</if>
                    <if test="STTP != null">STTP,</if>
                    <if test="FRGRD != null">FRGRD,</if>
                    <if test="ESSTYM != null">ESSTYM,</if>
                    <if test="BGFRYM != null">BGFRYM,</if>
                    <if test="ATCUNIT != null">ATCUNIT,</if>
                    <if test="ADMAUTH != null">ADMAUTH,</if>
                    <if test="LOCALITY != null and LOCALITY != ''">LOCALITY,</if>
                    <if test="STBK != null">STBK,</if>
                    <if test="STAZT != null">STAZT,</if>
                    <if test="DSTRVM != null">DSTRVM,</if>
                    <if test="DRNA != null">DRNA,</if>
                    <if test="PHCD != null">PHCD,</if>
                    <if test="USFL != null">USFL,</if>
                    <if test="COMMENTS != null">COMMENTS,</if>
                    <if test="MODITIME != null">MODITIME,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="STCD != null">#{STCD},</if>
                    <if test="STNM != null">#{STNM},</if>
                    <if test="RVNM != null">#{RVNM},</if>
                    <if test="HNNM != null">#{HNNM},</if>
                    <if test="BSNM != null">#{BSNM},</if>
                    <if test="LGTD != null">#{LGTD},</if>
                    <if test="LTTD != null">#{LTTD},</if>
                    <if test="STLC != null">#{STLC},</if>
                    <if test="ADDVCD != null">#{ADDVCD},</if>
                    <if test="DTMNM != null">#{DTMNM},</if>
                    <if test="DTMEL != null">#{DTMEL},</if>
                    <if test="DTPR != null">#{DTPR},</if>
                    <if test="STTP != null">#{STTP},</if>
                    <if test="FRGRD != null">#{FRGRD},</if>
                    <if test="ESSTYM != null">#{ESSTYM},</if>
                    <if test="BGFRYM != null">#{BGFRYM},</if>
                    <if test="ATCUNIT != null">#{ATCUNIT},</if>
                    <if test="ADMAUTH != null">#{ADMAUTH},</if>
                    <if test="LOCALITY != null and LOCALITY != ''">#{LOCALITY},</if>
                    <if test="STBK != null">#{STBK},</if>
                    <if test="STAZT != null">#{STAZT},</if>
                    <if test="DSTRVM != null">#{DSTRVM},</if>
                    <if test="DRNA != null">#{DRNA},</if>
                    <if test="PHCD != null">#{PHCD},</if>
                    <if test="USFL != null">#{USFL},</if>
                    <if test="COMMENTS != null">#{COMMENTS},</if>
                    <if test="MODITIME != null">#{MODITIME},</if>
        </trim>
    </insert>

    <update id="updateStStbprpB" parameterType="com.jhdr.irrigation.entity.param.StStbprpBEditParam">
        update st_stbprp_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="STNM != null">STNM = #{STNM},</if>
                    <if test="RVNM != null">RVNM = #{RVNM},</if>
                    <if test="HNNM != null">HNNM = #{HNNM},</if>
                    <if test="BSNM != null">BSNM = #{BSNM},</if>
                    <if test="LGTD != null">LGTD = #{LGTD},</if>
                    <if test="LTTD != null">LTTD = #{LTTD},</if>
                    <if test="STLC != null">STLC = #{STLC},</if>
                    <if test="ADDVCD != null">ADDVCD = #{ADDVCD},</if>
                    <if test="DTMNM != null">DTMNM = #{DTMNM},</if>
                    <if test="DTMEL != null">DTMEL = #{DTMEL},</if>
                    <if test="DTPR != null">DTPR = #{DTPR},</if>
                    <if test="STTP != null">STTP = #{STTP},</if>
                    <if test="FRGRD != null">FRGRD = #{FRGRD},</if>
                    <if test="ESSTYM != null">ESSTYM = #{ESSTYM},</if>
                    <if test="BGFRYM != null">BGFRYM = #{BGFRYM},</if>
                    <if test="ATCUNIT != null">ATCUNIT = #{ATCUNIT},</if>
                    <if test="ADMAUTH != null">ADMAUTH = #{ADMAUTH},</if>
                    <if test="LOCALITY != null and LOCALITY != ''">LOCALITY = #{LOCALITY},</if>
                    <if test="STBK != null">STBK = #{STBK},</if>
                    <if test="STAZT != null">STAZT = #{STAZT},</if>
                    <if test="DSTRVM != null">DSTRVM = #{DSTRVM},</if>
                    <if test="DRNA != null">DRNA = #{DRNA},</if>
                    <if test="PHCD != null">PHCD = #{PHCD},</if>
                    <if test="USFL != null">USFL = #{USFL},</if>
                    <if test="COMMENTS != null">COMMENTS = #{COMMENTS},</if>
                    <if test="MODITIME != null">MODITIME = #{MODITIME},</if>
        </trim>
        where STCD = #{STCD}
    </update>

    <delete id="deleteStStbprpBBySTCD" parameterType="String">
        delete from st_stbprp_b where STCD = #{STCD}
    </delete>

    <delete id="deleteStStbprpBBySTCDs" parameterType="String">
        delete from st_stbprp_b where STCD in
        <foreach item="STCD" collection="array" open="(" separator="," close=")">
            #{STCD}
        </foreach>
    </delete>
</mapper>
