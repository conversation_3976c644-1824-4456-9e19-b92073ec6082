<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.GisDatasDMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.GisDatasDPo" id="GisDatasDResult">
        <result property="GUID" column="GUID"/>
        <result property="CD" column="CD"/>
        <result property="NM" column="NM"/>
        <result property="GISDATA" column="GISDATA"/>
        <result property="GISSIGN" column="GISSIGN"/>
        <result property="LGTD" column="LGTD"/>
        <result property="LTTD" column="LTTD"/>
    </resultMap>

    <resultMap type="java.util.Map" id="WeatherDataResult">
        <result property="weather_condition" column="weather_condition"/>
        <result property="temperature" column="temperature"/>
        <result property="humidity" column="humidity"/>
        <result property="wind_direction" column="wind_direction"/>
        <result property="wind_speed" column="wind_speed"/>
    </resultMap>

    <sql id="selectGisDatasDVo">
        select GUID, CD, NM, GISDATA, GISSIGN, LGTD, LTTD
        from chxh.gis_datas_d
    </sql>

    <select id="selectGisDatasDList" resultType="com.jhdr.irrigation.entity.vo.GisDatasDVo">
        <if test="GISSIGN == null or (GISSIGN !='BZ' and GISSIGN !='HZ' and GISSIGN !='YL' and GISSIGN !='SW' and  GISSIGN !='LL'
        and GISSIGN !='FHZ' and GISSIGN !='FSW' and  GISSIGN !='FQX' and  GISSIGN !='FSQ' and  GISSIGN !='FJHZ' and  GISSIGN !='FTHZ') ">
            ( SELECT
            GUID,
            CD,
            NM,
            GISDATA,
            GISSIGN,
            LGTD,
            LTTD,
            NULL AS tm,
            0 AS DRP,
            '' AS z,
            0 AS wptnex_day,
            0 AS q,source,0 PSQ,0 status
            FROM
            chxh.gis_datas_d
            <where>
                GISSIGN NOT IN ( 'BZ', 'HZ', 'SW', 'YL', 'LL', 'FHZ', 'FSW', 'FQX','FSQ','FJHZ','FTHZ' )
                <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

                <if test="GISSIGN != null  and GISSIGN != ''">and GISSIGN = #{GISSIGN}</if>
            </where>
            ORDER BY lgtd  )

            <if test="GISSIGN == null or (GISSIGN !=null and GISSIGN =='YL')">
                UNION ALL
                (
                SELECT
                gdd.GUID,
                gdd.cd,
                gdd.nm,
                gdd.GISDATA,
                gdd.GISSIGN,
                gdd.LGTD,
                gdd.LTTD,
                t2.tm,
                ifnull ( t2.DRP, '0' ) AS DRP,
                '' AS z,
                0 AS wptnex_day,
                0 AS q,gdd.source,0 PSQ,0 status
                FROM
                gis_datas_d gdd
                LEFT JOIN (
                SELECT
                stcd,
                tm,
                DRP
                FROM
                (
                SELECT ROW_NUMBER
                ( ) OVER ( PARTITION BY stcd ORDER BY IDTM DESC ) rown,
                stcd,
                IDTM AS tm,
                ACCP AS DRP
                FROM
                st_pstat_r
                WHERE
                STTDRCD = 1
                AND IDTM >= ADDTIME ( sysdate, INTERVAL '-1' DAY )
                )
                WHERE
                rown = 1
                ) t2 ON gdd.cd = t2.stcd
                <where>
                    gdd.GISSIGN = 'YL'

                    <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                    <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

                </where>
                ORDER BY  gdd.LGTD, t2.DRP DESC
                )
            </if>
            <if test="GISSIGN == null or (GISSIGN !=null and GISSIGN =='HZ')">
                UNION ALL
                (
                SELECT
                gdd.GUID,
                gdd.cd,
                gdd.nm,
                gdd.GISDATA,
                gdd.GISSIGN,
                gdd.LGTD,
                gdd.LTTD,
                NULL AS tm,
                 '0'  AS DRP,
                '' AS z,
                0 AS wptnex_day,
                0 AS q,gdd.source,0 PSQ,
                CASE
                WHEN jsrr.gtopn > 0 THEN
                '1' ELSE'0'
                END AS status
                FROM
                gis_datas_d gdd
                LEFT JOIN jh_slcrs_r_real jsrr  ON gdd.CD = jsrr.prjnmcd
                <where>
                    gdd.CD IS NOT NULL and   gdd.GISSIGN = 'HZ'
                    <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                    <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

                </where>
                ORDER BY  gdd.LGTD
                )
            </if>
            <if test="GISSIGN == null or (GISSIGN !=null and GISSIGN =='BZ')">
                UNION ALL
                (
                SELECT
                gdd.GUID,
                gdd.cd,
                gdd.nm,
                gdd.GISDATA,
                gdd.GISSIGN,
                gdd.LGTD,
                gdd.LTTD,
                NULL AS tm,
                 '0'  AS DRP,
                '' AS z,
                0 AS wptnex_day,
                0 AS q,gdd.source,0 PSQ,
                CASE
                WHEN jprr.omcn > 0 THEN
                1 ELSE 0
                END AS status
                FROM
                gis_datas_d gdd
                LEFT JOIN jh_pump_r_real jprr  ON gdd.CD = jprr.prjnmcd
                <where>
                    gdd.CD IS NOT NULL and   gdd.GISSIGN = 'BZ'
                    <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                    <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

                </where>
                ORDER BY  gdd.LGTD
                )
            </if>
            <if test="GISSIGN == null or (GISSIGN !=null and GISSIGN =='SW')">
                UNION ALL
                (
                SELECT
                gdd.GUID,
                gdd.cd,
                gdd.nm,
                gdd.GISDATA,
                gdd.GISSIGN,
                gdd.LGTD,
                gdd.LTTD,
                t2.tm,
                0 AS DRP,
                t2.z AS z,
                t2.wptnex_day,
                0 AS q,gdd.source,0 PSQ,0 status
                FROM
                gis_datas_d gdd
                LEFT JOIN (
                SELECT
                stcd,
                tm,
                z,
                wptnex_day
                FROM
                (
                SELECT ROW_NUMBER
                ( ) OVER ( PARTITION BY stcd ORDER BY tm DESC ) rown,
                stcd,
                tm,
                ROUND( z, 2 ) AS z,
                wptnex_day
                FROM
                st_river_r
                WHERE
                tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )
                )
                WHERE
                rown = 1
                ) t2 ON gdd.cd = t2.stcd
                <where>
                    gdd.GISSIGN = 'SW'
                    <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                    <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

                </where>
                ORDER BY gdd.LGTD,t2.z DESC
                )
            </if>
            <if test="GISSIGN == null or (GISSIGN !=null and GISSIGN =='LL')">
                UNION ALL
                (
                SELECT
                gdd.GUID,
                gdd.cd,
                gdd.nm,
                gdd.GISDATA,
                gdd.GISSIGN,
                gdd.LGTD,
                gdd.LTTD,
                t2.tm,
                0 AS DRP,
                null AS z,
                0 AS wptnex_day,
                ifnull ( t2.q, '0' ) AS q,gdd.source,ifnull ( t2.psq, '0' ) AS psq,0 status
                FROM
                gis_datas_d gdd
                LEFT JOIN (
                SELECT
                stcd,tm, q,PSQ
                FROM
                (
                SELECT ROW_NUMBER
                ( ) OVER ( PARTITION BY stcd ORDER BY tm DESC ) rown,
                stcd,
                TM ,
                0 q,
                PSQ
                FROM
                jh_flowtn_r
                WHERE
                tm >= ADDTIME ( sysdate, INTERVAL '-1' DAY )
                )
                WHERE
                rown = 1
                ) t2 ON gdd.cd = t2.stcd
                <where>
                    gdd.GISSIGN = 'LL'
                    <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                    <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

                </where>
                ORDER BY gdd.LGTD
                )
            </if>

        </if>
        <if test="GISSIGN == 'YL' ">
            SELECT
            gdd.GUID,
            gdd.GISDATA,
            gdd.GISSIGN,
            gdd.LGTD,
            gdd.LTTD,
            gdd.cd,
            gdd.nm,
            ifnull ( t2.DRP, '0' ) AS DRP,
            t2.tm,gdd.source
            FROM
            gis_datas_d gdd
            LEFT JOIN (
            SELECT
            stcd,
            tm,
            DRP
            FROM
            (
            SELECT ROW_NUMBER
            ( ) OVER ( PARTITION BY stcd ORDER BY IDTM DESC ) rown,
            stcd,
            IDTM AS tm,
            ACCP AS DRP
            FROM
            st_pstat_r
            WHERE
            STTDRCD = 1
            AND IDTM >= ADDTIME ( sysdate, INTERVAL '-1' DAY )
            )
            WHERE
            rown = 1
            ) t2 ON gdd.cd = t2.stcd
            <where>
                <if test="CD != null  and CD != ''">and gdd.CD = #{CD}</if>
                <if test="NM != null  and NM != ''">and gdd.NM like concat('%', #{NM}, '%')</if>

                <if test="GISSIGN != null  and GISSIGN != ''">and gdd.GISSIGN = #{GISSIGN}</if>
            </where>
            order by gdd.LGTD,t2.DRP desc
        </if>
        <if test="GISSIGN =='HZ' ">
            SELECT
            gdd.GUID,
            gdd.cd,
            gdd.nm,
            gdd.GISDATA,
            gdd.GISSIGN,
            gdd.LGTD,
            gdd.LTTD,
            NULL AS tm,
            '0'  AS DRP,
            '' AS z,
            0 AS wptnex_day,
            0 AS q,gdd.source,0 PSQ,
            CASE
            WHEN jsrr.gtopn > 0 THEN
            '1' ELSE'0'
            END AS status
            FROM
            gis_datas_d gdd
            LEFT JOIN jh_slcrs_r_real jsrr  ON gdd.CD = jsrr.prjnmcd
            <where>
                gdd.CD IS NOT NULL and   gdd.GISSIGN = 'HZ'
                <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

            </where>
            ORDER BY  gdd.LGTD
        </if>
        <if test="GISSIGN =='BZ' ">
            SELECT
            gdd.GUID,
            gdd.cd,
            gdd.nm,
            gdd.GISDATA,
            gdd.GISSIGN,
            gdd.LGTD,
            gdd.LTTD,
            NULL AS tm,
            '0'  AS DRP,
            '' AS z,
            0 AS wptnex_day,
            0 AS q,gdd.source,0 PSQ,
            CASE
            WHEN jprr.omcn > 0 THEN
            1 ELSE 0
            END AS status
            FROM
            gis_datas_d gdd
            LEFT JOIN jh_pump_r_real jprr  ON gdd.CD = jprr.prjnmcd
            <where>
                gdd.CD IS NOT NULL and   gdd.GISSIGN = 'BZ'
                <if test="CD != null  and CD != ''">and CD = #{CD}</if>
                <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

            </where>
            ORDER BY  gdd.LGTD
        </if>
        <if test="GISSIGN == 'SW' or GISSIGN =='FSW'">
            SELECT
            gdd.GUID, gdd.GISDATA, gdd.GISSIGN, gdd.LGTD, gdd.LTTD, gdd.cd,gdd.nm,ifnull(t2.z,'')as z ,t2.tm,t2.wptnex_day,gdd.source
            FROM
            gis_datas_d gdd LEFT JOIN (SELECT stcd,tm,z,wptnex_day
            FROM
            (
            SELECT ROW_NUMBER() OVER ( PARTITION BY stcd ORDER BY tm DESC ) rown,stcd,tm,ROUND(z, 2)AS z,wptnex_day
            FROM
            st_river_r
            WHERE
            tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )
            )
            WHERE
            rown = 1
            ) t2 ON gdd.cd = t2.stcd
            <where>
                <if test="CD != null  and CD != ''">and gdd.CD = #{CD}</if>
                <if test="NM != null  and NM != ''">and gdd.NM like concat('%', #{NM}, '%')</if>

                <if test="GISSIGN != null  and GISSIGN != ''">and gdd.GISSIGN = #{GISSIGN}</if>
            </where>
            order by gdd.LGTD, t2.z desc
        </if>
        <if test="GISSIGN == 'LL' ">
            SELECT
            gdd.GUID,gdd.GISDATA,gdd.GISSIGN,gdd.LGTD,gdd.LTTD,gdd.cd,gdd.nm,0 q,t2.z AS z,t2.tm,gdd.SOURCE,ifnull ( t2.PSQ, '0' ) AS PSQ
            FROM
            gis_datas_d gdd
            LEFT JOIN (
            SELECT
            stcd,tm,q,z,PSQ
            FROM
            (
            SELECT ROW_NUMBER
            ( ) OVER ( PARTITION BY stcd ORDER BY tm DESC ) rown,
            stcd,
            tm,
            0 q,
            '' z,
            PSQ
            FROM
            jh_flowtn_r
            WHERE
            tm >= ADDTIME ( sysdate, INTERVAL '-1' DAY )
            )
            WHERE
            rown = 1
            ) t2 ON gdd.cd = t2.stcd
            <where>
                <if test="CD != null  and CD != ''">and gdd.CD = #{CD}</if>
                <if test="NM != null  and NM != ''">and gdd.NM like concat('%', #{NM}, '%')</if>

                <if test="GISSIGN != null  and GISSIGN != ''">and gdd.GISSIGN = #{GISSIGN}</if>
            </where>
            order by gdd.LGTD
        </if>
        <if test="GISSIGN =='FHZ'  or  GISSIGN =='FQX' or  GISSIGN =='FSQ' or  GISSIGN =='FJHZ' or  GISSIGN =='FTHZ'">
        SELECT
        GUID,
        CD,
        NM,
        GISDATA,
        GISSIGN,
        LGTD,
        LTTD,
        NULL AS tm,
        0 AS DRP,
        '' AS z,
        0 AS wptnex_day,
        0 AS q,source,0 PSQ
        FROM
        chxh.gis_datas_d
        <where>
            <if test="CD != null  and CD != ''">and CD = #{CD}</if>
            <if test="NM != null  and NM != ''">and NM like concat('%', #{NM}, '%')</if>

            <if test="GISSIGN != null  and GISSIGN != ''">and GISSIGN = #{GISSIGN}</if>
        </where>
            order by LGTD
        </if>
    </select>

    <select id="selectGisDatasDByGUID" resultType="com.jhdr.irrigation.entity.vo.GisDatasDVo">
        <include refid="selectGisDatasDVo"/>
        where GUID = #{GUID}
    </select>
    <select id="getZ" resultType="com.jhdr.irrigation.entity.vo.JhFlowspRZVo">
        select swr.STCD, swr.TM,ROUND(swr.UPZ, 2) AS z,supwptnex_day as wptnexDay
        from st_was_r swr
        left join st_stbprp_b ssbp on swr.STCD = ssbp.STCD
        WHERE
            swr.tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR ) and  ssbp.iswz = 1
        and swr.stcd in
        <foreach item="cd" collection="list" open="(" separator="," close=")">
            #{cd}
        </foreach>

    </select>
    <select id="getWeatherDayList" resultType="java.lang.String">
        select weather_json from weather_data where  status = 1
    </select>
    <select id="getFloodData" resultType="com.jhdr.irrigation.entity.vo.GisDatasDVo">
        select
        id as GUID,
        longitude as LGTD,
        latitude as LTTD,
        code as cd,
        name as nm
        FROM
        opo_goods_station where code=#{code} limit 1
    </select>
    <select id="weatherGetData" resultType="java.lang.String">
        select weather_json from weather_data where location=#{location} and status = 1 limit 1
    </select>

    <insert id="insertGisDatasD" parameterType="com.jhdr.irrigation.entity.param.GisDatasDAddParam">
        insert into chxh.gis_datas_d
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="GUID != null">GUID,</if>
            <if test="CD != null">CD,</if>
            <if test="NM != null">NM,</if>
            <if test="GISDATA != null">GISDATA,</if>
            <if test="GISSIGN != null">GISSIGN,</if>
            <if test="LGTD != null">LGTD,</if>
            <if test="LTTD != null">LTTD,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="GUID != null">#{GUID},</if>
            <if test="CD != null">#{CD},</if>
            <if test="NM != null">#{NM},</if>
            <if test="GISDATA != null">#{GISDATA},</if>
            <if test="GISSIGN != null">#{GISSIGN},</if>
            <if test="LGTD != null">#{LGTD},</if>
            <if test="LTTD != null">#{LTTD},</if>
        </trim>
    </insert>

    <update id="updateGisDatasD" parameterType="com.jhdr.irrigation.entity.param.GisDatasDEditParam">
        update chxh.gis_datas_d
        <trim prefix="SET" suffixOverrides=",">
            <if test="CD != null">CD = #{CD},</if>
            <if test="NM != null">NM = #{NM},</if>
            <if test="GISDATA != null">GISDATA = #{GISDATA},</if>
            <if test="GISSIGN != null">GISSIGN = #{GISSIGN},</if>
            <if test="LGTD != null">LGTD = #{LGTD},</if>
            <if test="LTTD != null">LTTD = #{LTTD},</if>
        </trim>
        where GUID = #{GUID}
    </update>

    <delete id="deleteGisDatasDByGUID" parameterType="String">
        delete
        from chxh.gis_datas_d
        where GUID = #{GUID}
    </delete>

    <delete id="deleteGisDatasDByGUIDs" parameterType="String">
        delete from chxh.gis_datas_d where GUID in
        <foreach item="GUID" collection="array" open="(" separator="," close=")">
            #{GUID}
        </foreach>
    </delete>

    <!-- 获取当前天气 -->
    <select id="getCurrentWeather" resultMap="WeatherDataResult">
        SELECT
            json_extract_path_text(weather_json::json, 'daily', '0', 'textDay') as weather_condition,
            json_extract_path_text(weather_json::json, 'daily', '0', 'tempMax') as temperature,
            json_extract_path_text(weather_json::json, 'daily', '0', 'humidity') as humidity,
            json_extract_path_text(weather_json::json, 'daily', '0', 'windDirDay') as wind_direction,
            json_extract_path_text(weather_json::json, 'daily', '0', 'windScaleDay') as wind_speed
        FROM weather_data
        WHERE status = 1
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 获取枢纽水情 -->
    <select id="getHubWaterLevels" resultType="java.util.Map">
        SELECT
            stnm as station_code,
            stnm as station_name,
            z as upper_water_level,
            q as lower_water_level,
            'normal' as water_trend
        FROM chxh.jh_river_r
        WHERE tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
        AND stcd IN (SELECT stcd FROM chxh.jh_river_b WHERE sttp = 'RR')
    </select>

    <!-- 获取枢纽工情 -->
    <select id="getHubGateOperations" resultType="java.util.Map">
        SELECT
            stcd as gate_code,
            stnm as gate_name,
            state as status,
            opdrcd as open_degree
        FROM chxh.jh_slcrs_r
        WHERE tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
    </select>

    <!-- 获取雨量信息 -->
    <select id="getShangqiaoRainfall" resultType="java.lang.Double">
        SELECT drp as rainfall
        FROM chxh.jh_pptn_r
        WHERE stcd = 'SQ001'
        AND tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
    </select>

    <!-- 获取平均雨量 -->
    <select id="getAverageRainfall" resultType="java.lang.Double">
        SELECT AVG(drp) as rainfall
        FROM chxh.jh_pptn_r
        WHERE tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
    </select>

    <!-- 获取最大雨量站点 -->
    <select id="getMaxRainfallStation" resultType="java.util.Map">
        SELECT
            r.stcd as station_code,
            b.stnm as station_name,
            r.drp as rainfall,
            b.addvcd as region
        FROM chxh.jh_pptn_r r
        LEFT JOIN chxh.jh_pptn_b b ON r.stcd = b.stcd
        WHERE r.tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
        ORDER BY r.drp DESC
        LIMIT 1
    </select>

    <!-- 获取泵站开启数 -->
    <select id="getActivePumpStationsByRegion" resultType="java.util.Map">
        SELECT
            b.addvcd as region_code,
            b.addvnm as region_name,
            COUNT(*) as count
        FROM chxh.jh_pump_r r
        LEFT JOIN chxh.jh_pump_b b ON r.stcd = b.stcd
        WHERE r.tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
        AND r.state = 1
        GROUP BY b.addvcd, b.addvnm
        HAVING COUNT(*) > 0
    </select>

    <!-- 获取闸站开启数 -->
    <select id="getActiveSluiceStationsByRegion" resultType="java.util.Map">
        SELECT
            b.addvcd as region_code,
            b.addvnm as region_name,
            COUNT(*) as count
        FROM chxh.jh_slcrs_r r
        LEFT JOIN chxh.jh_slcrs_b b ON r.stcd = b.stcd
        WHERE r.tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
        AND r.state = 1
        GROUP BY b.addvcd, b.addvnm
        HAVING COUNT(*) > 0
    </select>

    <!-- 获取流量站累计值 -->
    <select id="getTotalFlow" resultType="java.lang.Double">
        SELECT SUM(q)
        FROM chxh.jh_river_r
        WHERE tm BETWEEN #{dateTime} AND #{dateTime} + INTERVAL '1 day'
        AND stcd IN (SELECT stcd FROM chxh.jh_river_b WHERE sttp = 'ZQ')
    </select>
    <!-- 获取闸站数据 -->
    <select id="getSluiceStationCounts" resultType="com.jhdr.irrigation.entity.vo.SituationCountyNumVo">
        SELECT
            COUNT(DISTINCT jprl.prjnmcd) AS num,
            sum(thrslq) as thrslq
        FROM
            chxh.jhir_waga_b bb
                LEFT JOIN chxh.jh_slcrs_r_real jprl ON jprl.prjnmcd = bb.str_code AND jprl.gtopn > 0
        WHERE
            bb.status = 1 and bb.str_code not in ('341204000095','108131001','341203000022','341623000096')
        <if test="requestType == 2">
            AND  #{lgtd} > bb.lgtd
        </if>
        <if test="requestType == 1">
            AND bb.lgtd > #{lgtd}
        </if>
    </select>
    <!-- 获取泵站数据 -->
    <select id="getPumpStationCounts" resultType="com.jhdr.irrigation.entity.vo.SituationCountyNumVo">
        SELECT
            COUNT(DISTINCT CASE WHEN jprl.omcn > 0 THEN jprl.prjnmcd END) AS num,
            sum(jprl.pmpq) as pmpq
        FROM
            chxh.jhir_pust_b jpb
                LEFT JOIN chxh.jh_pump_r_real jprl ON jprl.prjnmcd = jpb.str_code
        WHERE
            jpb.is_river_side = 1
          AND jpb.status = 1 and jpb.str_code not in ('340321000284','341623000028')
          <if test="requestType == 2">
            AND  #{lgtd} > jpb.lgtd
          </if>
          <if test="requestType == 1">
            AND jpb.lgtd > #{lgtd}
          </if>
    </select>
</mapper>
