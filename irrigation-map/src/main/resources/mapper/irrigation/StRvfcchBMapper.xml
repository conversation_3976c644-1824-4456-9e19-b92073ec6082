<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.StRvfcchBMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.StRvfcchBPo" id="StRvfcchBResult">
        <result property="STCD"    column="STCD"    />
        <result property="LDKEL"    column="LDKEL"    />
        <result property="RDKEL"    column="RDKEL"    />
        <result property="WRZ"    column="WRZ"    />
        <result property="WRQ"    column="WRQ"    />
        <result property="GRZ"    column="GRZ"    />
        <result property="GRQ"    column="GRQ"    />
        <result property="FLPQ"    column="FLPQ"    />
        <result property="OBHTZ"    column="OBHTZ"    />
        <result property="OBHTZTM"    column="OBHTZTM"    />
        <result property="IVHZ"    column="IVHZ"    />
        <result property="IVHZTM"    column="IVHZTM"    />
        <result property="OBMXQ"    column="OBMXQ"    />
        <result property="OBMXQTM"    column="OBMXQTM"    />
        <result property="IVMXQ"    column="IVMXQ"    />
        <result property="IVMXQTM"    column="IVMXQTM"    />
        <result property="HMXS"    column="HMXS"    />
        <result property="HMXSTM"    column="HMXSTM"    />
        <result property="HMXAVV"    column="HMXAVV"    />
        <result property="HMXAVVTM"    column="HMXAVVTM"    />
        <result property="HLZ"    column="HLZ"    />
        <result property="HLZTM"    column="HLZTM"    />
        <result property="HMNQ"    column="HMNQ"    />
        <result property="HMNQTM"    column="HMNQTM"    />
        <result property="TAZ"    column="TAZ"    />
        <result property="TAQ"    column="TAQ"    />
        <result property="LAZ"    column="LAZ"    />
        <result property="LAQ"    column="LAQ"    />
        <result property="SFZ"    column="SFZ"    />
        <result property="SFQ"    column="SFQ"    />
        <result property="MODITIME"    column="MODITIME"    />
    </resultMap>

    <sql id="selectStRvfcchBVo">
        select STCD, LDKEL, RDKEL, WRZ, WRQ, GRZ, GRQ, FLPQ, OBHTZ, OBHTZTM, IVHZ, IVHZTM, OBMXQ, OBMXQTM, IVMXQ, IVMXQTM, HMXS, HMXSTM, HMXAVV, HMXAVVTM, HLZ, HLZTM, HMNQ, HMNQTM, TAZ, TAQ, LAZ, LAQ, SFZ, SFQ, MODITIME from st_rvfcch_b
    </sql>

    <select id="selectStRvfcchBList"  resultType="com.jhdr.irrigation.entity.vo.StRvfcchBVo">
        <include refid="selectStRvfcchBVo"/>
        <where>
            <if test="LDKEL != null "> and LDKEL = #{LDKEL}</if>
            <if test="RDKEL != null "> and RDKEL = #{RDKEL}</if>
            <if test="WRZ != null "> and WRZ = #{WRZ}</if>
            <if test="WRQ != null "> and WRQ = #{WRQ}</if>
            <if test="GRZ != null "> and GRZ = #{GRZ}</if>
            <if test="GRQ != null "> and GRQ = #{GRQ}</if>
            <if test="FLPQ != null "> and FLPQ = #{FLPQ}</if>
            <if test="OBHTZ != null "> and OBHTZ = #{OBHTZ}</if>
            <if test="OBHTZTM != null  and OBHTZTM != ''"> and OBHTZTM = #{OBHTZTM}</if>
            <if test="IVHZ != null "> and IVHZ = #{IVHZ}</if>
            <if test="IVHZTM != null  and IVHZTM != ''"> and IVHZTM = #{IVHZTM}</if>
            <if test="OBMXQ != null "> and OBMXQ = #{OBMXQ}</if>
            <if test="OBMXQTM != null  and OBMXQTM != ''"> and OBMXQTM = #{OBMXQTM}</if>
            <if test="IVMXQ != null "> and IVMXQ = #{IVMXQ}</if>
            <if test="IVMXQTM != null  and IVMXQTM != ''"> and IVMXQTM = #{IVMXQTM}</if>
            <if test="HMXS != null "> and HMXS = #{HMXS}</if>
            <if test="HMXSTM != null  and HMXSTM != ''"> and HMXSTM = #{HMXSTM}</if>
            <if test="HMXAVV != null "> and HMXAVV = #{HMXAVV}</if>
            <if test="HMXAVVTM != null  and HMXAVVTM != ''"> and HMXAVVTM = #{HMXAVVTM}</if>
            <if test="HLZ != null "> and HLZ = #{HLZ}</if>
            <if test="HLZTM != null  and HLZTM != ''"> and HLZTM = #{HLZTM}</if>
            <if test="HMNQ != null "> and HMNQ = #{HMNQ}</if>
            <if test="HMNQTM != null  and HMNQTM != ''"> and HMNQTM = #{HMNQTM}</if>
            <if test="TAZ != null "> and TAZ = #{TAZ}</if>
            <if test="TAQ != null "> and TAQ = #{TAQ}</if>
            <if test="LAZ != null "> and LAZ = #{LAZ}</if>
            <if test="LAQ != null "> and LAQ = #{LAQ}</if>
            <if test="SFZ != null "> and SFZ = #{SFZ}</if>
            <if test="SFQ != null "> and SFQ = #{SFQ}</if>
            <if test="MODITIME != null  and MODITIME != ''"> and MODITIME = #{MODITIME}</if>
        </where>
    </select>

    <select id="selectStRvfcchBBySTCD"  resultType="com.jhdr.irrigation.entity.vo.StRvfcchBVo">
            <include refid="selectStRvfcchBVo"/>
            where STCD = #{STCD} limit 1
    </select>

    <insert id="insertStRvfcchB" parameterType="com.jhdr.irrigation.entity.param.StRvfcchBAddParam">
        insert into st_rvfcch_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="STCD != null">STCD,</if>
                    <if test="LDKEL != null">LDKEL,</if>
                    <if test="RDKEL != null">RDKEL,</if>
                    <if test="WRZ != null">WRZ,</if>
                    <if test="WRQ != null">WRQ,</if>
                    <if test="GRZ != null">GRZ,</if>
                    <if test="GRQ != null">GRQ,</if>
                    <if test="FLPQ != null">FLPQ,</if>
                    <if test="OBHTZ != null">OBHTZ,</if>
                    <if test="OBHTZTM != null">OBHTZTM,</if>
                    <if test="IVHZ != null">IVHZ,</if>
                    <if test="IVHZTM != null">IVHZTM,</if>
                    <if test="OBMXQ != null">OBMXQ,</if>
                    <if test="OBMXQTM != null">OBMXQTM,</if>
                    <if test="IVMXQ != null">IVMXQ,</if>
                    <if test="IVMXQTM != null">IVMXQTM,</if>
                    <if test="HMXS != null">HMXS,</if>
                    <if test="HMXSTM != null">HMXSTM,</if>
                    <if test="HMXAVV != null">HMXAVV,</if>
                    <if test="HMXAVVTM != null">HMXAVVTM,</if>
                    <if test="HLZ != null">HLZ,</if>
                    <if test="HLZTM != null">HLZTM,</if>
                    <if test="HMNQ != null">HMNQ,</if>
                    <if test="HMNQTM != null">HMNQTM,</if>
                    <if test="TAZ != null">TAZ,</if>
                    <if test="TAQ != null">TAQ,</if>
                    <if test="LAZ != null">LAZ,</if>
                    <if test="LAQ != null">LAQ,</if>
                    <if test="SFZ != null">SFZ,</if>
                    <if test="SFQ != null">SFQ,</if>
                    <if test="MODITIME != null">MODITIME,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="STCD != null">#{STCD},</if>
                    <if test="LDKEL != null">#{LDKEL},</if>
                    <if test="RDKEL != null">#{RDKEL},</if>
                    <if test="WRZ != null">#{WRZ},</if>
                    <if test="WRQ != null">#{WRQ},</if>
                    <if test="GRZ != null">#{GRZ},</if>
                    <if test="GRQ != null">#{GRQ},</if>
                    <if test="FLPQ != null">#{FLPQ},</if>
                    <if test="OBHTZ != null">#{OBHTZ},</if>
                    <if test="OBHTZTM != null">#{OBHTZTM},</if>
                    <if test="IVHZ != null">#{IVHZ},</if>
                    <if test="IVHZTM != null">#{IVHZTM},</if>
                    <if test="OBMXQ != null">#{OBMXQ},</if>
                    <if test="OBMXQTM != null">#{OBMXQTM},</if>
                    <if test="IVMXQ != null">#{IVMXQ},</if>
                    <if test="IVMXQTM != null">#{IVMXQTM},</if>
                    <if test="HMXS != null">#{HMXS},</if>
                    <if test="HMXSTM != null">#{HMXSTM},</if>
                    <if test="HMXAVV != null">#{HMXAVV},</if>
                    <if test="HMXAVVTM != null">#{HMXAVVTM},</if>
                    <if test="HLZ != null">#{HLZ},</if>
                    <if test="HLZTM != null">#{HLZTM},</if>
                    <if test="HMNQ != null">#{HMNQ},</if>
                    <if test="HMNQTM != null">#{HMNQTM},</if>
                    <if test="TAZ != null">#{TAZ},</if>
                    <if test="TAQ != null">#{TAQ},</if>
                    <if test="LAZ != null">#{LAZ},</if>
                    <if test="LAQ != null">#{LAQ},</if>
                    <if test="SFZ != null">#{SFZ},</if>
                    <if test="SFQ != null">#{SFQ},</if>
                    <if test="MODITIME != null">#{MODITIME},</if>
        </trim>
    </insert>

    <update id="updateStRvfcchB" parameterType="com.jhdr.irrigation.entity.param.StRvfcchBEditParam">
        update st_rvfcch_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="LDKEL != null">LDKEL = #{LDKEL},</if>
                    <if test="RDKEL != null">RDKEL = #{RDKEL},</if>
                    <if test="WRZ != null">WRZ = #{WRZ},</if>
                    <if test="WRQ != null">WRQ = #{WRQ},</if>
                    <if test="GRZ != null">GRZ = #{GRZ},</if>
                    <if test="GRQ != null">GRQ = #{GRQ},</if>
                    <if test="FLPQ != null">FLPQ = #{FLPQ},</if>
                    <if test="OBHTZ != null">OBHTZ = #{OBHTZ},</if>
                    <if test="OBHTZTM != null">OBHTZTM = #{OBHTZTM},</if>
                    <if test="IVHZ != null">IVHZ = #{IVHZ},</if>
                    <if test="IVHZTM != null">IVHZTM = #{IVHZTM},</if>
                    <if test="OBMXQ != null">OBMXQ = #{OBMXQ},</if>
                    <if test="OBMXQTM != null">OBMXQTM = #{OBMXQTM},</if>
                    <if test="IVMXQ != null">IVMXQ = #{IVMXQ},</if>
                    <if test="IVMXQTM != null">IVMXQTM = #{IVMXQTM},</if>
                    <if test="HMXS != null">HMXS = #{HMXS},</if>
                    <if test="HMXSTM != null">HMXSTM = #{HMXSTM},</if>
                    <if test="HMXAVV != null">HMXAVV = #{HMXAVV},</if>
                    <if test="HMXAVVTM != null">HMXAVVTM = #{HMXAVVTM},</if>
                    <if test="HLZ != null">HLZ = #{HLZ},</if>
                    <if test="HLZTM != null">HLZTM = #{HLZTM},</if>
                    <if test="HMNQ != null">HMNQ = #{HMNQ},</if>
                    <if test="HMNQTM != null">HMNQTM = #{HMNQTM},</if>
                    <if test="TAZ != null">TAZ = #{TAZ},</if>
                    <if test="TAQ != null">TAQ = #{TAQ},</if>
                    <if test="LAZ != null">LAZ = #{LAZ},</if>
                    <if test="LAQ != null">LAQ = #{LAQ},</if>
                    <if test="SFZ != null">SFZ = #{SFZ},</if>
                    <if test="SFQ != null">SFQ = #{SFQ},</if>
                    <if test="MODITIME != null">MODITIME = #{MODITIME},</if>
        </trim>
        where STCD = #{STCD}
    </update>

    <delete id="deleteStRvfcchBBySTCD" parameterType="String">
        delete from st_rvfcch_b where STCD = #{STCD}
    </delete>

    <delete id="deleteStRvfcchBBySTCDs" parameterType="String">
        delete from st_rvfcch_b where STCD in
        <foreach item="STCD" collection="array" open="(" separator="," close=")">
            #{STCD}
        </foreach>
    </delete>
</mapper>
