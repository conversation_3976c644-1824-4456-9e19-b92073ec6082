<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhomVmBMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhomVmBPo" id="JhomVmBResult">
        <result property="cd"    column="cd"    />
        <result property="sernum"    column="sernum"    />
        <result property="deviceid"    column="deviceid"    />
        <result property="model"    column="model"    />
        <result property="oem"    column="oem"    />
        <result property="resolution"    column="resolution"    />
        <result property="viewang"    column="viewang"    />
        <result property="nvr"    column="nvr"    />
        <result property="nm"    column="nm"    />
        <result property="cn"    column="cn"    />
        <result property="protocol"    column="protocol"    />
        <result property="ip"    column="ip"    />
        <result property="ipport"    column="ipport"    />
        <result property="sbcn"    column="sbcn"    />
        <result property="mport"    column="mport"    />
        <result property="status"    column="status"    />
        <result property="project"    column="project"    />
        <result property="instUnit"    column="inst_unit"    />
        <result property="instdate"    column="instdate"    />
        <result property="mangUnit"    column="mang_unit"    />
        <result property="maitUnit"    column="mait_unit"    />
        <result property="maitTel"    column="mait_tel"    />
        <result property="addvcd"    column="addvcd"    />
        <result property="stlc"    column="stlc"    />
        <result property="instPic"    column="inst_pic"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="lastmaittm"    column="lastmaittm"    />
        <result property="nextmaittm"    column="nextmaittm"    />
        <result property="checktm"    column="checktm"    />
        <result property="recyn"    column="recyn"    />
        <result property="recmaxhours"    column="recmaxhours"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="lttd"    column="lttd"    />
        <result property="masterVersion"    column="master_version"    />
        <result property="codeVersion"    column="code_version"    />
        <result property="webVersion"    column="web_version"    />
        <result property="pluginVersion"    column="plugin_version"    />
        <result property="channelNum"    column="channel_num"    />
        <result property="diskNum"    column="disk_num"    />
        <result property="alarmEnter"    column="alarm_enter"    />
        <result property="alarmOutput"    column="alarm_output"    />
        <result property="method"    column="method"    />
        <result property="security"    column="security"    />
    </resultMap>

    <sql id="selectJhomVmBVo">
        select cd, sernum, deviceid, model, oem, resolution, viewang, nvr, nm, cn, protocol, ip, ipport, sbcn, mport, status, project, inst_unit, instdate, mang_unit, mait_unit, mait_tel, addvcd, stlc, inst_pic, username, password, lastmaittm, nextmaittm, checktm, recyn, recmaxhours, lgtd, lttd, master_version, code_version, web_version, plugin_version, channel_num, disk_num, alarm_enter, alarm_output, method, security from jhom_vm_b
    </sql>

    <select id="selectJhomVmBList"  resultType="com.jhdr.irrigation.entity.vo.JhomVmBVo">
        <include refid="selectJhomVmBVo"/>
        <where>
            <if test="cd != null  and cd != ''"> and cd = #{cd}</if>
            <if test="sernum != null  and sernum != ''"> and sernum = #{sernum}</if>
            <if test="deviceid != null  and deviceid != ''"> and deviceid = #{deviceid}</if>
            <if test="model != null  and model != ''"> and model = #{model}</if>
            <if test="oem != null  and oem != ''"> and oem = #{oem}</if>
            <if test="resolution != null "> and resolution = #{resolution}</if>
            <if test="viewang != null "> and viewang = #{viewang}</if>
            <if test="nvr != null  and nvr != ''"> and nvr = #{nvr}</if>
            <if test="nm != null  and nm != ''"> and nm = #{nm}</if>
            <if test="cn != null  and cn != ''"> and cn = #{cn}</if>
            <if test="protocol != null  and protocol != ''"> and protocol = #{protocol}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="ipport != null "> and ipport = #{ipport}</if>
            <if test="sbcn != null "> and sbcn = #{sbcn}</if>
            <if test="mport != null "> and mport = #{mport}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="project != null  and project != ''"> and project = #{project}</if>
            <if test="instUnit != null  and instUnit != ''"> and inst_unit = #{instUnit}</if>
            <if test="instdate != null  and instdate != ''"> and instdate = #{instdate}</if>
            <if test="mangUnit != null  and mangUnit != ''"> and mang_unit = #{mangUnit}</if>
            <if test="maitUnit != null  and maitUnit != ''"> and mait_unit = #{maitUnit}</if>
            <if test="maitTel != null  and maitTel != ''"> and mait_tel = #{maitTel}</if>
            <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
            <if test="stlc != null  and stlc != ''"> and stlc = #{stlc}</if>
            <if test="instPic != null  and instPic != ''"> and inst_pic = #{instPic}</if>
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="lastmaittm != null  and lastmaittm != ''"> and lastmaittm = #{lastmaittm}</if>
            <if test="nextmaittm != null  and nextmaittm != ''"> and nextmaittm = #{nextmaittm}</if>
            <if test="checktm != null  and checktm != ''"> and checktm = #{checktm}</if>
            <if test="recyn != null "> and recyn = #{recyn}</if>
            <if test="recmaxhours != null "> and recmaxhours = #{recmaxhours}</if>
            <if test="lgtd != null  and lgtd != ''"> and lgtd = #{lgtd}</if>
            <if test="lttd != null  and lttd != ''"> and lttd = #{lttd}</if>
            <if test="masterVersion != null  and masterVersion != ''"> and master_version = #{masterVersion}</if>
            <if test="codeVersion != null  and codeVersion != ''"> and code_version = #{codeVersion}</if>
            <if test="webVersion != null  and webVersion != ''"> and web_version = #{webVersion}</if>
            <if test="pluginVersion != null  and pluginVersion != ''"> and plugin_version = #{pluginVersion}</if>
            <if test="channelNum != null  and channelNum != ''"> and channel_num = #{channelNum}</if>
            <if test="diskNum != null "> and disk_num = #{diskNum}</if>
            <if test="alarmEnter != null  and alarmEnter != ''"> and alarm_enter = #{alarmEnter}</if>
            <if test="alarmOutput != null  and alarmOutput != ''"> and alarm_output = #{alarmOutput}</if>
            <if test="method != null  and method != ''"> and method = #{method}</if>
            <if test="security != null  and security != ''"> and security = #{security}</if>
        </where>
    </select>

    <select id="selectJhomVmBByCd"  resultType="com.jhdr.irrigation.entity.vo.JhomVmBVo">
            <include refid="selectJhomVmBVo"/>
            where cd = #{cd}
    </select>
    <!-- 查询区域层级-->
    <select id="selectRegionTreeList" resultType="com.jhdr.irrigation.entity.vo.VideoRegionDataVo">
        (SELECT
             hvr.index_code,
             hvr."name",
             hvr.parent_index_code,
             '' AS camera_code,
             null lgtd,
             null lttd,
             '' status,
             '' source
         FROM
             hikvision_video_region hvr ORDER BY
             hvr.sort+0)
        UNION ALL
        (SELECT
        jvb.camera_code index_code,
        jvb.nm AS NAME,
        ifnull (jvb.region_index_code, '-1') parent_index_code,
        jvb.camera_code,
        gdd.lgtd,
        gdd.lttd,
        jvb.status,
        gdd.source
        FROM
            jhom_vm_b jvb
                LEFT JOIN gis_datas_d gdd on gdd.CD= jvb.cd
        WHERE
            jvb.camera_code IS NOT NULL and jvb.camera_code != '' and jvb.usfl = 1 and gdd."CD" is not null and gdd.GISSIGN ='SP')
    </select>
    <select id="onlineNum" resultType="java.lang.Integer">
        SELECT count(1) FROM jhom_vm_b jvb
        LEFT JOIN gis_datas_d gdd on gdd.CD= jvb.cd
        WHERE jvb.status = '正常' and gdd."CD" is not null
    </select>

    <insert id="insertJhomVmB" parameterType="com.jhdr.irrigation.entity.param.JhomVmBAddParam">
        insert into jhom_vm_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="cd != null">cd,</if>
                    <if test="sernum != null">sernum,</if>
                    <if test="deviceid != null">deviceid,</if>
                    <if test="model != null">model,</if>
                    <if test="oem != null">oem,</if>
                    <if test="resolution != null">resolution,</if>
                    <if test="viewang != null">viewang,</if>
                    <if test="nvr != null">nvr,</if>
                    <if test="nm != null and nm != ''">nm,</if>
                    <if test="cn != null">cn,</if>
                    <if test="protocol != null">protocol,</if>
                    <if test="ip != null">ip,</if>
                    <if test="ipport != null">ipport,</if>
                    <if test="sbcn != null">sbcn,</if>
                    <if test="mport != null">mport,</if>
                    <if test="status != null">status,</if>
                    <if test="project != null">project,</if>
                    <if test="instUnit != null">inst_unit,</if>
                    <if test="instdate != null">instdate,</if>
                    <if test="mangUnit != null">mang_unit,</if>
                    <if test="maitUnit != null">mait_unit,</if>
                    <if test="maitTel != null">mait_tel,</if>
                    <if test="addvcd != null">addvcd,</if>
                    <if test="stlc != null">stlc,</if>
                    <if test="instPic != null">inst_pic,</if>
                    <if test="username != null">username,</if>
                    <if test="password != null">password,</if>
                    <if test="lastmaittm != null">lastmaittm,</if>
                    <if test="nextmaittm != null">nextmaittm,</if>
                    <if test="checktm != null">checktm,</if>
                    <if test="recyn != null">recyn,</if>
                    <if test="recmaxhours != null">recmaxhours,</if>
                    <if test="lgtd != null">lgtd,</if>
                    <if test="lttd != null">lttd,</if>
                    <if test="masterVersion != null">master_version,</if>
                    <if test="codeVersion != null">code_version,</if>
                    <if test="webVersion != null">web_version,</if>
                    <if test="pluginVersion != null">plugin_version,</if>
                    <if test="channelNum != null">channel_num,</if>
                    <if test="diskNum != null">disk_num,</if>
                    <if test="alarmEnter != null">alarm_enter,</if>
                    <if test="alarmOutput != null">alarm_output,</if>
                    <if test="method != null">method,</if>
                    <if test="security != null">security,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="cd != null">#{cd},</if>
                    <if test="sernum != null">#{sernum},</if>
                    <if test="deviceid != null">#{deviceid},</if>
                    <if test="model != null">#{model},</if>
                    <if test="oem != null">#{oem},</if>
                    <if test="resolution != null">#{resolution},</if>
                    <if test="viewang != null">#{viewang},</if>
                    <if test="nvr != null">#{nvr},</if>
                    <if test="nm != null and nm != ''">#{nm},</if>
                    <if test="cn != null">#{cn},</if>
                    <if test="protocol != null">#{protocol},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="ipport != null">#{ipport},</if>
                    <if test="sbcn != null">#{sbcn},</if>
                    <if test="mport != null">#{mport},</if>
                    <if test="status != null">#{status},</if>
                    <if test="project != null">#{project},</if>
                    <if test="instUnit != null">#{instUnit},</if>
                    <if test="instdate != null">#{instdate},</if>
                    <if test="mangUnit != null">#{mangUnit},</if>
                    <if test="maitUnit != null">#{maitUnit},</if>
                    <if test="maitTel != null">#{maitTel},</if>
                    <if test="addvcd != null">#{addvcd},</if>
                    <if test="stlc != null">#{stlc},</if>
                    <if test="instPic != null">#{instPic},</if>
                    <if test="username != null">#{username},</if>
                    <if test="password != null">#{password},</if>
                    <if test="lastmaittm != null">#{lastmaittm},</if>
                    <if test="nextmaittm != null">#{nextmaittm},</if>
                    <if test="checktm != null">#{checktm},</if>
                    <if test="recyn != null">#{recyn},</if>
                    <if test="recmaxhours != null">#{recmaxhours},</if>
                    <if test="lgtd != null">#{lgtd},</if>
                    <if test="lttd != null">#{lttd},</if>
                    <if test="masterVersion != null">#{masterVersion},</if>
                    <if test="codeVersion != null">#{codeVersion},</if>
                    <if test="webVersion != null">#{webVersion},</if>
                    <if test="pluginVersion != null">#{pluginVersion},</if>
                    <if test="channelNum != null">#{channelNum},</if>
                    <if test="diskNum != null">#{diskNum},</if>
                    <if test="alarmEnter != null">#{alarmEnter},</if>
                    <if test="alarmOutput != null">#{alarmOutput},</if>
                    <if test="method != null">#{method},</if>
                    <if test="security != null">#{security},</if>
        </trim>
    </insert>

    <update id="updateJhomVmB" parameterType="com.jhdr.irrigation.entity.param.JhomVmBEditParam">
        update jhom_vm_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="sernum != null">sernum = #{sernum},</if>
                    <if test="deviceid != null">deviceid = #{deviceid},</if>
                    <if test="model != null">model = #{model},</if>
                    <if test="oem != null">oem = #{oem},</if>
                    <if test="resolution != null">resolution = #{resolution},</if>
                    <if test="viewang != null">viewang = #{viewang},</if>
                    <if test="nvr != null">nvr = #{nvr},</if>
                    <if test="nm != null and nm != ''">nm = #{nm},</if>
                    <if test="cn != null">cn = #{cn},</if>
                    <if test="protocol != null">protocol = #{protocol},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="ipport != null">ipport = #{ipport},</if>
                    <if test="sbcn != null">sbcn = #{sbcn},</if>
                    <if test="mport != null">mport = #{mport},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="project != null">project = #{project},</if>
                    <if test="instUnit != null">inst_unit = #{instUnit},</if>
                    <if test="instdate != null">instdate = #{instdate},</if>
                    <if test="mangUnit != null">mang_unit = #{mangUnit},</if>
                    <if test="maitUnit != null">mait_unit = #{maitUnit},</if>
                    <if test="maitTel != null">mait_tel = #{maitTel},</if>
                    <if test="addvcd != null">addvcd = #{addvcd},</if>
                    <if test="stlc != null">stlc = #{stlc},</if>
                    <if test="instPic != null">inst_pic = #{instPic},</if>
                    <if test="username != null">username = #{username},</if>
                    <if test="password != null">password = #{password},</if>
                    <if test="lastmaittm != null">lastmaittm = #{lastmaittm},</if>
                    <if test="nextmaittm != null">nextmaittm = #{nextmaittm},</if>
                    <if test="checktm != null">checktm = #{checktm},</if>
                    <if test="recyn != null">recyn = #{recyn},</if>
                    <if test="recmaxhours != null">recmaxhours = #{recmaxhours},</if>
                    <if test="lgtd != null">lgtd = #{lgtd},</if>
                    <if test="lttd != null">lttd = #{lttd},</if>
                    <if test="masterVersion != null">master_version = #{masterVersion},</if>
                    <if test="codeVersion != null">code_version = #{codeVersion},</if>
                    <if test="webVersion != null">web_version = #{webVersion},</if>
                    <if test="pluginVersion != null">plugin_version = #{pluginVersion},</if>
                    <if test="channelNum != null">channel_num = #{channelNum},</if>
                    <if test="diskNum != null">disk_num = #{diskNum},</if>
                    <if test="alarmEnter != null">alarm_enter = #{alarmEnter},</if>
                    <if test="alarmOutput != null">alarm_output = #{alarmOutput},</if>
                    <if test="method != null">method = #{method},</if>
                    <if test="security != null">security = #{security},</if>
        </trim>
        where cd = #{cd}
    </update>

    <delete id="deleteJhomVmBByCd" parameterType="String">
        delete from jhom_vm_b where cd = #{cd}
    </delete>

    <delete id="deleteJhomVmBByCds" parameterType="String">
        delete from jhom_vm_b where cd in
        <foreach item="cd" collection="array" open="(" separator="," close=")">
            #{cd}
        </foreach>
    </delete>
</mapper>
