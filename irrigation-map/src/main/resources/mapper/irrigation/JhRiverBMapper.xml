<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhRiverBMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhRiverBPo" id="JhRiverBResult">
        <result property="ennmcd"    column="ennmcd"    />
        <result property="rvnm"    column="rvnm"    />
        <result property="rvtp"    column="rvtp"    />
        <result property="belws"    column="belws"    />
        <result property="rvpl"    column="rvpl"    />
        <result property="rved"    column="rved"    />
        <result property="mnstln"    column="mnstln"    />
        <result property="ttdrbsar"    column="ttdrbsar"    />
        <result property="drbspp"    column="drbspp"    />
        <result property="irrigatedarea"    column="irrigatedarea"    />
        <result property="munit"    column="munit"    />
        <result property="rvchief"    column="rvchief"    />
        <result property="rm"    column="rm"    />
    </resultMap>

    <sql id="selectJhRiverBVo">
        select ennmcd, rvnm, rvtp, belws, rvpl, rved, mnstln, ttdrbsar, drbspp, irrigatedarea, munit, rvchief, rm from chxh.jh_river_b
    </sql>

    <select id="selectJhRiverBList"  resultType="com.jhdr.irrigation.entity.vo.JhRiverBVo">
        <include refid="selectJhRiverBVo"/>
        <where>
            <if test="rvnm != null  and rvnm != ''"> and rvnm = #{rvnm}</if>
            <if test="rvtp != null  and rvtp != ''"> and rvtp = #{rvtp}</if>
            <if test="belws != null  and belws != ''"> and belws = #{belws}</if>
            <if test="rvpl != null  and rvpl != ''"> and rvpl = #{rvpl}</if>

        </where>
    </select>

    <select id="selectJhRiverBByEnnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhRiverBVo">
            <include refid="selectJhRiverBVo"/>
            where ennmcd = #{ennmcd}
    </select>
    <select id="sumLengthByRvtp" resultType="java.lang.String">
        select sum(mnstln) from chxh.jh_river_b where rvtp = #{type} and mnstln is not NULL
    </select>

    <insert id="insertJhRiverB" parameterType="com.jhdr.irrigation.entity.param.JhRiverBAddParam">
        insert into chxh.jh_river_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="ennmcd != null">ennmcd,</if>
                    <if test="rvnm != null">rvnm,</if>
                    <if test="rvtp != null">rvtp,</if>
                    <if test="belws != null">belws,</if>
                    <if test="rvpl != null">rvpl,</if>
                    <if test="rved != null">rved,</if>
                    <if test="mnstln != null">mnstln,</if>
                    <if test="ttdrbsar != null">ttdrbsar,</if>
                    <if test="drbspp != null">drbspp,</if>
                    <if test="irrigatedarea != null">irrigatedarea,</if>
                    <if test="munit != null">munit,</if>
                    <if test="rvchief != null">rvchief,</if>
                    <if test="rm != null">rm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="ennmcd != null">#{ennmcd},</if>
                    <if test="rvnm != null">#{rvnm},</if>
                    <if test="rvtp != null">#{rvtp},</if>
                    <if test="belws != null">#{belws},</if>
                    <if test="rvpl != null">#{rvpl},</if>
                    <if test="rved != null">#{rved},</if>
                    <if test="mnstln != null">#{mnstln},</if>
                    <if test="ttdrbsar != null">#{ttdrbsar},</if>
                    <if test="drbspp != null">#{drbspp},</if>
                    <if test="irrigatedarea != null">#{irrigatedarea},</if>
                    <if test="munit != null">#{munit},</if>
                    <if test="rvchief != null">#{rvchief},</if>
                    <if test="rm != null">#{rm},</if>
        </trim>
    </insert>

    <update id="updateJhRiverB" parameterType="com.jhdr.irrigation.entity.param.JhRiverBEditParam">
        update chxh.jh_river_b
        <trim prefix="SET" suffixOverrides=",">
                    <if test="rvnm != null">rvnm = #{rvnm},</if>
                    <if test="rvtp != null">rvtp = #{rvtp},</if>
                    <if test="belws != null">belws = #{belws},</if>
                    <if test="rvpl != null">rvpl = #{rvpl},</if>
                    <if test="rved != null">rved = #{rved},</if>
                    <if test="mnstln != null">mnstln = #{mnstln},</if>
                    <if test="ttdrbsar != null">ttdrbsar = #{ttdrbsar},</if>
                    <if test="drbspp != null">drbspp = #{drbspp},</if>
                    <if test="irrigatedarea != null">irrigatedarea = #{irrigatedarea},</if>
                    <if test="munit != null">munit = #{munit},</if>
                    <if test="rvchief != null">rvchief = #{rvchief},</if>
                    <if test="rm != null">rm = #{rm},</if>
        </trim>
        where ennmcd = #{ennmcd}
    </update>

    <delete id="deleteJhRiverBByEnnmcd" parameterType="String">
        delete from chxh.jh_river_b where ennmcd = #{ennmcd}
    </delete>

    <delete id="deleteJhRiverBByEnnmcds" parameterType="String">
        delete from chxh.jh_river_b where ennmcd in
        <foreach item="ennmcd" collection="array" open="(" separator="," close=")">
            #{ennmcd}
        </foreach>
    </delete>
</mapper>
