<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationProcessInspectMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.IrrigationProcessInspectPo" id="IrrigationProcessInspectResult">
        <result property="id"    column="id"    />
        <result property="inspectionUnit"    column="inspection_unit"    />
        <result property="inspectedUnit"    column="inspected_unit"    />
        <result property="inspectionType"    column="inspection_type"    />
        <result property="inspectionDate"    column="inspection_date"    />
        <result property="entryDate"    column="entry_date"    />
        <result property="hasHazards"    column="has_hazards"    />
        <result property="inspectionItems"    column="inspection_items"    />
        <result property="hazardContent"    column="hazard_content"    />
        <result property="hazardLevel"    column="hazard_level"    />
        <result property="hazardType"    column="hazard_type"    />
        <result property="hazardStatus"    column="hazard_status"    />
        <result property="supervisionStatus"    column="supervision_status"    />
        <result property="rectificationDeadline"    column="rectification_deadline"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectIrrigationProcessInspectVo">
        select id, inspection_unit, inspected_unit, inspection_type, inspection_date, entry_date, has_hazards, inspection_items, hazard_content, hazard_level, hazard_type, hazard_status, supervision_status, rectification_deadline, create_time from irrigation_process_inspect
    </sql>

    <select id="selectIrrigationProcessInspectList"  resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessInspectVo">
        <include refid="selectIrrigationProcessInspectVo"/>
        <where>

            <if test="inspectedUnit != null  and inspectedUnit != ''"> and inspected_unit = #{inspectedUnit}</if>
            <if test="inspectionType != null  and inspectionType != ''"> and inspection_type = #{inspectionType}</if>
            <if test="inspectionStartDate != null "> and inspection_date between #{inspectionStartDate} and #{inspectionEndDate}</if>
            <if test="entryStartDate != null"> and entry_date between #{entryStartDate} and #{entryEndDate}</if>
            <if test="inspectionItems != null  and inspectionItems != ''"> and inspection_items like concat('%', #{inspectionItems}, '%')</if>
        </where>
        order by id desc
    </select>

    <select id="selectIrrigationProcessInspectById"  resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessInspectVo">
            <include refid="selectIrrigationProcessInspectVo"/>
            where id = #{id}
    </select>

    <insert id="insertIrrigationProcessInspect" parameterType="com.jhdr.irrigation.entity.param.IrrigationProcessInspectAddParam">
        insert into irrigation_process_inspect
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="inspectionUnit != null">inspection_unit,</if>
                    <if test="inspectedUnit != null">inspected_unit,</if>
                    <if test="inspectionType != null">inspection_type,</if>
                    <if test="inspectionDate != null">inspection_date,</if>
                    <if test="entryDate != null">entry_date,</if>
                    <if test="hasHazards != null">has_hazards,</if>
                    <if test="inspectionItems != null">inspection_items,</if>
                    <if test="hazardContent != null">hazard_content,</if>
                    <if test="hazardLevel != null">hazard_level,</if>
                    <if test="hazardType != null">hazard_type,</if>
                    <if test="hazardStatus != null">hazard_status,</if>
                    <if test="supervisionStatus != null">supervision_status,</if>
                    <if test="rectificationDeadline != null">rectification_deadline,</if>
                    <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="inspectionUnit != null">#{inspectionUnit},</if>
                    <if test="inspectedUnit != null">#{inspectedUnit},</if>
                    <if test="inspectionType != null">#{inspectionType},</if>
                    <if test="inspectionDate != null">#{inspectionDate},</if>
                    <if test="entryDate != null">#{entryDate},</if>
                    <if test="hasHazards != null">#{hasHazards},</if>
                    <if test="inspectionItems != null">#{inspectionItems},</if>
                    <if test="hazardContent != null">#{hazardContent},</if>
                    <if test="hazardLevel != null">#{hazardLevel},</if>
                    <if test="hazardType != null">#{hazardType},</if>
                    <if test="hazardStatus != null">#{hazardStatus},</if>
                    <if test="supervisionStatus != null">#{supervisionStatus},</if>
                    <if test="rectificationDeadline != null">#{rectificationDeadline},</if>
                    <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateIrrigationProcessInspect" parameterType="com.jhdr.irrigation.entity.param.IrrigationProcessInspectEditParam">
        update irrigation_process_inspect
        <trim prefix="SET" suffixOverrides=",">
                    <if test="inspectionUnit != null">inspection_unit = #{inspectionUnit},</if>
                    <if test="inspectedUnit != null">inspected_unit = #{inspectedUnit},</if>
                    <if test="inspectionType != null">inspection_type = #{inspectionType},</if>
                    <if test="inspectionDate != null">inspection_date = #{inspectionDate},</if>
                    <if test="entryDate != null">entry_date = #{entryDate},</if>
                    <if test="hasHazards != null">has_hazards = #{hasHazards},</if>
                    <if test="inspectionItems != null">inspection_items = #{inspectionItems},</if>
                    <if test="hazardContent != null">hazard_content = #{hazardContent},</if>
                    <if test="hazardLevel != null">hazard_level = #{hazardLevel},</if>
                    <if test="hazardType != null">hazard_type = #{hazardType},</if>
                    <if test="hazardStatus != null">hazard_status = #{hazardStatus},</if>
                    <if test="supervisionStatus != null">supervision_status = #{supervisionStatus},</if>
                    <if test="rectificationDeadline != null">rectification_deadline = #{rectificationDeadline},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIrrigationProcessInspectById" parameterType="Long">
        delete from irrigation_process_inspect where id = #{id}
    </delete>

    <delete id="deleteIrrigationProcessInspectByIds" parameterType="String">
        delete from irrigation_process_inspect where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
