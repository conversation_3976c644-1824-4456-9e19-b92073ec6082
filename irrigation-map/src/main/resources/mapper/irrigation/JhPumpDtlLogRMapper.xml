<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhPumpDtlLogRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhPumpDtlLogRPo" id="JhPumpDtlLogRResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="pmpcd"    column="pmpcd"    />
        <result property="om"    column="om"    />
        <result property="ee"    column="ee"    />
        <result property="q"    column="q"    />
        <result property="operator"    column="operator"    />
    </resultMap>

    <sql id="selectJhPumpDtlLogRVo">
        select prjnmcd, clltm, pmpcd, om, ee, q, operator from jh_pump_dtl_log_r
    </sql>

    <select id="selectJhPumpDtlLogRList"  resultType="com.jhdr.irrigation.entity.vo.JhPumpDtlLogRVo">
        <include refid="selectJhPumpDtlLogRVo"/>
        <where>
             prjnmcd = #{prjnmcd}
            <if test="clltmStart != null and clltmStart != '' "> and clltm &gt;= #{clltmStart}</if>
            <if test="clltmEnd != null and clltmEnd != '' "> and clltm &lt;= #{clltmEnd}</if>
        </where>
    </select>

    <select id="selectJhPumpDtlLogRByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhPumpDtlLogRVo">
            <include refid="selectJhPumpDtlLogRVo"/>
            where prjnmcd = #{prjnmcd}
    </select>

    <insert id="insertJhPumpDtlLogR" parameterType="com.jhdr.irrigation.entity.param.JhPumpDtlLogRAddParam">
        insert into jh_pump_dtl_log_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null">clltm,</if>
                    <if test="pmpcd != null">pmpcd,</if>
                    <if test="om != null">om,</if>
                    <if test="ee != null">ee,</if>
                    <if test="q != null">q,</if>
                    <if test="operator != null">operator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null">#{clltm},</if>
                    <if test="pmpcd != null">#{pmpcd},</if>
                    <if test="om != null">#{om},</if>
                    <if test="ee != null">#{ee},</if>
                    <if test="q != null">#{q},</if>
                    <if test="operator != null">#{operator},</if>
        </trim>
    </insert>

    <update id="updateJhPumpDtlLogR" parameterType="com.jhdr.irrigation.entity.param.JhPumpDtlLogREditParam">
        update jh_pump_dtl_log_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null">clltm = #{clltm},</if>
                    <if test="pmpcd != null">pmpcd = #{pmpcd},</if>
                    <if test="om != null">om = #{om},</if>
                    <if test="ee != null">ee = #{ee},</if>
                    <if test="q != null">q = #{q},</if>
                    <if test="operator != null">operator = #{operator},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhPumpDtlLogRByPrjnmcd" parameterType="String">
        delete from jh_pump_dtl_log_r where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhPumpDtlLogRByPrjnmcds" parameterType="String">
        delete from jh_pump_dtl_log_r where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
