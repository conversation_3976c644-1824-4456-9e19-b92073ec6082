<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhSlcrsRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhSlcrsRPo" id="JhSlcrsRResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="slupsz"    column="slupsz"    />
        <result property="sldsz"    column="sldsz"    />
        <result property="thrslq"    column="thrslq"    />
        <result property="gtopn"    column="gtopn"    />
        <result property="upswtp"    column="upswtp"    />
        <result property="dswtp"    column="dswtp"    />
        <result property="rpdpcd"    column="rpdpcd"    />
        <result property="reporter"    column="reporter"    />
        <result property="rpttlnmb"    column="rpttlnmb"    />
        <result property="rmk"    column="rmk"    />
        <result property="gtcn"    column="gtcn"    />
    </resultMap>

    <sql id="selectJhSlcrsRVo">
        select prjnmcd, clltm, slupsz, sldsz, thrslq, gtopn, upswtp, dswtp, rpdpcd, reporter, rpttlnmb, rmk, gtcn from chxh.jh_slcrs_r
    </sql>

    <select id="selectJhSlcrsRList"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsRWaterVo">
        select str_code as prjnmcd,tm as clltm,upz as slupsz,dwz as sldsz from chxh.jh_upz_r
        <where>
            str_code = #{prjnmcd}
            <if test="clltmStart != null and clltmStart != '' "> and tm &gt;= #{clltmStart}</if>
            <if test="clltmEnd != null and clltmEnd != '' "> and tm &lt;= #{clltmEnd}</if>
        </where>
        order by clltm desc
    </select>

    <select id="selectJhSlcrsRByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsRVo">
        select prjnmcd, clltm, slupsz, sldsz, thrslq, gtopn, upswtp, dswtp, rpdpcd,
               reporter, rpttlnmb, rmk, gtcn from chxh.jh_slcrs_r_real
        where prjnmcd = #{prjnmcd}
    </select>

    <insert id="insertJhSlcrsR" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsRAddParam">
        insert into chxh.jh_slcrs_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null">clltm,</if>
                    <if test="slupsz != null">slupsz,</if>
                    <if test="sldsz != null">sldsz,</if>
                    <if test="thrslq != null">thrslq,</if>
                    <if test="gtopn != null">gtopn,</if>
                    <if test="upswtp != null">upswtp,</if>
                    <if test="dswtp != null">dswtp,</if>
                    <if test="rpdpcd != null">rpdpcd,</if>
                    <if test="reporter != null">reporter,</if>
                    <if test="rpttlnmb != null">rpttlnmb,</if>
                    <if test="rmk != null">rmk,</if>
                    <if test="gtcn != null">gtcn,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null">#{clltm},</if>
                    <if test="slupsz != null">#{slupsz},</if>
                    <if test="sldsz != null">#{sldsz},</if>
                    <if test="thrslq != null">#{thrslq},</if>
                    <if test="gtopn != null">#{gtopn},</if>
                    <if test="upswtp != null">#{upswtp},</if>
                    <if test="dswtp != null">#{dswtp},</if>
                    <if test="rpdpcd != null">#{rpdpcd},</if>
                    <if test="reporter != null">#{reporter},</if>
                    <if test="rpttlnmb != null">#{rpttlnmb},</if>
                    <if test="rmk != null">#{rmk},</if>
                    <if test="gtcn != null">#{gtcn},</if>
        </trim>
    </insert>

    <update id="updateJhSlcrsR" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsREditParam">
        update chxh.jh_slcrs_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null">clltm = #{clltm},</if>
                    <if test="slupsz != null">slupsz = #{slupsz},</if>
                    <if test="sldsz != null">sldsz = #{sldsz},</if>
                    <if test="thrslq != null">thrslq = #{thrslq},</if>
                    <if test="gtopn != null">gtopn = #{gtopn},</if>
                    <if test="upswtp != null">upswtp = #{upswtp},</if>
                    <if test="dswtp != null">dswtp = #{dswtp},</if>
                    <if test="rpdpcd != null">rpdpcd = #{rpdpcd},</if>
                    <if test="reporter != null">reporter = #{reporter},</if>
                    <if test="rpttlnmb != null">rpttlnmb = #{rpttlnmb},</if>
                    <if test="rmk != null">rmk = #{rmk},</if>
                    <if test="gtcn != null">gtcn = #{gtcn},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhSlcrsRByPrjnmcd" parameterType="String">
        delete from chxh.jh_slcrs_r where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhSlcrsRByPrjnmcds" parameterType="String">
        delete from chxh.jh_slcrs_r where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
