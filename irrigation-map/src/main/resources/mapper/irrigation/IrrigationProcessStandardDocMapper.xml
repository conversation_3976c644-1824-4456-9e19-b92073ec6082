<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationProcessStandardDocMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.IrrigationProcessStandardDocPo" id="IrrigationProcessStandardDocResult">
        <result property="docId"    column="doc_id"    />
        <result property="standardId"    column="standard_id"    />
        <result property="docName"    column="doc_name"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectIrrigationProcessStandardDocVo">
        select doc_id, standard_id, doc_name, sort_order, create_time from irrigation_process_standard_doc
    </sql>

    <select id="selectIrrigationProcessStandardDocList"  resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessStandardDocVo">
        <include refid="selectIrrigationProcessStandardDocVo"/>
        <where>
            <if test="standardId != null "> and standard_id = #{standardId}</if>
        </where>
        order by sort_order
    </select>

    <select id="selectIrrigationProcessStandardDocByDocId"  resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessStandardDocVo">
            <include refid="selectIrrigationProcessStandardDocVo"/>
            where doc_id = #{docId}
    </select>

    <insert id="insertIrrigationProcessStandardDoc" parameterType="com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocAddParam">
        insert into irrigation_process_standard_doc
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="docId != null">doc_id,</if>
                    <if test="standardId != null">standard_id,</if>
                    <if test="docName != null">doc_name,</if>
                    <if test="sortOrder != null">sort_order,</if>
                    <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="docId != null">#{docId},</if>
                    <if test="standardId != null">#{standardId},</if>
                    <if test="docName != null">#{docName},</if>
                    <if test="sortOrder != null">#{sortOrder},</if>
                    <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateIrrigationProcessStandardDoc" parameterType="com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocEditParam">
        update irrigation_process_standard_doc
        <trim prefix="SET" suffixOverrides=",">
                    <if test="standardId != null">standard_id = #{standardId},</if>
                    <if test="docName != null">doc_name = #{docName},</if>
                    <if test="sortOrder != null">sort_order = #{sortOrder},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where doc_id = #{docId}
    </update>

    <delete id="deleteIrrigationProcessStandardDocByDocId" parameterType="Long">
        delete from irrigation_process_standard_doc where doc_id = #{docId}
    </delete>

    <delete id="deleteIrrigationProcessStandardDocByDocIds" parameterType="String">
        delete from irrigation_process_standard_doc where doc_id in
        <foreach item="docId" collection="array" open="(" separator="," close=")">
            #{docId}
        </foreach>
    </delete>
</mapper>
