<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhSlcrsDtlRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhSlcrsDtlRPo" id="JhSlcrsDtlRResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="gpcd"    column="gpcd"    />
        <result property="gtoph"    column="gtoph"    />
        <result property="gtq"    column="gtq"    />
        <result property="gtup"    column="gtup"    />
        <result property="gtdw"    column="gtdw"    />
        <result property="gtaop"    column="gtaop"    />
        <result property="gtaco"    column="gtaco"    />
    </resultMap>

    <sql id="selectJhSlcrsDtlRVo">
        select prjnmcd, clltm, gpcd, gtoph, gtq, gtup, gtdw, gtaop, gtaco from jh_slcrs_dtl_r
    </sql>

    <select id="selectJhSlcrsDtlRList"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlRVo">
        <include refid="selectJhSlcrsDtlRVo"/>
        <where>
            <if test="clltm != null  and clltm != ''"> and clltm = #{clltm}</if>
            <if test="gpcd != null "> and gpcd = #{gpcd}</if>
            <if test="gtoph != null "> and gtoph = #{gtoph}</if>
            <if test="gtq != null "> and gtq = #{gtq}</if>
            <if test="gtup != null "> and gtup = #{gtup}</if>
            <if test="gtdw != null "> and gtdw = #{gtdw}</if>
            <if test="gtaop != null "> and gtaop = #{gtaop}</if>
            <if test="gtaco != null "> and gtaco = #{gtaco}</if>
        </where>
    </select>

    <select id="selectJhSlcrsDtlRByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlRVo">
            <include refid="selectJhSlcrsDtlRVo"/>
            where prjnmcd = #{prjnmcd}
    </select>
    <!-- 查询水闸运行状况明细-->
    <select id="getSlcrsRInfoVos" resultType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlRInfoVo">
        select * from jh_slcrs_dtl_r_real where prjnmcd = #{prjnmcd}
    </select>

    <insert id="insertJhSlcrsDtlR" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsDtlRAddParam">
        insert into jh_slcrs_dtl_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null and clltm != ''">clltm,</if>
                    <if test="gpcd != null">gpcd,</if>
                    <if test="gtoph != null">gtoph,</if>
                    <if test="gtq != null">gtq,</if>
                    <if test="gtup != null">gtup,</if>
                    <if test="gtdw != null">gtdw,</if>
                    <if test="gtaop != null">gtaop,</if>
                    <if test="gtaco != null">gtaco,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null and clltm != ''">#{clltm},</if>
                    <if test="gpcd != null">#{gpcd},</if>
                    <if test="gtoph != null">#{gtoph},</if>
                    <if test="gtq != null">#{gtq},</if>
                    <if test="gtup != null">#{gtup},</if>
                    <if test="gtdw != null">#{gtdw},</if>
                    <if test="gtaop != null">#{gtaop},</if>
                    <if test="gtaco != null">#{gtaco},</if>
        </trim>
    </insert>

    <update id="updateJhSlcrsDtlR" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsDtlREditParam">
        update jh_slcrs_dtl_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null and clltm != ''">clltm = #{clltm},</if>
                    <if test="gpcd != null">gpcd = #{gpcd},</if>
                    <if test="gtoph != null">gtoph = #{gtoph},</if>
                    <if test="gtq != null">gtq = #{gtq},</if>
                    <if test="gtup != null">gtup = #{gtup},</if>
                    <if test="gtdw != null">gtdw = #{gtdw},</if>
                    <if test="gtaop != null">gtaop = #{gtaop},</if>
                    <if test="gtaco != null">gtaco = #{gtaco},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhSlcrsDtlRByPrjnmcd" parameterType="String">
        delete from jh_slcrs_dtl_r where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhSlcrsDtlRByPrjnmcds" parameterType="String">
        delete from jh_slcrs_dtl_r where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
