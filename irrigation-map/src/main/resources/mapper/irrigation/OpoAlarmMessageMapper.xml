<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.OpoAlarmMessageMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.OpoAlarmMessagePo" id="OpoAlarmMessageResult">
        <result property="id"    column="id"    />
        <result property="alarmCode"    column="alarm_code"    />
        <result property="staName"    column="sta_name"    />
        <result property="staCode"    column="sta_code"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="deviceType"    column="device_type"    />
        <result property="staType"    column="sta_type"    />
        <result property="construction"    column="construction"    />
        <result property="alarmRank"    column="alarm_rank"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="addTime"    column="add_time"    />
        <result property="step"    column="step"    />
        <result property="remark"    column="remark"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectOpoAlarmMessageVo">
        select id, alarm_code, sta_name, sta_code, device_name, device_code, device_type, sta_type, construction, alarm_rank, alarm_type, add_time, step, remark, is_delete from opo_alarm_message
    </sql>

    <select id="selectOpoAlarmMessageList"  resultType="com.jhdr.irrigation.entity.vo.OpoAlarmMessageVo">
        <include refid="selectOpoAlarmMessageVo"/>
        <where>
           sta_code = #{staCode}
            <if test="addTimeStart != null and addTimeStart != '' "> and add_time &gt;= #{addTimeStart}</if>
            <if test="addTimeEnd != null and addTimeEnd != '' "> and add_time &lt;= #{addTimeEnd}</if>
        </where>
        order by add_time desc
    </select>

    <select id="selectOpoAlarmMessageById"  resultType="com.jhdr.irrigation.entity.vo.OpoAlarmMessageVo">
            <include refid="selectOpoAlarmMessageVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoAlarmMessage" parameterType="com.jhdr.irrigation.entity.param.OpoAlarmMessageAddParam">
        insert into opo_alarm_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="alarmCode != null">alarm_code,</if>
                    <if test="staName != null">sta_name,</if>
                    <if test="staCode != null">sta_code,</if>
                    <if test="deviceName != null">device_name,</if>
                    <if test="deviceCode != null">device_code,</if>
                    <if test="deviceType != null">device_type,</if>
                    <if test="staType != null">sta_type,</if>
                    <if test="construction != null">construction,</if>
                    <if test="alarmRank != null">alarm_rank,</if>
                    <if test="alarmType != null">alarm_type,</if>
                    <if test="addTime != null">add_time,</if>
                    <if test="step != null">step,</if>
                    <if test="remark != null">remark,</if>
                    <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="alarmCode != null">#{alarmCode},</if>
                    <if test="staName != null">#{staName},</if>
                    <if test="staCode != null">#{staCode},</if>
                    <if test="deviceName != null">#{deviceName},</if>
                    <if test="deviceCode != null">#{deviceCode},</if>
                    <if test="deviceType != null">#{deviceType},</if>
                    <if test="staType != null">#{staType},</if>
                    <if test="construction != null">#{construction},</if>
                    <if test="alarmRank != null">#{alarmRank},</if>
                    <if test="alarmType != null">#{alarmType},</if>
                    <if test="addTime != null">#{addTime},</if>
                    <if test="step != null">#{step},</if>
                    <if test="remark != null">#{remark},</if>
                    <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateOpoAlarmMessage" parameterType="com.jhdr.irrigation.entity.param.OpoAlarmMessageEditParam">
        update opo_alarm_message
        <trim prefix="SET" suffixOverrides=",">
                    <if test="alarmCode != null">alarm_code = #{alarmCode},</if>
                    <if test="staName != null">sta_name = #{staName},</if>
                    <if test="staCode != null">sta_code = #{staCode},</if>
                    <if test="deviceName != null">device_name = #{deviceName},</if>
                    <if test="deviceCode != null">device_code = #{deviceCode},</if>
                    <if test="deviceType != null">device_type = #{deviceType},</if>
                    <if test="staType != null">sta_type = #{staType},</if>
                    <if test="construction != null">construction = #{construction},</if>
                    <if test="alarmRank != null">alarm_rank = #{alarmRank},</if>
                    <if test="alarmType != null">alarm_type = #{alarmType},</if>
                    <if test="addTime != null">add_time = #{addTime},</if>
                    <if test="step != null">step = #{step},</if>
                    <if test="remark != null">remark = #{remark},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoAlarmMessageById" parameterType="Long">
        delete from opo_alarm_message where id = #{id}
    </delete>

    <delete id="deleteOpoAlarmMessageByIds" parameterType="String">
        delete from opo_alarm_message where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
