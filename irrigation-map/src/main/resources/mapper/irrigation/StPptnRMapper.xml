<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.StPptnRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.StPptnRPo" id="StPptnRResult">
        <result property="STCD"    column="STCD"    />
        <result property="TM"    column="TM"    />
        <result property="DRP"    column="DRP"    />
        <result property="INTV"    column="INTV"    />
        <result property="PDR"    column="PDR"    />
        <result property="DYP"    column="DYP"    />
        <result property="WTH"    column="WTH"    />
    </resultMap>

    <sql id="selectStPptnRVo">
        select STCD, TM, DRP, INTV, PDR, DYP, WTH from st_pptn_r
    </sql>

    <select id="selectStPptnRList"  resultType="com.jhdr.irrigation.entity.vo.StPptnRVo">

        <if test="type ==null or type == 1 ">
            select STCD,IDTM as TM,ACCP as DRP from st_pstat_r
            <where>
                STCD=#{STCD} and ACCP  is not NULL and STTDRCD = 1
                <if test="TMStart != null  "><!-- 开始时间检索 -->
                    and IDTM &gt;= #{TMStart}
                </if>
                <if test="TMEnd != null "><!-- 结束时间检索 -->
                    and IDTM &lt;= #{TMEnd}
                </if>
            </where>
            order by IDTM asc
        </if>
        <if test="type == 2">
            select STCD, TM, DRP, INTV, PDR, DYP, WTH from st_pptn_r
            <where>
                STCD=#{STCD} and DRP  is not NULL
                <if test="TMStart != null  "><!-- 开始时间检索 -->
                    and TM &gt;= #{TMStart}
                </if>
                <if test="TMEnd != null "><!-- 结束时间检索 -->
                    and TM &lt;= #{TMEnd}
                </if>
            </where>
            order by TM asc
        </if>

    </select>

    <select id="selectStPptnRBySTCD"  resultType="com.jhdr.irrigation.entity.vo.StPptnRVo">
        SELECT ROW_NUMBER
                   ( ) OVER ( PARTITION BY stcd ORDER BY IDTM DESC ) rown,
                stcd,
               IDTM AS tm,
               ACCP AS DRP
        FROM
            st_pstat_r
        WHERE
            STTDRCD = 1
          AND IDTM >= ADDTIME ( sysdate, INTERVAL '-1' DAY )
          and stcd = #{STCD} limit 1
    </select>
    <select id="getRainfallData" resultType="java.util.Map">
        select IDTM as TM,ACCP as DRP from st_pstat_r
        <where>
            STCD=#{STCD} and ACCP  is not NULL and STTDRCD = 1
            <if test="TMStart != null  "><!-- 开始时间检索 -->
                and IDTM &gt;= #{TMStart}
            </if>
            <if test="TMEnd != null "><!-- 结束时间检索 -->
                and IDTM &lt;= #{TMEnd}
            </if>
        </where>
        order by IDTM asc
    </select>

    <insert id="insertStPptnR" parameterType="com.jhdr.irrigation.entity.param.StPptnRAddParam">
        insert into st_pptn_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="STCD != null">STCD,</if>
                    <if test="TM != null">TM,</if>
                    <if test="DRP != null">DRP,</if>
                    <if test="INTV != null">INTV,</if>
                    <if test="PDR != null">PDR,</if>
                    <if test="DYP != null">DYP,</if>
                    <if test="WTH != null">WTH,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="STCD != null">#{STCD},</if>
                    <if test="TM != null">#{TM},</if>
                    <if test="DRP != null">#{DRP},</if>
                    <if test="INTV != null">#{INTV},</if>
                    <if test="PDR != null">#{PDR},</if>
                    <if test="DYP != null">#{DYP},</if>
                    <if test="WTH != null">#{WTH},</if>
        </trim>
    </insert>

    <update id="updateStPptnR" parameterType="com.jhdr.irrigation.entity.param.StPptnREditParam">
        update st_pptn_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="TM != null">TM = #{TM},</if>
                    <if test="DRP != null">DRP = #{DRP},</if>
                    <if test="INTV != null">INTV = #{INTV},</if>
                    <if test="PDR != null">PDR = #{PDR},</if>
                    <if test="DYP != null">DYP = #{DYP},</if>
                    <if test="WTH != null">WTH = #{WTH},</if>
        </trim>
        where STCD = #{STCD}
    </update>

    <delete id="deleteStPptnRBySTCD" parameterType="String">
        delete from st_pptn_r where STCD = #{STCD}
    </delete>

    <delete id="deleteStPptnRBySTCDs" parameterType="String">
        delete from st_pptn_r where STCD in
        <foreach item="STCD" collection="array" open="(" separator="," close=")">
            #{STCD}
        </foreach>
    </delete>
</mapper>
