<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.HubBasicInformationMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.HubBasicInformationPo" id="HubBasicInformationResult">
        <result property="id"    column="id"    />
        <result property="hubCode"    column="hub_code"    />
        <result property="adminArea"    column="admin_area"    />
        <result property="profile"    column="profile"    />
        <result property="status"    column="status"    />
        <result property="gataCode"    column="gata_code"    />
        <result property="name"    column="name"    />
        <result property="pustCode"    column="pust_code"    />
        <result property="lttd"    column="lttd"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="dispatchUnit"    column="dispatch_unit"    />
        <result property="address"    column="address"    />
    </resultMap>

    <sql id="selectHubBasicInformationVo">
        select id, hub_code, admin_area, profile, status, gata_code, name, pust_code, lttd, lgtd, dispatch_unit, address from hub_basic_information
    </sql>

    <select id="selectHubBasicInformationList"  resultType="com.jhdr.irrigation.entity.vo.HubBasicInformationVo">
        <include refid="selectHubBasicInformationVo"/>
        <where>
            <if test="hubCode != null  and hubCode != ''"> and hub_code = #{hubCode}</if>
            <if test="adminArea != null  and adminArea != ''"> and admin_area = #{adminArea}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="gataCode != null "> and gata_code = #{gataCode}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="pustCode != null  and pustCode != ''"> and pust_code = #{pustCode}</if>
            <if test="lttd != null "> and lttd = #{lttd}</if>
            <if test="lgtd != null "> and lgtd = #{lgtd}</if>
            <if test="dispatchUnit != null  and dispatchUnit != ''"> and dispatch_unit = #{dispatchUnit}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
        </where>
    </select>

    <select id="selectHubBasicInformationById"  resultType="com.jhdr.irrigation.entity.vo.HubBasicInformationVo">
            <include refid="selectHubBasicInformationVo"/>
            where id = #{id}
    </select>
    <select id="engineerHubList" resultType="com.jhdr.irrigation.entity.vo.EngineerHubInfoVo">
        SELECT
            hbi.hub_code as hubCd,
            gdd.NM as hubName,
            gdd.lttd,
            gdd.lgtd,
            hbi.gata_code,
            hbi.pust_code,
             0 status
        FROM
            hub_basic_information hbi
        LEFT JOIN gis_datas_d gdd on hbi.hub_code=gdd.CD
        WHERE hbi.status = 1 and gdd."GISSIGN" = 'SN' and gdd.CD is not null

    </select>
    <select id="getProjectInfo" resultType="com.jhdr.irrigation.entity.vo.PreventionProjectVo">
        SELECT
            hbi.hub_code,
            hbi."name",
            hbi.address,
            hbi.dispatch_unit,
            jwb.gaor_num,
            jwb.zpll,
            jwb.waga_type,
            jpb.pump_num,
            jpb.des_flow,
            jpb.en_size
        FROM
            hub_basic_information hbi
                LEFT JOIN jhir_waga_b jwb ON hbi.gata_code = jwb.str_code
                LEFT JOIN jhir_pust_b jpb ON jpb.str_code = hbi.pust_code
        WHERE
            hbi.hub_code = #{hubCd}
    </select>
    <select id="getRainInfos" resultType="com.jhdr.irrigation.entity.vo.PreventionRainVo">
        SELECT
            hbi.hub_code,
            hbi."name",
            jwb.raincd
        from hub_basic_information hbi
        left join jhir_waga_b jwb on hbi.gata_code = jwb.str_code
        where hbi.hub_code in
        <foreach item="hubCd" collection="hubCodes" open="(" separator="," close=")">
            #{hubCd}
        </foreach>

    </select>
    <select id="getWaterInfos" resultType="com.jhdr.irrigation.entity.vo.PreventionWaterVo">
        SELECT
        hbi.hub_code,
        hbi."name",
        hbi.gata_code,
        jwb.ww_z
        FROM
        hub_basic_information hbi
        LEFT JOIN jhir_waga_b jwb ON hbi.gata_code = jwb.str_code
        WHERE
        hbi.hub_code in
        <foreach item="hubCd" collection="hubCodes" open="(" separator="," close=")">
            #{hubCd}
        </foreach>
    </select>

    <insert id="insertHubBasicInformation" parameterType="com.jhdr.irrigation.entity.param.HubBasicInformationAddParam">
        insert into hub_basic_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="hubCode != null">hub_code,</if>
                    <if test="adminArea != null">admin_area,</if>
                    <if test="profile != null">profile,</if>
                    <if test="status != null">status,</if>
                    <if test="gataCode != null">gata_code,</if>
                    <if test="name != null">name,</if>
                    <if test="pustCode != null">pust_code,</if>
                    <if test="lttd != null">lttd,</if>
                    <if test="lgtd != null">lgtd,</if>
                    <if test="dispatchUnit != null">dispatch_unit,</if>
                    <if test="address != null">address,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="hubCode != null">#{hubCode},</if>
                    <if test="adminArea != null">#{adminArea},</if>
                    <if test="profile != null">#{profile},</if>
                    <if test="status != null">#{status},</if>
                    <if test="gataCode != null">#{gataCode},</if>
                    <if test="name != null">#{name},</if>
                    <if test="pustCode != null">#{pustCode},</if>
                    <if test="lttd != null">#{lttd},</if>
                    <if test="lgtd != null">#{lgtd},</if>
                    <if test="dispatchUnit != null">#{dispatchUnit},</if>
                    <if test="address != null">#{address},</if>
        </trim>
    </insert>

    <update id="updateHubBasicInformation" parameterType="com.jhdr.irrigation.entity.param.HubBasicInformationEditParam">
        update hub_basic_information
        <trim prefix="SET" suffixOverrides=",">
                    <if test="hubCode != null">hub_code = #{hubCode},</if>
                    <if test="adminArea != null">admin_area = #{adminArea},</if>
                    <if test="profile != null">profile = #{profile},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="gataCode != null">gata_code = #{gataCode},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="pustCode != null">pust_code = #{pustCode},</if>
                    <if test="lttd != null">lttd = #{lttd},</if>
                    <if test="lgtd != null">lgtd = #{lgtd},</if>
                    <if test="dispatchUnit != null">dispatch_unit = #{dispatchUnit},</if>
                    <if test="address != null">address = #{address},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHubBasicInformationById" parameterType="Long">
        delete from hub_basic_information where id = #{id}
    </delete>

    <delete id="deleteHubBasicInformationByIds" parameterType="String">
        delete from hub_basic_information where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
