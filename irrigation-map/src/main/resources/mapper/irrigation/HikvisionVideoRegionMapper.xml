<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.HikvisionVideoRegionMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.HikvisionVideoRegionPo" id="HikvisionVideoRegionResult">
        <result property="id"    column="id"    />
        <result property="regionPath"    column="region_path"    />
        <result property="cascadeType"    column="cascade_type"    />
        <result property="catalogType"    column="catalog_type"    />
        <result property="cascadeCode"    column="cascade_code"    />
        <result property="available"    column="available"    />
        <result property="indexCode"    column="index_code"    />
        <result property="updateTime"    column="update_time"    />
        <result property="sort"    column="sort"    />
        <result property="leaf"    column="leaf"    />
        <result property="regionCode"    column="region_code"    />
        <result property="createTime"    column="create_time"    />
        <result property="name"    column="name"    />
        <result property="parentIndexCode"    column="parent_index_code"    />
    </resultMap>

    <sql id="selectHikvisionVideoRegionVo">
        select id, region_path, cascade_type, catalog_type, cascade_code, available, index_code, update_time, sort, leaf, region_code, create_time, name, parent_index_code from hikvision_video_region
    </sql>

    <select id="selectHikvisionVideoRegionList"  resultType="com.jhdr.irrigation.entity.vo.HikvisionVideoRegionVo">
        <include refid="selectHikvisionVideoRegionVo"/>
        <where>
            <if test="regionPath != null  and regionPath != ''"> and region_path = #{regionPath}</if>
            <if test="cascadeType != null  and cascadeType != ''"> and cascade_type = #{cascadeType}</if>
            <if test="catalogType != null  and catalogType != ''"> and catalog_type = #{catalogType}</if>
            <if test="cascadeCode != null  and cascadeCode != ''"> and cascade_code = #{cascadeCode}</if>
            <if test="available != null  and available != ''"> and available = #{available}</if>
            <if test="indexCode != null  and indexCode != ''"> and index_code = #{indexCode}</if>
            <if test="sort != null  and sort != ''"> and sort = #{sort}</if>
            <if test="leaf != null  and leaf != ''"> and leaf = #{leaf}</if>
            <if test="regionCode != null  and regionCode != ''"> and region_code = #{regionCode}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="parentIndexCode != null  and parentIndexCode != ''"> and parent_index_code = #{parentIndexCode}</if>
        </where>
    </select>

    <select id="selectHikvisionVideoRegionById"  resultType="com.jhdr.irrigation.entity.vo.HikvisionVideoRegionVo">
            <include refid="selectHikvisionVideoRegionVo"/>
            where id = #{id}
    </select>
    <select id="selectVideo" resultType="java.lang.String">
        select site_url from jhom_vm_b where site_type = '2' and  camera_code=#{indexCode} limit 1
    </select>

    <insert id="insertHikvisionVideoRegion" parameterType="com.jhdr.irrigation.entity.param.HikvisionVideoRegionAddParam">
        insert into hikvision_video_region
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="regionPath != null">region_path,</if>
                    <if test="cascadeType != null">cascade_type,</if>
                    <if test="catalogType != null">catalog_type,</if>
                    <if test="cascadeCode != null">cascade_code,</if>
                    <if test="available != null">available,</if>
                    <if test="indexCode != null">index_code,</if>
                    <if test="updateTime != null">update_time,</if>
                    <if test="sort != null">sort,</if>
                    <if test="leaf != null">leaf,</if>
                    <if test="regionCode != null">region_code,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="name != null">name,</if>
                    <if test="parentIndexCode != null">parent_index_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="regionPath != null">#{regionPath},</if>
                    <if test="cascadeType != null">#{cascadeType},</if>
                    <if test="catalogType != null">#{catalogType},</if>
                    <if test="cascadeCode != null">#{cascadeCode},</if>
                    <if test="available != null">#{available},</if>
                    <if test="indexCode != null">#{indexCode},</if>
                    <if test="updateTime != null">#{updateTime},</if>
                    <if test="sort != null">#{sort},</if>
                    <if test="leaf != null">#{leaf},</if>
                    <if test="regionCode != null">#{regionCode},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="name != null">#{name},</if>
                    <if test="parentIndexCode != null">#{parentIndexCode},</if>
        </trim>
    </insert>

    <update id="updateHikvisionVideoRegion" parameterType="com.jhdr.irrigation.entity.param.HikvisionVideoRegionEditParam">
        update hikvision_video_region
        <trim prefix="SET" suffixOverrides=",">
                    <if test="regionPath != null">region_path = #{regionPath},</if>
                    <if test="cascadeType != null">cascade_type = #{cascadeType},</if>
                    <if test="catalogType != null">catalog_type = #{catalogType},</if>
                    <if test="cascadeCode != null">cascade_code = #{cascadeCode},</if>
                    <if test="available != null">available = #{available},</if>
                    <if test="indexCode != null">index_code = #{indexCode},</if>
                    <if test="updateTime != null">update_time = #{updateTime},</if>
                    <if test="sort != null">sort = #{sort},</if>
                    <if test="leaf != null">leaf = #{leaf},</if>
                    <if test="regionCode != null">region_code = #{regionCode},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="parentIndexCode != null">parent_index_code = #{parentIndexCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHikvisionVideoRegionById" parameterType="Long">
        delete from hikvision_video_region where id = #{id}
    </delete>

    <delete id="deleteHikvisionVideoRegionByIds" parameterType="String">
        delete from hikvision_video_region where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
