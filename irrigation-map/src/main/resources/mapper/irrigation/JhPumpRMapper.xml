<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhPumpRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhPumpRPo" id="JhPumpRResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="ppupz"    column="ppupz"    />
        <result property="ppdwz"    column="ppdwz"    />
        <result property="omcn"    column="omcn"    />
        <result property="ompwr"    column="ompwr"    />
        <result property="pmpq"    column="pmpq"    />
        <result property="wchrcd"    column="wchrcd"    />
        <result property="ppupwptn"    column="ppupwptn"    />
        <result property="ppdwwptn"    column="ppdwwptn"    />
        <result property="msqmt"    column="msqmt"    />
        <result property="pdchcd"    column="pdchcd"    />
        <result property="ppee"    column="ppee"    />
        <result property="ppcn"    column="ppcn"    />
        <result property="ppuab"    column="ppuab"    />
        <result property="ppubc"    column="ppubc"    />
        <result property="ppuca"    column="ppuca"    />
        <result property="ppia"    column="ppia"    />
        <result property="ppib"    column="ppib"    />
        <result property="ppic"    column="ppic"    />
        <result property="ppapwr"    column="ppapwr"    />
        <result property="pprpwr"    column="pprpwr"    />
        <result property="ppcos"    column="ppcos"    />
    </resultMap>

    <sql id="selectJhPumpRVo">
        select prjnmcd, clltm, ppupz, ppdwz, omcn, ompwr, pmpq, wchrcd, ppupwptn, ppdwwptn, msqmt, pdchcd, ppee, ppcn, ppuab, ppubc, ppuca, ppia, ppib, ppic, ppapwr, pprpwr, ppcos from jh_pump_r
    </sql>

    <select id="selectJhPumpRList"  resultType="com.jhdr.irrigation.entity.vo.JhPumpRVo">
        <include refid="selectJhPumpRVo"/>
        <where>
            <if test="clltm != null "> and clltm = #{clltm}</if>
            <if test="ppupz != null "> and ppupz = #{ppupz}</if>
            <if test="ppdwz != null "> and ppdwz = #{ppdwz}</if>
            <if test="omcn != null "> and omcn = #{omcn}</if>
            <if test="ompwr != null "> and ompwr = #{ompwr}</if>
            <if test="pmpq != null "> and pmpq = #{pmpq}</if>
            <if test="wchrcd != null "> and wchrcd = #{wchrcd}</if>
            <if test="ppupwptn != null "> and ppupwptn = #{ppupwptn}</if>
            <if test="ppdwwptn != null "> and ppdwwptn = #{ppdwwptn}</if>
            <if test="msqmt != null "> and msqmt = #{msqmt}</if>
            <if test="pdchcd != null "> and pdchcd = #{pdchcd}</if>
            <if test="ppee != null "> and ppee = #{ppee}</if>
            <if test="ppcn != null "> and ppcn = #{ppcn}</if>
            <if test="ppuab != null "> and ppuab = #{ppuab}</if>
            <if test="ppubc != null "> and ppubc = #{ppubc}</if>
            <if test="ppuca != null "> and ppuca = #{ppuca}</if>
            <if test="ppia != null "> and ppia = #{ppia}</if>
            <if test="ppib != null "> and ppib = #{ppib}</if>
            <if test="ppic != null "> and ppic = #{ppic}</if>
            <if test="ppapwr != null "> and ppapwr = #{ppapwr}</if>
            <if test="pprpwr != null "> and pprpwr = #{pprpwr}</if>
            <if test="ppcos != null "> and ppcos = #{ppcos}</if>
        </where>
    </select>

    <select id="selectJhPumpRByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhPumpRVo">
            <include refid="selectJhPumpRVo"/>
            where prjnmcd = #{prjnmcd} order by clltm desc limit 1
    </select>

    <insert id="insertJhPumpR" parameterType="com.jhdr.irrigation.entity.param.JhPumpRAddParam">
        insert into jh_pump_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null">clltm,</if>
                    <if test="ppupz != null">ppupz,</if>
                    <if test="ppdwz != null">ppdwz,</if>
                    <if test="omcn != null">omcn,</if>
                    <if test="ompwr != null">ompwr,</if>
                    <if test="pmpq != null">pmpq,</if>
                    <if test="wchrcd != null">wchrcd,</if>
                    <if test="ppupwptn != null">ppupwptn,</if>
                    <if test="ppdwwptn != null">ppdwwptn,</if>
                    <if test="msqmt != null">msqmt,</if>
                    <if test="pdchcd != null">pdchcd,</if>
                    <if test="ppee != null">ppee,</if>
                    <if test="ppcn != null">ppcn,</if>
                    <if test="ppuab != null">ppuab,</if>
                    <if test="ppubc != null">ppubc,</if>
                    <if test="ppuca != null">ppuca,</if>
                    <if test="ppia != null">ppia,</if>
                    <if test="ppib != null">ppib,</if>
                    <if test="ppic != null">ppic,</if>
                    <if test="ppapwr != null">ppapwr,</if>
                    <if test="pprpwr != null">pprpwr,</if>
                    <if test="ppcos != null">ppcos,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null">#{clltm},</if>
                    <if test="ppupz != null">#{ppupz},</if>
                    <if test="ppdwz != null">#{ppdwz},</if>
                    <if test="omcn != null">#{omcn},</if>
                    <if test="ompwr != null">#{ompwr},</if>
                    <if test="pmpq != null">#{pmpq},</if>
                    <if test="wchrcd != null">#{wchrcd},</if>
                    <if test="ppupwptn != null">#{ppupwptn},</if>
                    <if test="ppdwwptn != null">#{ppdwwptn},</if>
                    <if test="msqmt != null">#{msqmt},</if>
                    <if test="pdchcd != null">#{pdchcd},</if>
                    <if test="ppee != null">#{ppee},</if>
                    <if test="ppcn != null">#{ppcn},</if>
                    <if test="ppuab != null">#{ppuab},</if>
                    <if test="ppubc != null">#{ppubc},</if>
                    <if test="ppuca != null">#{ppuca},</if>
                    <if test="ppia != null">#{ppia},</if>
                    <if test="ppib != null">#{ppib},</if>
                    <if test="ppic != null">#{ppic},</if>
                    <if test="ppapwr != null">#{ppapwr},</if>
                    <if test="pprpwr != null">#{pprpwr},</if>
                    <if test="ppcos != null">#{ppcos},</if>
        </trim>
    </insert>

    <update id="updateJhPumpR" parameterType="com.jhdr.irrigation.entity.param.JhPumpREditParam">
        update jh_pump_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null">clltm = #{clltm},</if>
                    <if test="ppupz != null">ppupz = #{ppupz},</if>
                    <if test="ppdwz != null">ppdwz = #{ppdwz},</if>
                    <if test="omcn != null">omcn = #{omcn},</if>
                    <if test="ompwr != null">ompwr = #{ompwr},</if>
                    <if test="pmpq != null">pmpq = #{pmpq},</if>
                    <if test="wchrcd != null">wchrcd = #{wchrcd},</if>
                    <if test="ppupwptn != null">ppupwptn = #{ppupwptn},</if>
                    <if test="ppdwwptn != null">ppdwwptn = #{ppdwwptn},</if>
                    <if test="msqmt != null">msqmt = #{msqmt},</if>
                    <if test="pdchcd != null">pdchcd = #{pdchcd},</if>
                    <if test="ppee != null">ppee = #{ppee},</if>
                    <if test="ppcn != null">ppcn = #{ppcn},</if>
                    <if test="ppuab != null">ppuab = #{ppuab},</if>
                    <if test="ppubc != null">ppubc = #{ppubc},</if>
                    <if test="ppuca != null">ppuca = #{ppuca},</if>
                    <if test="ppia != null">ppia = #{ppia},</if>
                    <if test="ppib != null">ppib = #{ppib},</if>
                    <if test="ppic != null">ppic = #{ppic},</if>
                    <if test="ppapwr != null">ppapwr = #{ppapwr},</if>
                    <if test="pprpwr != null">pprpwr = #{pprpwr},</if>
                    <if test="ppcos != null">ppcos = #{ppcos},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhPumpRByPrjnmcd" parameterType="String">
        delete from jh_pump_r where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhPumpRByPrjnmcds" parameterType="String">
        delete from jh_pump_r where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
