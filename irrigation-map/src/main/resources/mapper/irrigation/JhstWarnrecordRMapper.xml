<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhstWarnrecordRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhstWarnrecordRPo" id="JhstWarnrecordRResult">
        <result property="id"    column="id"    />
        <result property="stWarnTm"    column="st_warn_tm"    />
        <result property="warnNm"    column="warn_nm"    />
        <result property="warnType"    column="warn_type"    />
        <result property="warnGrade"    column="warn_grade"    />
        <result property="warnSource"    column="warn_source"    />
        <result property="warnStatus"    column="warn_status"    />
        <result property="stcd"    column="stcd"    />
        <result property="stnm"    column="stnm"    />
        <result property="bsnm"    column="bsnm"    />
        <result property="hnnm"    column="hnnm"    />
        <result property="rvnm"    column="rvnm"    />
        <result property="stlc"    column="stlc"    />
        <result property="addvcd"    column="addvcd"    />
        <result property="countyNm"    column="county_nm"    />
        <result property="townNm"    column="town_nm"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="lttd"    column="lttd"    />
        <result property="warnC1"    column="warn_c1"    />
        <result property="warnC2"    column="warn_c2"    />
        <result property="warnC3"    column="warn_c3"    />
        <result property="warnSummary"    column="warn_summary"    />
        <result property="sender"    column="sender"    />
        <result property="senderTm"    column="sender_tm"    />
        <result property="ctm"    column="ctm"    />
    </resultMap>

    <sql id="selectJhstWarnrecordRVo">
        select id, st_warn_tm, warn_nm, warn_type, warn_grade, warn_source, warn_status, stcd, stnm, bsnm, hnnm, rvnm, stlc, addvcd, county_nm, town_nm, lgtd, lttd, warn_c1, warn_c2, warn_c3, warn_summary, sender, sender_tm, ctm from jhst_warnrecord_r
    </sql>

    <select id="selectJhstWarnrecordRList"  resultType="com.jhdr.irrigation.entity.vo.JhstWarnrecordRVo">
        <include refid="selectJhstWarnrecordRVo"/>
        <where>
            <if test="stWarnTm != null "> and st_warn_tm = #{stWarnTm}</if>
            <if test="warnNm != null  and warnNm != ''"> and warn_nm = #{warnNm}</if>
            <if test="warnType != null  and warnType != ''"> and warn_type = #{warnType}</if>
            <if test="warnGrade != null  and warnGrade != ''"> and warn_grade = #{warnGrade}</if>
            <if test="warnSource != null  and warnSource != ''"> and warn_source = #{warnSource}</if>
            <if test="warnStatus != null  and warnStatus != ''"> and warn_status = #{warnStatus}</if>
            <if test="stcd != null  and stcd != ''"> and stcd = #{stcd}</if>
            <if test="stnm != null  and stnm != ''"> and stnm = #{stnm}</if>
            <if test="bsnm != null  and bsnm != ''"> and bsnm = #{bsnm}</if>
            <if test="hnnm != null  and hnnm != ''"> and hnnm = #{hnnm}</if>
            <if test="rvnm != null  and rvnm != ''"> and rvnm = #{rvnm}</if>
            <if test="stlc != null  and stlc != ''"> and stlc = #{stlc}</if>
            <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
            <if test="countyNm != null  and countyNm != ''"> and county_nm = #{countyNm}</if>
            <if test="townNm != null  and townNm != ''"> and town_nm = #{townNm}</if>
            <if test="lgtd != null "> and lgtd = #{lgtd}</if>
            <if test="lttd != null "> and lttd = #{lttd}</if>
            <if test="warnC1 != null  and warnC1 != ''"> and warn_c1 = #{warnC1}</if>
            <if test="warnC2 != null  and warnC2 != ''"> and warn_c2 = #{warnC2}</if>
            <if test="warnC3 != null  and warnC3 != ''"> and warn_c3 = #{warnC3}</if>
            <if test="warnSummary != null  and warnSummary != ''"> and warn_summary = #{warnSummary}</if>
            <if test="sender != null  and sender != ''"> and sender = #{sender}</if>
            <if test="senderTm != null "> and sender_tm = #{senderTm}</if>
            <if test="ctm != null "> and ctm = #{ctm}</if>
        </where>
    </select>

    <select id="selectJhstWarnrecordRById"  resultType="com.jhdr.irrigation.entity.vo.JhstWarnrecordRVo">
            <include refid="selectJhstWarnrecordRVo"/>
            where id = #{id}
    </select>

    <insert id="insertJhstWarnrecordR" parameterType="com.jhdr.irrigation.entity.param.JhstWarnrecordRAddParam">
        insert into jhst_warnrecord_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="stWarnTm != null">st_warn_tm,</if>
                    <if test="warnNm != null">warn_nm,</if>
                    <if test="warnType != null">warn_type,</if>
                    <if test="warnGrade != null">warn_grade,</if>
                    <if test="warnSource != null">warn_source,</if>
                    <if test="warnStatus != null">warn_status,</if>
                    <if test="stcd != null">stcd,</if>
                    <if test="stnm != null">stnm,</if>
                    <if test="bsnm != null">bsnm,</if>
                    <if test="hnnm != null">hnnm,</if>
                    <if test="rvnm != null">rvnm,</if>
                    <if test="stlc != null">stlc,</if>
                    <if test="addvcd != null">addvcd,</if>
                    <if test="countyNm != null">county_nm,</if>
                    <if test="townNm != null">town_nm,</if>
                    <if test="lgtd != null">lgtd,</if>
                    <if test="lttd != null">lttd,</if>
                    <if test="warnC1 != null">warn_c1,</if>
                    <if test="warnC2 != null">warn_c2,</if>
                    <if test="warnC3 != null">warn_c3,</if>
                    <if test="warnSummary != null">warn_summary,</if>
                    <if test="sender != null">sender,</if>
                    <if test="senderTm != null">sender_tm,</if>
                    <if test="ctm != null">ctm,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="stWarnTm != null">#{stWarnTm},</if>
                    <if test="warnNm != null">#{warnNm},</if>
                    <if test="warnType != null">#{warnType},</if>
                    <if test="warnGrade != null">#{warnGrade},</if>
                    <if test="warnSource != null">#{warnSource},</if>
                    <if test="warnStatus != null">#{warnStatus},</if>
                    <if test="stcd != null">#{stcd},</if>
                    <if test="stnm != null">#{stnm},</if>
                    <if test="bsnm != null">#{bsnm},</if>
                    <if test="hnnm != null">#{hnnm},</if>
                    <if test="rvnm != null">#{rvnm},</if>
                    <if test="stlc != null">#{stlc},</if>
                    <if test="addvcd != null">#{addvcd},</if>
                    <if test="countyNm != null">#{countyNm},</if>
                    <if test="townNm != null">#{townNm},</if>
                    <if test="lgtd != null">#{lgtd},</if>
                    <if test="lttd != null">#{lttd},</if>
                    <if test="warnC1 != null">#{warnC1},</if>
                    <if test="warnC2 != null">#{warnC2},</if>
                    <if test="warnC3 != null">#{warnC3},</if>
                    <if test="warnSummary != null">#{warnSummary},</if>
                    <if test="sender != null">#{sender},</if>
                    <if test="senderTm != null">#{senderTm},</if>
                    <if test="ctm != null">#{ctm},</if>
        </trim>
    </insert>

    <update id="updateJhstWarnrecordR" parameterType="com.jhdr.irrigation.entity.param.JhstWarnrecordREditParam">
        update jhst_warnrecord_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="stWarnTm != null">st_warn_tm = #{stWarnTm},</if>
                    <if test="warnNm != null">warn_nm = #{warnNm},</if>
                    <if test="warnType != null">warn_type = #{warnType},</if>
                    <if test="warnGrade != null">warn_grade = #{warnGrade},</if>
                    <if test="warnSource != null">warn_source = #{warnSource},</if>
                    <if test="warnStatus != null">warn_status = #{warnStatus},</if>
                    <if test="stcd != null">stcd = #{stcd},</if>
                    <if test="stnm != null">stnm = #{stnm},</if>
                    <if test="bsnm != null">bsnm = #{bsnm},</if>
                    <if test="hnnm != null">hnnm = #{hnnm},</if>
                    <if test="rvnm != null">rvnm = #{rvnm},</if>
                    <if test="stlc != null">stlc = #{stlc},</if>
                    <if test="addvcd != null">addvcd = #{addvcd},</if>
                    <if test="countyNm != null">county_nm = #{countyNm},</if>
                    <if test="townNm != null">town_nm = #{townNm},</if>
                    <if test="lgtd != null">lgtd = #{lgtd},</if>
                    <if test="lttd != null">lttd = #{lttd},</if>
                    <if test="warnC1 != null">warn_c1 = #{warnC1},</if>
                    <if test="warnC2 != null">warn_c2 = #{warnC2},</if>
                    <if test="warnC3 != null">warn_c3 = #{warnC3},</if>
                    <if test="warnSummary != null">warn_summary = #{warnSummary},</if>
                    <if test="sender != null">sender = #{sender},</if>
                    <if test="senderTm != null">sender_tm = #{senderTm},</if>
                    <if test="ctm != null">ctm = #{ctm},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJhstWarnrecordRById" parameterType="Long">
        delete from jhst_warnrecord_r where id = #{id}
    </delete>

    <delete id="deleteJhstWarnrecordRByIds" parameterType="String">
        delete from jhst_warnrecord_r where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
