<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhPumpDtlRRealMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhPumpDtlRRealPo" id="JhPumpDtlRRealResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="pmpcd"    column="pmpcd"    />
        <result property="om"    column="om"    />
        <result property="uab"    column="uab"    />
        <result property="ubc"    column="ubc"    />
        <result property="uca"    column="uca"    />
        <result property="ia"    column="ia"    />
        <result property="ib"    column="ib"    />
        <result property="ic"    column="ic"    />
        <result property="apwr"    column="apwr"    />
        <result property="rpwr"    column="rpwr"    />
        <result property="cos"    column="cos"    />
        <result property="ee"    column="ee"    />
        <result property="q"    column="q"    />
    </resultMap>

    <sql id="selectJhPumpDtlRRealVo">
        select prjnmcd, clltm, pmpcd, om, uab, ubc, uca, ia, ib, ic, apwr, rpwr, cos, ee, q from jh_pump_dtl_r_real
    </sql>

    <select id="selectJhPumpDtlRRealList"  resultType="com.jhdr.irrigation.entity.vo.JhPumpDtlRRealVo">
        <include refid="selectJhPumpDtlRRealVo"/>
        <where>
            <if test="clltm != null  and clltm != ''"> and clltm = #{clltm}</if>
            <if test="pmpcd != null "> and pmpcd = #{pmpcd}</if>
            <if test="om != null "> and om = #{om}</if>
            <if test="uab != null "> and uab = #{uab}</if>
            <if test="ubc != null "> and ubc = #{ubc}</if>
            <if test="uca != null "> and uca = #{uca}</if>
            <if test="ia != null "> and ia = #{ia}</if>
            <if test="ib != null "> and ib = #{ib}</if>
            <if test="ic != null "> and ic = #{ic}</if>
            <if test="apwr != null "> and apwr = #{apwr}</if>
            <if test="rpwr != null "> and rpwr = #{rpwr}</if>
            <if test="cos != null "> and cos = #{cos}</if>
            <if test="ee != null "> and ee = #{ee}</if>
            <if test="q != null "> and q = #{q}</if>
        </where>
    </select>

    <select id="selectJhPumpDtlRRealByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhPumpDtlRRealVo">
            <include refid="selectJhPumpDtlRRealVo"/>
            where prjnmcd = #{prjnmcd}
    </select>
    <select id="getPumpDtlRInfoVos" resultType="com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo">
        select prjnmcd, pmpcd, om, ee, q,accq from jh_pump_dtl_r_real
        where prjnmcd = #{prjnmcd}  order by pmpcd asc
    </select>

    <insert id="insertJhPumpDtlRReal" parameterType="com.jhdr.irrigation.entity.param.JhPumpDtlRRealAddParam">
        insert into jh_pump_dtl_r_real
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null and clltm != ''">clltm,</if>
                    <if test="pmpcd != null">pmpcd,</if>
                    <if test="om != null">om,</if>
                    <if test="uab != null">uab,</if>
                    <if test="ubc != null">ubc,</if>
                    <if test="uca != null">uca,</if>
                    <if test="ia != null">ia,</if>
                    <if test="ib != null">ib,</if>
                    <if test="ic != null">ic,</if>
                    <if test="apwr != null">apwr,</if>
                    <if test="rpwr != null">rpwr,</if>
                    <if test="cos != null">cos,</if>
                    <if test="ee != null">ee,</if>
                    <if test="q != null">q,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null and clltm != ''">#{clltm},</if>
                    <if test="pmpcd != null">#{pmpcd},</if>
                    <if test="om != null">#{om},</if>
                    <if test="uab != null">#{uab},</if>
                    <if test="ubc != null">#{ubc},</if>
                    <if test="uca != null">#{uca},</if>
                    <if test="ia != null">#{ia},</if>
                    <if test="ib != null">#{ib},</if>
                    <if test="ic != null">#{ic},</if>
                    <if test="apwr != null">#{apwr},</if>
                    <if test="rpwr != null">#{rpwr},</if>
                    <if test="cos != null">#{cos},</if>
                    <if test="ee != null">#{ee},</if>
                    <if test="q != null">#{q},</if>
        </trim>
    </insert>

    <update id="updateJhPumpDtlRReal" parameterType="com.jhdr.irrigation.entity.param.JhPumpDtlRRealEditParam">
        update jh_pump_dtl_r_real
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null and clltm != ''">clltm = #{clltm},</if>
                    <if test="pmpcd != null">pmpcd = #{pmpcd},</if>
                    <if test="om != null">om = #{om},</if>
                    <if test="uab != null">uab = #{uab},</if>
                    <if test="ubc != null">ubc = #{ubc},</if>
                    <if test="uca != null">uca = #{uca},</if>
                    <if test="ia != null">ia = #{ia},</if>
                    <if test="ib != null">ib = #{ib},</if>
                    <if test="ic != null">ic = #{ic},</if>
                    <if test="apwr != null">apwr = #{apwr},</if>
                    <if test="rpwr != null">rpwr = #{rpwr},</if>
                    <if test="cos != null">cos = #{cos},</if>
                    <if test="ee != null">ee = #{ee},</if>
                    <if test="q != null">q = #{q},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhPumpDtlRRealByPrjnmcd" parameterType="String">
        delete from jh_pump_dtl_r_real where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhPumpDtlRRealByPrjnmcds" parameterType="String">
        delete from jh_pump_dtl_r_real where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
