<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhSlcrsDtlLogRMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhSlcrsDtlLogRPo" id="JhSlcrsDtlLogRResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="gpcd"    column="gpcd"    />
        <result property="operator"    column="operator"    />
        <result property="om"    column="om"    />
    </resultMap>

    <sql id="selectJhSlcrsDtlLogRVo">
        select prjnmcd, clltm, gpcd, operator, om from jh_slcrs_dtl_log_r
    </sql>

    <select id="selectJhSlcrsDtlLogRList"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlLogRVo">
        <include refid="selectJhSlcrsDtlLogRVo"/>
        <where>
            prjnmcd = #{prjnmcd}
            <if test="clltmStart != null and clltmStart != '' "> and clltm &gt;= #{clltmStart}</if>
            <if test="clltmEnd != null and clltmEnd != '' "> and clltm &lt;= #{clltmEnd}</if>
        </where>
        order by clltm desc
    </select>

    <select id="selectJhSlcrsDtlLogRByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlLogRVo">
            <include refid="selectJhSlcrsDtlLogRVo"/>
            where prjnmcd = #{prjnmcd}
    </select>

    <insert id="insertJhSlcrsDtlLogR" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsDtlLogRAddParam">
        insert into jh_slcrs_dtl_log_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null">clltm,</if>
                    <if test="gpcd != null">gpcd,</if>
                    <if test="operator != null">operator,</if>
                    <if test="om != null">om,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null">#{clltm},</if>
                    <if test="gpcd != null">#{gpcd},</if>
                    <if test="operator != null">#{operator},</if>
                    <if test="om != null">#{om},</if>
        </trim>
    </insert>

    <update id="updateJhSlcrsDtlLogR" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsDtlLogREditParam">
        update jh_slcrs_dtl_log_r
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null">clltm = #{clltm},</if>
                    <if test="gpcd != null">gpcd = #{gpcd},</if>
                    <if test="operator != null">operator = #{operator},</if>
                    <if test="om != null">om = #{om},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhSlcrsDtlLogRByPrjnmcd" parameterType="String">
        delete from jh_slcrs_dtl_log_r where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhSlcrsDtlLogRByPrjnmcds" parameterType="String">
        delete from jh_slcrs_dtl_log_r where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
