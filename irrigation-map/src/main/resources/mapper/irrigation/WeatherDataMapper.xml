<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.WeatherDataMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.WeatherDataPo" id="WeatherDataResult">
        <result property="id"    column="id"    />
        <result property="region"    column="region"    />
        <result property="location"    column="location"    />
        <result property="weatherJson"    column="weather_json"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectWeatherDataVo">
        select id, region, location, weather_json, status, create_time from weather_data
    </sql>

    <select id="selectWeatherDataList"  resultType="com.jhdr.irrigation.entity.vo.WeatherDataVo">
        <include refid="selectWeatherDataVo"/>
        <where>
            <if test="region != null  and region != ''"> and region = #{region}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="weatherJson != null  and weatherJson != ''"> and weather_json = #{weatherJson}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectWeatherDataById"  resultType="com.jhdr.irrigation.entity.vo.WeatherDataVo">
            <include refid="selectWeatherDataVo"/>
            where id = #{id}
    </select>

    <insert id="insertWeatherData" parameterType="com.jhdr.irrigation.entity.param.WeatherDataAddParam">
        insert into weather_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="region != null">region,</if>
                    <if test="location != null">location,</if>
                    <if test="weatherJson != null">weather_json,</if>
                    <if test="status != null">status,</if>
                    <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="region != null">#{region},</if>
                    <if test="location != null">#{location},</if>
                    <if test="weatherJson != null">#{weatherJson},</if>
                    <if test="status != null">#{status},</if>
                    <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateWeatherData" parameterType="com.jhdr.irrigation.entity.param.WeatherDataEditParam">
        update weather_data
        <trim prefix="SET" suffixOverrides=",">
                    <if test="region != null">region = #{region},</if>
                    <if test="location != null">location = #{location},</if>
                    <if test="weatherJson != null">weather_json = #{weatherJson},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateDataStatus">
        update weather_data set status = 0 where status = 1
    </update>

    <delete id="deleteWeatherDataById" parameterType="Long">
        delete from weather_data where id = #{id}
    </delete>

    <delete id="deleteWeatherDataByIds" parameterType="String">
        delete from weather_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
