<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationProcessFileMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.IrrigationProcessFilePo" id="IrrigationProcessFileResult">
        <result property="id"    column="id"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileType"    column="file_type"    />
        <result property="moduleType"    column="module_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="moduleId"    column="module_id"    />
    </resultMap>

    <sql id="selectIrrigationProcessFileVo">
        select id, file_name, file_url, file_type, module_type, create_time, module_id from irrigation_process_file
    </sql>

    <select id="selectIrrigationProcessFileList"  resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessFileVo">
        <include refid="selectIrrigationProcessFileVo"/>
        <where>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="moduleType != null  and moduleType != ''"> and module_type = #{moduleType}</if>
            <if test="moduleId != null "> and module_id = #{moduleId}</if>
            <if test="startTime != null">and create_time between #{startTime} and #{endTime}</if>

        </where>
        order by id
    </select>

    <select id="selectIrrigationProcessFileById"  resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessFileVo">
            <include refid="selectIrrigationProcessFileVo"/>
            where id = #{id}
    </select>

    <insert id="insertIrrigationProcessFile" parameterType="com.jhdr.irrigation.entity.param.IrrigationProcessFileAddParam">
        insert into irrigation_process_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="fileName != null">file_name,</if>
                    <if test="fileUrl != null">file_url,</if>
                    <if test="fileType != null">file_type,</if>
                    <if test="moduleType != null">module_type,</if>
                    <if test="createTime != null">create_time,</if>
                    <if test="moduleId != null">module_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="fileName != null">#{fileName},</if>
                    <if test="fileUrl != null">#{fileUrl},</if>
                    <if test="fileType != null">#{fileType},</if>
                    <if test="moduleType != null">#{moduleType},</if>
                    <if test="createTime != null">#{createTime},</if>
                    <if test="moduleId != null">#{moduleId},</if>
        </trim>
    </insert>

    <update id="updateIrrigationProcessFile" parameterType="com.jhdr.irrigation.entity.param.IrrigationProcessFileEditParam">
        update irrigation_process_file
        <trim prefix="SET" suffixOverrides=",">
                    <if test="fileName != null">file_name = #{fileName},</if>
                    <if test="fileUrl != null">file_url = #{fileUrl},</if>
                    <if test="fileType != null">file_type = #{fileType},</if>
                    <if test="moduleType != null">module_type = #{moduleType},</if>
                    <if test="createTime != null">create_time = #{createTime},</if>
                    <if test="moduleId != null">module_id = #{moduleId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIrrigationProcessFileById" parameterType="Long">
        delete from irrigation_process_file where id = #{id}
    </delete>

    <delete id="deleteIrrigationProcessFileByIds" parameterType="String">
        delete from irrigation_process_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
