<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhSlcrsRRealMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhSlcrsRRealPo" id="JhSlcrsRRealResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="slupsz"    column="slupsz"    />
        <result property="sldsz"    column="sldsz"    />
        <result property="thrslq"    column="thrslq"    />
        <result property="gtopn"    column="gtopn"    />
        <result property="upswtp"    column="upswtp"    />
        <result property="dswtp"    column="dswtp"    />
        <result property="rpdpcd"    column="rpdpcd"    />
        <result property="reporter"    column="reporter"    />
        <result property="rpttlnmb"    column="rpttlnmb"    />
        <result property="rmk"    column="rmk"    />
        <result property="gtcn"    column="gtcn"    />
    </resultMap>

    <sql id="selectJhSlcrsRRealVo">
        select prjnmcd, clltm, slupsz, sldsz, thrslq, gtopn, upswtp, dswtp, rpdpcd, reporter, rpttlnmb, rmk, gtcn from jh_slcrs_r_real
    </sql>

    <resultMap type="com.jhdr.irrigation.entity.vo.IrrigationProcessGateVo" id="ProcessGate">
        <result property="strCode"    column="str_code"    />
        <result property="strName"    column="str_name"    />
        <result property="slupsz"    column="slupsz"    />
        <result property="sldsz"    column="sldsz"    />
        <result property="thrslq"    column="thrslq"    />
        <result property="status"    column="status"    />
        <collection  property="gateCondition" ofType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlRInfoVo">
            <result column="gpcd" property="gpcd"/>
            <result column="gtoph" property="gtoph"/>
            <result column="gtq" property="gtq"/>
            <result column="accq" property="accq"/>
        </collection >

    </resultMap>

    <select id="selectJhSlcrsRRealList"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsRRealVo">
        <include refid="selectJhSlcrsRRealVo"/>
        <where>
            <if test="clltm != null "> and clltm = #{clltm}</if>
            <if test="sldsz != null "> and sldsz = #{sldsz}</if>
            <if test="thrslq != null "> and thrslq = #{thrslq}</if>
            <if test="gtopn != null "> and gtopn = #{gtopn}</if>
            <if test="upswtp != null "> and upswtp = #{upswtp}</if>
            <if test="dswtp != null "> and dswtp = #{dswtp}</if>
            <if test="rpdpcd != null  and rpdpcd != ''"> and rpdpcd = #{rpdpcd}</if>
            <if test="reporter != null  and reporter != ''"> and reporter = #{reporter}</if>
            <if test="rpttlnmb != null  and rpttlnmb != ''"> and rpttlnmb = #{rpttlnmb}</if>
            <if test="rmk != null  and rmk != ''"> and rmk = #{rmk}</if>
            <if test="gtcn != null "> and gtcn = #{gtcn}</if>
        </where>
    </select>

    <select id="selectJhSlcrsRRealByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhSlcrsRRealVo">

        select prjnmcd, clltm, slupsz, sldsz, thrslq, gtopn, upswtp,
               dswtp, rpdpcd, reporter, rpttlnmb, rmk, gtcn,accq from jh_slcrs_r_real
            where prjnmcd = #{prjnmcd}   LIMIT 1
    </select>
    <select id="getSlcrsRRealInfoVos" resultType="com.jhdr.irrigation.entity.vo.JhSlcrsDtlRInfoVo">
        select prjnmcd, clltm, gpcd,gtoph,case when gtoph>0 then '1' else '0' end as om,gtq,accq from chxh.jh_slcrs_dtl_r_real
        where prjnmcd = #{prjnmcd}  order by gpcd asc
    </select>
    <select id="getGateStationOpenNum" resultType="java.lang.Integer">
        select count(1) from chxh.jh_slcrs_r_real jsrr
        left join gis_datas_d gdd on gdd.CD = jsrr.prjnmcd
        where jsrr.gtopn >0  and gdd.CD is not null
    </select>
    <select id="engineerGateStationCount"
            resultType="com.jhdr.irrigation.entity.vo.EngineerGateStationCountVo">
        SELECT
            gdd.cd AS gateStationCd,
            gdd.NM AS gateStationName,
            gdd.lttd,gdd.lgtd,
            slupsz,
            sldsz,
            COALESCE(thrslq, 0) as thrslq,
            upswtp,
            clltm,
            dswtp,
            CASE
                WHEN jsrr.gtopn > 0 THEN
                    '1' ELSE'0'
                END AS status
        FROM
            gis_datas_d gdd
                LEFT JOIN jh_slcrs_r_real jsrr  ON gdd.CD = jsrr.prjnmcd
        WHERE
            gdd.CD IS NOT NULL
          AND gdd."GISSIGN" = 'HZ'
        order  by  gdd.lgtd
    </select>
    <select id="temporaryData" resultType="com.jhdr.irrigation.entity.vo.JhSlcrsRRealTemporaryDataVo">
        SELECT
            TRUNC(upz, 2) AS slupsz,
            dwz sldsz,
            supwptnex_day upswtp,
            sdwwptnex_day dswtp
        FROM
            jh_upz_r_real
        WHERE
            tm >= ADDTIME ( sysdate, INTERVAL '-3' HOUR )
          AND str_code = #{prjnmcd}
        ORDER BY
            tm DESC
            LIMIT 1
    </select>
    <select id="gateList" resultMap="ProcessGate">
        SELECT
            gdd.str_code ,
            gdd.str_name ,
        TRUNC(jurr.upz, 2) AS slupsz,
        jurr.dwz sldsz,
            jsrr.thrslq,
            CASE
                WHEN jsrr.gtopn > 0 THEN
                    '1' ELSE'0'
                END AS status,
            jd.gpcd,
            jd.gtoph,
            jd.gtq,
            jd.accq
        FROM
            jhir_waga_b gdd
        LEFT JOIN jh_slcrs_r_real jsrr  ON gdd.str_code = jsrr.prjnmcd
        LEFT JOIN jh_upz_r_real jurr  ON gdd.str_code = jurr.str_code and jurr.tm >= ADDTIME ( sysdate, INTERVAL '-3' HOUR )
        left join jh_slcrs_dtl_r_real jd on jd.prjnmcd = jsrr.prjnmcd
        WHERE
            gdd.status = 1
          <if test="conditionType != null and conditionType  == 1 ">
              AND gdd.major_type in ('1','2')
          </if>
          <if test="conditionType != null and conditionType == 2">
              AND gdd.major_type in ('3')
          </if>
          <if test="conditionType != null and conditionType == 3">
              AND gdd.major_type in ('4','5')
          </if>
        order  by  gdd.lgtd, jd.gpcd
    </select>

    <select id="gateListCount" resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessGateVo">
        SELECT
        gdd.str_code ,
        gdd.str_name ,
        TRUNC(jurr.upz, 2) AS slupsz,
        jurr.dwz sldsz,
        jsrr.thrslq,
        CASE
        WHEN jsrr.gtopn > 0 THEN
        '1' ELSE'0'
        END AS status
        FROM
        jhir_waga_b gdd
        LEFT JOIN jh_slcrs_r_real jsrr  ON gdd.str_code = jsrr.prjnmcd
        LEFT JOIN jh_upz_r_real jurr  ON gdd.str_code = jurr.str_code and jurr.tm >= ADDTIME ( sysdate, INTERVAL '-3' HOUR )
        WHERE
        gdd.status = 1
        <if test="conditionType != null and conditionType  == 1 ">
            AND gdd.major_type in ('1','2')
        </if>
        <if test="conditionType != null and conditionType == 2">
            AND gdd.major_type in ('3')
        </if>
        <if test="conditionType != null and conditionType == 3">
            AND gdd.major_type in ('4','5')
        </if>
        order  by  gdd.lgtd
    </select>
    <select id="yearAccq" resultType="java.math.BigDecimal">
        -- 查询当年的累计流量差值
        SELECT
            (MAX(accq) - MIN(accq)) AS year_total_accq
        FROM jh_slcrs_r
        WHERE clltm >= DATE_TRUNC('YEAR', CURRENT_DATE)
          AND clltm <![CDATA[<]]>  DATE_TRUNC('YEAR', CURRENT_DATE) + INTERVAL '1 YEAR' and prjnmcd =#{prjnmcd};
    </select>

    <insert id="insertJhSlcrsRReal" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsRRealAddParam">
        insert into jh_slcrs_r_real
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null">clltm,</if>
                    <if test="slupsz != null">slupsz,</if>
                    <if test="sldsz != null">sldsz,</if>
                    <if test="thrslq != null">thrslq,</if>
                    <if test="gtopn != null">gtopn,</if>
                    <if test="upswtp != null">upswtp,</if>
                    <if test="dswtp != null">dswtp,</if>
                    <if test="rpdpcd != null">rpdpcd,</if>
                    <if test="reporter != null">reporter,</if>
                    <if test="rpttlnmb != null">rpttlnmb,</if>
                    <if test="rmk != null">rmk,</if>
                    <if test="gtcn != null">gtcn,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null">#{clltm},</if>
                    <if test="slupsz != null">#{slupsz},</if>
                    <if test="sldsz != null">#{sldsz},</if>
                    <if test="thrslq != null">#{thrslq},</if>
                    <if test="gtopn != null">#{gtopn},</if>
                    <if test="upswtp != null">#{upswtp},</if>
                    <if test="dswtp != null">#{dswtp},</if>
                    <if test="rpdpcd != null">#{rpdpcd},</if>
                    <if test="reporter != null">#{reporter},</if>
                    <if test="rpttlnmb != null">#{rpttlnmb},</if>
                    <if test="rmk != null">#{rmk},</if>
                    <if test="gtcn != null">#{gtcn},</if>
        </trim>
    </insert>

    <update id="updateJhSlcrsRReal" parameterType="com.jhdr.irrigation.entity.param.JhSlcrsRRealEditParam">
        update jh_slcrs_r_real
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null">clltm = #{clltm},</if>
                    <if test="slupsz != null">slupsz = #{slupsz},</if>
                    <if test="sldsz != null">sldsz = #{sldsz},</if>
                    <if test="thrslq != null">thrslq = #{thrslq},</if>
                    <if test="gtopn != null">gtopn = #{gtopn},</if>
                    <if test="upswtp != null">upswtp = #{upswtp},</if>
                    <if test="dswtp != null">dswtp = #{dswtp},</if>
                    <if test="rpdpcd != null">rpdpcd = #{rpdpcd},</if>
                    <if test="reporter != null">reporter = #{reporter},</if>
                    <if test="rpttlnmb != null">rpttlnmb = #{rpttlnmb},</if>
                    <if test="rmk != null">rmk = #{rmk},</if>
                    <if test="gtcn != null">gtcn = #{gtcn},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhSlcrsRRealByPrjnmcd" parameterType="String">
        delete from jh_slcrs_r_real where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhSlcrsRRealByPrjnmcds" parameterType="String">
        delete from jh_slcrs_r_real where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
