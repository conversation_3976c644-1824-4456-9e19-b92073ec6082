<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.JhPumpRRealMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.JhPumpRRealPo" id="JhPumpRRealResult">
        <result property="prjnmcd"    column="prjnmcd"    />
        <result property="clltm"    column="clltm"    />
        <result property="ppupz"    column="ppupz"    />
        <result property="ppdwz"    column="ppdwz"    />
        <result property="omcn"    column="omcn"    />
        <result property="ompwr"    column="ompwr"    />
        <result property="pmpq"    column="pmpq"    />
        <result property="wchrcd"    column="wchrcd"    />
        <result property="ppupwptn"    column="ppupwptn"    />
        <result property="ppdwwptn"    column="ppdwwptn"    />
        <result property="msqmt"    column="msqmt"    />
        <result property="pdchcd"    column="pdchcd"    />
        <result property="ppee"    column="ppee"    />
        <result property="ppcn"    column="ppcn"    />
        <result property="ppuab"    column="ppuab"    />
        <result property="ppubc"    column="ppubc"    />
        <result property="ppuca"    column="ppuca"    />
        <result property="ppia"    column="ppia"    />
        <result property="ppib"    column="ppib"    />
        <result property="ppic"    column="ppic"    />
        <result property="ppapwr"    column="ppapwr"    />
        <result property="pprpwr"    column="pprpwr"    />
        <result property="ppcos"    column="ppcos"    />
    </resultMap>

    <resultMap type="com.jhdr.irrigation.entity.vo.EngineerPumpCountVo" id="EngineerPumpVo">
        <result property="pumpCd"    column="pumpCd"    />
        <result property="lttd"    column="lttd"    />
        <result property="pumpName"    column="pumpName"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="ppupz"    column="ppupz"    />
        <result property="ppdwz"    column="ppdwz"    />
        <result property="pmpq"    column="pmpq"    />
        <result property="ppupwptn"    column="ppupwptn"    />
        <result property="ppdwwptn"    column="ppdwwptn"    />
        <result property="clltm"    column="clltm"    />
        <result property="status"    column="status"    />
        <collection  property="pumpCondition" ofType="com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo">
            <result column="pmpcd" property="pmpcd"/>
            <result column="om" property="om"/>
            <result column="ee" property="ee"/>
            <result column="q" property="q"/>
        </collection >

    </resultMap>
    <resultMap type="com.jhdr.irrigation.entity.vo.IrrigationProcessPumpCountVo" id="ProcessPump">
        <result property="pumpCd"    column="pumpCd"    />
        <result property="pumpName"    column="pumpName"    />
        <result property="ppupz"    column="ppupz"    />
        <result property="pmpq"    column="pmpq"    />
        <result property="status"    column="status"    />
        <result property="ppee"    column="ppee"    />
        <collection  property="pumpCondition" ofType="com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo">
            <result column="pmpcd" property="pmpcd"/>
            <result column="om" property="om"/>
            <result column="ee" property="ee"/>
            <result column="q" property="q"/>
        </collection >

    </resultMap>

    <sql id="selectJhPumpRRealVo">
        select prjnmcd, clltm, ppupz, ppdwz, omcn, ompwr, pmpq, wchrcd, ppupwptn, ppdwwptn, msqmt, pdchcd, ppee, ppcn, ppuab, ppubc, ppuca, ppia, ppib, ppic, ppapwr, pprpwr, ppcos from jh_pump_r_real
    </sql>

    <select id="selectJhPumpRRealList"  resultType="com.jhdr.irrigation.entity.vo.JhPumpRRealVo">
        <include refid="selectJhPumpRRealVo"/>
        <where>
            <if test="ppupz != null "> and ppupz = #{ppupz}</if>
            <if test="ppdwz != null "> and ppdwz = #{ppdwz}</if>
            <if test="omcn != null "> and omcn = #{omcn}</if>
            <if test="ompwr != null "> and ompwr = #{ompwr}</if>
            <if test="pmpq != null "> and pmpq = #{pmpq}</if>
            <if test="wchrcd != null "> and wchrcd = #{wchrcd}</if>
            <if test="ppupwptn != null "> and ppupwptn = #{ppupwptn}</if>
            <if test="ppdwwptn != null "> and ppdwwptn = #{ppdwwptn}</if>
            <if test="msqmt != null "> and msqmt = #{msqmt}</if>
            <if test="pdchcd != null "> and pdchcd = #{pdchcd}</if>
            <if test="ppee != null "> and ppee = #{ppee}</if>
            <if test="ppcn != null "> and ppcn = #{ppcn}</if>
            <if test="ppuab != null "> and ppuab = #{ppuab}</if>
            <if test="ppubc != null "> and ppubc = #{ppubc}</if>
            <if test="ppuca != null "> and ppuca = #{ppuca}</if>
            <if test="ppia != null "> and ppia = #{ppia}</if>
            <if test="ppib != null "> and ppib = #{ppib}</if>
            <if test="ppic != null "> and ppic = #{ppic}</if>
            <if test="ppapwr != null "> and ppapwr = #{ppapwr}</if>
            <if test="pprpwr != null "> and pprpwr = #{pprpwr}</if>
            <if test="ppcos != null "> and ppcos = #{ppcos}</if>
        </where>
    </select>

    <select id="selectJhPumpRRealByPrjnmcd"  resultType="com.jhdr.irrigation.entity.vo.JhPumpRRealVo">
        select prjnmcd, clltm, ppupz, ppdwz, omcn, ompwr, pmpq, wchrcd, ppupwptn, ppdwwptn, msqmt, pdchcd, ppee, ppcn, ppuab, ppubc, ppuca, ppia, ppib, ppic, ppapwr, pprpwr, ppcos,runtm,accq from jh_pump_r_real
            where prjnmcd = #{prjnmcd}  limit 1
    </select>
    <select id="getPumpOpenNum" resultType="java.lang.Integer">
        select count(1) from jh_pump_r_real jprr
        left join gis_datas_d gdd on gdd.CD = jprr.prjnmcd
        where jprr.omcn >0  and gdd.CD is not null
    </select>
    <select id="getEngineerPumpCount" resultMap="EngineerPumpVo">
        SELECT
            gdd.cd AS pumpCd,
            gdd.lttd,
            gdd."NM" AS pumpName,
            gdd.lgtd,
            jprr.ppupz,
            jprr.ppdwz,
            jprr.pmpq,
            jprr.ppupwptn,
            jprr.ppdwwptn,
            jprr.clltm,
            CASE
                WHEN jprr.omcn > 0 THEN
                    1 ELSE 0
                END AS status,
            jprd.pmpcd,
            jprd.om,
            jprd.ee,
            jprd.q
        FROM
            gis_datas_d gdd
                LEFT JOIN  jh_pump_r_real jprr ON gdd.CD = jprr.prjnmcd
                LEFT JOIN jh_pump_dtl_r_real jprd ON jprd.prjnmcd = jprr.prjnmcd

        WHERE
            gdd.CD IS NOT NULL
          AND gdd."GISSIGN" = 'BZ'
        ORDER BY
            gdd.lgtd,jprd.pmpcd ASC
    </select>
    <select id="pumpList"  resultMap="ProcessPump">
        SELECT
            gdd.str_code AS pumpCd,
            gdd.str_name AS pumpName,
            jprr.ppupz,
            jprr.ppdwz,
            jprr.pmpq,
            CASE
                WHEN jprr.omcn > 0 THEN
                    1 ELSE 0
                END AS status,
            jprr.ppee,
            jprd.pmpcd,
            jprd.om,
            jprd.ee,
            jprd.q
        FROM
            jhir_pust_b gdd
                LEFT JOIN  jh_pump_r_real jprr ON gdd.str_code = jprr.prjnmcd
                LEFT JOIN jh_pump_dtl_r_real jprd ON jprd.prjnmcd = jprr.prjnmcd
        WHERE
            gdd.status = 1

        <if test="conditionType != null and conditionType  == 1 ">
            AND gdd.en_size like concat('%大%')
        </if>
        <if test="conditionType != null and conditionType  == 2 ">
            AND gdd.en_size like concat('%中%')
        </if>
        <if test="conditionType != null and conditionType  == 3 ">
            AND gdd.en_size like concat('%小%')
        </if>

        ORDER BY gdd.str_code ASC,
            jprd.pmpcd ASC
    </select>

    <select id="pumpListCount" resultType="com.jhdr.irrigation.entity.vo.IrrigationProcessPumpCountVo">
        SELECT
        gdd.str_code AS pumpCd,
        gdd.str_name AS pumpName,
        jprr.ppupz,
        jprr.ppdwz,
        jprr.pmpq,
        CASE
        WHEN jprr.omcn > 0 THEN
        1 ELSE 0
        END AS status,
        jprr.ppee
        FROM
        jhir_pust_b gdd
        LEFT JOIN  jh_pump_r_real jprr ON gdd.str_code = jprr.prjnmcd
        WHERE
        gdd.status = 1

        <if test="conditionType != null and conditionType  == 1 ">
            AND gdd.en_size like concat('%大%')
        </if>
        <if test="conditionType != null and conditionType  == 2 ">
            AND gdd.en_size like concat('%中%')
        </if>
        <if test="conditionType != null and conditionType  == 3 ">
            AND gdd.en_size like concat('%小%')
        </if>

        ORDER BY gdd.str_code ASC
    </select>
    <select id="yearAccq" resultType="java.math.BigDecimal">
        -- 查询当年的累计流量差值
        SELECT
            (MAX(accq) - MIN(accq)) AS year_total_accq
        FROM jh_pump_r
        WHERE clltm >= DATE_TRUNC('YEAR', CURRENT_DATE)
          AND clltm <![CDATA[<]]>  DATE_TRUNC('YEAR', CURRENT_DATE) + INTERVAL '1 YEAR' and prjnmcd =#{prjnmcd};
    </select>

    <insert id="insertJhPumpRReal" parameterType="com.jhdr.irrigation.entity.param.JhPumpRRealAddParam">
        insert into jh_pump_r_real
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">prjnmcd,</if>
                    <if test="clltm != null">clltm,</if>
                    <if test="ppupz != null">ppupz,</if>
                    <if test="ppdwz != null">ppdwz,</if>
                    <if test="omcn != null">omcn,</if>
                    <if test="ompwr != null">ompwr,</if>
                    <if test="pmpq != null">pmpq,</if>
                    <if test="wchrcd != null">wchrcd,</if>
                    <if test="ppupwptn != null">ppupwptn,</if>
                    <if test="ppdwwptn != null">ppdwwptn,</if>
                    <if test="msqmt != null">msqmt,</if>
                    <if test="pdchcd != null">pdchcd,</if>
                    <if test="ppee != null">ppee,</if>
                    <if test="ppcn != null">ppcn,</if>
                    <if test="ppuab != null">ppuab,</if>
                    <if test="ppubc != null">ppubc,</if>
                    <if test="ppuca != null">ppuca,</if>
                    <if test="ppia != null">ppia,</if>
                    <if test="ppib != null">ppib,</if>
                    <if test="ppic != null">ppic,</if>
                    <if test="ppapwr != null">ppapwr,</if>
                    <if test="pprpwr != null">pprpwr,</if>
                    <if test="ppcos != null">ppcos,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="prjnmcd != null">#{prjnmcd},</if>
                    <if test="clltm != null">#{clltm},</if>
                    <if test="ppupz != null">#{ppupz},</if>
                    <if test="ppdwz != null">#{ppdwz},</if>
                    <if test="omcn != null">#{omcn},</if>
                    <if test="ompwr != null">#{ompwr},</if>
                    <if test="pmpq != null">#{pmpq},</if>
                    <if test="wchrcd != null">#{wchrcd},</if>
                    <if test="ppupwptn != null">#{ppupwptn},</if>
                    <if test="ppdwwptn != null">#{ppdwwptn},</if>
                    <if test="msqmt != null">#{msqmt},</if>
                    <if test="pdchcd != null">#{pdchcd},</if>
                    <if test="ppee != null">#{ppee},</if>
                    <if test="ppcn != null">#{ppcn},</if>
                    <if test="ppuab != null">#{ppuab},</if>
                    <if test="ppubc != null">#{ppubc},</if>
                    <if test="ppuca != null">#{ppuca},</if>
                    <if test="ppia != null">#{ppia},</if>
                    <if test="ppib != null">#{ppib},</if>
                    <if test="ppic != null">#{ppic},</if>
                    <if test="ppapwr != null">#{ppapwr},</if>
                    <if test="pprpwr != null">#{pprpwr},</if>
                    <if test="ppcos != null">#{ppcos},</if>
        </trim>
    </insert>

    <update id="updateJhPumpRReal" parameterType="com.jhdr.irrigation.entity.param.JhPumpRRealEditParam">
        update jh_pump_r_real
        <trim prefix="SET" suffixOverrides=",">
                    <if test="clltm != null">clltm = #{clltm},</if>
                    <if test="ppupz != null">ppupz = #{ppupz},</if>
                    <if test="ppdwz != null">ppdwz = #{ppdwz},</if>
                    <if test="omcn != null">omcn = #{omcn},</if>
                    <if test="ompwr != null">ompwr = #{ompwr},</if>
                    <if test="pmpq != null">pmpq = #{pmpq},</if>
                    <if test="wchrcd != null">wchrcd = #{wchrcd},</if>
                    <if test="ppupwptn != null">ppupwptn = #{ppupwptn},</if>
                    <if test="ppdwwptn != null">ppdwwptn = #{ppdwwptn},</if>
                    <if test="msqmt != null">msqmt = #{msqmt},</if>
                    <if test="pdchcd != null">pdchcd = #{pdchcd},</if>
                    <if test="ppee != null">ppee = #{ppee},</if>
                    <if test="ppcn != null">ppcn = #{ppcn},</if>
                    <if test="ppuab != null">ppuab = #{ppuab},</if>
                    <if test="ppubc != null">ppubc = #{ppubc},</if>
                    <if test="ppuca != null">ppuca = #{ppuca},</if>
                    <if test="ppia != null">ppia = #{ppia},</if>
                    <if test="ppib != null">ppib = #{ppib},</if>
                    <if test="ppic != null">ppic = #{ppic},</if>
                    <if test="ppapwr != null">ppapwr = #{ppapwr},</if>
                    <if test="pprpwr != null">pprpwr = #{pprpwr},</if>
                    <if test="ppcos != null">ppcos = #{ppcos},</if>
        </trim>
        where prjnmcd = #{prjnmcd}
    </update>

    <delete id="deleteJhPumpRRealByPrjnmcd" parameterType="String">
        delete from jh_pump_r_real where prjnmcd = #{prjnmcd}
    </delete>

    <delete id="deleteJhPumpRRealByPrjnmcds" parameterType="String">
        delete from jh_pump_r_real where prjnmcd in
        <foreach item="prjnmcd" collection="array" open="(" separator="," close=")">
            #{prjnmcd}
        </foreach>
    </delete>
</mapper>
