package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhPumpDtlRRealPo;
import com.jhdr.irrigation.entity.param.JhPumpDtlRRealParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlRRealAddParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlRRealEditParam;
import com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo;
import com.jhdr.irrigation.entity.vo.JhPumpDtlRRealVo;


/**
 * 泵机运行快照Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface JhPumpDtlRRealMapper extends BaseMapper<JhPumpDtlRRealPo>
{
    /**
     * 查询泵机运行快照
     *
     * @param prjnmcd 泵机运行快照主键
     * @return 泵机运行快照
     */
    public JhPumpDtlRRealVo selectJhPumpDtlRRealByPrjnmcd(String prjnmcd);

    /**
     * 查询泵机运行快照列表
     *
     * @param jhPumpDtlRReal 泵机运行快照
     * @return 泵机运行快照集合
     */
    public List<JhPumpDtlRRealVo> selectJhPumpDtlRRealList(JhPumpDtlRRealParam jhPumpDtlRReal);

    /**
     * 新增泵机运行快照
     *
     * @param jhPumpDtlRReal 泵机运行快照
     * @return 结果
     */
    public int insertJhPumpDtlRReal(JhPumpDtlRRealAddParam jhPumpDtlRReal);

    /**
     * 修改泵机运行快照
     *
     * @param jhPumpDtlRReal 泵机运行快照
     * @return 结果
     */
    public int updateJhPumpDtlRReal(JhPumpDtlRRealEditParam jhPumpDtlRReal);

    /**
     * 删除泵机运行快照
     *
     * @param prjnmcd 泵机运行快照主键
     * @return 结果
     */
    public int deleteJhPumpDtlRRealByPrjnmcd(String prjnmcd);

    /**
     * 批量删除泵机运行快照
     *
     * @param prjnmcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhPumpDtlRRealByPrjnmcds(String[] prjnmcds);

    /**
     * 获取泵机运行快照详细信息
     *
     * @param prjnmcd 泵机运行快照主键
     * @return 泵机运行快照详细信息
     */
    List<JhPumpDtlRInfoVo> getPumpDtlRInfoVos(String prjnmcd);
}
