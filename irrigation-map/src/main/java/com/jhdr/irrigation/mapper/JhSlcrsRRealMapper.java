package com.jhdr.irrigation.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.param.IrrigationProcessWorkParam;
import com.jhdr.irrigation.entity.po.JhSlcrsRRealPo;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealEditParam;
import com.jhdr.irrigation.entity.vo.*;


/**
 * 闸站运行状况快照Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface JhSlcrsRRealMapper extends BaseMapper<JhSlcrsRRealPo>
{
    /**
     * 查询闸站运行状况快照
     *
     * @param prjnmcd 闸站运行状况快照主键
     * @return 闸站运行状况快照
     */
    public JhSlcrsRRealVo selectJhSlcrsRRealByPrjnmcd(String prjnmcd);

    /**
     * 查询闸站运行状况快照列表
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 闸站运行状况快照集合
     */
    public List<JhSlcrsRRealVo> selectJhSlcrsRRealList(JhSlcrsRRealParam jhSlcrsRReal);

    /**
     * 新增闸站运行状况快照
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 结果
     */
    public int insertJhSlcrsRReal(JhSlcrsRRealAddParam jhSlcrsRReal);

    /**
     * 修改闸站运行状况快照
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 结果
     */
    public int updateJhSlcrsRReal(JhSlcrsRRealEditParam jhSlcrsRReal);

    /**
     * 删除闸站运行状况快照
     *
     * @param prjnmcd 闸站运行状况快照主键
     * @return 结果
     */
    public int deleteJhSlcrsRRealByPrjnmcd(String prjnmcd);

    /**
     * 批量删除闸站运行状况快照
     *
     * @param prjnmcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhSlcrsRRealByPrjnmcds(String[] prjnmcds);

    /**
     * 查询闸站运行状况快照列表
     *
     * @param
     * @return 闸站运行状况快照集合
     */
    List<JhSlcrsDtlRInfoVo> getSlcrsRRealInfoVos(String prjnmcd);

    /**
     * 查询闸站开启数量
     *
     * @param
     * @return 闸站开启数量
     */
    Integer getGateStationOpenNum();

    /**
     * 查询闸站运行状况快照列表
     *
     * @param
     * @return 闸站运行状况快照集合
     */
    List<EngineerGateStationCountVo> engineerGateStationCount();

    JhSlcrsRRealTemporaryDataVo temporaryData(String prjnmcd);

    List<IrrigationProcessGateVo> gateList(IrrigationProcessWorkParam processWorkParam);

    List<IrrigationProcessGateVo> gateListCount(IrrigationProcessWorkParam processWorkParam);

    BigDecimal yearAccq(String prjnmcd);
}
