package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhPumpDtlLogRPo;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogRParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogRAddParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogREditParam;
import com.jhdr.irrigation.entity.vo.JhPumpDtlLogRVo;


/**
 * 泵机运行操作记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface JhPumpDtlLogRMapper extends BaseMapper<JhPumpDtlLogRPo>
{
    /**
     * 查询泵机运行操作记录
     *
     * @param prjnmcd 泵机运行操作记录主键
     * @return 泵机运行操作记录
     */
    public JhPumpDtlLogRVo selectJhPumpDtlLogRByPrjnmcd(String prjnmcd);

    /**
     * 查询泵机运行操作记录列表
     *
     * @param jhPumpDtlLogR 泵机运行操作记录
     * @return 泵机运行操作记录集合
     */
    public List<JhPumpDtlLogRVo> selectJhPumpDtlLogRList(JhPumpDtlLogRParam jhPumpDtlLogR);

    /**
     * 新增泵机运行操作记录
     *
     * @param jhPumpDtlLogR 泵机运行操作记录
     * @return 结果
     */
    public int insertJhPumpDtlLogR(JhPumpDtlLogRAddParam jhPumpDtlLogR);

    /**
     * 修改泵机运行操作记录
     *
     * @param jhPumpDtlLogR 泵机运行操作记录
     * @return 结果
     */
    public int updateJhPumpDtlLogR(JhPumpDtlLogREditParam jhPumpDtlLogR);

    /**
     * 删除泵机运行操作记录
     *
     * @param prjnmcd 泵机运行操作记录主键
     * @return 结果
     */
    public int deleteJhPumpDtlLogRByPrjnmcd(String prjnmcd);

    /**
     * 批量删除泵机运行操作记录
     *
     * @param prjnmcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhPumpDtlLogRByPrjnmcds(String[] prjnmcds);
}
