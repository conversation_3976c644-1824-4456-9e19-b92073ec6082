package com.jhdr.irrigation.mapper;

import java.util.List;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.WeatherDataPo;
import com.jhdr.irrigation.entity.param.WeatherDataParam;
import com.jhdr.irrigation.entity.param.WeatherDataAddParam;
import com.jhdr.irrigation.entity.param.WeatherDataEditParam;
import com.jhdr.irrigation.entity.vo.WeatherDataVo;
import org.apache.ibatis.annotations.Mapper;


/**
 * 天气数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Mapper
public interface WeatherDataMapper extends BaseMapper<WeatherDataPo>
{
    /**
     * 查询天气数据
     *
     * @param id 天气数据主键
     * @return 天气数据
     */
    public WeatherDataVo selectWeatherDataById(Long id);

    /**
     * 查询天气数据列表
     *
     * @param weatherData 天气数据
     * @return 天气数据集合
     */
    public List<WeatherDataVo> selectWeatherDataList(WeatherDataParam weatherData);

    /**
     * 新增天气数据
     *
     * @param weatherData 天气数据
     * @return 结果
     */
    public int insertWeatherData(WeatherDataAddParam weatherData);

    /**
     * 修改天气数据
     *
     * @param weatherData 天气数据
     * @return 结果
     */
    public int updateWeatherData(WeatherDataEditParam weatherData);

    /**
     * 删除天气数据
     *
     * @param id 天气数据主键
     * @return 结果
     */
    public int deleteWeatherDataById(Long id);

    /**
     * 批量删除天气数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWeatherDataByIds(Long[] ids);

    /**
     * 天气数据入库
     *
     * @param
     * @return 结果
     */
    void updateDataStatus();

    // 获取指定时间的天气数据
    WeatherDataPo getCurrentWeather(LocalDateTime dateTime);
}
