package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.IrrigationProcessStandardDocPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessStandardDocVo;


/**
 * 工程运行-标准化-文档Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
public interface IrrigationProcessStandardDocMapper extends BaseMapper<IrrigationProcessStandardDocPo>
{
    /**
     * 查询工程运行-标准化-文档
     *
     * @param docId 工程运行-标准化-文档主键
     * @return 工程运行-标准化-文档
     */
    public IrrigationProcessStandardDocVo selectIrrigationProcessStandardDocByDocId(Long docId);

    /**
     * 查询工程运行-标准化-文档列表
     *
     * @param irrigationProcessStandardDoc 工程运行-标准化-文档
     * @return 工程运行-标准化-文档集合
     */
    public List<IrrigationProcessStandardDocVo> selectIrrigationProcessStandardDocList(IrrigationProcessStandardDocParam irrigationProcessStandardDoc);

    /**
     * 新增工程运行-标准化-文档
     *
     * @param irrigationProcessStandardDoc 工程运行-标准化-文档
     * @return 结果
     */
    public int insertIrrigationProcessStandardDoc(IrrigationProcessStandardDocAddParam irrigationProcessStandardDoc);

    /**
     * 修改工程运行-标准化-文档
     *
     * @param irrigationProcessStandardDoc 工程运行-标准化-文档
     * @return 结果
     */
    public int updateIrrigationProcessStandardDoc(IrrigationProcessStandardDocEditParam irrigationProcessStandardDoc);

    /**
     * 删除工程运行-标准化-文档
     *
     * @param docId 工程运行-标准化-文档主键
     * @return 结果
     */
    public int deleteIrrigationProcessStandardDocByDocId(Long docId);

    /**
     * 批量删除工程运行-标准化-文档
     *
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessStandardDocByDocIds(Long[] docIds);
}
