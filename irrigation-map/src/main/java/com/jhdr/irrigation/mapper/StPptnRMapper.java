package com.jhdr.irrigation.mapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.StPptnRPo;
import com.jhdr.irrigation.entity.param.StPptnRParam;
import com.jhdr.irrigation.entity.param.StPptnRAddParam;
import com.jhdr.irrigation.entity.param.StPptnREditParam;
import com.jhdr.irrigation.entity.vo.StPptnRVo;


/**
 * 降水量Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface StPptnRMapper extends BaseMapper<StPptnRPo>
{
    /**
     * 查询降水量
     *
     * @param STCD 降水量主键
     * @return 降水量
     */
    public StPptnRVo selectStPptnRBySTCD(String STCD);

    /**
     * 查询降水量列表
     *
     * @param stPptnR 降水量
     * @return 降水量集合
     */
    public List<StPptnRVo> selectStPptnRList(StPptnRParam stPptnR);

    /**
     * 新增降水量
     *
     * @param stPptnR 降水量
     * @return 结果
     */
    public int insertStPptnR(StPptnRAddParam stPptnR);

    /**
     * 修改降水量
     *
     * @param stPptnR 降水量
     * @return 结果
     */
    public int updateStPptnR(StPptnREditParam stPptnR);

    /**
     * 删除降水量
     *
     * @param STCD 降水量主键
     * @return 结果
     */
    public int deleteStPptnRBySTCD(String STCD);

    /**
     * 批量删除降水量
     *
     * @param STCDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStPptnRBySTCDs(String[] STCDs);

    /**
     * 获取雨量数据
     *
     * @param
     * @return 结果
     */
    Map<LocalDate, BigDecimal> getRainfallData(StPptnRParam stPptnR);
}
