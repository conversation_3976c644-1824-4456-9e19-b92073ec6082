package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.OpoAlarmMessagePo;
import com.jhdr.irrigation.entity.param.OpoAlarmMessageParam;
import com.jhdr.irrigation.entity.param.OpoAlarmMessageAddParam;
import com.jhdr.irrigation.entity.param.OpoAlarmMessageEditParam;
import com.jhdr.irrigation.entity.vo.OpoAlarmMessageVo;


/**
 * 报警信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface OpoAlarmMessageMapper extends BaseMapper<OpoAlarmMessagePo>
{
    /**
     * 查询报警信息
     *
     * @param id 报警信息主键
     * @return 报警信息
     */
    public OpoAlarmMessageVo selectOpoAlarmMessageById(Long id);

    /**
     * 查询报警信息列表
     *
     * @param opoAlarmMessage 报警信息
     * @return 报警信息集合
     */
    public List<OpoAlarmMessageVo> selectOpoAlarmMessageList(OpoAlarmMessageParam opoAlarmMessage);

    /**
     * 新增报警信息
     *
     * @param opoAlarmMessage 报警信息
     * @return 结果
     */
    public int insertOpoAlarmMessage(OpoAlarmMessageAddParam opoAlarmMessage);

    /**
     * 修改报警信息
     *
     * @param opoAlarmMessage 报警信息
     * @return 结果
     */
    public int updateOpoAlarmMessage(OpoAlarmMessageEditParam opoAlarmMessage);

    /**
     * 删除报警信息
     *
     * @param id 报警信息主键
     * @return 结果
     */
    public int deleteOpoAlarmMessageById(Long id);

    /**
     * 批量删除报警信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoAlarmMessageByIds(Long[] ids);
}
