package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhirWagaBPo;
import com.jhdr.irrigation.entity.param.JhirWagaBParam;
import com.jhdr.irrigation.entity.param.JhirWagaBAddParam;
import com.jhdr.irrigation.entity.param.JhirWagaBEditParam;
import com.jhdr.irrigation.entity.vo.JhirWagaBVo;


/**
 * 闸站基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface JhirWagaBMapper extends BaseMapper<JhirWagaBPo>
{
    /**
     * 查询闸站基础信息
     *
     * @param strCode 闸站基础信息主键
     * @return 闸站基础信息
     */
    public JhirWagaBVo selectJhirWagaBByStrCode(String strCode);

    /**
     * 查询闸站基础信息列表
     *
     * @param jhirWagaB 闸站基础信息
     * @return 闸站基础信息集合
     */
    public List<JhirWagaBVo> selectJhirWagaBList(JhirWagaBParam jhirWagaB);

    /**
     * 新增闸站基础信息
     *
     * @param jhirWagaB 闸站基础信息
     * @return 结果
     */
    public int insertJhirWagaB(JhirWagaBAddParam jhirWagaB);

    /**
     * 修改闸站基础信息
     *
     * @param jhirWagaB 闸站基础信息
     * @return 结果
     */
    public int updateJhirWagaB(JhirWagaBEditParam jhirWagaB);

    /**
     * 删除闸站基础信息
     *
     * @param strCode 闸站基础信息主键
     * @return 结果
     */
    public int deleteJhirWagaBByStrCode(String strCode);

    /**
     * 批量删除闸站基础信息
     *
     * @param strCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhirWagaBByStrCodes(String[] strCodes);
}
