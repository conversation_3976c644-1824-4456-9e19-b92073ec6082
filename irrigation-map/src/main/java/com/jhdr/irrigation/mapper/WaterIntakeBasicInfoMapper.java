package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.WaterIntakeBasicInfoPo;
import com.jhdr.irrigation.entity.param.WaterIntakeBasicInfoParam;
import com.jhdr.irrigation.entity.param.WaterIntakeBasicInfoAddParam;
import com.jhdr.irrigation.entity.param.WaterIntakeBasicInfoEditParam;
import com.jhdr.irrigation.entity.vo.WaterIntakeBasicInfoVo;


/**
 * 取水口基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface WaterIntakeBasicInfoMapper extends BaseMapper<WaterIntakeBasicInfoPo>
{
    /**
     * 查询取水口基本信息
     *
     * @param id 取水口基本信息主键
     * @return 取水口基本信息
     */
    public WaterIntakeBasicInfoVo selectWaterIntakeBasicInfoById(Long id);

    /**
     * 查询取水口基本信息列表
     *
     * @param waterIntakeBasicInfo 取水口基本信息
     * @return 取水口基本信息集合
     */
    public List<WaterIntakeBasicInfoVo> selectWaterIntakeBasicInfoList(WaterIntakeBasicInfoParam waterIntakeBasicInfo);

    /**
     * 新增取水口基本信息
     *
     * @param waterIntakeBasicInfo 取水口基本信息
     * @return 结果
     */
    public int insertWaterIntakeBasicInfo(WaterIntakeBasicInfoAddParam waterIntakeBasicInfo);

    /**
     * 修改取水口基本信息
     *
     * @param waterIntakeBasicInfo 取水口基本信息
     * @return 结果
     */
    public int updateWaterIntakeBasicInfo(WaterIntakeBasicInfoEditParam waterIntakeBasicInfo);

    /**
     * 删除取水口基本信息
     *
     * @param id 取水口基本信息主键
     * @return 结果
     */
    public int deleteWaterIntakeBasicInfoById(Long id);

    /**
     * 批量删除取水口基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWaterIntakeBasicInfoByIds(Long[] ids);
}
