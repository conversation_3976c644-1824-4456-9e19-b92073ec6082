package com.jhdr.irrigation.mapper;

import java.math.BigDecimal;
import java.util.List;

import cn.hutool.core.lang.Opt;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhFlowycRPo;
import com.jhdr.irrigation.entity.param.JhFlowycRParam;
import com.jhdr.irrigation.entity.param.JhFlowycRAddParam;
import com.jhdr.irrigation.entity.param.JhFlowycREditParam;
import com.jhdr.irrigation.entity.vo.FlowCrossSectionVo;
import com.jhdr.irrigation.entity.vo.JhFlowtnRVo;
import com.jhdr.irrigation.entity.vo.JhFlowycRVo;
import com.jhdr.irrigation.entity.vo.YearAccqResultVo;
import com.jhdr.irrigation.service.impl.JhFlowycRServiceImpl;
import org.apache.ibatis.annotations.Param;


/**
 * 原始水量数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface JhFlowycRMapper extends BaseMapper<JhFlowycRPo>
{
    /**
     * 查询原始水量数据
     *
     * @param STCD 原始水量数据主键
     * @return 原始水量数据
     */
    public JhFlowycRVo selectJhFlowycRBySTCD(String STCD);

    /**
     * 查询原始水量数据列表
     *
     * @param jhFlowycR 原始水量数据
     * @return 原始水量数据集合
     */
    public List<JhFlowtnRVo> selectJhFlowycRList(JhFlowycRParam jhFlowycR);

    /**
     * 新增原始水量数据
     *
     * @param jhFlowycR 原始水量数据
     * @return 结果
     */
    public int insertJhFlowycR(JhFlowycRAddParam jhFlowycR);

    /**
     * 修改原始水量数据
     *
     * @param jhFlowycR 原始水量数据
     * @return 结果
     */
    public int updateJhFlowycR(JhFlowycREditParam jhFlowycR);

    /**
     * 删除原始水量数据
     *
     * @param STCD 原始水量数据主键
     * @return 结果
     */
    public int deleteJhFlowycRBySTCD(String STCD);

    /**
     * 批量删除原始水量数据
     *
     * @param STCDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhFlowycRBySTCDs(String[] STCDs);

    JhFlowtnRVo getOnePsqTn(String stcd);

    List<FlowCrossSectionVo> selectFlowCrossSectionList(String stcd);

    BigDecimal yearAccq(String stcd);

    // 月流量
    List<JhFlowtnRVo> selectJhFlowycRMonthList(JhFlowycRParam jhFlowycR);

    List<YearAccqResultVo> batchYearAccq(@Param("stcdList") List<String> stcdList);}
