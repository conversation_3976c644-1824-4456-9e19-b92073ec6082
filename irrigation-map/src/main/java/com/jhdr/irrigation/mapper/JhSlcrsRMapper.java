package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhSlcrsRPo;
import com.jhdr.irrigation.entity.param.JhSlcrsRParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsREditParam;
import com.jhdr.irrigation.entity.vo.JhSlcrsRVo;
import com.jhdr.irrigation.entity.vo.JhSlcrsRWaterVo;


/**
 * 水闸运行状况Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface JhSlcrsRMapper extends BaseMapper<JhSlcrsRPo>
{
    /**
     * 查询水闸运行状况
     *
     * @param prjnmcd 水闸运行状况主键
     * @return 水闸运行状况
     */
    public JhSlcrsRVo selectJhSlcrsRByPrjnmcd(String prjnmcd);

    /**
     * 查询水闸运行状况列表
     *
     * @param jhSlcrsR 水闸运行状况
     * @return 水闸运行状况集合
     */
    public List<JhSlcrsRWaterVo> selectJhSlcrsRList(JhSlcrsRParam jhSlcrsR);

    /**
     * 新增水闸运行状况
     *
     * @param jhSlcrsR 水闸运行状况
     * @return 结果
     */
    public int insertJhSlcrsR(JhSlcrsRAddParam jhSlcrsR);

    /**
     * 修改水闸运行状况
     *
     * @param jhSlcrsR 水闸运行状况
     * @return 结果
     */
    public int updateJhSlcrsR(JhSlcrsREditParam jhSlcrsR);

    /**
     * 删除水闸运行状况
     *
     * @param prjnmcd 水闸运行状况主键
     * @return 结果
     */
    public int deleteJhSlcrsRByPrjnmcd(String prjnmcd);

    /**
     * 批量删除水闸运行状况
     *
     * @param prjnmcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhSlcrsRByPrjnmcds(String[] prjnmcds);
}
