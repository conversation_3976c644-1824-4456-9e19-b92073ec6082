package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.IrrigationProcessOrganizePo;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessOrganizeVo;


/**
 * 工程运行-组织架构Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
public interface IrrigationProcessOrganizeMapper extends BaseMapper<IrrigationProcessOrganizePo>
{
    /**
     * 查询工程运行-组织架构
     *
     * @param id 工程运行-组织架构主键
     * @return 工程运行-组织架构
     */
    public IrrigationProcessOrganizeVo selectIrrigationProcessOrganizeById(Long id);

    /**
     * 查询工程运行-组织架构列表
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 工程运行-组织架构集合
     */
    public List<IrrigationProcessOrganizeVo> selectIrrigationProcessOrganizeList(IrrigationProcessOrganizeParam irrigationProcessOrganize);

    /**
     * 新增工程运行-组织架构
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 结果
     */
    public int insertIrrigationProcessOrganize(IrrigationProcessOrganizeAddParam irrigationProcessOrganize);

    /**
     * 修改工程运行-组织架构
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 结果
     */
    public int updateIrrigationProcessOrganize(IrrigationProcessOrganizeEditParam irrigationProcessOrganize);

    /**
     * 删除工程运行-组织架构
     *
     * @param id 工程运行-组织架构主键
     * @return 结果
     */
    public int deleteIrrigationProcessOrganizeById(Long id);

    /**
     * 批量删除工程运行-组织架构
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessOrganizeByIds(Long[] ids);
}
