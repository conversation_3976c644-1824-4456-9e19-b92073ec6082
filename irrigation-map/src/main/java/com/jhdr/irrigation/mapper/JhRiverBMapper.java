package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhRiverBPo;
import com.jhdr.irrigation.entity.param.JhRiverBParam;
import com.jhdr.irrigation.entity.param.JhRiverBAddParam;
import com.jhdr.irrigation.entity.param.JhRiverBEditParam;
import com.jhdr.irrigation.entity.vo.JhRiverBVo;


/**
 * 河流信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface JhRiverBMapper extends BaseMapper<JhRiverBPo>
{
    /**
     * 查询河流信息
     *
     * @param ennmcd 河流信息主键
     * @return 河流信息
     */
    public JhRiverBVo selectJhRiverBByEnnmcd(String ennmcd);

    /**
     * 查询河流信息列表
     *
     * @param jhRiverB 河流信息
     * @return 河流信息集合
     */
    public List<JhRiverBVo> selectJhRiverBList(JhRiverBParam jhRiverB);

    /**
     * 新增河流信息
     *
     * @param jhRiverB 河流信息
     * @return 结果
     */
    public int insertJhRiverB(JhRiverBAddParam jhRiverB);

    /**
     * 修改河流信息
     *
     * @param jhRiverB 河流信息
     * @return 结果
     */
    public int updateJhRiverB(JhRiverBEditParam jhRiverB);

    /**
     * 删除河流信息
     *
     * @param ennmcd 河流信息主键
     * @return 结果
     */
    public int deleteJhRiverBByEnnmcd(String ennmcd);

    /**
     * 批量删除河流信息
     *
     * @param ennmcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhRiverBByEnnmcds(String[] ennmcds);

    /**
     * 根据类型查询长度
     * @param type
     * @return
     */
    String sumLengthByRvtp(String type);
}
