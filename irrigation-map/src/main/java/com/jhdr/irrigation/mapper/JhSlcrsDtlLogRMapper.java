package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhSlcrsDtlLogRPo;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlLogRParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlLogRAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlLogREditParam;
import com.jhdr.irrigation.entity.vo.JhSlcrsDtlLogRVo;


/**
 * 水闸运行操作日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface JhSlcrsDtlLogRMapper extends BaseMapper<JhSlcrsDtlLogRPo>
{
    /**
     * 查询水闸运行操作日志
     *
     * @param prjnmcd 水闸运行操作日志主键
     * @return 水闸运行操作日志
     */
    public JhSlcrsDtlLogRVo selectJhSlcrsDtlLogRByPrjnmcd(String prjnmcd);

    /**
     * 查询水闸运行操作日志列表
     *
     * @param jhSlcrsDtlLogR 水闸运行操作日志
     * @return 水闸运行操作日志集合
     */
    public List<JhSlcrsDtlLogRVo> selectJhSlcrsDtlLogRList(JhSlcrsDtlLogRParam jhSlcrsDtlLogR);

    /**
     * 新增水闸运行操作日志
     *
     * @param jhSlcrsDtlLogR 水闸运行操作日志
     * @return 结果
     */
    public int insertJhSlcrsDtlLogR(JhSlcrsDtlLogRAddParam jhSlcrsDtlLogR);

    /**
     * 修改水闸运行操作日志
     *
     * @param jhSlcrsDtlLogR 水闸运行操作日志
     * @return 结果
     */
    public int updateJhSlcrsDtlLogR(JhSlcrsDtlLogREditParam jhSlcrsDtlLogR);

    /**
     * 删除水闸运行操作日志
     *
     * @param prjnmcd 水闸运行操作日志主键
     * @return 结果
     */
    public int deleteJhSlcrsDtlLogRByPrjnmcd(String prjnmcd);

    /**
     * 批量删除水闸运行操作日志
     *
     * @param prjnmcds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhSlcrsDtlLogRByPrjnmcds(String[] prjnmcds);
}
