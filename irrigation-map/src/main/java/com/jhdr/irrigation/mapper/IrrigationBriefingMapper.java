package com.jhdr.irrigation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.IrrigationBriefingPo;
import org.apache.ibatis.annotations.Mapper;
import java.util.Date;
import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IrrigationBriefingMapper extends BaseMapper<IrrigationBriefingPo> {
    IrrigationBriefingPo getByReportDate(@Param("reportDate") String reportDate);
}
