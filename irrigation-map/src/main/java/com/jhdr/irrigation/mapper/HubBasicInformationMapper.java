package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.HubBasicInformationPo;
import com.jhdr.irrigation.entity.param.HubBasicInformationParam;
import com.jhdr.irrigation.entity.param.HubBasicInformationAddParam;
import com.jhdr.irrigation.entity.param.HubBasicInformationEditParam;
import com.jhdr.irrigation.entity.vo.*;
import org.apache.ibatis.annotations.Param;


/**
 * 枢纽基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface HubBasicInformationMapper extends BaseMapper<HubBasicInformationPo>
{
    /**
     * 查询枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 枢纽基础信息
     */
    public HubBasicInformationVo selectHubBasicInformationById(Long id);

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息集合
     */
    public List<HubBasicInformationVo> selectHubBasicInformationList(HubBasicInformationParam hubBasicInformation);

    /**
     * 新增枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int insertHubBasicInformation(HubBasicInformationAddParam hubBasicInformation);

    /**
     * 修改枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int updateHubBasicInformation(HubBasicInformationEditParam hubBasicInformation);

    /**
     * 删除枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 结果
     */
    public int deleteHubBasicInformationById(Long id);

    /**
     * 批量删除枢纽基础信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHubBasicInformationByIds(Long[] ids);

    /**
     * 工情-运行统计-枢纽
     * @return
     */
    List<EngineerHubInfoVo> engineerHubList();

    /**
     * 水旱防御-枢纽信息
     * @return
     */
    PreventionProjectVo getProjectInfo(String hubCd);

    // 水旱防御-降雨信息
    List<PreventionRainVo> getRainInfos(@Param("hubCodes") List<String> hubCodes );

    // 水旱防御-水位信息
    List<PreventionWaterVo> getWaterInfos(@Param("hubCodes")List<String> hubCodes);
}
