package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.GisDatasDPo;
import com.jhdr.irrigation.entity.param.GisDatasDParam;
import com.jhdr.irrigation.entity.param.GisDatasDAddParam;
import com.jhdr.irrigation.entity.param.GisDatasDEditParam;
import com.jhdr.irrigation.entity.vo.GisDatasDVo;
import com.jhdr.irrigation.entity.vo.JhFlowspRZVo;
import java.time.LocalDateTime;
import java.util.Map;

import com.jhdr.irrigation.entity.vo.SituationCountyNumVo;
import org.apache.ibatis.annotations.Mapper;
import com.jhdr.irrigation.entity.po.WeatherDataPo;
import org.apache.ibatis.annotations.Param;

/**
 * GIS数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Mapper
public interface GisDatasDMapper extends BaseMapper<GisDatasDPo>
{
    /**
     * 查询GIS数据
     *
     * @param GUID GIS数据主键
     * @return GIS数据
     */
    public GisDatasDVo selectGisDatasDByGUID(String GUID);

    /**
     * 查询GIS数据列表
     *
     * @param gisDatasD GIS数据
     * @return GIS数据集合
     */
    public List<GisDatasDVo> selectGisDatasDList(GisDatasDParam gisDatasD);

    /**
     * 新增GIS数据
     *
     * @param gisDatasD GIS数据
     * @return 结果
     */
    public int insertGisDatasD(GisDatasDAddParam gisDatasD);

    /**
     * 修改GIS数据
     *
     * @param gisDatasD GIS数据
     * @return 结果
     */
    public int updateGisDatasD(GisDatasDEditParam gisDatasD);

    /**
     * 删除GIS数据
     *
     * @param GUID GIS数据主键
     * @return 结果
     */
    public int deleteGisDatasDByGUID(String GUID);

    /**
     * 批量删除GIS数据
     *
     * @param GUIDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGisDatasDByGUIDs(String[] GUIDs);

    // 查询涵闸水位
    List<JhFlowspRZVo> getZ(List<String> cds);

    List<String> getWeatherDayList();

    List<GisDatasDVo> getFloodData(String code);

    String weatherGetData(String location);

    Map<String, Object> getCurrentWeather();
    List<Map<String, Object>> getHubWaterLevels(@Param("dateTime") LocalDateTime dateTime);
    List<Map<String, Object>> getHubGateOperations(@Param("dateTime") LocalDateTime dateTime);
    Double getShangqiaoRainfall(@Param("dateTime") LocalDateTime dateTime);
    Double getAverageRainfall(@Param("dateTime") LocalDateTime dateTime);
    Map<String, Object> getMaxRainfallStation(@Param("dateTime") LocalDateTime dateTime);
    List<Map<String, Object>> getActivePumpStationsByRegion(@Param("dateTime") LocalDateTime dateTime);
    List<Map<String, Object>> getActiveSluiceStationsByRegion(@Param("dateTime") LocalDateTime dateTime);
    Double getTotalFlow(@Param("dateTime") LocalDateTime dateTime);

    // 获取 pump station counts by requestType 0 所有 1 上桥 2 阚疃
    SituationCountyNumVo getPumpStationCounts(@Param("requestType") int requestType,@Param("lgtd") String lgtd);
    // 获取 sluice station counts by requestType 0 所有 1 上桥 2 阚疃
    SituationCountyNumVo getSluiceStationCounts(@Param("requestType") int requestType,@Param("lgtd") String lgtd);
}
