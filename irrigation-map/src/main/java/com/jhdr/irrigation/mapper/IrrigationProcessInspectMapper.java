package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.IrrigationProcessInspectPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessInspectVo;


/**
 * 工程运行-检查Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IrrigationProcessInspectMapper extends BaseMapper<IrrigationProcessInspectPo>
{
    /**
     * 查询工程运行-检查
     *
     * @param id 工程运行-检查主键
     * @return 工程运行-检查
     */
    public IrrigationProcessInspectVo selectIrrigationProcessInspectById(Long id);

    /**
     * 查询工程运行-检查列表
     *
     * @param irrigationProcessInspect 工程运行-检查
     * @return 工程运行-检查集合
     */
    public List<IrrigationProcessInspectVo> selectIrrigationProcessInspectList(IrrigationProcessInspectParam irrigationProcessInspect);

    /**
     * 新增工程运行-检查
     *
     * @param irrigationProcessInspect 工程运行-检查
     * @return 结果
     */
    public int insertIrrigationProcessInspect(IrrigationProcessInspectAddParam irrigationProcessInspect);

    /**
     * 修改工程运行-检查
     *
     * @param irrigationProcessInspect 工程运行-检查
     * @return 结果
     */
    public int updateIrrigationProcessInspect(IrrigationProcessInspectEditParam irrigationProcessInspect);

    /**
     * 删除工程运行-检查
     *
     * @param id 工程运行-检查主键
     * @return 结果
     */
    public int deleteIrrigationProcessInspectById(Long id);

    /**
     * 批量删除工程运行-检查
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessInspectByIds(Long[] ids);
}
