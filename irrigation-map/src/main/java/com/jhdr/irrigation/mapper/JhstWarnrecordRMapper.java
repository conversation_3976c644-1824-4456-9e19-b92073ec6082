package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhstWarnrecordRPo;
import com.jhdr.irrigation.entity.param.JhstWarnrecordRParam;
import com.jhdr.irrigation.entity.param.JhstWarnrecordRAddParam;
import com.jhdr.irrigation.entity.param.JhstWarnrecordREditParam;
import com.jhdr.irrigation.entity.vo.JhstWarnrecordRVo;


/**
 * 告警记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
public interface JhstWarnrecordRMapper extends BaseMapper<JhstWarnrecordRPo>
{
    /**
     * 查询告警记录
     *
     * @param id 告警记录主键
     * @return 告警记录
     */
    public JhstWarnrecordRVo selectJhstWarnrecordRById(Long id);

    /**
     * 查询告警记录列表
     *
     * @param jhstWarnrecordR 告警记录
     * @return 告警记录集合
     */
    public List<JhstWarnrecordRVo> selectJhstWarnrecordRList(JhstWarnrecordRParam jhstWarnrecordR);

    /**
     * 新增告警记录
     *
     * @param jhstWarnrecordR 告警记录
     * @return 结果
     */
    public int insertJhstWarnrecordR(JhstWarnrecordRAddParam jhstWarnrecordR);

    /**
     * 修改告警记录
     *
     * @param jhstWarnrecordR 告警记录
     * @return 结果
     */
    public int updateJhstWarnrecordR(JhstWarnrecordREditParam jhstWarnrecordR);

    /**
     * 删除告警记录
     *
     * @param id 告警记录主键
     * @return 结果
     */
    public int deleteJhstWarnrecordRById(Long id);

    /**
     * 批量删除告警记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhstWarnrecordRByIds(Long[] ids);
}
