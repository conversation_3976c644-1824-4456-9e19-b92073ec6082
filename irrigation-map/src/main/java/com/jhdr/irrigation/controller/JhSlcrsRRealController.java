package com.jhdr.irrigation.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.*;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.*;
import com.jhdr.irrigation.service.impl.HikvisionVideoTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 闸站运行状况快照
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Api(tags = "gis闸站运行状况快照")
@RestController
@RequestMapping("/slcrs/real")
public class JhSlcrsRRealController extends BaseController {
    @Autowired
    private IJhSlcrsRRealService jhSlcrsRRealService;
    @Autowired
    private IJhSlcrsRService jhSlcrsRService;
    @Autowired
    private IJhSlcrsDtlLogRService jhSlcrsDtlLogRService;
    @Autowired
    private IJhirWagaBService jhirWagaBService;
    @Autowired
    private IStPptnRService stPptnRService;
    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;
    @Autowired
    private IJhomVmBService jhomVmBService;
    @Autowired
    private HikvisionVideoTool hikvisionVideoTool;
    @Autowired
    private IGisDatasDService gisDatasDService;

    /**
     * 获取闸站运行状况基本信息 传gis数据中的CD
     */
    @ApiOperation(value = "获取闸站运行状况基本信息 传gis数据中的CD", notes = "irrigation:slcrsReal:base")
    @RequiresPermissions("irrigation:slcrsReal:base")
    @GetMapping(value = "/base/{prjnmcd}")
    public R<JhSlcrsRBaseVo> getBaseInfo(@PathVariable("prjnmcd") String prjnmcd) {
        //todo 闸站信息逻辑问题
        JhSlcrsRRealVo jhSlcrsRVo = jhSlcrsRRealService.selectJhSlcrsRRealByPrjnmcd(prjnmcd);
        JhSlcrsRBaseVo baseVo = BeanUtil.toBean(jhSlcrsRVo, JhSlcrsRBaseVo.class);
        if (ObjectUtil.isEmpty(baseVo)) {
            baseVo = new JhSlcrsRBaseVo();
            baseVo.setPrjnmcd(prjnmcd);
        }
        //闸站对应闸门开度列表
        List<JhSlcrsDtlRInfoVo> slcrsDtlRInfoVos = jhSlcrsRRealService.getSlcrsRRealInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(slcrsDtlRInfoVos)) {
            baseVo.setGateCondition(slcrsDtlRInfoVos);
        }
        if (ObjectUtil.isNotEmpty(baseVo)) {
            JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(prjnmcd);
            if (ObjectUtil.isNotEmpty(temporaryData)) {
                baseVo.setSldsz(temporaryData.getSldsz());
                baseVo.setSlupsz(temporaryData.getSlupsz());
                baseVo.setUpswtp(temporaryData.getUpswtp());
                baseVo.setDswtp(temporaryData.getDswtp());
            } else {
                baseVo.setSldsz(null);
                baseVo.setSlupsz(null);
                baseVo.setUpswtp(null);
                baseVo.setDswtp(null);
            }
        }
        JhirWagaBVo jhirWagaBVo = jhirWagaBService.selectJhirWagaBByStrCode(prjnmcd);
        baseVo.setMethod(jhirWagaBVo.getMethod());
        return R.ok(baseVo);
    }

    /**
     * 获取闸站基础信息详细信息
     */
    @ApiOperation(value = "获取闸站基础信息详细信息 传gis数据CD值", notes = "irrigation:waga:query")
    @RequiresPermissions("irrigation:waga:query")
    @GetMapping(value = "/waga/{strCode}")
    public R<JhirWagaBVo> getWagaInfo(@PathVariable("strCode") String strCode) {

        return R.ok(jhirWagaBService.selectJhirWagaBByStrCode(strCode));
    }

    /**
     * 获取闸站运行状况详细信息
     */
    @ApiOperation(value = "获取闸站运行状况详细信息", notes = "irrigation:slcrsReal:query")
    @RequiresPermissions("irrigation:slcrsReal:query")
    @GetMapping(value = "/info/{prjnmcd}")
    public R<JhSlcrsRInfoVo> getInfo(@PathVariable("prjnmcd") String prjnmcd) {
        JhSlcrsRRealVo jhSlcrsRVo = jhSlcrsRRealService.selectJhSlcrsRRealByPrjnmcd(prjnmcd);
        JhSlcrsRInfoVo infoVo = BeanUtil.toBean(jhSlcrsRVo, JhSlcrsRInfoVo.class);
        if (ObjectUtil.isEmpty(infoVo)) {
            infoVo = new JhSlcrsRInfoVo();
        }
        //闸站对应闸门开度列表
        List<JhSlcrsDtlRInfoVo> slcrsDtlRInfoVos = jhSlcrsRRealService.getSlcrsRRealInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(slcrsDtlRInfoVos)) {
            for (JhSlcrsDtlRInfoVo slcrsDtlRInfoVo : slcrsDtlRInfoVos) {
                if (ObjectUtil.isEmpty(slcrsDtlRInfoVo.getGtq())){
                    slcrsDtlRInfoVo.setGtq(new BigDecimal(0));
                }
            }
            infoVo.setSlcrsRInfoVos(slcrsDtlRInfoVos);
        }
        JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(prjnmcd);
        if (ObjectUtil.isNotEmpty(temporaryData)) {
            infoVo.setSldsz(temporaryData.getSldsz());
            infoVo.setSlupsz(temporaryData.getSlupsz());
            infoVo.setUpswtp(temporaryData.getUpswtp());
            infoVo.setDswtp(temporaryData.getDswtp());
        } else {
            infoVo.setSldsz(null);
            infoVo.setSlupsz(null);
            infoVo.setUpswtp(null);
            infoVo.setDswtp(null);
        }
        if (ObjectUtil.isNotEmpty(infoVo)) {
            if (ObjectUtil.isEmpty(infoVo.getThrslq())){
                infoVo.setThrslq(new BigDecimal(0));
            }

        }
        return R.ok(infoVo);
    }


    /**
     * 获取闸站水雨晴过程线详细信息
     */
    @ApiOperation(value = "水雨晴-过程线详细信息", notes = "irrigation:slcrsProcessLine:query")
    @RequiresPermissions("irrigation:slcrsProcessLine:query")
    @GetMapping(value = "/process/line")
    public R<List<JhSlcrsRWaterVo>> getProcessLineInfo(JhSlcrsRParam slcrsRParam) {
        // 假设有一个服务方法用于查询闸站的总体水雨晴过程线数据
        List<JhSlcrsRWaterVo> waterVos = jhSlcrsRService.selectJhSlcrsRList(slcrsRParam);

        return R.ok(waterVos);
    }


    /**
     * 获取闸站降雨信息
     */
    @ApiOperation(value = "水雨晴-降雨信息 传gis数据CD值", notes = "irrigation:slcrsRain:query")
    @RequiresPermissions("irrigation:slcrsRain:query")
    @GetMapping(value = "/rain")
    public R<List<StPptnRVo>> getRainInfo(StPptnRainParam rainParam) {

        JhirWagaBVo jhirWagaBVo = jhirWagaBService.selectJhirWagaBByStrCode(rainParam.getSTCD());
        List<StPptnRVo> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(jhirWagaBVo) && ObjectUtil.isNotEmpty(jhirWagaBVo.getRaincd())) {
            list = stPptnRService.selectStPptnRList(new StPptnRParam().
                    setSTCD(jhirWagaBVo.getRaincd()).setTMStart(rainParam.getTMStart()).
                    setTMEnd(rainParam.getTMEnd()).setType(rainParam.getType()));
        }


        return R.ok(list);
    }


    /**
     * 断面图示 传gis数据中的CD
     */
    @ApiOperation(value = "断面图示 传gis数据中的CD", notes = "irrigation:river:section")
    @RequiresPermissions("irrigation:river:section")
    @GetMapping(value = "/section/{STCD}")
    public R<FlowSectionVo> getSectionInfo(@PathVariable("STCD") String STCD) {
        JhirWagaBVo jhirWagaBVo = jhirWagaBService.selectJhirWagaBByStrCode(STCD);
        FlowSectionVo flowSectionVo = new FlowSectionVo();
        if (ObjectUtil.isNotNull(jhirWagaBVo)) {
            flowSectionVo.setWrz(jhirWagaBVo.getWwZ());
            flowSectionVo.setGrz(jhirWagaBVo.getSwZ());
            flowSectionVo.setObhtz(jhirWagaBVo.getHisMaxZ());
        }

        JhSlcrsRRealPo one = jhSlcrsRRealService.getOne(new QueryWrapper<JhSlcrsRRealPo>()
                .eq("prjnmcd", STCD)
                .orderBy(true, false, "clltm").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)) {
            flowSectionVo.setZ(one.getSlupsz());
        }
        return R.ok(flowSectionVo);
    }

    /**
     * 查询水闸运行操作日志列表
     */
    @ApiOperation(value = "操作记录列表", notes = "irrigation:slcrsLog:list")
    @RequiresPermissions("irrigation:slcrsLog:list")
    @GetMapping("/slcrs/list")
    public TableDataInfo<List<JhSlcrsDtlLogRVo>> operateList(JhSlcrsDtlLogRParam jhSlcrsDtlLogRParam) {
        startPage();
        List<JhSlcrsDtlLogRVo> list = jhSlcrsDtlLogRService.selectJhSlcrsDtlLogRList(jhSlcrsDtlLogRParam);
        return getDataTable(list);
    }

    /**
     * 查询报警信息列表
     */
    @ApiOperation(value = "报警信息列表", notes = "irrigation:alarm:list")
    @RequiresPermissions("irrigation:alarm:list")
    @GetMapping("/alarm/list")
    public TableDataInfo<List<OpoAlarmMessageVo>> alarmList(OpoAlarmMessageParam opoAlarmMessageParam) {
        startPage();
        List<OpoAlarmMessageVo> list = opoAlarmMessageService.selectOpoAlarmMessageList(opoAlarmMessageParam);
        return getDataTable(list);
    }

    /**
     * 获取视频播放地址
     */
    @ApiOperation(value = "获取视频播放地址 传GIS数据中的CD值", notes = "irrigation:video:see")
    @RequiresPermissions("irrigation:video:see")
    @GetMapping(value = "/see/{cd}")
    public R<String> getSeeInfo(@PathVariable("cd") String cd) {
        String url = "";
        JhirWagaBPo info = jhirWagaBService.getOne(new QueryWrapper<JhirWagaBPo>()
                .eq("str_code", cd).last("limit 1"), false);
        if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getVmBCd())) {

            JhomVmBPo video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                    .eq("cd", info.getVmBCd()).last("limit 1"), false);
            if (ObjectUtil.isNotEmpty(video) && ObjectUtil.isNotEmpty(video.getCameraCode())) {
                //todo 根据cameraCode获取播放地址
                url = hikvisionVideoTool.getVideoPlayUrl(video.getCameraCode());
            }
        }
        return R.ok(url);
    }


    /**
     * 统计闸站信息汇总
     */
    @ApiOperation(value = "统计闸站信息汇总", notes = "irrigation:slcrs:summary")
    @RequiresPermissions("irrigation:slcrs:summary")
    @GetMapping(value = "/summary")
    public R<JhSlcrsSummaryVo> getSummaryInfo() {

        JhSlcrsSummaryVo summaryVo = new JhSlcrsSummaryVo();
        long count = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "HZ"));
        summaryVo.setStationNum(count + "");
        return R.ok(summaryVo);

    }
}
