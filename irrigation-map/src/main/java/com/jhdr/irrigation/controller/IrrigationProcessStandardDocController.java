package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.IrrigationProcessStandardDocPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessStandardDocVo;
import com.jhdr.irrigation.service.IIrrigationProcessStandardDocService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 工程运行-标准化-文档
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Api(tags = "工程运行-标准化-文档")
@RestController
@RequestMapping("/doc")
public class IrrigationProcessStandardDocController extends BaseController
{
    @Autowired
    private IIrrigationProcessStandardDocService irrigationProcessStandardDocService;

    /**
     * 查询工程运行-标准化-文档列表
     */
    @ApiOperation(value = "查询工程运行-标准化-文档列表",notes="irrigation:doc:list")
    @RequiresPermissions("irrigation:doc:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationProcessStandardDocVo>> list(IrrigationProcessStandardDocParam irrigationProcessStandardDocParam)
    {
        startPage();
        List<IrrigationProcessStandardDocVo> list = irrigationProcessStandardDocService.selectIrrigationProcessStandardDocList(irrigationProcessStandardDocParam);

        return getDataTable(list);
    }


}
