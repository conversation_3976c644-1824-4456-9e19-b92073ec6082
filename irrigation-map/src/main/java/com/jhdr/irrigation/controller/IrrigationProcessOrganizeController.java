package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.IrrigationProcessOrganizePo;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessOrganizeVo;
import com.jhdr.irrigation.service.IIrrigationProcessOrganizeService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 工程运行-组织架构
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Api(tags = "工程运行-组织架构")
@RestController
@RequestMapping("/organize")
public class IrrigationProcessOrganizeController extends BaseController
{
    @Autowired
    private IIrrigationProcessOrganizeService irrigationProcessOrganizeService;

    /**
     * 查询工程运行-组织架构列表
     */
    @ApiOperation(value = "查询工程运行-组织架构列表",notes="irrigation:organize:list")
    @RequiresPermissions("irrigation:organize:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationProcessOrganizeVo>> list(IrrigationProcessOrganizeParam irrigationProcessOrganizeParam)
    {
        startPage();
        List<IrrigationProcessOrganizeVo> list = irrigationProcessOrganizeService.selectIrrigationProcessOrganizeList(irrigationProcessOrganizeParam);
        return getDataTable(list);
    }



    /**
     * 获取工程运行-组织架构详细信息
     */
    @ApiOperation(value = "获取工程运行-组织架构详细信息",notes="irrigation:organize:query")
    @RequiresPermissions("irrigation:organize:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationProcessOrganizeVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(irrigationProcessOrganizeService.selectIrrigationProcessOrganizeById(id));
    }

    /**
     * 新增工程运行-组织架构
     */
    @ApiOperation(value = "新增工程运行-组织架构",notes="irrigation:organize:add")
    @RequiresPermissions("irrigation:organize:add")
    @Log(title = "工程运行-组织架构", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationProcessOrganizeAddParam irrigationProcessOrganize)
    {
        return toAjaxR(irrigationProcessOrganizeService.insertIrrigationProcessOrganize(irrigationProcessOrganize),"新增");
    }

    /**
     * 修改工程运行-组织架构
     */
    @ApiOperation(value = "修改工程运行-组织架构",notes="irrigation:organize:edit")
    @RequiresPermissions("irrigation:organize:edit")
    @Log(title = "工程运行-组织架构", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationProcessOrganizeEditParam irrigationProcessOrganize)
    {
        return toAjaxR(irrigationProcessOrganizeService.updateIrrigationProcessOrganize(irrigationProcessOrganize),"修改");
    }

    /**
     * 删除工程运行-组织架构
     */
    @ApiOperation(value = "删除工程运行-组织架构",notes="irrigation:organize:remove")
    @RequiresPermissions("irrigation:organize:remove")
    @Log(title = "工程运行-组织架构", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationProcessOrganizeService.deleteIrrigationProcessOrganizeByIds(ids),"删除");
    }
}
