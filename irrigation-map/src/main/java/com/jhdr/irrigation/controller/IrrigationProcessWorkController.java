package com.jhdr.irrigation.controller;

import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessWorkParam;
import com.jhdr.irrigation.entity.vo.EngineerPumpCountVo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessGateVo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessPumpCountVo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessStandardDocVo;
import com.jhdr.irrigation.service.IIrrigationProcessStandardDocService;
import com.jhdr.irrigation.service.IJhPumpRRealService;
import com.jhdr.irrigation.service.IJhSlcrsRRealService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工程运行-工情工况
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Api(tags = "工程运行-工情工况")
@RestController
@RequestMapping("/condition")
public class IrrigationProcessWorkController extends BaseController
{

    @Resource
    private IJhPumpRRealService pumpRRealService;
    @Resource
    private IJhSlcrsRRealService jhSlcrsRRealService;
    @ApiOperation(value = "泵站 中 小型")
    @GetMapping("/pump/list")
    public TableDataInfo<List<IrrigationProcessPumpCountVo>> pumpList(IrrigationProcessWorkParam processWorkParam)
    {
        startPage();
        List<IrrigationProcessPumpCountVo> list = pumpRRealService.pumpList(processWorkParam);
        return getDataTable(list);
    }


    @ApiOperation(value = "闸站 大  中 小型")
    @GetMapping("/gate/list")
    public TableDataInfo<List<IrrigationProcessGateVo>> gateList(IrrigationProcessWorkParam processWorkParam)
    {
        startPage();
        List<IrrigationProcessGateVo> list = jhSlcrsRRealService.gateList(processWorkParam);
        return getDataTable(list);
    }
}
