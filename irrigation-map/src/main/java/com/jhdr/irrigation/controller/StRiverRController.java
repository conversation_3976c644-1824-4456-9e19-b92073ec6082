package com.jhdr.irrigation.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.StRiverRParam;
import com.jhdr.irrigation.entity.po.StRiverRPo;
import com.jhdr.irrigation.entity.po.StWasRPo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.IStRiverRService;
import com.jhdr.irrigation.service.IStRvfcchBService;
import com.jhdr.irrigation.service.IStStbprpBService;
import com.jhdr.irrigation.service.IStWasRService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 河道水情
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Api(tags = "gis水位站")
@RestController
@RequestMapping("/river")
public class StRiverRController extends BaseController {
    @Autowired
    private IStRiverRService stRiverRService;

    @Resource
    private IStWasRService stWasRService;
    @Autowired
    private IStStbprpBService stStbprpBService;
    @Autowired
    private IStRvfcchBService stRvfcchBService;

    /**
     * 获取水位站最外层基础信息 传gis数据中的CD
     */
    @ApiOperation(value = "获取水位站最外层基础信息 传gis数据中的CD", notes = "irrigation:river:base")
    @RequiresPermissions("irrigation:river:base")
    @GetMapping(value = "/base/{STCD}")
    public R<WaterLevelVo> getBaseInfo(@PathVariable("STCD") String STCD) {
        WaterLevelVo baseVo = new WaterLevelVo();
        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(STCD);
        StStbprpFlowVo stStbprpFlowVo = BeanUtil.toBean(stStbprpBVo, StStbprpFlowVo.class);
        baseVo.setStbprpFlowVo(stStbprpFlowVo);
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(STCD);
        baseVo.setRvfcchBVo(stRvfcchBVo);
      if (ObjectUtil.isNotEmpty(stStbprpBVo)&&ObjectUtil.isNotEmpty(stStbprpBVo.getIswz())&&1==stStbprpBVo.getIswz()){
          //最新水位，同样时间查询，六小时没数据，则为空
          StWasRPo one = stWasRService.getOne(new QueryWrapper<StWasRPo>().
                  eq("STCD", STCD).apply("tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )")
                  .orderBy(true, false, "TM").last(" limit 1"), false);

          if (ObjectUtil.isNotNull(one)) {
              baseVo.setZ(one.getUpz());
              baseVo.setTm(one.getTm());
          }
      }else {
          //最新水位，同样时间查询，六小时没数据，则为空
          StRiverRPo one = stRiverRService.getOne(new QueryWrapper<StRiverRPo>().
                  eq("STCD", STCD).apply("tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )")
                  .orderBy(true, false, "TM").last(" limit 1"), false);
          if (ObjectUtil.isNotNull(one)) {
              baseVo.setZ(one.getZ());
              baseVo.setTm(one.getTm());
          }

      }

        return R.ok(baseVo);
    }


    @ApiOperation(value = "获取水位层级")
    @GetMapping(value = "/region")
    public R<List<StRiverRegionDataVo> > regionList() {
        List<StRiverRegionDataVo> list = stStbprpBService.selectRegionWaterTreeList();
        return R.ok(list);
    }


    /**
     * 过程线列表
     */
    @ApiOperation(value = "过程线列表", notes = "irrigation:river:list")
    @RequiresPermissions("irrigation:river:list")
    @GetMapping("/list")
    public R<List<StRiverRVo>> list(StRiverRParam stRiverRParam) {
        List<StRiverRVo> list = stRiverRService.selectStRiverRList(stRiverRParam);
        return R.ok(list);
    }

    /**
     * 过程线列表
     */
    @ApiOperation(value = "导出过程线列表", notes = "irrigation:river:export")
    @RequiresPermissions("irrigation:river:export")
    @Log(title = "过程线列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StRiverRParam stRiverR) {
        List<StRiverRVo> list = stRiverRService.selectStRiverRList(stRiverR);
        ExcelUtil<StRiverRVo> util = new ExcelUtil<StRiverRVo>(StRiverRVo.class);
        util.exportExcel(response, list, "水位站数据");
    }


    /**
     * 断面图示 传gis数据中的CD
     */
    @ApiOperation(value = "断面图示 传gis数据中的CD",notes="irrigation:river:section")
    @RequiresPermissions("irrigation:river:section")
    @GetMapping(value = "/section/{STCD}")
    public R<FlowSectionVo> getSectionInfo(@PathVariable("STCD") String STCD)
    {

        FlowSectionVo flowSectionVo = new FlowSectionVo();
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(STCD);
        if (ObjectUtil.isNotEmpty(stRvfcchBVo)){
            flowSectionVo.setGrz(stRvfcchBVo.getGrz());
            flowSectionVo.setWrz(stRvfcchBVo.getWrz());
            flowSectionVo.setObhtz(stRvfcchBVo.getObhtz());
        }

        StRiverRPo one = stRiverRService.getOne(new QueryWrapper<StRiverRPo>()
                .eq("STCD", STCD)
                .orderBy(true, false, "TM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)){
            flowSectionVo.setZ(one.getZ());
        }
        return R.ok(flowSectionVo);
    }
}
