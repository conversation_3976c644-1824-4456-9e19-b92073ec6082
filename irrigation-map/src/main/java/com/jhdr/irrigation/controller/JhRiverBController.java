package com.jhdr.irrigation.controller;

import java.math.BigDecimal;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.irrigation.entity.vo.JhRiverSummaryVo;
import com.jhdr.irrigation.entity.vo.SummaryStreamVo;
import com.jhdr.irrigation.entity.vo.TrunkCanalVo;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.JhRiverBPo;
import com.jhdr.irrigation.entity.param.JhRiverBParam;
import com.jhdr.irrigation.entity.param.JhRiverBAddParam;
import com.jhdr.irrigation.entity.param.JhRiverBEditParam;
import com.jhdr.irrigation.entity.vo.JhRiverBVo;
import com.jhdr.irrigation.service.IJhRiverBService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 河流信息
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(tags = "gis河流信息")
@RestController
@RequestMapping("/gis/river")
public class JhRiverBController extends BaseController
{
    @Autowired
    private IJhRiverBService jhRiverBService;

    /**
     * 查询河流信息列表
     */
    @ApiOperation(value = "查询河流信息列表",notes="irrigation:river:list")
    @RequiresPermissions("irrigation:river:list")
    @GetMapping("/list")
    public R<List<JhRiverBVo>> list(JhRiverBParam jhRiverBParam)
    {
        List<JhRiverBVo> list = jhRiverBService.selectJhRiverBList(jhRiverBParam);
        return R.ok(list);
    }



    /**
     * 获取河流信息详细信息
     */
    @ApiOperation(value = "获取河流信息详细信息",notes="irrigation:river:query")
    @RequiresPermissions("irrigation:river:query")
    @GetMapping(value = "/{ennmcd}")
    public R<JhRiverBVo> getInfo(@PathVariable("ennmcd") String ennmcd)
    {

        return R.ok(jhRiverBService.selectJhRiverBByEnnmcd(ennmcd));
    }

    /**
     * 统计河流信息汇总
     */
    @ApiOperation(value = "统计河流信息汇总 type 1河流 2干渠 3支渠  region地区信息逗号分割", notes = "irrigation:river:summary")
    @RequiresPermissions("irrigation:river:summary")
    @GetMapping(value = "/summary/{type}")
    public R<JhRiverSummaryVo> getSummaryInfo(@PathVariable("type") String type,String region) {

        JhRiverSummaryVo jhRiverSummaryVo = new JhRiverSummaryVo();

        switch (type)
            {
                case "1":
                    //"河流汇总信息"
                    SummaryStreamVo summaryStreamVo = new SummaryStreamVo();
                    //茨淮新河数据
                    JhRiverBPo ea3E0000000L = jhRiverBService.getById("EA3E0000000L");

                    //"茨淮新河长度:公里"
                    summaryStreamVo.setChxhMnstln(ea3E0000000L.getMnstln());
                    summaryStreamVo.setChxhTtdrbsar(ea3E0000000L.getTtdrbsar());
                    //茨淮新河数据
                    JhRiverBPo ea3EB000000L = jhRiverBService.getById("EA3EB000000L");
                    summaryStreamVo.setXfhMnstln(ea3EB000000L.getMnstln());
                    summaryStreamVo.setXfhTtdrbsar(ea3EB000000L.getTtdrbsar());

                    jhRiverSummaryVo.setSummaryStreamVo(summaryStreamVo);
                    break;
                case "2":
                    //"干渠汇总信息"

                    TrunkCanalVo trunkCanalVo=jhRiverBService.sumLengthByRvtp("干渠",region);
                    jhRiverSummaryVo.setTrunkCanalVo(trunkCanalVo);
                    break;
                case "3":
                    //"支渠汇总信息"
                    TrunkCanalVo branchCanalVo=jhRiverBService.sumLengthByRvtp("支渠",region);
                    jhRiverSummaryVo.setBranchCanalVo(branchCanalVo);
                    break;
                default:
                    break;
            }

        return R.ok(jhRiverSummaryVo);
    }

}
