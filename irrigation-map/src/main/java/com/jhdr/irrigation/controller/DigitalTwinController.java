package com.jhdr.irrigation.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.*;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.*;
import com.jhdr.irrigation.service.impl.DigitalTwinServiceImpl;
import com.jhdr.irrigation.service.impl.HikvisionVideoTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数字孪生平台
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(tags = "数字孪生平台")
@RestController
@RequestMapping("/digital")
public class DigitalTwinController extends BaseController {

    @Resource
    private IJhPumpDtlRRealService pumpDtlRRealService;
    @Resource
    private IJhSlcrsRRealService jhSlcrsRRealService;
    @Resource
    private IJhPumpRRealService jhPumpRRealService;
    @Resource
    private DigitalTwinServiceImpl digitalTwinService;
    @Resource
    private IStStbprpBService stStbprpBService;
    @Resource
    private IGisDatasDService gisDatasDService;
    @Resource
    private IJhomVmBService jhomVmBService;
    @Resource
    private HikvisionVideoTool hikvisionVideoTool;
    @Autowired
    private IJhirWagaBService jhirWagaBService;
    @Autowired
    private IJhSlcrsRService jhSlcrsRService;
    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;
    @Autowired
    private IStPptnRService stPptnRService;
    @Autowired
    private IJhirPustBService jhirPustBService;
    @Autowired
    private IJhFlowycRService jhFlowycRService;
    @Autowired
    private IStRvfcchBService stRvfcchBService;
    @ApiOperation(value = "工情-数量统计 包含：枢纽，泵站，闸站 ")
    @GetMapping(value = "/engineer/counting")
    public R<EngineerCountingVo> engineerCounting()
    {
        EngineerCountingVo   engineerCountingVo= digitalTwinService.getEngineerCounting();
        return R.ok(engineerCountingVo);
    }

    @ApiOperation(value = "工情-运行统计-枢纽")
    @GetMapping(value = "/engineer/hub/count")
    public R<List<EngineerHubCountVo>> engineerHubCount()
    {
        List<EngineerHubCountVo> hubCountVos = new ArrayList<>();
        hubCountVos= digitalTwinService.engineerHubCount();
        return R.ok(hubCountVos);
    }

    @ApiOperation(value = "工情-运行统计-泵站")
    @GetMapping(value = "/engineer/pump/count")
    public R<List<EngineerPumpCountVo>> engineerPumpCount()
    {
        List<EngineerPumpCountVo> pumpCountVos =  digitalTwinService.engineerPumpCount();
        if (ObjectUtil.isNotEmpty(pumpCountVos)) {
            // 根据 status 字段倒序排列
            pumpCountVos = pumpCountVos.stream()
                    .sorted((vo1, vo2) -> {
                        int status1 = Integer.parseInt(vo1.getStatus());
                        int status2 = Integer.parseInt(vo2.getStatus());
                        return Integer.compare(status2, status1); // 倒序排列
                    })
                    .collect(Collectors.toList());
        }

        return R.ok(pumpCountVos);
    }

    /**
     * 获取泵站基本信息详细信息
     */
    @ApiOperation(value = "获取泵站基本信息详细信息 传站编码")
    @GetMapping(value = "/pust/{strCode}")
    public R<JhirPustBVo> getPustInfo(@PathVariable("strCode") String strCode)
    {
        return R.ok(jhirPustBService.selectJhirPustBByStrCode(strCode));
    }

    @ApiOperation(value = "工情-运行统计-闸站")
    @GetMapping(value = "/engineer/gate/station/count")
    public R<List<EngineerGateStationCountVo>> engineerGateStationCount()
    {
        List<EngineerGateStationCountVo> gateStationCountVos = new ArrayList<>();
        gateStationCountVos=   digitalTwinService.engineerGateStationCount();
        if (ObjectUtil.isNotEmpty(gateStationCountVos)) {
            // 根据 status 字段倒序排列
            gateStationCountVos = gateStationCountVos.stream()
                    .sorted((vo1, vo2) -> {
                        int status1 = Integer.parseInt(vo1.getStatus());
                        int status2 = Integer.parseInt(vo2.getStatus());
                        return Integer.compare(status2, status1); // 倒序排列
                    })
                    .collect(Collectors.toList());
        }
        return R.ok(gateStationCountVos);
    }

    /**
     * 工情-枢纽信息
     */
    @ApiOperation(value = "工情-枢纽信息 枢纽详情 输入枢纽编码")
    @GetMapping(value = "/engineer/hub/{hubCd}")
    public R<EngineerHubVo> engineerHub(@PathVariable("hubCd") String hubCd)
    {
        if (ObjectUtil.isNotEmpty(hubCd)){
            EngineerHubVo  hubVo=digitalTwinService.getEngineerHub(hubCd);
            return R.ok(hubVo);
        }
        return R.fail("请输入枢纽编码");
    }

    @ApiOperation(value = "工情-闸站 运行详情 输入闸站编码")
    @GetMapping(value = "/engineer/gate/station/{gateStationCd}")
    public R<JhSlcrsRInfoVo> gateStationCdInfo(@PathVariable("gateStationCd") String gateStationCd)
    {
        JhSlcrsRRealVo jhSlcrsRVo =jhSlcrsRRealService.selectJhSlcrsRRealByPrjnmcd(gateStationCd);
        JhSlcrsRInfoVo infoVo=  BeanUtil.toBean(jhSlcrsRVo,JhSlcrsRInfoVo.class);
        if(ObjectUtil.isEmpty(infoVo)){
            infoVo=new JhSlcrsRInfoVo();
        }
        //闸站对应闸门开度列表
        List<JhSlcrsDtlRInfoVo> slcrsDtlRInfoVos= jhSlcrsRRealService.getSlcrsRRealInfoVos(gateStationCd);
        if(ObjectUtil.isNotEmpty(slcrsDtlRInfoVos)){
            infoVo.setSlcrsRInfoVos(slcrsDtlRInfoVos);
        }
        if (ObjectUtil.isNotEmpty(infoVo)) {
            JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(gateStationCd);
            if (ObjectUtil.isNotEmpty(temporaryData)){
                infoVo.setSldsz(temporaryData.getSldsz());
                infoVo.setSlupsz(temporaryData.getSlupsz());
                infoVo.setUpswtp(temporaryData.getUpswtp());
                infoVo.setDswtp(temporaryData.getDswtp());
            }else {
                infoVo.setSldsz(null);
                infoVo.setSlupsz(null);
                infoVo.setUpswtp(null);
                infoVo.setDswtp(null);
            }
        }
        return R.ok(infoVo);
    }


    @ApiOperation(value = "工情-闸站-水雨晴-过程线详细信息", notes = "irrigation:slcrsProcessLine:query")
    @RequiresPermissions("irrigation:slcrsProcessLine:query")
    @GetMapping(value = "/process/line")
    public R<List<JhSlcrsRWaterVo>> getProcessLineInfo(JhSlcrsRParam slcrsRParam) {
        // 假设有一个服务方法用于查询闸站的总体水雨晴过程线数据
        List<JhSlcrsRWaterVo> waterVos = jhSlcrsRService.selectJhSlcrsRList(slcrsRParam);

        return R.ok(waterVos);
    }

    /**
     * 获取闸站降雨信息
     */
    @ApiOperation(value = "工情-闸站-水雨晴-降雨信息 ", notes = "irrigation:slcrsRain:query")
    @RequiresPermissions("irrigation:slcrsRain:query")
    @GetMapping(value = "/rain")
    public R<List<StPptnRVo>> getRainInfo(StPptnRainParam rainParam) {

        JhirWagaBVo jhirWagaBVo = jhirWagaBService.selectJhirWagaBByStrCode(rainParam.getSTCD());
        List<StPptnRVo> list = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(jhirWagaBVo) && ObjectUtil.isNotEmpty(jhirWagaBVo.getRaincd())) {
            list = stPptnRService.selectStPptnRList(new StPptnRParam().
                    setSTCD(jhirWagaBVo.getRaincd()).setTMStart(rainParam.getTMStart()).
                    setTMEnd(rainParam.getTMEnd()).setType(rainParam.getType()));
        }
        return R.ok(list);
    }
    /**
     * 查询报警信息列表
     */
    @ApiOperation(value = "工情-闸站-报警信息列表", notes = "irrigation:alarm:list")
    @RequiresPermissions("irrigation:alarm:list")
    @GetMapping("/alarm/list")
    public TableDataInfo<List<OpoAlarmMessageVo>> alarmList(OpoAlarmMessageParam opoAlarmMessageParam) {
        startPage();
        List<OpoAlarmMessageVo> list = opoAlarmMessageService.selectOpoAlarmMessageList(opoAlarmMessageParam);
        return getDataTable(list);
    }

    /**
     * 获取视频播放地址
     */
    @ApiOperation(value = "工情-闸站-获取视频播放地址 闸站编码", notes = "irrigation:video:see")
    @RequiresPermissions("irrigation:video:see")
    @GetMapping(value = "/see/{cd}")
    public R<String> getSeeCdInfo(@PathVariable("cd") String cd) {
        String url = "";
        JhirWagaBPo info = jhirWagaBService.getOne(new QueryWrapper<JhirWagaBPo>()
                .eq("str_code", cd).last("limit 1"), false);
        if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getVmBCd())) {

            JhomVmBPo video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                    .eq("cd", info.getVmBCd()).last("limit 1"), false);
            if (ObjectUtil.isNotEmpty(video) && ObjectUtil.isNotEmpty(video.getCameraCode())) {
                //todo 根据cameraCode获取播放地址
                url = hikvisionVideoTool.getVideoPlayUrl(video.getCameraCode());
            }
        }
        return R.ok(url);
    }

    /**
     * 获取闸站基础信息详细信息
     */
    @ApiOperation(value = "获取闸站基础信息详细信息 传站编码")
    @GetMapping(value = "/waga/{strCode}")
    public R<JhirWagaBVo> getWagaInfo(@PathVariable("strCode") String strCode) {

        return R.ok(jhirWagaBService.selectJhirWagaBByStrCode(strCode));
    }

    @ApiOperation(value = "工情-泵站 运行详情 输入泵站编码")
    @GetMapping(value = "/engineer/pump/{prjnmcd}")
    public R<JhPumpRInfoVo> getInfo(@PathVariable("prjnmcd") String prjnmcd)
    {
        JhPumpRRealVo jhPumpRVo= jhPumpRRealService.selectJhPumpRRealByPrjnmcd(prjnmcd);
        JhPumpRInfoVo infoVo=  BeanUtil.toBean(jhPumpRVo,JhPumpRInfoVo.class);
        if (ObjectUtil.isEmpty(infoVo)){
            infoVo=new JhPumpRInfoVo();
        }
        List<JhPumpDtlRInfoVo> pumpDtlRInfoVos= pumpDtlRRealService.getPumpDtlRInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(pumpDtlRInfoVos)){
            infoVo.setPumpDtlRInfoVos(pumpDtlRInfoVos);
        }

        JhSlcrsRRealTemporaryDataVo temporaryData =  jhSlcrsRRealService.temporaryData(prjnmcd);
        if (ObjectUtil.isNotEmpty(temporaryData)){
            infoVo.setPpupz(temporaryData.getSlupsz());
            infoVo.setPpdwz(temporaryData.getSldsz());
            infoVo.setPpupwptn(temporaryData.getUpswtp());
            infoVo.setPpdwwptn(temporaryData.getDswtp());
        }else {
            infoVo.setPpupz(null);
            infoVo.setPpdwz(null);
            infoVo.setPpupwptn(null);
            infoVo.setPpdwwptn(null);
        }

        return R.ok(infoVo);
    }

    @ApiOperation(value = "水雨情-数量统计 包含：水位站，雨量站")
    @GetMapping(value = "/situation/counting")
    public R<WaterSituationCountingVo> waterSituationCounting()
    {
        WaterSituationCountingVo   waterSituationCountingVo= digitalTwinService.waterSituationCounting();

        return R.ok(waterSituationCountingVo);
    }

    @ApiOperation(value = "水雨情-河道水位列表")
    @GetMapping("/situation/water/list")
    public R<List<WaterSituationGisDatasDVo>> waterList()
    {
        GisDatasDParam gisDatasDParam = new GisDatasDParam();
        gisDatasDParam.setGISSIGN("SW");
        List<GisDatasDVo> list = gisDatasDService.selectGisDatasDList(gisDatasDParam);
        List<WaterSituationGisDatasDVo> datasDVos = BeanUtil.copyToList(list, WaterSituationGisDatasDVo.class);

        // 定义要移除的站点名称
        List<String> stationsToRemove = Arrays.asList(
                "荆山湖退洪闸", "荆山湖进洪闸", "淮南", "蚌埠闸", "蚌埠闸下",
                "吴家渡", "阜阳闸下", "阜阳闸", "关集"
        );

        // 过滤掉要移除的站点
        datasDVos = datasDVos.stream()
                .filter(vo -> !stationsToRemove.contains(vo.getNm()))
                .collect(Collectors.toList());

        return R.ok(datasDVos);
    }

    @ApiOperation(value = "水雨情-流量站列表")
    @GetMapping("/situation/flow/list")
    public R<List<WaterSituationGisDatasDVo>> flowList()
    {
        GisDatasDParam gisDatasDParam = new GisDatasDParam();
        gisDatasDParam.setGISSIGN("LL");
        List<GisDatasDVo> list = gisDatasDService.selectGisDatasDList(gisDatasDParam);
        List<WaterSituationGisDatasDVo> datasDVos = BeanUtil.copyToList(list, WaterSituationGisDatasDVo.class);
        if (ObjectUtil.isNotEmpty(datasDVos)){
            // 1. 收集所有需要查询的CD
            List<String> cdList = datasDVos.stream()
                    .map(WaterSituationGisDatasDVo::getCd)
                    .collect(Collectors.toList());

            // 2. 批量查询年累计水量
            Map<String, BigDecimal> yearAccqMap = jhFlowycRService.batchYearAccq(cdList);

            for (WaterSituationGisDatasDVo flowSectionVo : datasDVos){
                BigDecimal accq = yearAccqMap.getOrDefault(flowSectionVo.getCd(), BigDecimal.ZERO);
                if (ObjectUtil.isNotEmpty(accq)) {
                    accq = accq.setScale(2, BigDecimal.ROUND_HALF_UP);
                }
                flowSectionVo.setAccqYear(accq);

                // 将 q 字段从 String 转换为 BigDecimal 并保留两位小数
                BigDecimal qValue = new BigDecimal(flowSectionVo.getQ());
                qValue = qValue.setScale(2, BigDecimal.ROUND_HALF_UP);
                flowSectionVo.setQ(qValue.toString());
            }
            // 根据 q 字段倒序排列
            datasDVos = datasDVos.stream()
                    .sorted((vo1, vo2) -> {
                        BigDecimal q1 = new BigDecimal(vo1.getQ());
                        BigDecimal q2 = new BigDecimal(vo2.getQ());
                        return q2.compareTo(q1); // 倒序排列
                    })
                    .collect(Collectors.toList());
        }
        return R.ok(datasDVos);
    }


    @ApiOperation(value = "获取水位站基础信息 传站编码")
    @GetMapping(value = "/base/water/{cd}")
    public R<StStbprpFlowVo> getWaterBaseInfo(@PathVariable("cd") String cd) {

        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(cd);
        StStbprpFlowVo stStbprpFlowVo = BeanUtil.toBean(stStbprpBVo, StStbprpFlowVo.class);

        return R.ok(stStbprpFlowVo);
    }

    /**
     * 获取流量站最外层基础信息 传gis数据中的CD
     */
    @ApiOperation(value = "获取流量站最外层基础信息 传gis数据中的CD")
    @GetMapping(value = "/flow/base/{STCD}")
    public R<FlowVo> getFlowBaseInfo(@PathVariable("STCD") String STCD)
    {
        FlowVo baseVo=new FlowVo();
        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(STCD);
        StStbprpFlowVo stStbprpFlowVo = BeanUtil.toBean(stStbprpBVo, StStbprpFlowVo.class);
        baseVo.setStbprpFlowVo(stStbprpFlowVo);
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(STCD);
        baseVo.setRvfcchBVo(stRvfcchBVo);

        JhFlowtnRVo one = jhFlowycRService.getOnePsqTn(STCD);
//                (new QueryWrapper<JhFlowycRPo>()
//                .eq("STCD", STCD).apply("DATATM >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )").orderBy(true, false, "DATATM").last("limit 1"), false);
        JhFlowycRPo one111 = jhFlowycRService.getOne(new QueryWrapper<JhFlowycRPo>()
                .eq("STCD", STCD).orderBy(true, false, "DATATM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)){
            baseVo.setQ(one111.getQ()==null?new BigDecimal(0):one111.getQ());
            baseVo.setPsq(one.getPsq()==null?new BigDecimal(0):one.getPsq());
            baseVo.setTm(one111.getDatatm());
        }
        return R.ok(baseVo);
    }

    @ApiOperation(value = "流量站-过程线列表")
    @GetMapping("/flow/list")
    public R<List<JhFlowtnRVo>> flowList(JhFlowycRParam jhFlowycRParam)
    {
        List<JhFlowtnRVo> list = jhFlowycRService.selectJhFlowycRList(jhFlowycRParam);
        return R.ok(list);
    }

    @ApiOperation(value = "水雨情-水位站详情数据列表")
    @GetMapping("/situation/water/info/list")
    public R<WaterSituationInfoListVo> waterInfoList(WaterRiverRParam waterRiverRParam) {

        WaterSituationInfoListVo waterSituationInfoListVo = digitalTwinService.waterInfoList(waterRiverRParam);
        return R.ok(waterSituationInfoListVo);
    }

    @ApiOperation(value = "水雨情-水位站最外层搜索，树结构")
    @GetMapping(value = "/situation/water/region")
    public R<List<StRiverRegionDataVo> > waterRegionList() {
        List<StRiverRegionDataVo> list = stStbprpBService.selectRegionWaterTreeList();
        return R.ok(list);
    }

    @ApiOperation(value = "水雨情-24小时累计降雨量列表")
    @GetMapping("/situation/rain/list")
    public R<List<WaterSituationGisDatasDVo>> rainList()
    {
        GisDatasDParam gisDatasDParam = new GisDatasDParam();
        gisDatasDParam.setGISSIGN("YL");
        List<GisDatasDVo> list = gisDatasDService.selectGisDatasDList(gisDatasDParam);
        List<WaterSituationGisDatasDVo> datasDVos = BeanUtil.copyToList(list, WaterSituationGisDatasDVo.class);
        if (ObjectUtil.isNotEmpty(datasDVos)) {

            // 定义要移除的站点名称
            List<String> stationsToRemove = Arrays.asList("丁集", "孙集", "乌江");

            // 过滤掉要移除的站点
            datasDVos = datasDVos.stream()
                    .filter(vo -> !stationsToRemove.contains(vo.getNm()))
                    .collect(Collectors.toList());


            // 根据 drp 字段倒序排列
            datasDVos = datasDVos.stream()
                    .sorted((vo1, vo2) -> {
                        BigDecimal drp1 = new BigDecimal(vo1.getDrp());
                        BigDecimal drp2 = new BigDecimal(vo2.getDrp());
                        return drp2.compareTo(drp1); // 倒序排列
                    })
                    .collect(Collectors.toList());
        }
        return R.ok(datasDVos);
    }


    @ApiOperation(value = "获取雨量站基础信息 传站编码")
    @GetMapping(value = "/base/rain/{cd}")
    public R<StStbprpBaseVo> getRainBaseInfo(@PathVariable("cd") String cd) {
        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(cd);
        StStbprpBaseVo baseVo = BeanUtil.toBean(stStbprpBVo, StStbprpBaseVo.class);

        return R.ok(baseVo);
    }

    @ApiOperation(value = "水雨情-雨量站降雨柱状图 ")
    @GetMapping(value = "/situation/rain/bar")
    public R<List<StPptnRVo>> getRainBar(WaterRainParam rainParam) {
        List<StPptnRVo> list = digitalTwinService.getRainBar(rainParam);
        return R.ok(list);
    }

    @ApiOperation(value = "水雨情-雨量站最外层搜索，树结构")
    @GetMapping(value = "/situation/rain/region")
    public R<List<StRiverRegionDataVo> > rainRegionList() {
        List<StRiverRegionDataVo> list = stStbprpBService.selectRegionTreeList("PP");
        return R.ok(list);
    }

    @ApiOperation(value = "视频-数量统计 ")
    @GetMapping(value = "/video/counting")
    public R<VideoSituationCountingVo> videoSituationCounting()
    {
        VideoSituationCountingVo   situationCountingVo= new VideoSituationCountingVo();
        Long videoStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "SP"));
        //视频站数量
        situationCountingVo.setVideoAllNum(videoStationCount+"");
        //获取视频在线数量
        Integer onlineNum = jhomVmBService.onlineNum();
        situationCountingVo.setVideoOpenNum(onlineNum+"");
        return R.ok(situationCountingVo);
    }

    /**
     * 获取视频播放地址
     */
    @ApiOperation(value = "获取视频播放地址 参数：区域层级中cameraCode")
    @GetMapping(value = "/video/see/{cameraCode}")
    public R<VideoDataVo> getSeeInfo(@PathVariable("cameraCode") String cd)
    {
        VideoDataVo videoDataVo = new VideoDataVo();
        String url="";
        JhomVmBPo video =null;
        video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                .eq("cd", cd).last("limit 1"), false);
        if (ObjectUtil.isEmpty(video)){
            video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                    .eq("camera_code", cd).last("limit 1"), false);
        }
        if(ObjectUtil.isNotEmpty(video)&&ObjectUtil.isNotEmpty(video.getCameraCode()) ){
            //todo 根据cameraCode获取播放地址
            url =hikvisionVideoTool.getVideoPlayUrl(video.getCameraCode());
            videoDataVo.setCameraCode(video.getCameraCode());
            videoDataVo.setUrl(url);
            videoDataVo.setNm(video.getNm());
        }
        return R.ok(videoDataVo);
    }

    @ApiOperation(value = "获取视频区域层级")
    @GetMapping("/video/region")
    public R<List<VideoRegionDataVo>> regionList()
    {
        List<VideoRegionDataVo> list = jhomVmBService.selectRegionTreeList();
        return R.ok(list);
    }

}
