package com.jhdr.irrigation.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileEditParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessFileVo;
import com.jhdr.irrigation.service.IIrrigationProcessFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 工程运行-文件管理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Api(tags = "工程运行-文件管理")
@RestController
@RequestMapping("/file")
public class IrrigationProcessFileController extends BaseController
{
    @Autowired
    private IIrrigationProcessFileService irrigationProcessFileService;

    /**
     * 查询工程运行-文件管理列表
     */
    @ApiOperation(value = "查询工程运行-文件管理列表",notes="irrigation:file:list")
    @RequiresPermissions("irrigation:file:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationProcessFileVo>> list(IrrigationProcessFileParam irrigationProcessFileParam)
    {
        startPage();
        List<IrrigationProcessFileVo> list = irrigationProcessFileService.selectIrrigationProcessFileList(irrigationProcessFileParam);
        return getDataTable(list);
    }



    /**
     * 获取工程运行-文件管理详细信息
     */
    @ApiOperation(value = "获取工程运行-文件管理详细信息",notes="irrigation:file:query")
    @RequiresPermissions("irrigation:file:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationProcessFileVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(irrigationProcessFileService.selectIrrigationProcessFileById(id));
    }

    /**
     * 新增工程运行-文件管理
     */
    @ApiOperation(value = "新增工程运行-文件管理",notes="irrigation:file:add")
    @RequiresPermissions("irrigation:file:add")
    @Log(title = "工程运行-文件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationProcessFileAddParam irrigationProcessFile)
    {
        return toAjaxR(irrigationProcessFileService.insertIrrigationProcessFile(irrigationProcessFile),"新增");
    }

    /**
     * 修改工程运行-文件管理
     */
    @ApiOperation(value = "修改工程运行-文件管理",notes="irrigation:file:edit")
    @RequiresPermissions("irrigation:file:edit")
    @Log(title = "工程运行-文件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationProcessFileEditParam irrigationProcessFile)
    {
        return toAjaxR(irrigationProcessFileService.updateIrrigationProcessFile(irrigationProcessFile),"修改");
    }

    /**
     * 删除工程运行-文件管理
     */
    @ApiOperation(value = "删除工程运行-文件管理",notes="irrigation:file:remove")
    @RequiresPermissions("irrigation:file:remove")
    @Log(title = "工程运行-文件管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationProcessFileService.deleteIrrigationProcessFileByIds(ids),"删除");
    }
}
