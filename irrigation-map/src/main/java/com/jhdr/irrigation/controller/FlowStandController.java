package com.jhdr.irrigation.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.JhFlowycRParam;
import com.jhdr.irrigation.entity.po.JhFlowycRPo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.IJhFlowycRService;
import com.jhdr.irrigation.service.IStRvfcchBService;
import com.jhdr.irrigation.service.IStStbprpBService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * gis流量站
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Api(tags = "gis流量站")
@RestController
@RequestMapping("/flow")
public class FlowStandController extends BaseController
{
    @Autowired
    private IJhFlowycRService jhFlowycRService;

    @Autowired
    private IStStbprpBService stStbprpBService;
    @Autowired
    private IStRvfcchBService stRvfcchBService;


    /**
     * 获取流量站最外层基础信息 传gis数据中的CD
     */
    @ApiOperation(value = "获取流量站最外层基础信息 传gis数据中的CD",notes="irrigation:pptn:base")
    @RequiresPermissions("irrigation:pptn:base")
    @GetMapping(value = "/base/{STCD}")
    public R<FlowVo> getBaseInfo(@PathVariable("STCD") String STCD)
    {
        FlowVo baseVo=new FlowVo();
        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(STCD);
        StStbprpFlowVo stStbprpFlowVo = BeanUtil.toBean(stStbprpBVo, StStbprpFlowVo.class);
        baseVo.setStbprpFlowVo(stStbprpFlowVo);
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(STCD);
        baseVo.setRvfcchBVo(stRvfcchBVo);

        JhFlowtnRVo one = jhFlowycRService.getOnePsqTn(STCD);
//                (new QueryWrapper<JhFlowycRPo>()
//                .eq("STCD", STCD).apply("DATATM >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )").orderBy(true, false, "DATATM").last("limit 1"), false);
        JhFlowycRPo one111 = jhFlowycRService.getOne(new QueryWrapper<JhFlowycRPo>()
                .eq("STCD", STCD).orderBy(true, false, "DATATM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)){
            baseVo.setQ(one111.getQ()==null?new BigDecimal(0):one111.getQ());
            baseVo.setPsq(one.getPsq()==null?new BigDecimal(0):one.getPsq());

            baseVo.setTm(one111.getDatatm());
        }
        return R.ok(baseVo);
    }

    @ApiOperation(value = "获取流量层级")
    @GetMapping(value = "/region")
    public R<List<StRiverRegionDataVo> > regionList() {
        List<StRiverRegionDataVo> list = stStbprpBService.selectRegionTreeList("ZQ");
        return R.ok(list);
    }



    /**
     * 查询原始水量数据列表
     */
    @ApiOperation(value = "过程线列表",notes="irrigation:flowycProto:list")
    @RequiresPermissions("irrigation:flowycProto:list")
    @GetMapping("/list")
    public R<List<JhFlowtnRVo>> list(JhFlowycRParam jhFlowycRParam)
    {

        List<JhFlowtnRVo> list = jhFlowycRService.selectJhFlowycRList(jhFlowycRParam);
        return R.ok(list);
    }


    /**
     * 月度过程线列表
     */
    @ApiOperation(value = "月度过程线列表")

    @GetMapping("/month/list")
    public R<List<JhFlowtnRVo>> monthList(JhFlowycRParam jhFlowycRParam)
    {

        List<JhFlowtnRVo> list = jhFlowycRService.selectJhFlowycRMonthList(jhFlowycRParam);
        return R.ok(list);
    }


    /**
     * 导出原始水量数据列表
     */
    @ApiOperation(value = "导出过程线列表",notes="irrigation:flowycProto:export")
    @RequiresPermissions("irrigation:flowycProto:export")
    @Log(title = "过程线列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JhFlowycRParam jhFlowycR)
    {
        List<JhFlowtnRVo> list = jhFlowycRService.selectJhFlowycRList(jhFlowycR);
        ExcelUtil<JhFlowtnRVo> util = new ExcelUtil<JhFlowtnRVo>(JhFlowtnRVo.class);
        util.exportExcel(response, list, "过程线列表");
    }

    /***
    *
    * 断面图示 传gis数据中的CD
    *
    */
    @ApiOperation(value = "断面图示 传gis数据中的CD")
    @GetMapping(value = "/cross/section/{stcd}")
    public R<List<FlowCrossSectionVo>> getCrossSectionInfo(@PathVariable("stcd") String stcd)
    {
        List<FlowCrossSectionVo> list = jhFlowycRService.selectFlowCrossSectionList(stcd);
        return R.ok(list);
    }

    /**
     * 断面图示 传gis数据中的CD
     */
    @ApiOperation(value = "断面图示 传gis数据中的CD",notes="irrigation:pptn:section")
    @RequiresPermissions("irrigation:pptn:section")
    @GetMapping(value = "/section/{STCD}")
    public R<FlowSectionVo> getSectionInfo(@PathVariable("STCD") String STCD)
    {
        FlowSectionVo flowSectionVo = new FlowSectionVo();
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(STCD);
        if (ObjectUtil.isNotEmpty(stRvfcchBVo)){
            flowSectionVo.setGrz(stRvfcchBVo.getGrz());
            flowSectionVo.setWrz(stRvfcchBVo.getWrz());
            flowSectionVo.setObhtz(stRvfcchBVo.getObhtz());
        }
        JhFlowycRPo one = jhFlowycRService.getOne(new QueryWrapper<JhFlowycRPo>()
                .eq("STCD", STCD).orderBy(true, false, "DATATM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)){
            flowSectionVo.setZ(one.getZ());
        }
        return R.ok(flowSectionVo);
    }

}
