package com.jhdr.irrigation.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.HubBasicInformationPo;
import com.jhdr.irrigation.entity.po.JhPumpRRealPo;
import com.jhdr.irrigation.entity.po.JhPumpRRealPo;
import com.jhdr.irrigation.entity.po.JhSlcrsRPo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 枢纽
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(tags = "gis枢纽信息")
@RestController
@RequestMapping("/pivot")
public class PivotController {


    @Autowired
    private IJhSlcrsRService slcrsRService;

    @Autowired
    private IJhPumpRRealService pumpRRealService;

    @Autowired
    private IHubBasicInformationService hubBasicInformationService;

    @Autowired
    private IJhPumpDtlRRealService pumpDtlRRealService;

    @Autowired
    private IJhSlcrsRRealService jhSlcrsRRealService;

    /**
     * 获取上桥枢纽信息
     */
    @ApiOperation(value = "枢纽信息 请输入枢纽编码",notes="irrigation:pivot:query")
    @RequiresPermissions("irrigation:pivot:query")
    @GetMapping(value = "/shangqiao/{code}")
    public R<PivotShangQiaoVo> getShangqiaoPivotInfo(@PathVariable("code") String code)
    {
        PivotShangQiaoVo pivotShangQiaoVo = new PivotShangQiaoVo();
        if (ObjectUtil.isNotEmpty(code)){
            HubBasicInformationPo hubInfo = hubBasicInformationService.getOne(new QueryWrapper<HubBasicInformationPo>()
                    .eq("hub_code", code).last("limit 1"), false);

            if (ObjectUtil.isNotEmpty(hubInfo)&& ObjectUtil.isNotEmpty(hubInfo.getGataCode())){
                String gateCode=hubInfo.getGataCode().toString();

                JhSlcrsRPo slcrsRPo = slcrsRService.getOne(new QueryWrapper<JhSlcrsRPo>()
                        .eq("prjnmcd",gateCode)
                        .orderBy(true,false,"clltm").last("limit 1"),false);
                pivotShangQiaoVo= BeanUtil.toBean(slcrsRPo, PivotShangQiaoVo.class);
                if (ObjectUtil.isEmpty(pivotShangQiaoVo)){
                    pivotShangQiaoVo=new PivotShangQiaoVo();
               }
                //闸站对应闸门开度列表
                List<JhSlcrsDtlRInfoVo> slcrsDtlRInfoVos= jhSlcrsRRealService.getSlcrsRRealInfoVos(hubInfo.getGataCode()+"");
                if(ObjectUtil.isNotEmpty(slcrsDtlRInfoVos)){
                    pivotShangQiaoVo.setGateCondition(slcrsDtlRInfoVos);
                }
                pivotShangQiaoVo.setPslcrsCd(gateCode);
                JhSlcrsRRealTemporaryDataVo temporaryData =  jhSlcrsRRealService.temporaryData(gateCode);
                if (ObjectUtil.isNotEmpty(temporaryData)){
                    pivotShangQiaoVo.setSldsz(temporaryData.getSldsz());
                    pivotShangQiaoVo.setSlupsz(temporaryData.getSlupsz());
                    pivotShangQiaoVo.setUpswtp(temporaryData.getUpswtp());
                    pivotShangQiaoVo.setDswtp(temporaryData.getDswtp());
                }else {
                    pivotShangQiaoVo.setSldsz(null);
                    pivotShangQiaoVo.setSlupsz(null);
                    pivotShangQiaoVo.setUpswtp(null);
                    pivotShangQiaoVo.setDswtp(null);
                }

            }
            if (ObjectUtil.isNotEmpty(hubInfo)&& ObjectUtil.isNotEmpty(hubInfo.getGataCode())){
                String  pustCode=hubInfo.getPustCode();
                pivotShangQiaoVo.setPumpCd(pustCode);
                JhPumpRRealPo one = pumpRRealService.getOne(new QueryWrapper<JhPumpRRealPo>()
                        .eq("prjnmcd", pustCode)
                        .orderBy(true, false, "clltm").last("limit 1"), false);
                if (one != null){
                    pivotShangQiaoVo.setPmpq(one.getPmpq()==null?new BigDecimal(0):one.getPmpq());
                    pivotShangQiaoVo.setOmcn(one.getOmcn());
                    pivotShangQiaoVo.setPpcn(one.getPpcn());
                }
                List<JhPumpDtlRInfoVo> pumpDtlRInfoVos= pumpDtlRRealService.getPumpDtlRInfoVos(hubInfo.getPustCode());
                if (ObjectUtil.isNotEmpty(pumpDtlRInfoVos)){
                    pivotShangQiaoVo.setPumpCondition(pumpDtlRInfoVos);
                }
            }


            return R.ok(pivotShangQiaoVo);
        }
        return R.fail("请输入枢纽编码");
    }


}
