package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.IrrigationProcessInspectPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessInspectVo;
import com.jhdr.irrigation.service.IIrrigationProcessInspectService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 工程运行-检查
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Api(tags = "工程运行-检查")
@RestController
@RequestMapping("/inspect")
public class IrrigationProcessInspectController extends BaseController
{
    @Autowired
    private IIrrigationProcessInspectService irrigationProcessInspectService;

    /**
     * 查询工程运行-检查列表
     */
    @ApiOperation(value = "查询工程运行-检查列表",notes="irrigation:inspect:list")
    @RequiresPermissions("irrigation:inspect:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationProcessInspectVo>> list(IrrigationProcessInspectParam irrigationProcessInspectParam)
    {
        startPage();
        List<IrrigationProcessInspectVo> list = irrigationProcessInspectService.selectIrrigationProcessInspectList(irrigationProcessInspectParam);
        return getDataTable(list);
    }



    /**
     * 获取工程运行-检查详细信息
     */
    @ApiOperation(value = "获取工程运行-检查详细信息",notes="irrigation:inspect:query")
    @RequiresPermissions("irrigation:inspect:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationProcessInspectVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(irrigationProcessInspectService.selectIrrigationProcessInspectById(id));
    }

    /**
     * 新增工程运行-检查
     */
    @ApiOperation(value = "新增工程运行-检查",notes="irrigation:inspect:add")
    @RequiresPermissions("irrigation:inspect:add")
    @Log(title = "工程运行-检查", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationProcessInspectAddParam irrigationProcessInspect)
    {
        return toAjaxR(irrigationProcessInspectService.insertIrrigationProcessInspect(irrigationProcessInspect),"新增");
    }

    /**
     * 修改工程运行-检查
     */
    @ApiOperation(value = "修改工程运行-检查",notes="irrigation:inspect:edit")
    @RequiresPermissions("irrigation:inspect:edit")
    @Log(title = "工程运行-检查", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationProcessInspectEditParam irrigationProcessInspect)
    {
        return toAjaxR(irrigationProcessInspectService.updateIrrigationProcessInspect(irrigationProcessInspect),"修改");
    }

    /**
     * 删除工程运行-检查
     */
    @ApiOperation(value = "删除工程运行-检查",notes="irrigation:inspect:remove")
    @RequiresPermissions("irrigation:inspect:remove")
    @Log(title = "工程运行-检查", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationProcessInspectService.deleteIrrigationProcessInspectByIds(ids),"删除");
    }
}
