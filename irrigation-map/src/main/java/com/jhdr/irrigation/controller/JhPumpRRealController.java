package com.jhdr.irrigation.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogRParam;
import com.jhdr.irrigation.entity.param.OpoAlarmMessageParam;
import com.jhdr.irrigation.entity.po.JhirPustBPo;
import com.jhdr.irrigation.entity.po.JhomVmBPo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.*;
import com.jhdr.irrigation.service.impl.HikvisionVideoTool;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * gis泵站数据
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "gis泵站数据")
@RestController
@RequestMapping("/pump/station/real")
public class JhPumpRRealController extends BaseController
{
    @Autowired
    private IJhPumpRRealService jhPumpRRealService;

    @Autowired
    private IJhPumpDtlRRealService pumpDtlRRealService;

    @Autowired
    private IJhPumpDtlLogRService jhPumpDtlLogRService;

    @Autowired
    private IJhirPustBService jhirPustBService;

    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;

    @Autowired
    private IJhomVmBService jhomVmBService;

    @Autowired
    private HikvisionVideoTool hikvisionVideoTool;

    @Autowired
    private IJhSlcrsRRealService jhSlcrsRRealService;

    /**
     * 获取泵站最外层基础信息 传gis数据中的CD
     */
    @ApiOperation(value = "获取泵站最外层基础信息 传gis数据中的CD",notes="irrigation:pumpReal:base")
    @RequiresPermissions("irrigation:pumpReal:base")
    @GetMapping(value = "/base/{prjnmcd}")
    public R<JhPumpRBaseVo> getBaseInfo(@PathVariable("prjnmcd") String prjnmcd)
    {
        JhPumpRRealVo jhPumpRVo = jhPumpRRealService.selectJhPumpRRealByPrjnmcd(prjnmcd);
        JhPumpRBaseVo baseVo=  BeanUtil.toBean(jhPumpRVo,JhPumpRBaseVo.class);
        if(ObjectUtil.isNotEmpty(baseVo)&&ObjectUtil.isEmpty(baseVo.getPmpq())){
            baseVo.setPmpq(BigDecimal.valueOf(0));
        }

        if(ObjectUtil.isEmpty(jhPumpRVo)){
            baseVo=new JhPumpRBaseVo();
            baseVo.setPrjnmcd(prjnmcd);
        }
        if (ObjectUtil.isNotEmpty(baseVo))
        {
            JhSlcrsRRealTemporaryDataVo temporaryData =  jhSlcrsRRealService.temporaryData(prjnmcd);

            if (ObjectUtil.isNotEmpty(temporaryData)){
                baseVo.setPpupz(temporaryData.getSlupsz());
                baseVo.setPpdwz(temporaryData.getSldsz());
                baseVo.setPpupwptn(temporaryData.getUpswtp());
                baseVo.setPpdwwptn(temporaryData.getDswtp());
            }else {
                baseVo.setPpupz(null);
                baseVo.setPpdwz(null);
                baseVo.setPpupwptn(null);
                baseVo.setPpdwwptn(null);
            }
        }
        List<JhPumpDtlRInfoVo> pumpDtlRInfoVos= pumpDtlRRealService.getPumpDtlRInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(pumpDtlRInfoVos)){
            baseVo.setPumpCondition(pumpDtlRInfoVos);
        }
        JhirPustBVo jhirPustBVo = jhirPustBService.selectJhirPustBByStrCode(prjnmcd);

        baseVo.setMethod(jhirPustBVo.getMethod());
        return R.ok(baseVo);
    }

    /**
     * 获取泵站运行状况详细信息
     */
    @ApiOperation(value = "获取泵站运行状况详细信息 传gis数据中的CD",notes="irrigation:pumpReal:query")
    @RequiresPermissions("irrigation:pumpReal:query")
    @GetMapping(value = "/info/{prjnmcd}")
    public R<JhPumpRInfoVo> getInfo(@PathVariable("prjnmcd") String prjnmcd)
    {
        JhPumpRRealVo jhPumpRVo= jhPumpRRealService.selectJhPumpRRealByPrjnmcd(prjnmcd);
        JhPumpRInfoVo   infoVo=  BeanUtil.toBean(jhPumpRVo,JhPumpRInfoVo.class);
         if (ObjectUtil.isEmpty(infoVo)){
             infoVo =new JhPumpRInfoVo();
         }
         if(ObjectUtil.isEmpty(infoVo.getPmpq())){
             infoVo.setPmpq(BigDecimal.valueOf(0));
         }
        JhSlcrsRRealTemporaryDataVo temporaryData =  jhSlcrsRRealService.temporaryData(prjnmcd);
        if (ObjectUtil.isNotEmpty(temporaryData)){
            infoVo.setPpupz(temporaryData.getSlupsz());
            infoVo.setPpdwz(temporaryData.getSldsz());
            infoVo.setPpupwptn(temporaryData.getUpswtp());
            infoVo.setPpdwwptn(temporaryData.getDswtp());
        }else {
            infoVo.setPpupz(null);
            infoVo.setPpdwz(null);
            infoVo.setPpupwptn(null);
            infoVo.setPpdwwptn(null);
        }

        List<JhPumpDtlRInfoVo> pumpDtlRInfoVos= pumpDtlRRealService.getPumpDtlRInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(pumpDtlRInfoVos)){
            infoVo.setPumpDtlRInfoVos(pumpDtlRInfoVos);
        }
        return R.ok(infoVo);
    }



    /**
     * 查询泵机运行操作记录列表
     */
    @ApiOperation(value = "查询泵机运行操作记录列表",notes="irrigation:pumpLog:list")
    @RequiresPermissions("irrigation:pumpLog:list")
    @GetMapping("/list")
    public TableDataInfo<List<JhPumpDtlLogRVo>> list(JhPumpDtlLogRParam jhPumpDtlLogRParam)
    {
        startPage();
        List<JhPumpDtlLogRVo> list = jhPumpDtlLogRService.selectJhPumpDtlLogRList(jhPumpDtlLogRParam);
        return getDataTable(list);
    }

    /**
     * 获取泵站基本信息详细信息
     */
    @ApiOperation(value = "获取泵站基本信息详细信息 gis数据中的CD传参",notes="irrigation:pust:query")
    @RequiresPermissions("irrigation:pust:query")
    @GetMapping(value = "/pust/{strCode}")
    public R<JhirPustBVo> getPustInfo(@PathVariable("strCode") String strCode)
    {

        return R.ok(jhirPustBService.selectJhirPustBByStrCode(strCode));
    }


    /**
     * 查询报警信息列表
     */
    @ApiOperation(value = "查询报警信息列表",notes="irrigation:alarm:list")
    @RequiresPermissions("irrigation:alarm:list")
    @GetMapping("/alarm/list")
    public TableDataInfo<List<OpoAlarmMessageVo>> alarmList(OpoAlarmMessageParam opoAlarmMessageParam)
    {
        startPage();
        List<OpoAlarmMessageVo> list = opoAlarmMessageService.selectOpoAlarmMessageList(opoAlarmMessageParam);
        return getDataTable(list);
    }

    /**
     * 统计泵站信息汇总
     */
    @ApiOperation(value = "统计泵站信息汇总", notes = "irrigation:pust:summary")
    @RequiresPermissions("irrigation:pust:summary")
    @GetMapping(value = "/summary")
    public R<JhirPustSummaryVo> getSummaryInfo() {
      JhirPustSummaryVo summary=  jhirPustBService.selectPustNumByGis();
        return R.ok(summary);

    }

    /**
     * 获取视频播放地址
     */
    @ApiOperation(value = "获取视频播放地址 传GIS数据中的CD值",notes="irrigation:video:see")
    @RequiresPermissions("irrigation:video:see")
    @GetMapping(value = "/see/{cd}")
    public R<String> getSeeInfo(@PathVariable("cd") String cd)
    {
        String url="";
        JhirPustBPo pustInfo = jhirPustBService.getOne(new QueryWrapper<JhirPustBPo>()
                .eq("str_code", cd).last("limit 1"), false);
        if(ObjectUtil.isNotEmpty(pustInfo)&&ObjectUtil.isNotEmpty(pustInfo.getVmBCd()) ){

            JhomVmBPo video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                    .eq("cd", pustInfo.getVmBCd()).last("limit 1"), false);
            if(ObjectUtil.isNotEmpty(video)&&ObjectUtil.isNotEmpty(video.getCameraCode()) ){
                //todo 根据cameraCode获取播放地址
                url=  hikvisionVideoTool.getVideoPlayUrl(video.getCameraCode());
            }
        }
        return R.ok(url);
    }


}
