package com.jhdr.irrigation.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.GisDatasDAddParam;
import com.jhdr.irrigation.entity.param.WeatherDataParam;
import com.jhdr.irrigation.entity.po.WeatherDataPo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.IWeatherDataService;
import com.jhdr.irrigation.service.impl.DisasterPreventionServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 灾害防御大屏
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
@Api(tags = "灾害防御大屏")
@RestController
@RequestMapping("/prevention")
public class DisasterPreventionController {

    @Autowired
    private IWeatherDataService weatherDataService;

    @Resource
    private DisasterPreventionServiceImpl disasterPreventionService;

    @ApiOperation(value = "调度运用、地图、河道水清-枢纽信息 汇总")
    @GetMapping(value = "/hubs/list")
    public R<List<PreventionHubVo>> hubs() {

        List<PreventionHubVo> hubsVo = disasterPreventionService.getHubs();

       return R.ok(hubsVo);
    }

    @ApiOperation(value = "工程信息-枢纽信息")
    @GetMapping(value = "/project/list")
    public R<List<PreventionProjectVo>> projectInfo() {

        List<PreventionProjectVo> projectInfo = disasterPreventionService.getProjectInfo();

       return R.ok(projectInfo);
    }

    @ApiOperation(value = "视频监控")
    @GetMapping(value = "/video/list")
    public R<List<PreventionVideoVo>> videoInfo() {

        List<PreventionVideoVo> videoInfo = disasterPreventionService.getVideoInfo();

       return R.ok(videoInfo);
    }


    @ApiOperation(value = "雨量统计-枢纽 ")
    @GetMapping(value = "/rain/statistics")
    public R<List<PreventionRainVo>> getRainInfos() {

        List<PreventionRainVo> rainInfos = disasterPreventionService.getRainInfos();
        return R.ok(rainInfos);
    }

    @ApiOperation(value = "水情信息-枢纽 ")
    @GetMapping(value = "/water/statistics")
    public R<List<PreventionWaterVo>> getWaterInfos() {

        List<PreventionWaterVo> waterVos = disasterPreventionService.getWaterInfos();
        return R.ok(waterVos);
    }

    @ApiOperation(value = "超限站点 弹窗 ")
    @GetMapping(value = "/warn/list")
    public R<List<PreventionWarnVo>> getWarnList() {

        List<PreventionWarnVo> warnVos = disasterPreventionService.getWarnList();
        return R.ok(warnVos);
    }

    /**
     * 天气信息入库
     */
    @ApiOperation(value = "天气信息入库")
//    @Log(title = "获取天气信息入库", businessType = BusinessType.INSERT)
    @PostMapping(value = "/weather")
    public R weatherSaveData(@RequestBody WeatherDataParam weatherDataParam )
    {
        weatherDataService.saveWeatherData(weatherDataParam);

        return R.ok();
    }

    /**
     * 获取天气信息
     */
    @ApiOperation(value = "获取天气信息  region=怀远县 颍泉区 颍东区 蒙城县 利辛县 凤台县 潘集区")
    @GetMapping(value = "/weather/{region}")
    public String weatherGetData(@PathVariable("region") String region)
    {
        if (StringUtils.isEmpty(region)){
            return "";
        }
        WeatherDataPo weatherDataPo = weatherDataService.getOne(new QueryWrapper<WeatherDataPo>()
                .eq("region", region).eq("status", 1).last("limit 1"));
        if (ObjectUtil.isNotEmpty(weatherDataPo)&&
                StringUtils.isNotEmpty(weatherDataPo.getWeatherJson())&&weatherDataPo.getWeatherJson().contains("\"code\":\"200\"")){
            return weatherDataPo.getWeatherJson().replace("\"code\":\"200\"", "\"code\":200");
        }

        return R.fail().toString();
    }
}
