package com.jhdr.irrigation.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectEditParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessProjectInfoVo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessProjectVo;
import com.jhdr.irrigation.service.IIrrigationProcessProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工程运行-项目管理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Api(tags = "工程运行-项目管理")
@RestController
@RequestMapping("/project")
public class IrrigationProcessProjectController extends BaseController
{
    @Autowired
    private IIrrigationProcessProjectService irrigationProcessProjectService;

    /**
     * 查询工程运行-项目管理列表
     */
    @ApiOperation(value = "查询工程运行-项目管理列表",notes="irrigation:project:list")
    @RequiresPermissions("irrigation:project:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationProcessProjectVo>> list(IrrigationProcessProjectParam irrigationProcessProjectParam)
    {
        startPage();
        List<IrrigationProcessProjectVo> list = irrigationProcessProjectService.selectIrrigationProcessProjectList(irrigationProcessProjectParam);
        return getDataTable(list);
    }



    /**
     * 获取工程运行-项目管理详细信息
     */
    @ApiOperation(value = "获取工程运行-项目管理详细信息",notes="irrigation:project:query")
    @RequiresPermissions("irrigation:project:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationProcessProjectInfoVo> getInfo(@PathVariable("id") Long id)
    {
        IrrigationProcessProjectInfoVo irrigationProcessProjectVo = irrigationProcessProjectService.selectIrrigationProcessProjectById(id);
        return R.ok(irrigationProcessProjectVo);
    }

    /**
     * 新增工程运行-项目管理
     */
    @ApiOperation(value = "新增工程运行-项目管理",notes="irrigation:project:add")
    @RequiresPermissions("irrigation:project:add")
    @Log(title = "工程运行-项目管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationProcessProjectAddParam irrigationProcessProject)
    {
        return toAjaxR(irrigationProcessProjectService.insertIrrigationProcessProject(irrigationProcessProject),"新增");
    }

    /**
     * 修改工程运行-项目管理
     */
    @ApiOperation(value = "修改工程运行-项目管理",notes="irrigation:project:edit")
    @RequiresPermissions("irrigation:project:edit")
    @Log(title = "工程运行-项目管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationProcessProjectEditParam irrigationProcessProject)
    {
        return toAjaxR(irrigationProcessProjectService.updateIrrigationProcessProject(irrigationProcessProject),"修改");
    }

    /**
     * 删除工程运行-项目管理
     */
    @ApiOperation(value = "删除工程运行-项目管理",notes="irrigation:project:remove")
    @RequiresPermissions("irrigation:project:remove")
    @Log(title = "工程运行-项目管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationProcessProjectService.deleteIrrigationProcessProjectByIds(ids),"删除");
    }
}
