package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.irrigation.entity.po.JhRiverBPo;
import com.jhdr.irrigation.entity.po.WeatherDataPo;
import com.jhdr.irrigation.entity.vo.IrrigationBriefingVo;
import com.jhdr.irrigation.entity.vo.IrrigationFacilitiesOverviewVo;
import com.jhdr.irrigation.entity.vo.IrrigationWeatherDayVo;
import com.jhdr.irrigation.service.IJhRiverBService;
import com.jhdr.irrigation.service.impl.HikvisionVideoTool;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.GisDatasDPo;
import com.jhdr.irrigation.entity.param.GisDatasDParam;
import com.jhdr.irrigation.entity.param.GisDatasDAddParam;
import com.jhdr.irrigation.entity.param.GisDatasDEditParam;
import com.jhdr.irrigation.entity.vo.GisDatasDVo;
import com.jhdr.irrigation.service.IGisDatasDService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.LocalDate;
import java.util.Date;
import java.time.ZoneId;

/**
 * GIS数据
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Api(tags = "GIS数据")
@RestController
@RequestMapping("/gis/datas")
public class GisDatasDController extends BaseController
{
    @Autowired
    private IGisDatasDService gisDatasDService;

    @Autowired
    private IJhRiverBService jhRiverBService;

    @Autowired
    private HikvisionVideoTool hikvisionVideoTool;
    /**
     * 查询GIS数据列表
     */
    @ApiOperation(value = "查询GIS数据列表 BZ 泵站 " +
            "HZ 涵闸 " +
            "LL 流量 " +
            "SP 视频 " +
            "SW 水位 " +
            "YL 雨量 " +
            "SN 枢纽 " +
            "QSK 取水口 " +
            "FSW 水旱水位站 " +
            "FHZ 水旱重点工程 " +
            "FQX 水旱气象站 " +
            "FSQ 水旱上桥防汛 " +
            "FJHZ 水旱进洪闸 " +
            "FTHZ 水旱退洪闸",notes="irrigation:gisDatas:list")
    @RequiresPermissions("irrigation:gisDatas:list")
    @GetMapping("/list")
    public R<List<GisDatasDVo>> list(GisDatasDParam gisDatasDParam)
    {

        List<GisDatasDVo> list = gisDatasDService.selectGisDatasDList(gisDatasDParam);
//        if (ObjectUtil.isNotNull(list)) {
//            for (GisDatasDVo datasDVo : list) {
//                if (ObjectUtil.isNotEmpty(datasDVo) && ObjectUtil.isNotEmpty(datasDVo.getSource())) {
//                    switch (datasDVo.getSource()) {
//                        case "10":
//                            datasDVo.setNm(datasDVo.getNm() + "[省水文局]");
//                            break;
//                        case "20":
//                            datasDVo.setNm(datasDVo.getNm() + "[南瑞集团]");
//                            break;
//                        case "30":
//                            datasDVo.setNm(datasDVo.getNm() + "[金海迪尔]");
//                            break;
//                        case "40":
//                            datasDVo.setNm(datasDVo.getNm() + "[省气象局]");
//                            break;
//                    }
//
//                }
//            }
//        }
        return R.ok(list);
    }

    /**
     * 获取天气信息
     */
    @ApiOperation(value = "气象站-获取天气信息 ")
    @GetMapping(value = "/weather/{cd}")
    public R<List<IrrigationWeatherDayVo>> weatherGetData(@PathVariable("cd") String cd)
    {
        List<IrrigationWeatherDayVo> weatherDayVos=  gisDatasDService.weatherGetData(cd);
        return R.ok(weatherDayVos);
    }



    /**
     * 测试
     */
    @ApiOperation(value = "测试")
    @GetMapping("/test")
    public R<String> test()
    {

        hikvisionVideoTool.getNodesByParams();
        return R.ok();
    }


    /**
     * 新增GIS数据
     */
    @ApiOperation(value = "新增GIS数据",notes="irrigation:gisDatas:add")
    @RequiresPermissions("irrigation:gisDatas:add")
    @Log(title = "GIS数据", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody GisDatasDAddParam gisDatasD)
    {
        return toAjaxR(gisDatasDService.insertGisDatasD(gisDatasD),"新增");
    }

    /**
     * 统计灌区基础设施概况
     */
    @ApiOperation(value = "统计灌区基础设施概况", notes = "irrigation:gisDatas:overview")
    @GetMapping("/overview")
    @RequiresPermissions("irrigation:gisDatas:overview")
    public R<IrrigationFacilitiesOverviewVo> getIrrigationFacilitiesOverview() {

        IrrigationFacilitiesOverviewVo facilitiesOverviewVo = new IrrigationFacilitiesOverviewVo();
        //河流数量
        facilitiesOverviewVo.setRiverCount(2L);
        Long trunkCanalCount =  jhRiverBService.count(new QueryWrapper<JhRiverBPo>().eq("rvtp", "干渠"));
        //干渠数量
        facilitiesOverviewVo.setTrunkCanalCount(trunkCanalCount);
        Long branchCanalCount =  jhRiverBService.count(new QueryWrapper<JhRiverBPo>().eq("rvtp", "支渠"));
        //支渠数量
        facilitiesOverviewVo.setBranchCanalCount(branchCanalCount);
        Long hubCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "SN"));
        //枢纽数量
        facilitiesOverviewVo.setHubCount(hubCount);
        //灌区数量
        facilitiesOverviewVo.setIrrigationAreaCount(7L);
        Long pumpStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "BZ"));
        //泵站数量
        facilitiesOverviewVo.setPumpStationCount(pumpStationCount);
        Long sluiceGateCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "HZ"));
        //闸站数量
        facilitiesOverviewVo.setSluiceGateCount(sluiceGateCount);
        Long waterLevelStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "SW"));
        //水位站数量
        facilitiesOverviewVo.setWaterLevelStationCount(waterLevelStationCount);
        Long flowStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "LL"));
        //流量站数量
        facilitiesOverviewVo.setFlowStationCount(flowStationCount);
        Long rainfallStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "YL"));
        //雨量站数量
        facilitiesOverviewVo.setRainfallStationCount(rainfallStationCount);
        Long videoStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "SP"));
        //视频站数量
        facilitiesOverviewVo.setVideoStationCount(videoStationCount);
        Long intakeCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "QSK"));
        //取水口数量
        facilitiesOverviewVo.setIntakeCount(intakeCount);

        // 水旱灾害-行蓄洪区数量
        facilitiesOverviewVo.setFloodControlAreaCount(1);
        // 重点工程数量
        facilitiesOverviewVo.setKeyProjectCount(2);
        // 水位站数量
        Long floodWaterLevelStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "FSW"));
        facilitiesOverviewVo.setFloodWaterLevelStationCount(Math.toIntExact(floodWaterLevelStationCount));
        // 气象站数量
       facilitiesOverviewVo.setWeatherStationCount(7);
        // 上桥枢纽
        facilitiesOverviewVo.setUpperBridgeHub(1);
        // 进洪闸
        facilitiesOverviewVo.setIntakeGate(1);
        // 退洪闸
        facilitiesOverviewVo.setDischargeGate(1);
        return R.ok(facilitiesOverviewVo);
    }

    /**
     * 简报每日八点半
     * 包含当前天气、枢纽水情、枢纽工情、雨量、泵站开启数、闸站开启数、流量站累计值
     * 每日8:30执行一次并入库
     * 8:30前查询昨天数据，8:30后查询当天数据
     */
    @ApiOperation(value = "简报")
    @GetMapping("/briefing")
    @RequiresPermissions("irrigation:gisDatas:briefing")
    public R<IrrigationBriefingVo> getIrrigationBriefing(String reportDate) {

        // 根据时间判断获取今天或昨天的简报
        IrrigationBriefingVo briefingVo;
        briefingVo = gisDatasDService.getIrrigationBriefing(reportDate);
        return R.ok(briefingVo);
    }

}
