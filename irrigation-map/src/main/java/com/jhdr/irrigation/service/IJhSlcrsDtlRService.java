package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.JhSlcrsDtlRPo;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlRParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlRAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlREditParam;
import com.jhdr.irrigation.entity.vo.JhSlcrsDtlRInfoVo;
import com.jhdr.irrigation.entity.vo.JhSlcrsDtlRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 水闸运行明细Service接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface IJhSlcrsDtlRService extends IService<JhSlcrsDtlRPo>
{

    /**
     * 查询水闸运行明细列表
     *
     * @param jhSlcrsDtlR 水闸运行明细
     * @return 水闸运行明细集合
     */
    public List<JhSlcrsDtlRVo> queryList(JhSlcrsDtlRPo jhSlcrsDtlR);

    /**
     * 查询水闸运行明细
     *
     * @param prjnmcd 水闸运行明细主键
     * @return 水闸运行明细
     */
    public JhSlcrsDtlRVo selectJhSlcrsDtlRByPrjnmcd(String prjnmcd);

    /**
     * 查询水闸运行明细列表
     *
     * @param jhSlcrsDtlR 水闸运行明细
     * @return 水闸运行明细集合
     */
    public List<JhSlcrsDtlRVo> selectJhSlcrsDtlRList(JhSlcrsDtlRParam jhSlcrsDtlR);

    /**
     * 新增水闸运行明细
     *
     * @param jhSlcrsDtlR 水闸运行明细
     * @return 结果
     */
    public int insertJhSlcrsDtlR(JhSlcrsDtlRAddParam jhSlcrsDtlR);

    /**
     * 修改水闸运行明细
     *
     * @param jhSlcrsDtlR 水闸运行明细
     * @return 结果
     */
    public int updateJhSlcrsDtlR(JhSlcrsDtlREditParam jhSlcrsDtlR);

    /**
     * 批量删除水闸运行明细
     *
     * @param prjnmcds 需要删除的水闸运行明细主键集合
     * @return 结果
     */
    public int deleteJhSlcrsDtlRByPrjnmcds(String[] prjnmcds);

    /**
     * 删除水闸运行明细信息
     *
     * @param prjnmcd 水闸运行明细主键
     * @return 结果
     */
    public int deleteJhSlcrsDtlRByPrjnmcd(String prjnmcd);

    /**
     * 查询水闸运行明细基本信息
     *
     * @param prjnmcd 水闸运行明细主键
     * @return 水闸运行明细基本信息
     */
    List<JhSlcrsDtlRInfoVo> getSlcrsRInfoVos(String prjnmcd);
}
