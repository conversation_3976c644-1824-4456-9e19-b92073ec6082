package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhSlcrsDtlLogRPo;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlLogRParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlLogRAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlLogREditParam;
import com.jhdr.irrigation.entity.vo.JhSlcrsDtlLogRVo;
import com.jhdr.irrigation.mapper.JhSlcrsDtlLogRMapper;
import com.jhdr.irrigation.service.IJhSlcrsDtlLogRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 水闸运行操作日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class JhSlcrsDtlLogRServiceImpl extends ServiceImpl<JhSlcrsDtlLogRMapper, JhSlcrsDtlLogRPo> implements IJhSlcrsDtlLogRService {

    @Override
    public List<JhSlcrsDtlLogRVo> queryList(JhSlcrsDtlLogRPo jhSlcrsDtlLogR) {
        LambdaQueryWrapper<JhSlcrsDtlLogRPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhSlcrsDtlLogR.getClltm())){
            lqw.eq(JhSlcrsDtlLogRPo::getClltm ,jhSlcrsDtlLogR.getClltm());
        }
        if (StringUtils.isNotBlank(jhSlcrsDtlLogR.getOperator())){
            lqw.eq(JhSlcrsDtlLogRPo::getOperator ,jhSlcrsDtlLogR.getOperator());
        }
        if (StringUtils.isNotBlank(jhSlcrsDtlLogR.getOm())){
            lqw.eq(JhSlcrsDtlLogRPo::getOm ,jhSlcrsDtlLogR.getOm());
        }
        List<JhSlcrsDtlLogRVo> jhSlcrsDtlLogRVos= BeanUtil.copyToList(this.list(lqw), JhSlcrsDtlLogRVo.class);
        return jhSlcrsDtlLogRVos;
    }
    /**
     * 查询水闸运行操作日志
     *
     * @param prjnmcd 水闸运行操作日志主键
     * @return 水闸运行操作日志
     */
    @Override
    public JhSlcrsDtlLogRVo selectJhSlcrsDtlLogRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhSlcrsDtlLogRByPrjnmcd(prjnmcd);
    }

    /**
     * 查询水闸运行操作日志列表
     *
     * @param jhSlcrsDtlLogR 水闸运行操作日志
     * @return 水闸运行操作日志
     */
    @Override
    public List<JhSlcrsDtlLogRVo> selectJhSlcrsDtlLogRList(JhSlcrsDtlLogRParam jhSlcrsDtlLogR)
    {
        return baseMapper.selectJhSlcrsDtlLogRList(jhSlcrsDtlLogR);
    }

    /**
     * 新增水闸运行操作日志
     *
     * @param jhSlcrsDtlLogRAddParam 水闸运行操作日志
     * @return 结果
     */
    @Override
    public int insertJhSlcrsDtlLogR(JhSlcrsDtlLogRAddParam jhSlcrsDtlLogRAddParam)
    {

        JhSlcrsDtlLogRPo jhSlcrsDtlLogR=new JhSlcrsDtlLogRPo();
        BeanUtil.copyProperties(jhSlcrsDtlLogRAddParam,jhSlcrsDtlLogR);
        return baseMapper.insert(jhSlcrsDtlLogR);
    }

    /**
     * 修改水闸运行操作日志
     *
     * @param jhSlcrsDtlLogREditParam 水闸运行操作日志
     * @return 结果
     */
    @Override
    public int updateJhSlcrsDtlLogR(JhSlcrsDtlLogREditParam jhSlcrsDtlLogREditParam)
    {
        JhSlcrsDtlLogRPo jhSlcrsDtlLogR=new JhSlcrsDtlLogRPo();
        BeanUtil.copyProperties(jhSlcrsDtlLogREditParam,jhSlcrsDtlLogR);
        return baseMapper.updateById(jhSlcrsDtlLogR);
    }

    /**
     * 批量删除水闸运行操作日志
     *
     * @param prjnmcds 需要删除的水闸运行操作日志主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsDtlLogRByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhSlcrsDtlLogRByPrjnmcds(prjnmcds);
    }

    /**
     * 删除水闸运行操作日志信息
     *
     * @param prjnmcd 水闸运行操作日志主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsDtlLogRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhSlcrsDtlLogRByPrjnmcd(prjnmcd);
    }
}
