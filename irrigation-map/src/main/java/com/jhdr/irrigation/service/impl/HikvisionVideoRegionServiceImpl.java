package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.HikvisionVideoRegionPo;
import com.jhdr.irrigation.entity.param.HikvisionVideoRegionParam;
import com.jhdr.irrigation.entity.param.HikvisionVideoRegionAddParam;
import com.jhdr.irrigation.entity.param.HikvisionVideoRegionEditParam;
import com.jhdr.irrigation.entity.vo.HikvisionVideoRegionVo;
import com.jhdr.irrigation.mapper.HikvisionVideoRegionMapper;
import com.jhdr.irrigation.service.IHikvisionVideoRegionService;

import java.util.ArrayList;

import java.util.List;

/**
 * 海康视频区域层级Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
@Service
public class HikvisionVideoRegionServiceImpl extends ServiceImpl<HikvisionVideoRegionMapper, HikvisionVideoRegionPo> implements IHikvisionVideoRegionService {

    @Override
    public List<HikvisionVideoRegionVo> queryList(HikvisionVideoRegionPo hikvisionVideoRegion) {
        LambdaQueryWrapper<HikvisionVideoRegionPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getRegionPath())){
            lqw.eq(HikvisionVideoRegionPo::getRegionPath ,hikvisionVideoRegion.getRegionPath());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getCascadeType())){
            lqw.eq(HikvisionVideoRegionPo::getCascadeType ,hikvisionVideoRegion.getCascadeType());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getCatalogType())){
            lqw.eq(HikvisionVideoRegionPo::getCatalogType ,hikvisionVideoRegion.getCatalogType());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getCascadeCode())){
            lqw.eq(HikvisionVideoRegionPo::getCascadeCode ,hikvisionVideoRegion.getCascadeCode());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getAvailable())){
            lqw.eq(HikvisionVideoRegionPo::getAvailable ,hikvisionVideoRegion.getAvailable());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getIndexCode())){
            lqw.eq(HikvisionVideoRegionPo::getIndexCode ,hikvisionVideoRegion.getIndexCode());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getSort())){
            lqw.eq(HikvisionVideoRegionPo::getSort ,hikvisionVideoRegion.getSort());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getLeaf())){
            lqw.eq(HikvisionVideoRegionPo::getLeaf ,hikvisionVideoRegion.getLeaf());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getRegionCode())){
            lqw.eq(HikvisionVideoRegionPo::getRegionCode ,hikvisionVideoRegion.getRegionCode());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getName())){
            lqw.like(HikvisionVideoRegionPo::getName ,hikvisionVideoRegion.getName());
        }
        if (StringUtils.isNotBlank(hikvisionVideoRegion.getParentIndexCode())){
            lqw.eq(HikvisionVideoRegionPo::getParentIndexCode ,hikvisionVideoRegion.getParentIndexCode());
        }
        List<HikvisionVideoRegionVo> hikvisionVideoRegionVos= BeanUtil.copyToList(this.list(lqw), HikvisionVideoRegionVo.class);
        return hikvisionVideoRegionVos;
    }
    /**
     * 查询海康视频区域层级
     *
     * @param id 海康视频区域层级主键
     * @return 海康视频区域层级
     */
    @Override
    public HikvisionVideoRegionVo selectHikvisionVideoRegionById(Long id)
    {
        return baseMapper.selectHikvisionVideoRegionById(id);
    }

    /**
     * 查询海康视频区域层级列表
     *
     * @param hikvisionVideoRegion 海康视频区域层级
     * @return 海康视频区域层级
     */
    @Override
    public List<HikvisionVideoRegionVo> selectHikvisionVideoRegionList(HikvisionVideoRegionParam hikvisionVideoRegion)
    {
        return baseMapper.selectHikvisionVideoRegionList(hikvisionVideoRegion);
    }

    /**
     * 新增海康视频区域层级
     *
     * @param hikvisionVideoRegionAddParam 海康视频区域层级
     * @return 结果
     */
    @Override
    public int insertHikvisionVideoRegion(HikvisionVideoRegionAddParam hikvisionVideoRegionAddParam)
    {

        HikvisionVideoRegionPo hikvisionVideoRegion=new HikvisionVideoRegionPo();
        BeanUtil.copyProperties(hikvisionVideoRegionAddParam,hikvisionVideoRegion);
        return baseMapper.insert(hikvisionVideoRegion);
    }

    /**
     * 修改海康视频区域层级
     *
     * @param hikvisionVideoRegionEditParam 海康视频区域层级
     * @return 结果
     */
    @Override
    public int updateHikvisionVideoRegion(HikvisionVideoRegionEditParam hikvisionVideoRegionEditParam)
    {

        HikvisionVideoRegionPo hikvisionVideoRegion=new HikvisionVideoRegionPo();
        BeanUtil.copyProperties(hikvisionVideoRegionEditParam,hikvisionVideoRegion);
        return baseMapper.updateById(hikvisionVideoRegion);
    }

    /**
     * 批量删除海康视频区域层级
     *
     * @param ids 需要删除的海康视频区域层级主键
     * @return 结果
     */
    @Override
    public int deleteHikvisionVideoRegionByIds(Long[] ids)
    {
        return baseMapper.deleteHikvisionVideoRegionByIds(ids);
    }

    /**
     * 删除海康视频区域层级信息
     *
     * @param id 海康视频区域层级主键
     * @return 结果
     */
    @Override
    public int deleteHikvisionVideoRegionById(Long id)
    {
        return baseMapper.deleteHikvisionVideoRegionById(id);
    }
}
