package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhPumpDtlRPo;
import com.jhdr.irrigation.entity.param.JhPumpDtlRParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlRAddParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlREditParam;
import com.jhdr.irrigation.entity.vo.JhPumpDtlRVo;
import com.jhdr.irrigation.mapper.JhPumpDtlRMapper;
import com.jhdr.irrigation.service.IJhPumpDtlRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 泵站运行明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class JhPumpDtlRServiceImpl extends ServiceImpl<JhPumpDtlRMapper, JhPumpDtlRPo> implements IJhPumpDtlRService {

    @Override
    public List<JhPumpDtlRVo> queryList(JhPumpDtlRPo jhPumpDtlR) {
        LambdaQueryWrapper<JhPumpDtlRPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhPumpDtlR.getClltm())){
            lqw.eq(JhPumpDtlRPo::getClltm ,jhPumpDtlR.getClltm());
        }
        if (jhPumpDtlR.getPmpcd() != null){
            lqw.eq(JhPumpDtlRPo::getPmpcd ,jhPumpDtlR.getPmpcd());
        }
        if (jhPumpDtlR.getOm() != null){
            lqw.eq(JhPumpDtlRPo::getOm ,jhPumpDtlR.getOm());
        }
        if (jhPumpDtlR.getUab() != null){
            lqw.eq(JhPumpDtlRPo::getUab ,jhPumpDtlR.getUab());
        }
        if (jhPumpDtlR.getUbc() != null){
            lqw.eq(JhPumpDtlRPo::getUbc ,jhPumpDtlR.getUbc());
        }
        if (jhPumpDtlR.getUca() != null){
            lqw.eq(JhPumpDtlRPo::getUca ,jhPumpDtlR.getUca());
        }
        if (jhPumpDtlR.getIa() != null){
            lqw.eq(JhPumpDtlRPo::getIa ,jhPumpDtlR.getIa());
        }
        if (jhPumpDtlR.getIb() != null){
            lqw.eq(JhPumpDtlRPo::getIb ,jhPumpDtlR.getIb());
        }
        if (jhPumpDtlR.getIc() != null){
            lqw.eq(JhPumpDtlRPo::getIc ,jhPumpDtlR.getIc());
        }
        if (jhPumpDtlR.getApwr() != null){
            lqw.eq(JhPumpDtlRPo::getApwr ,jhPumpDtlR.getApwr());
        }
        if (jhPumpDtlR.getRpwr() != null){
            lqw.eq(JhPumpDtlRPo::getRpwr ,jhPumpDtlR.getRpwr());
        }
        if (jhPumpDtlR.getCos() != null){
            lqw.eq(JhPumpDtlRPo::getCos ,jhPumpDtlR.getCos());
        }
        if (jhPumpDtlR.getEe() != null){
            lqw.eq(JhPumpDtlRPo::getEe ,jhPumpDtlR.getEe());
        }
        if (jhPumpDtlR.getQ() != null){
            lqw.eq(JhPumpDtlRPo::getQ ,jhPumpDtlR.getQ());
        }
        List<JhPumpDtlRVo> jhPumpDtlRVos= BeanUtil.copyToList(this.list(lqw), JhPumpDtlRVo.class);
        return jhPumpDtlRVos;
    }
    /**
     * 查询泵站运行明细
     *
     * @param prjnmcd 泵站运行明细主键
     * @return 泵站运行明细
     */
    @Override
    public JhPumpDtlRVo selectJhPumpDtlRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhPumpDtlRByPrjnmcd(prjnmcd);
    }

    /**
     * 查询泵站运行明细列表
     *
     * @param jhPumpDtlR 泵站运行明细
     * @return 泵站运行明细
     */
    @Override
    public List<JhPumpDtlRVo> selectJhPumpDtlRList(JhPumpDtlRParam jhPumpDtlR)
    {
        return baseMapper.selectJhPumpDtlRList(jhPumpDtlR);
    }

    /**
     * 新增泵站运行明细
     *
     * @param jhPumpDtlRAddParam 泵站运行明细
     * @return 结果
     */
    @Override
    public int insertJhPumpDtlR(JhPumpDtlRAddParam jhPumpDtlRAddParam)
    {

        JhPumpDtlRPo jhPumpDtlR=new JhPumpDtlRPo();
        BeanUtil.copyProperties(jhPumpDtlRAddParam,jhPumpDtlR);
        return baseMapper.insert(jhPumpDtlR);
    }

    /**
     * 修改泵站运行明细
     *
     * @param jhPumpDtlREditParam 泵站运行明细
     * @return 结果
     */
    @Override
    public int updateJhPumpDtlR(JhPumpDtlREditParam jhPumpDtlREditParam)
    {
        JhPumpDtlRPo jhPumpDtlR=new JhPumpDtlRPo();
        BeanUtil.copyProperties(jhPumpDtlREditParam,jhPumpDtlR);
        return baseMapper.updateById(jhPumpDtlR);
    }

    /**
     * 批量删除泵站运行明细
     *
     * @param prjnmcds 需要删除的泵站运行明细主键
     * @return 结果
     */
    @Override
    public int deleteJhPumpDtlRByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhPumpDtlRByPrjnmcds(prjnmcds);
    }

    /**
     * 删除泵站运行明细信息
     *
     * @param prjnmcd 泵站运行明细主键
     * @return 结果
     */
    @Override
    public int deleteJhPumpDtlRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhPumpDtlRByPrjnmcd(prjnmcd);
    }

    @Override
    public List<JhPumpDtlRInfoVo> getPumpDtlRInfoVos(String prjnmcd) {

        return baseMapper.getPumpDtlRInfoVos(prjnmcd);
    }
}
