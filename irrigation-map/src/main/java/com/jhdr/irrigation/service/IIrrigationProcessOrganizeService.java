package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationProcessOrganizePo;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessOrganizeEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessOrganizeVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 工程运行-组织架构Service接口
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
public interface IIrrigationProcessOrganizeService extends IService<IrrigationProcessOrganizePo>
{

    /**
     * 查询工程运行-组织架构列表
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 工程运行-组织架构集合
     */
    public List<IrrigationProcessOrganizeVo> queryList(IrrigationProcessOrganizePo irrigationProcessOrganize);

    /**
     * 查询工程运行-组织架构
     *
     * @param id 工程运行-组织架构主键
     * @return 工程运行-组织架构
     */
    public IrrigationProcessOrganizeVo selectIrrigationProcessOrganizeById(Long id);

    /**
     * 查询工程运行-组织架构列表
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 工程运行-组织架构集合
     */
    public List<IrrigationProcessOrganizeVo> selectIrrigationProcessOrganizeList(IrrigationProcessOrganizeParam irrigationProcessOrganize);

    /**
     * 新增工程运行-组织架构
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 结果
     */
    public int insertIrrigationProcessOrganize(IrrigationProcessOrganizeAddParam irrigationProcessOrganize);

    /**
     * 修改工程运行-组织架构
     *
     * @param irrigationProcessOrganize 工程运行-组织架构
     * @return 结果
     */
    public int updateIrrigationProcessOrganize(IrrigationProcessOrganizeEditParam irrigationProcessOrganize);

    /**
     * 批量删除工程运行-组织架构
     *
     * @param ids 需要删除的工程运行-组织架构主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessOrganizeByIds(Long[] ids);

    /**
     * 删除工程运行-组织架构信息
     *
     * @param id 工程运行-组织架构主键
     * @return 结果
     */
    public int deleteIrrigationProcessOrganizeById(Long id);

}
