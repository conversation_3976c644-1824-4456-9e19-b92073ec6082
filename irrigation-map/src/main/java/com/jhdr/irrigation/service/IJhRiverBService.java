package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.JhRiverBPo;
import com.jhdr.irrigation.entity.param.JhRiverBParam;
import com.jhdr.irrigation.entity.param.JhRiverBAddParam;
import com.jhdr.irrigation.entity.param.JhRiverBEditParam;
import com.jhdr.irrigation.entity.vo.JhRiverBVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.irrigation.entity.vo.TrunkCanalVo;

/**
 * 河流信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-19
 */
public interface IJhRiverBService extends IService<JhRiverBPo>
{

    /**
     * 查询河流信息列表
     *
     * @param jhRiverB 河流信息
     * @return 河流信息集合
     */
    public List<JhRiverBVo> queryList(JhRiverBPo jhRiverB);

    /**
     * 查询河流信息
     *
     * @param ennmcd 河流信息主键
     * @return 河流信息
     */
    public JhRiverBVo selectJhRiverBByEnnmcd(String ennmcd);

    /**
     * 查询河流信息列表
     *
     * @param jhRiverB 河流信息
     * @return 河流信息集合
     */
    public List<JhRiverBVo> selectJhRiverBList(JhRiverBParam jhRiverB);

    /**
     * 新增河流信息
     *
     * @param jhRiverB 河流信息
     * @return 结果
     */
    public int insertJhRiverB(JhRiverBAddParam jhRiverB);

    /**
     * 修改河流信息
     *
     * @param jhRiverB 河流信息
     * @return 结果
     */
    public int updateJhRiverB(JhRiverBEditParam jhRiverB);

    /**
     * 批量删除河流信息
     *
     * @param ennmcds 需要删除的河流信息主键集合
     * @return 结果
     */
    public int deleteJhRiverBByEnnmcds(String[] ennmcds);

    /**
     * 删除河流信息信息
     *
     * @param ennmcd 河流信息主键
     * @return 结果
     */
    public int deleteJhRiverBByEnnmcd(String ennmcd);

    /**
     * 统计河流信息汇总 type  2干渠 3支渠
     * @param type
     * @return
     */
    TrunkCanalVo  sumLengthByRvtp(String type,String region);
}
