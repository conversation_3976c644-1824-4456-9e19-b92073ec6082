package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.IrrigationProcessFilePo;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessFileVo;
import com.jhdr.irrigation.mapper.IrrigationProcessFileMapper;
import com.jhdr.irrigation.service.IIrrigationProcessFileService;

import java.util.ArrayList;

import java.util.List;

/**
 * 工程运行-文件管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class IrrigationProcessFileServiceImpl extends ServiceImpl<IrrigationProcessFileMapper, IrrigationProcessFilePo> implements IIrrigationProcessFileService {


    /**
     * 查询工程运行-文件管理
     *
     * @param id 工程运行-文件管理主键
     * @return 工程运行-文件管理
     */
    @Override
    public IrrigationProcessFileVo selectIrrigationProcessFileById(Long id)
    {
        return baseMapper.selectIrrigationProcessFileById(id);
    }

    /**
     * 查询工程运行-文件管理列表
     *
     * @param irrigationProcessFile 工程运行-文件管理
     * @return 工程运行-文件管理
     */
    @Override
    public List<IrrigationProcessFileVo> selectIrrigationProcessFileList(IrrigationProcessFileParam irrigationProcessFile)
    {

        return baseMapper.selectIrrigationProcessFileList(irrigationProcessFile);
    }

    /**
     * 新增工程运行-文件管理
     *
     * @param irrigationProcessFileAddParam 工程运行-文件管理
     * @return 结果
     */
    @Override
    public int insertIrrigationProcessFile(IrrigationProcessFileAddParam irrigationProcessFileAddParam)
    {


        IrrigationProcessFilePo irrigationProcessFile=new IrrigationProcessFilePo();
        BeanUtil.copyProperties(irrigationProcessFileAddParam,irrigationProcessFile);
        irrigationProcessFile.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(irrigationProcessFile);
    }

    /**
     * 修改工程运行-文件管理
     *
     * @param irrigationProcessFileEditParam 工程运行-文件管理
     * @return 结果
     */
    @Override
    public int updateIrrigationProcessFile(IrrigationProcessFileEditParam irrigationProcessFileEditParam)
    {
        IrrigationProcessFilePo irrigationProcessFile=new IrrigationProcessFilePo();
        BeanUtil.copyProperties(irrigationProcessFileEditParam,irrigationProcessFile);
        return baseMapper.updateById(irrigationProcessFile);
    }

    /**
     * 批量删除工程运行-文件管理
     *
     * @param ids 需要删除的工程运行-文件管理主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationProcessFileByIds(Long[] ids)
    {
        return baseMapper.deleteIrrigationProcessFileByIds(ids);
    }

    /**
     * 删除工程运行-文件管理信息
     *
     * @param id 工程运行-文件管理主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationProcessFileById(Long id)
    {
        return baseMapper.deleteIrrigationProcessFileById(id);
    }
}
