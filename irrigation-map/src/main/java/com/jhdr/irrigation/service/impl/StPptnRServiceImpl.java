package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jhdr.irrigation.entity.vo.RainfallVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.StPptnRPo;
import com.jhdr.irrigation.entity.param.StPptnRParam;
import com.jhdr.irrigation.entity.param.StPptnRAddParam;
import com.jhdr.irrigation.entity.param.StPptnREditParam;
import com.jhdr.irrigation.entity.vo.StPptnRVo;
import com.jhdr.irrigation.mapper.StPptnRMapper;
import com.jhdr.irrigation.service.IStPptnRService;

import javax.xml.crypto.Data;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 降水量Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service
public class StPptnRServiceImpl extends ServiceImpl<StPptnRMapper, StPptnRPo> implements IStPptnRService {

    @Override
    public List<StPptnRVo> queryList(StPptnRPo stPptnR) {
        LambdaQueryWrapper<StPptnRPo> lqw = Wrappers.lambdaQuery();
        if (stPptnR.getDRP() != null){
            lqw.eq(StPptnRPo::getDRP ,stPptnR.getDRP());
        }
        if (stPptnR.getINTV() != null){
            lqw.eq(StPptnRPo::getINTV ,stPptnR.getINTV());
        }
        if (stPptnR.getPDR() != null){
            lqw.eq(StPptnRPo::getPDR ,stPptnR.getPDR());
        }
        if (stPptnR.getDYP() != null){
            lqw.eq(StPptnRPo::getDYP ,stPptnR.getDYP());
        }
        if (StringUtils.isNotBlank(stPptnR.getWTH())){
            lqw.eq(StPptnRPo::getWTH ,stPptnR.getWTH());
        }
        List<StPptnRVo> stPptnRVos= BeanUtil.copyToList(this.list(lqw), StPptnRVo.class);
        return stPptnRVos;
    }
    /**
     * 查询降水量
     *
     * @param STCD 降水量主键
     * @return 降水量
     */
    @Override
    public StPptnRVo selectStPptnRBySTCD(String STCD)
    {
        return baseMapper.selectStPptnRBySTCD(STCD);
    }

    /**
     * 查询降水量列表
     *
     * @param stPptnR 降水量
     * @return 降水量
     */
    @Override
    public List<StPptnRVo> selectStPptnRList(StPptnRParam stPptnR)
    {

        List<StPptnRVo> rainfallList  = new ArrayList<>();
        List<StPptnRVo> rainfallLists = new ArrayList<>();
        BigDecimal cumulativeDrp = BigDecimal.ZERO; // 初始化累计时段降水量为0
        Map<LocalDateTime, BigDecimal> rainfallData = new TreeMap<>();
        //日雨量查询
        if (ObjectUtil.isNotEmpty(stPptnR)&&
                (ObjectUtil.isEmpty(stPptnR.getType())||stPptnR.getType()==1)){
            String startDateStr = stPptnR.getTMStart();
            String endDateStr = stPptnR.getTMEnd();
            if (ObjectUtil.isNotEmpty(stPptnR.getTMStart())&&ObjectUtil.isNotEmpty(stPptnR.getTMEnd())){

                startDateStr = updateQueryData(startDateStr);
                endDateStr = updateQueryData(endDateStr);
                stPptnR.setTMStart(startDateStr);
                stPptnR.setTMEnd(endDateStr);

            }else {
                throw new RuntimeException("请输入查询时间");
            }
            rainfallList=  baseMapper.selectStPptnRList(stPptnR);
            for (StPptnRVo rainfall : rainfallList) {
                rainfallData.put(rainfall.getTm(), rainfall.getDrp());
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime startDate = LocalDateTime.parse(startDateStr, formatter);
            LocalDateTime endDate = LocalDateTime.parse(endDateStr, formatter);

            while (!startDate.isAfter(endDate)) {
                rainfallData.putIfAbsent(startDate, new BigDecimal(0.0));
                startDate = startDate.plusDays(1);
            }
            //汇总处理
            for (Map.Entry<LocalDateTime, BigDecimal> entry : rainfallData.entrySet()) {
                StPptnRVo rainfall = new StPptnRVo();
                rainfall.setTm(entry.getKey().minusDays(1));
                rainfall.setStcd(stPptnR.getSTCD());
                rainfall.setDrp(entry.getValue());
                rainfallLists.add(rainfall);
            }

        }else  //小时雨量查询
            if (ObjectUtil.isNotEmpty(stPptnR)&&stPptnR.getType()==2){
                String startDateStr = stPptnR.getTMStart();
                String endDateStr = stPptnR.getTMEnd();
                if (ObjectUtil.isNotEmpty(stPptnR.getTMStart())&&ObjectUtil.isNotEmpty(stPptnR.getTMEnd())){

                    // 截取日期部分
                    String startPart = startDateStr.substring(0, 14);
                    // 拼接午夜的时间部分
                    String startTime = "00:00";
                    // 将日期部分和午夜时间部分拼接起来
                    startDateStr = startPart + startTime;

                    // 截取日期部分
                    String endPart = endDateStr.substring(0, 14);
                    // 拼接午夜的时间部分
                    String endTime = "00:00";
                    // 将日期部分和午夜时间部分拼接起来
                    endDateStr = endPart + endTime;
                }else {
                    throw new RuntimeException("请输入查询时间");
                }
                rainfallList=  baseMapper.selectStPptnRList(stPptnR);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startDate = LocalDateTime.parse(startDateStr, formatter);
            LocalDateTime endDate = LocalDateTime.parse(endDateStr, formatter);
            Map<LocalDateTime, BigDecimal> hourlyRainfallMap = new TreeMap<>();
            for (StPptnRVo hr : rainfallList) {
                hourlyRainfallMap.put(hr.getTm(), hr.getDrp());
            }
            for (LocalDateTime dt = startDate; !dt.isAfter(endDate); dt = dt.plusHours(1)) {
                BigDecimal amount = hourlyRainfallMap.getOrDefault(dt, new BigDecimal(0.0));
                StPptnRVo pptnRVo = new StPptnRVo();
                pptnRVo.setTm(dt);
                pptnRVo.setStcd(stPptnR.getSTCD());
                pptnRVo.setDrp(amount);
                rainfallLists.add(pptnRVo);
            }
        }
         //统一处理累计雨量
        for (StPptnRVo record : rainfallLists) {
            if (ObjectUtil.isNotEmpty(record.getDrp())){
                // 累加前n项的降水量到累计降水量
                cumulativeDrp = cumulativeDrp.add(record.getDrp());
                // 将累计值设置到当前对象的totalDrp字段
                record.setTotalDrp(cumulativeDrp);
            }
        }
       return rainfallLists;
    }


    private String updateQueryData(String dateStr) {

        // 截取日期部分
        String part = dateStr.substring(0, 14);
        // 拼接午夜的时间部分
        String time = "00:00";
        // 将日期部分和午夜时间部分拼接起来
        dateStr = part + time;

        // 创建一个日期时间格式器，用于解析输入的日期时间字符串
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析输入的日期时间字符串
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, inputFormatter);
        LocalDateTime addDateTime = dateTime.plusDays(1);
        // 清除时间部分，保留日期，设置时间为午夜
        LocalDateTime midnight = addDateTime.withHour(0).withMinute(0).withSecond(0);

        // 创建一个新的日期时间格式器，用于输出午夜的日期时间字符串
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将午夜的日期时间格式化为字符串
        String midnightStr = midnight.format(outputFormatter);

         // 输出：2024-05-17 00:00:00
        return midnightStr;
    }

    /**
     * 新增降水量
     *
     * @param stPptnRAddParam 降水量
     * @return 结果
     */
    @Override
    public int insertStPptnR(StPptnRAddParam stPptnRAddParam)
    {

        StPptnRPo stPptnR=new StPptnRPo();
        BeanUtil.copyProperties(stPptnRAddParam,stPptnR);
        return baseMapper.insert(stPptnR);
    }

    /**
     * 修改降水量
     *
     * @param stPptnREditParam 降水量
     * @return 结果
     */
    @Override
    public int updateStPptnR(StPptnREditParam stPptnREditParam)
    {
        StPptnRPo stPptnR=new StPptnRPo();
        BeanUtil.copyProperties(stPptnREditParam,stPptnR);
        return baseMapper.updateById(stPptnR);
    }

    /**
     * 批量删除降水量
     *
     * @param STCDs 需要删除的降水量主键
     * @return 结果
     */
    @Override
    public int deleteStPptnRBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteStPptnRBySTCDs(STCDs);
    }

    /**
     * 删除降水量信息
     *
     * @param STCD 降水量主键
     * @return 结果
     */
    @Override
    public int deleteStPptnRBySTCD(String STCD)
    {
        return baseMapper.deleteStPptnRBySTCD(STCD);
    }
}
