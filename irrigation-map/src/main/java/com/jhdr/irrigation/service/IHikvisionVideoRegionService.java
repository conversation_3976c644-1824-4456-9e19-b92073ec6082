package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.HikvisionVideoRegionPo;
import com.jhdr.irrigation.entity.param.HikvisionVideoRegionParam;
import com.jhdr.irrigation.entity.param.HikvisionVideoRegionAddParam;
import com.jhdr.irrigation.entity.param.HikvisionVideoRegionEditParam;
import com.jhdr.irrigation.entity.vo.HikvisionVideoRegionVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 海康视频区域层级Service接口
 *
 * <AUTHOR>
 * @date 2024-07-04
 */
public interface IHikvisionVideoRegionService extends IService<HikvisionVideoRegionPo>
{

    /**
     * 查询海康视频区域层级列表
     *
     * @param hikvisionVideoRegion 海康视频区域层级
     * @return 海康视频区域层级集合
     */
    public List<HikvisionVideoRegionVo> queryList(HikvisionVideoRegionPo hikvisionVideoRegion);

    /**
     * 查询海康视频区域层级
     *
     * @param id 海康视频区域层级主键
     * @return 海康视频区域层级
     */
    public HikvisionVideoRegionVo selectHikvisionVideoRegionById(Long id);

    /**
     * 查询海康视频区域层级列表
     *
     * @param hikvisionVideoRegion 海康视频区域层级
     * @return 海康视频区域层级集合
     */
    public List<HikvisionVideoRegionVo> selectHikvisionVideoRegionList(HikvisionVideoRegionParam hikvisionVideoRegion);

    /**
     * 新增海康视频区域层级
     *
     * @param hikvisionVideoRegion 海康视频区域层级
     * @return 结果
     */
    public int insertHikvisionVideoRegion(HikvisionVideoRegionAddParam hikvisionVideoRegion);

    /**
     * 修改海康视频区域层级
     *
     * @param hikvisionVideoRegion 海康视频区域层级
     * @return 结果
     */
    public int updateHikvisionVideoRegion(HikvisionVideoRegionEditParam hikvisionVideoRegion);

    /**
     * 批量删除海康视频区域层级
     *
     * @param ids 需要删除的海康视频区域层级主键集合
     * @return 结果
     */
    public int deleteHikvisionVideoRegionByIds(Long[] ids);

    /**
     * 删除海康视频区域层级信息
     *
     * @param id 海康视频区域层级主键
     * @return 结果
     */
    public int deleteHikvisionVideoRegionById(Long id);

}
