package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.vo.*;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.HubBasicInformationPo;
import com.jhdr.irrigation.entity.param.HubBasicInformationParam;
import com.jhdr.irrigation.entity.param.HubBasicInformationAddParam;
import com.jhdr.irrigation.entity.param.HubBasicInformationEditParam;
import com.jhdr.irrigation.mapper.HubBasicInformationMapper;
import com.jhdr.irrigation.service.IHubBasicInformationService;

import java.util.ArrayList;

import java.util.List;

/**
 * 枢纽基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
public class HubBasicInformationServiceImpl extends ServiceImpl<HubBasicInformationMapper, HubBasicInformationPo> implements IHubBasicInformationService {


    /**
     * 查询枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 枢纽基础信息
     */
    @Override
    public HubBasicInformationVo selectHubBasicInformationById(Long id)
    {
        return baseMapper.selectHubBasicInformationById(id);
    }

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息
     */
    @Override
    public List<HubBasicInformationVo> selectHubBasicInformationList(HubBasicInformationParam hubBasicInformation)
    {
        return baseMapper.selectHubBasicInformationList(hubBasicInformation);
    }

    /**
     * 新增枢纽基础信息
     *
     * @param hubBasicInformationAddParam 枢纽基础信息
     * @return 结果
     */
    @Override
    public int insertHubBasicInformation(HubBasicInformationAddParam hubBasicInformationAddParam)
    {

        HubBasicInformationPo hubBasicInformation=new HubBasicInformationPo();
        BeanUtil.copyProperties(hubBasicInformationAddParam,hubBasicInformation);
        return baseMapper.insert(hubBasicInformation);
    }

    /**
     * 修改枢纽基础信息
     *
     * @param hubBasicInformationEditParam 枢纽基础信息
     * @return 结果
     */
    @Override
    public int updateHubBasicInformation(HubBasicInformationEditParam hubBasicInformationEditParam)
    {
        HubBasicInformationPo hubBasicInformation=new HubBasicInformationPo();
        BeanUtil.copyProperties(hubBasicInformationEditParam,hubBasicInformation);
        return baseMapper.updateById(hubBasicInformation);
    }

    /**
     * 批量删除枢纽基础信息
     *
     * @param ids 需要删除的枢纽基础信息主键
     * @return 结果
     */
    @Override
    public int deleteHubBasicInformationByIds(Long[] ids)
    {
        return baseMapper.deleteHubBasicInformationByIds(ids);
    }

    /**
     * 删除枢纽基础信息信息
     *
     * @param id 枢纽基础信息主键
     * @return 结果
     */
    @Override
    public int deleteHubBasicInformationById(Long id)
    {
        return baseMapper.deleteHubBasicInformationById(id);
    }

    /**
     * 获取所有枢纽信息
     * @return
     */
    @Override
    public List<EngineerHubInfoVo> engineerHubList() {
        return baseMapper.engineerHubList();
    }

    @Override
    public PreventionProjectVo getProjectInfo(String hubCd) {
        return baseMapper.getProjectInfo(hubCd);
    }

    @Override
    public List<PreventionRainVo> getRainInfos( List<String> hubCodes ) {
        return baseMapper.getRainInfos(hubCodes);
    }

    @Override
    public List<PreventionWaterVo> getWaterInfos(List<String> hubCodes) {
        return baseMapper.getWaterInfos(hubCodes);
    }
}
