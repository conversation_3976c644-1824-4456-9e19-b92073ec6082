package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.po.StStbprpBPo;
import com.jhdr.irrigation.entity.vo.StRiverRegionDataVo;
import com.jhdr.irrigation.entity.vo.VideoRegionDataVo;
import com.jhdr.irrigation.mapper.StStbprpBMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.StRiverRPo;
import com.jhdr.irrigation.entity.param.StRiverRParam;
import com.jhdr.irrigation.entity.param.StRiverRAddParam;
import com.jhdr.irrigation.entity.param.StRiverREditParam;
import com.jhdr.irrigation.entity.vo.StRiverRVo;
import com.jhdr.irrigation.mapper.StRiverRMapper;
import com.jhdr.irrigation.service.IStRiverRService;

import javax.annotation.Resource;
import java.util.ArrayList;

import java.util.Collections;
import java.util.List;

/**
 * 河道水情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service
public class StRiverRServiceImpl extends ServiceImpl<StRiverRMapper, StRiverRPo> implements IStRiverRService {

    /**
     * 查询河道水情
     *
     * @param STCD 河道水情主键
     * @return 河道水情
     */
    @Override
    public StRiverRVo selectStRiverRBySTCD(String STCD)
    {
        return baseMapper.selectStRiverRBySTCD(STCD);
    }

    /**
     * 查询河道水情列表
     *
     * @param stRiverR 河道水情
     * @return 河道水情
     */
    @Override
    public List<StRiverRVo> selectStRiverRList(StRiverRParam stRiverR)
    {
        //状态 1 st_river_r 状态2 st_was_r
        int isRiver= baseMapper.getIsRiver(stRiverR.getSTCD());
        stRiverR.setStatus(isRiver);
        return baseMapper.selectStRiverRList(stRiverR);
    }

    /**
     * 新增河道水情
     *
     * @param stRiverRAddParam 河道水情
     * @return 结果
     */
    @Override
    public int insertStRiverR(StRiverRAddParam stRiverRAddParam)
    {

        StRiverRPo stRiverR=new StRiverRPo();
        BeanUtil.copyProperties(stRiverRAddParam,stRiverR);
        return baseMapper.insert(stRiverR);
    }

    /**
     * 修改河道水情
     *
     * @param stRiverREditParam 河道水情
     * @return 结果
     */
    @Override
    public int updateStRiverR(StRiverREditParam stRiverREditParam)
    {
        StRiverRPo stRiverR=new StRiverRPo();
        BeanUtil.copyProperties(stRiverREditParam,stRiverR);
        return baseMapper.updateById(stRiverR);
    }

    /**
     * 批量删除河道水情
     *
     * @param STCDs 需要删除的河道水情主键
     * @return 结果
     */
    @Override
    public int deleteStRiverRBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteStRiverRBySTCDs(STCDs);
    }

    /**
     * 删除河道水情信息
     *
     * @param STCD 河道水情主键
     * @return 结果
     */
    @Override
    public int deleteStRiverRBySTCD(String STCD)
    {
        return baseMapper.deleteStRiverRBySTCD(STCD);
    }

    @Override
    public Integer waterOpenNum() {
        return baseMapper.waterOpenNum();
    }

    @Override
    public List<StRiverRVo> selectStRiverRListEight(StRiverRParam stRiverR) {
        //状态 1 st_river_r 状态2 st_was_r
        int isRiver= baseMapper.getIsRiver(stRiverR.getSTCD());
        stRiverR.setStatus(isRiver);
        return baseMapper.selectStRiverRListEight(stRiverR);
    }


}
