package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileParam;
import com.jhdr.irrigation.entity.po.IrrigationProcessFilePo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessProjectInfoVo;
import com.jhdr.irrigation.service.IIrrigationProcessFileService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.IrrigationProcessProjectPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessProjectVo;
import com.jhdr.irrigation.mapper.IrrigationProcessProjectMapper;
import com.jhdr.irrigation.service.IIrrigationProcessProjectService;

import javax.annotation.Resource;
import java.util.ArrayList;

import java.util.List;

/**
 * 工程运行-项目管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class IrrigationProcessProjectServiceImpl extends ServiceImpl<IrrigationProcessProjectMapper, IrrigationProcessProjectPo> implements IIrrigationProcessProjectService {


    @Resource
    private IIrrigationProcessFileService processFileService;

    /**
     * 查询工程运行-项目管理
     *
     * @param id 工程运行-项目管理主键
     * @return 工程运行-项目管理
     */
    @Override
    public IrrigationProcessProjectInfoVo selectIrrigationProcessProjectById(Long id)
    {
        IrrigationProcessProjectVo irrigationProcessProjectVo = baseMapper.selectIrrigationProcessProjectById(id);
        IrrigationProcessProjectInfoVo irrigationProcessProjectInfoVo = BeanUtil.copyProperties(irrigationProcessProjectVo, IrrigationProcessProjectInfoVo.class);

        irrigationProcessProjectInfoVo.setApprovalFile(processFileService.selectIrrigationProcessFileList(new IrrigationProcessFileParam().setModuleId(id).setModuleType("1")));
        irrigationProcessProjectInfoVo.setProcessFile(processFileService.selectIrrigationProcessFileList(new IrrigationProcessFileParam().setModuleId(id).setModuleType("2")));


        return irrigationProcessProjectInfoVo;
    }

    /**
     * 查询工程运行-项目管理列表
     *
     * @param irrigationProcessProject 工程运行-项目管理
     * @return 工程运行-项目管理
     */
    @Override
    public List<IrrigationProcessProjectVo> selectIrrigationProcessProjectList(IrrigationProcessProjectParam irrigationProcessProject)
    {
        return baseMapper.selectIrrigationProcessProjectList(irrigationProcessProject);
    }

    /**
     * 新增工程运行-项目管理
     *
     * @param irrigationProcessProjectAddParam 工程运行-项目管理
     * @return 结果
     */
    @Override
    public int insertIrrigationProcessProject(IrrigationProcessProjectAddParam irrigationProcessProjectAddParam)
    {

        IrrigationProcessProjectPo irrigationProcessProject=new IrrigationProcessProjectPo();
        BeanUtil.copyProperties(irrigationProcessProjectAddParam,irrigationProcessProject);
        irrigationProcessProject.setCreateTime(DateUtils.getNowDate());
         baseMapper.insert(irrigationProcessProject);
         //处理文件
        List<IrrigationProcessFilePo> processFilePos =new ArrayList<>();
         if (ObjectUtil.isNotNull(irrigationProcessProjectAddParam.getApprovalFile())){
             List<IrrigationProcessFilePo> irrigationProcessFilePos = BeanUtil.copyToList(irrigationProcessProjectAddParam.getApprovalFile(), IrrigationProcessFilePo.class);
             for (IrrigationProcessFilePo irrigationProcessFilePo : irrigationProcessFilePos) {
                 irrigationProcessFilePo.setModuleId(irrigationProcessProject.getId());
                 irrigationProcessFilePo.setModuleType("1");
             }
             processFilePos.addAll(irrigationProcessFilePos);
         }
         if (ObjectUtil.isNotNull(irrigationProcessProjectAddParam.getProcessFile())){
             List<IrrigationProcessFilePo> irrigationProcessFilePos = BeanUtil.copyToList(irrigationProcessProjectAddParam.getProcessFile(), IrrigationProcessFilePo.class);
             for (IrrigationProcessFilePo irrigationProcessFilePo : irrigationProcessFilePos) {
                 irrigationProcessFilePo.setModuleId(irrigationProcessProject.getId());
                 irrigationProcessFilePo.setModuleType("2");
             }
             processFilePos.addAll(irrigationProcessFilePos);
         }

         //处理文件
        processFileService.saveBatch(processFilePos);

        return 1;
    }

    /**
     * 修改工程运行-项目管理
     *
     * @param irrigationProcessProjectEditParam 工程运行-项目管理
     * @return 结果
     */
    @Override
    public int updateIrrigationProcessProject(IrrigationProcessProjectEditParam irrigationProcessProjectEditParam)
    {
        IrrigationProcessProjectPo irrigationProcessProject=new IrrigationProcessProjectPo();
        BeanUtil.copyProperties(irrigationProcessProjectEditParam,irrigationProcessProject);
         baseMapper.updateById(irrigationProcessProject);
         baseMapper.deleteFileByModuleId(irrigationProcessProject.getId());
        //处理文件
        List<IrrigationProcessFilePo> processFilePos =new ArrayList<>();
        if (ObjectUtil.isNotNull(irrigationProcessProjectEditParam.getApprovalFile())){
            List<IrrigationProcessFilePo> irrigationProcessFilePos = BeanUtil.copyToList(irrigationProcessProjectEditParam.getApprovalFile(), IrrigationProcessFilePo.class);
            for (IrrigationProcessFilePo irrigationProcessFilePo : irrigationProcessFilePos) {
                irrigationProcessFilePo.setModuleId(irrigationProcessProject.getId());
                irrigationProcessFilePo.setModuleType("1");
            }
            processFilePos.addAll(irrigationProcessFilePos);
        }
        if (ObjectUtil.isNotNull(irrigationProcessProjectEditParam.getProcessFile())){
            List<IrrigationProcessFilePo> irrigationProcessFilePos = BeanUtil.copyToList(irrigationProcessProjectEditParam.getProcessFile(), IrrigationProcessFilePo.class);
            for (IrrigationProcessFilePo irrigationProcessFilePo : irrigationProcessFilePos) {
                irrigationProcessFilePo.setModuleId(irrigationProcessProject.getId());
                irrigationProcessFilePo.setModuleType("2");
            }
            processFilePos.addAll(irrigationProcessFilePos);
        }
        //处理文件
        processFileService.saveBatch(processFilePos);

        return 1;
    }

    /**
     * 批量删除工程运行-项目管理
     *
     * @param ids 需要删除的工程运行-项目管理主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationProcessProjectByIds(Long[] ids)
    {
        return baseMapper.deleteIrrigationProcessProjectByIds(ids);
    }

    /**
     * 删除工程运行-项目管理信息
     *
     * @param id 工程运行-项目管理主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationProcessProjectById(Long id)
    {
        return baseMapper.deleteIrrigationProcessProjectById(id);
    }
}
