package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.irrigation.entity.param.StPptnRParam;
import com.jhdr.irrigation.entity.param.StRiverRParam;
import com.jhdr.irrigation.entity.param.WaterRainParam;
import com.jhdr.irrigation.entity.param.WaterRiverRParam;
import com.jhdr.irrigation.entity.po.*;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 数字孪生统一处理平台
 */
@Component
public class DigitalTwinServiceImpl {
    @Resource
    private IHubBasicInformationService hubBasicInformationService;
    @Resource
    private IGisDatasDService gisDatasDService;
    @Resource
    private IJhSlcrsRService slcrsRService;
    @Resource
    private IJhPumpRRealService pumpRRealService;
    @Resource
    private IJhSlcrsRRealService jhSlcrsRRealService;
    @Resource
    private IJhPumpDtlRRealService pumpDtlRRealService;
    @Resource
    private IStRiverRService stRiverRService;
    @Resource
    private IStRvfcchBService stRvfcchBService;
    @Resource
    private IStPptnRService stPptnRService;


    /**
     * 工情-数量统计 包含：枢纽，泵站，闸站
     */
    public EngineerCountingVo getEngineerCounting() {

        EngineerCountingVo engineerCounting = new EngineerCountingVo();

        //枢纽计算总数和开启数量
        List<HubBasicInformationPo> hubInfos = hubBasicInformationService.list(new QueryWrapper<HubBasicInformationPo>()
                .eq("status", 1));

        if (ObjectUtil.isNotEmpty(hubInfos)) {
            engineerCounting.setHubAllNum(hubInfos.size() + "");
            int hubOnlineNum = 0;
            for (HubBasicInformationPo hubInfo : hubInfos) {
                if (ObjectUtil.isNotEmpty(hubInfo.getGataCode())) {
                    long gataCount = jhSlcrsRRealService.count(new QueryWrapper<JhSlcrsRRealPo>()
                            .eq("prjnmcd", hubInfo.getGataCode() + "").last("and gtopn >0"));
                    if (gataCount > 0) {
                        hubOnlineNum++;
                        continue;
                    }
                    long pumpCount = pumpRRealService.count(new QueryWrapper<JhPumpRRealPo>()
                            .eq("prjnmcd", hubInfo.getPustCode()).last("and omcn >0"));
                    if (pumpCount > 0) {
                        hubOnlineNum++;
                    }
                }
            }
            engineerCounting.setHubOpenNum(hubOnlineNum + "");
        }
        //泵站 总数和开启数量
        Long pumpStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "BZ"));
        //泵站数量
        engineerCounting.setPumpAllNum(pumpStationCount + "");
        Integer pumpOpenNum = pumpRRealService.getPumpOpenNum();
        engineerCounting.setPumpOpenNum(pumpOpenNum + "");
        //闸站 总数和开启数量
        Long gateStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "HZ"));
        engineerCounting.setGateStationAllNum(gateStationCount + "");
        Integer gateStationOpenNum = jhSlcrsRRealService.getGateStationOpenNum();
        engineerCounting.setGateStationOpenNum(gateStationOpenNum + "");

        return engineerCounting;
    }

    /**
     * 工情-运行统计-枢纽
     */
    public EngineerHubVo getEngineerHub(String hubCd) {
        EngineerHubVo hubVo = new EngineerHubVo();
        HubBasicInformationPo hubInfo = hubBasicInformationService.getOne(new QueryWrapper<HubBasicInformationPo>()
                .eq("hub_code", hubCd).last("limit 1"), false);

        if (ObjectUtil.isNotEmpty(hubInfo) && ObjectUtil.isNotEmpty(hubInfo.getGataCode())) {
            String gateCode = hubInfo.getGataCode().toString();
            JhSlcrsRPo slcrsRPo = slcrsRService.getOne(new QueryWrapper<JhSlcrsRPo>()
                    .eq("prjnmcd", gateCode)
                    .orderBy(true, false, "clltm").last("limit 1"), false);
            hubVo = BeanUtil.toBean(slcrsRPo, EngineerHubVo.class);
            if (hubVo == null) {
                hubVo = new EngineerHubVo();
            }
            //闸站对应闸门开度列表
            List<JhSlcrsDtlRInfoVo> slcrsDtlRInfoVos = jhSlcrsRRealService.getSlcrsRRealInfoVos(hubInfo.getGataCode() + "");
            if (ObjectUtil.isNotEmpty(slcrsDtlRInfoVos)) {
                hubVo.setGateCondition(slcrsDtlRInfoVos);
            }
            JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(gateCode);
            if (ObjectUtil.isNotEmpty(temporaryData)) {
                hubVo.setSldsz(temporaryData.getSldsz());
                hubVo.setSlupsz(temporaryData.getSlupsz());
            } else {
                hubVo.setSldsz(null);
                hubVo.setSlupsz(null);
            }
        }
        if (ObjectUtil.isNotEmpty(hubInfo) && ObjectUtil.isNotEmpty(hubInfo.getPustCode())) {
            String pustCode = hubInfo.getPustCode();
            JhPumpRRealPo one = pumpRRealService.getOne(new QueryWrapper<JhPumpRRealPo>()
                    .eq("prjnmcd", pustCode)
                    .orderBy(true, false, "clltm").last("limit 1"), false);
            if (one != null) {
                hubVo.setPmpq(one.getPmpq());

                List<JhPumpDtlRInfoVo> pumpDtlRInfoVos = pumpDtlRRealService.getPumpDtlRInfoVos(hubInfo.getPustCode());
                if (ObjectUtil.isNotEmpty(pumpDtlRInfoVos)) {
                    hubVo.setPumpCondition(pumpDtlRInfoVos);
                }
            }
        }


        return hubVo;
    }

    public List<EngineerHubCountVo> engineerHubCount() {
        List<EngineerHubInfoVo> hubCountVos = hubBasicInformationService.engineerHubList();
        List<EngineerHubCountVo> engineerCounting = new ArrayList<>();
        for (EngineerHubInfoVo hubCountVo : hubCountVos) {
            if (ObjectUtil.isNotEmpty(hubCountVo)) {
                EngineerHubCountVo engineerHubCountVo = BeanUtil.toBean(hubCountVo, EngineerHubCountVo.class);
                if (ObjectUtil.isNotEmpty(hubCountVo.getGataCode())) {
                    long gataCount = jhSlcrsRRealService.count(new QueryWrapper<JhSlcrsRRealPo>()
                            .eq("prjnmcd", hubCountVo.getGataCode() + "").last("and gtopn >0"));
                    if (gataCount > 0) {
                        engineerHubCountVo.setStatus("1");

                    }
                    long pumpCount = pumpRRealService.count(new QueryWrapper<JhPumpRRealPo>()
                            .eq("prjnmcd", hubCountVo.getPustCode()).last("and omcn >0"));
                    if (pumpCount > 0) {
                        engineerHubCountVo.setStatus("1");
                    }
                }
                engineerCounting.add(engineerHubCountVo);
            }
        }

        return engineerCounting;
    }

    //工情-运行统计-泵站
    public List<EngineerPumpCountVo> engineerPumpCount() {

        List<EngineerPumpCountVo> engineerPumpCountVos = pumpRRealService.getEngineerPumpCount();
        List<EngineerPumpCountVo> pumpCountVos = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(engineerPumpCountVos)) {
            //阚疃341623000028   上桥340321000284

            // 移除特定编码的泵站并将它添加到目标列表的开头
            for (EngineerPumpCountVo stn : new ArrayList<>(engineerPumpCountVos)) { // 使用新 ArrayList 避免并发修改异常
                if ("341623000028".equals(stn.getPumpCd())) {
                    engineerPumpCountVos.remove(stn);
                    pumpCountVos.add(stn); // 添加到开头
                }
                if ("340321000284".equals(stn.getPumpCd())) {
                    engineerPumpCountVos.remove(stn);
                    pumpCountVos.add(stn); // 添加到开头
                }
            }
            pumpCountVos.addAll(engineerPumpCountVos);
        }
        return pumpCountVos;
    }

    //工情-闸站
    public List<EngineerGateStationCountVo> engineerGateStationCount() {
        List<EngineerGateStationCountVo> engineerGateStationCountVos = jhSlcrsRRealService.engineerGateStationCount();
        List<EngineerGateStationCountVo> gateStationCountVos = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(engineerGateStationCountVos)) {
            //阚疃 341623000096   插花341203000022 茨河铺341204000095 上桥108131001

            // 移除特定编码的泵站并将它添加到目标列表的开头
            for (EngineerGateStationCountVo stn : new ArrayList<>(engineerGateStationCountVos)) { // 使用新 ArrayList 避免并发修改异常
                if ("341623000096".equals(stn.getGateStationCd())) {
                    engineerGateStationCountVos.remove(stn);
                    gateStationCountVos.add(stn); // 添加到开头
                }
                if ("341203000022".equals(stn.getGateStationCd())) {
                    engineerGateStationCountVos.remove(stn);
                    gateStationCountVos.add(stn); // 添加到开头
                }
                if ("341204000095".equals(stn.getGateStationCd())) {
                    engineerGateStationCountVos.remove(stn);
                    gateStationCountVos.add(stn); // 添加到开头
                }
                if ("108131001".equals(stn.getGateStationCd())) {
                    engineerGateStationCountVos.remove(stn);
                    gateStationCountVos.add(stn); // 添加到开头
                }
            }
            gateStationCountVos.addAll(engineerGateStationCountVos);
        }
        return gateStationCountVos;
    }

    //水雨情-数量统计 包含：水位站，雨量站
    public WaterSituationCountingVo waterSituationCounting() {
        WaterSituationCountingVo waterSituationCountingVo = new WaterSituationCountingVo();
        //水位站总数量
        Long waterLevelStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "SW"));
        waterSituationCountingVo.setWaterAllNum(waterLevelStationCount-9 + "");
//       Integer waterOpenNum =stRiverRService.waterOpenNum();
//
        waterSituationCountingVo.setWaterOpenNum(waterLevelStationCount-9 + "");

        //雨量站总数量
        Long rainfallStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "YL"));
        waterSituationCountingVo.setRainAllNum(rainfallStationCount-3 + "");
        waterSituationCountingVo.setRainOpenNum(rainfallStationCount-3 + "");
        //流量站总数量
        Long flowStationCount = gisDatasDService.count(new QueryWrapper<GisDatasDPo>().eq("GISSIGN", "LL"));
        waterSituationCountingVo.setFlowAllNum(flowStationCount + "");
        waterSituationCountingVo.setFlowOpenNum(flowStationCount + "");

        return waterSituationCountingVo;
    }

    public WaterSituationInfoListVo waterInfoList(WaterRiverRParam riverRParam) {
        if (ObjectUtil.isNotEmpty(riverRParam)) {
            StRiverRParam stRiverRParam = new StRiverRParam();
            stRiverRParam.setSTCD(riverRParam.getStcd());
            stRiverRParam.setTMEnd(riverRParam.getTmEnd());
            stRiverRParam.setTMStart(riverRParam.getTmStart());
            WaterSituationInfoListVo waterSituationInfoListVo = new WaterSituationInfoListVo();
            List<StRiverRVo> list = stRiverRService.selectStRiverRList(stRiverRParam);

            waterSituationInfoListVo.setWaterList(list);
            StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(stRiverRParam.getSTCD());
            if (ObjectUtil.isNotEmpty(stRvfcchBVo)) {
                waterSituationInfoListVo.setGrz(stRvfcchBVo.getGrz());
                waterSituationInfoListVo.setWrz(stRvfcchBVo.getWrz());
                waterSituationInfoListVo.setObhtz(stRvfcchBVo.getObhtz());
            }
            return waterSituationInfoListVo;
        } else {
            throw new RuntimeException("参数错误");
        }

    }

    public List<StPptnRVo> getRainBar(WaterRainParam rainParam) {
        if (ObjectUtil.isNotEmpty(rainParam)) {
            StPptnRParam stPptnRParam = new StPptnRParam();
            stPptnRParam.setSTCD(rainParam.getStcd());
            stPptnRParam.setTMEnd(rainParam.getTmEnd());
            stPptnRParam.setTMStart(rainParam.getTmStart());
            stPptnRParam.setType(rainParam.getType());
            List<StPptnRVo> list = stPptnRService.selectStPptnRList(stPptnRParam);
            return list;
        } else {
            throw new RuntimeException("参数错误");
        }
    }
}
