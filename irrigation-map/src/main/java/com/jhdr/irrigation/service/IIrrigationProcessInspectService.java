package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationProcessInspectPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessInspectEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessInspectVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 工程运行-检查Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IIrrigationProcessInspectService extends IService<IrrigationProcessInspectPo>
{



    /**
     * 查询工程运行-检查
     *
     * @param id 工程运行-检查主键
     * @return 工程运行-检查
     */
    public IrrigationProcessInspectVo selectIrrigationProcessInspectById(Long id);

    /**
     * 查询工程运行-检查列表
     *
     * @param irrigationProcessInspect 工程运行-检查
     * @return 工程运行-检查集合
     */
    public List<IrrigationProcessInspectVo> selectIrrigationProcessInspectList(IrrigationProcessInspectParam irrigationProcessInspect);

    /**
     * 新增工程运行-检查
     *
     * @param irrigationProcessInspect 工程运行-检查
     * @return 结果
     */
    public int insertIrrigationProcessInspect(IrrigationProcessInspectAddParam irrigationProcessInspect);

    /**
     * 修改工程运行-检查
     *
     * @param irrigationProcessInspect 工程运行-检查
     * @return 结果
     */
    public int updateIrrigationProcessInspect(IrrigationProcessInspectEditParam irrigationProcessInspect);

    /**
     * 批量删除工程运行-检查
     *
     * @param ids 需要删除的工程运行-检查主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessInspectByIds(Long[] ids);

    /**
     * 删除工程运行-检查信息
     *
     * @param id 工程运行-检查主键
     * @return 结果
     */
    public int deleteIrrigationProcessInspectById(Long id);

}
