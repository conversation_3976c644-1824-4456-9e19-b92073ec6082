package com.jhdr.irrigation.service;

import java.math.BigDecimal;
import java.util.List;

import com.jhdr.irrigation.entity.param.IrrigationProcessWorkParam;
import com.jhdr.irrigation.entity.po.JhPumpRRealPo;
import com.jhdr.irrigation.entity.param.JhPumpRRealParam;
import com.jhdr.irrigation.entity.param.JhPumpRRealAddParam;
import com.jhdr.irrigation.entity.param.JhPumpRRealEditParam;
import com.jhdr.irrigation.entity.vo.EngineerPumpCountVo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessPumpCountVo;
import com.jhdr.irrigation.entity.vo.JhPumpRRealVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 泵站运行状况快照Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IJhPumpRRealService extends IService<JhPumpRRealPo>
{



    /**
     * 查询泵站运行状况快照
     *
     * @param prjnmcd 泵站运行状况快照主键
     * @return 泵站运行状况快照
     */
    public JhPumpRRealVo selectJhPumpRRealByPrjnmcd(String prjnmcd);

    /**
     * 查询泵站运行状况快照列表
     *
     * @param jhPumpRReal 泵站运行状况快照
     * @return 泵站运行状况快照集合
     */
    public List<JhPumpRRealVo> selectJhPumpRRealList(JhPumpRRealParam jhPumpRReal);

    /**
     * 新增泵站运行状况快照
     *
     * @param jhPumpRReal 泵站运行状况快照
     * @return 结果
     */
    public int insertJhPumpRReal(JhPumpRRealAddParam jhPumpRReal);

    /**
     * 修改泵站运行状况快照
     *
     * @param jhPumpRReal 泵站运行状况快照
     * @return 结果
     */
    public int updateJhPumpRReal(JhPumpRRealEditParam jhPumpRReal);

    /**
     * 批量删除泵站运行状况快照
     *
     * @param prjnmcds 需要删除的泵站运行状况快照主键集合
     * @return 结果
     */
    public int deleteJhPumpRRealByPrjnmcds(String[] prjnmcds);

    /**
     * 删除泵站运行状况快照信息
     *
     * @param prjnmcd 泵站运行状况快照主键
     * @return 结果
     */
    public int deleteJhPumpRRealByPrjnmcd(String prjnmcd);

    /**
     * 获取泵站开启数量
     * @return
     */
    Integer getPumpOpenNum();

    /**
     * 获取泵站运行统计
     * @return
     */
    List<EngineerPumpCountVo> getEngineerPumpCount();

    List<IrrigationProcessPumpCountVo> pumpList(IrrigationProcessWorkParam processWorkParam);

    //当年累计流量
    BigDecimal yearAccq(String prjnmcd);
}
