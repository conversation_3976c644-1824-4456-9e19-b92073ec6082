package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.StWasRPo;
import com.jhdr.irrigation.entity.param.StWasRParam;
import com.jhdr.irrigation.entity.param.StWasRAddParam;
import com.jhdr.irrigation.entity.param.StWasREditParam;
import com.jhdr.irrigation.entity.vo.StWasRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 堰闸水情Service接口
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
public interface IStWasRService extends IService<StWasRPo>
{



    /**
     * 查询堰闸水情
     *
     * @param STCD 堰闸水情主键
     * @return 堰闸水情
     */
    public StWasRVo selectStWasRBySTCD(String STCD);

    /**
     * 查询堰闸水情列表
     *
     * @param stWasR 堰闸水情
     * @return 堰闸水情集合
     */
    public List<StWasRVo> selectStWasRList(StWasRParam stWasR);

    /**
     * 新增堰闸水情
     *
     * @param stWasR 堰闸水情
     * @return 结果
     */
    public int insertStWasR(StWasRAddParam stWasR);

    /**
     * 修改堰闸水情
     *
     * @param stWasR 堰闸水情
     * @return 结果
     */
    public int updateStWasR(StWasREditParam stWasR);

    /**
     * 批量删除堰闸水情
     *
     * @param STCDs 需要删除的堰闸水情主键集合
     * @return 结果
     */
    public int deleteStWasRBySTCDs(String[] STCDs);

    /**
     * 删除堰闸水情信息
     *
     * @param STCD 堰闸水情主键
     * @return 结果
     */
    public int deleteStWasRBySTCD(String STCD);

}
