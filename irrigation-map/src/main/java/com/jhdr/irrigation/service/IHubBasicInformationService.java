package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.HubBasicInformationPo;
import com.jhdr.irrigation.entity.param.HubBasicInformationParam;
import com.jhdr.irrigation.entity.param.HubBasicInformationAddParam;
import com.jhdr.irrigation.entity.param.HubBasicInformationEditParam;
import com.jhdr.irrigation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 枢纽基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface IHubBasicInformationService extends IService<HubBasicInformationPo>
{


    /**
     * 查询枢纽基础信息
     *
     * @param id 枢纽基础信息主键
     * @return 枢纽基础信息
     */
    public HubBasicInformationVo selectHubBasicInformationById(Long id);

    /**
     * 查询枢纽基础信息列表
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 枢纽基础信息集合
     */
    public List<HubBasicInformationVo> selectHubBasicInformationList(HubBasicInformationParam hubBasicInformation);

    /**
     * 新增枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int insertHubBasicInformation(HubBasicInformationAddParam hubBasicInformation);

    /**
     * 修改枢纽基础信息
     *
     * @param hubBasicInformation 枢纽基础信息
     * @return 结果
     */
    public int updateHubBasicInformation(HubBasicInformationEditParam hubBasicInformation);

    /**
     * 批量删除枢纽基础信息
     *
     * @param ids 需要删除的枢纽基础信息主键集合
     * @return 结果
     */
    public int deleteHubBasicInformationByIds(Long[] ids);

    /**
     * 删除枢纽基础信息信息
     *
     * @param id 枢纽基础信息主键
     * @return 结果
     */
    public int deleteHubBasicInformationById(Long id);

    /**
     * 工情-运行统计-枢纽
     * @return
     */
    List<EngineerHubInfoVo> engineerHubList();

    /**
     * 水旱防御-枢纽信息
     * @return
     */
    PreventionProjectVo getProjectInfo(String hubCd);


    /**
     * 水旱防御-关联查询枢纽信合和雨量编码
     * @return
     */
    List<PreventionRainVo> getRainInfos( List<String> hubCodes );

    /**
     * 水旱防御-水位信息
     * @return
     */
    List<PreventionWaterVo> getWaterInfos(List<String> hubCodes);
}
