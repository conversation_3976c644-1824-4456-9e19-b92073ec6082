package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jhdr.irrigation.entity.vo.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhFlowycRPo;
import com.jhdr.irrigation.entity.param.JhFlowycRParam;
import com.jhdr.irrigation.entity.param.JhFlowycRAddParam;
import com.jhdr.irrigation.entity.param.JhFlowycREditParam;
import com.jhdr.irrigation.mapper.JhFlowycRMapper;
import com.jhdr.irrigation.service.IJhFlowycRService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 原始水量数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Service
public class JhFlowycRServiceImpl extends ServiceImpl<JhFlowycRMapper, JhFlowycRPo> implements IJhFlowycRService {


    /**
     * 查询原始水量数据
     *
     * @param STCD 原始水量数据主键
     * @return 原始水量数据
     */
    @Override
    public JhFlowycRVo selectJhFlowycRBySTCD(String STCD)
    {
        return baseMapper.selectJhFlowycRBySTCD(STCD);
    }

    /**
     * 查询原始水量数据列表
     *
     * @param jhFlowycR 原始水量数据
     * @return 原始水量数据
     */
    @Override
    public List<JhFlowtnRVo> selectJhFlowycRList(JhFlowycRParam jhFlowycR)
    {
        jhFlowycR.setTMStart(updateQueryData(jhFlowycR.getTMStart()));
        jhFlowycR.setTMEnd(updateQueryData(jhFlowycR.getTMEnd()));

        List<JhFlowtnRVo> jhFlowtnRVos = baseMapper.selectJhFlowycRList(jhFlowycR);
        BigDecimal totalPsq = BigDecimal.ZERO;

        //统一处理累计日水量
        for (JhFlowtnRVo record : jhFlowtnRVos) {
            if (ObjectUtil.isNotEmpty(record.getPsq())){
                // 累加前n项的降水量到累计降水量
                totalPsq = totalPsq.add(record.getPsq());
                // 将累计值设置到当前对象的totalDrp字段
                record.setTotalPsq(totalPsq);
            }
        }
        return jhFlowtnRVos;
    }

    private String updateQueryData(String dateStr) {

        // 截取日期部分
        String part = dateStr.substring(0, 10);
        // 拼接午夜的时间部分
        String time = " 00:00:00";
        // 将日期部分和午夜时间部分拼接起来
        dateStr = part + time;

        // 创建一个日期时间格式器，用于解析输入的日期时间字符串
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析输入的日期时间字符串
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, inputFormatter);
        LocalDateTime addDateTime = dateTime.plusDays(1);
        // 清除时间部分，保留日期，设置时间为午夜
        LocalDateTime midnight = addDateTime.withHour(0).withMinute(0).withSecond(0);

        // 创建一个新的日期时间格式器，用于输出午夜的日期时间字符串
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将午夜的日期时间格式化为字符串
        String midnightStr = midnight.format(outputFormatter);

        // 输出：2024-05-17 00:00:00
        return midnightStr;
    }

    /**
     * 新增原始水量数据
     *
     * @param jhFlowycRAddParam 原始水量数据
     * @return 结果
     */
    @Override
    public int insertJhFlowycR(JhFlowycRAddParam jhFlowycRAddParam)
    {

        JhFlowycRPo jhFlowycR=new JhFlowycRPo();
        BeanUtil.copyProperties(jhFlowycRAddParam,jhFlowycR);
        return baseMapper.insert(jhFlowycR);
    }

    /**
     * 修改原始水量数据
     *
     * @param jhFlowycREditParam 原始水量数据
     * @return 结果
     */
    @Override
    public int updateJhFlowycR(JhFlowycREditParam jhFlowycREditParam)
    {
        JhFlowycRPo jhFlowycR=new JhFlowycRPo();
        BeanUtil.copyProperties(jhFlowycREditParam,jhFlowycR);
        return baseMapper.updateById(jhFlowycR);
    }

    /**
     * 批量删除原始水量数据
     *
     * @param STCDs 需要删除的原始水量数据主键
     * @return 结果
     */
    @Override
    public int deleteJhFlowycRBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteJhFlowycRBySTCDs(STCDs);
    }

    /**
     * 删除原始水量数据信息
     *
     * @param STCD 原始水量数据主键
     * @return 结果
     */
    @Override
    public int deleteJhFlowycRBySTCD(String STCD)
    {
        return baseMapper.deleteJhFlowycRBySTCD(STCD);
    }

    @Override
    public JhFlowtnRVo getOnePsqTn(String stcd) {
        return baseMapper.getOnePsqTn(stcd);
    }

    @Override
    public List<FlowCrossSectionVo> selectFlowCrossSectionList(String stcd) {
        return baseMapper.selectFlowCrossSectionList(stcd);
    }

    @Override
    public BigDecimal yearAccq(String stcd) {
        return baseMapper.yearAccq(stcd);
    }
    @Override
    public Map<String, BigDecimal> batchYearAccq(List<String> stcdList) {
        if (stcdList.isEmpty()) {
            return Collections.emptyMap();
        }
        return baseMapper.batchYearAccq(stcdList).stream()
                .collect(Collectors.toMap(
                        YearAccqResultVo::getStcd,
                        YearAccqResultVo::getYearAccq,
                        (v1, v2) -> v1
                ));
    }



    @Override
    public List<JhFlowtnRVo> selectJhFlowycRMonthList(JhFlowycRParam jhFlowycR) {
        jhFlowycR.setTMStart(updateQueryData(jhFlowycR.getTMStart()));
        jhFlowycR.setTMEnd(updateQueryData(jhFlowycR.getTMEnd()));

        List<JhFlowtnRVo> jhFlowtnRVos = baseMapper.selectJhFlowycRMonthList(jhFlowycR);
        BigDecimal totalPsq = BigDecimal.ZERO;

        //统一处理累计日水量
        for (JhFlowtnRVo record : jhFlowtnRVos) {
            if (ObjectUtil.isNotEmpty(record.getPsq())){
                // 累加前n项的降水量到累计降水量
                totalPsq = totalPsq.add(record.getPsq());
                // 将累计值设置到当前对象的totalDrp字段
                record.setTotalPsq(totalPsq);
            }
        }
        return jhFlowtnRVos;
    }
}
