package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileParam;
import com.jhdr.irrigation.service.IIrrigationProcessFileService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.IrrigationProcessStandardDocPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardDocEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessStandardDocVo;
import com.jhdr.irrigation.mapper.IrrigationProcessStandardDocMapper;
import com.jhdr.irrigation.service.IIrrigationProcessStandardDocService;

import javax.annotation.Resource;
import java.util.ArrayList;

import java.util.List;

/**
 * 工程运行-标准化-文档Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
@Service
public class IrrigationProcessStandardDocServiceImpl extends ServiceImpl<IrrigationProcessStandardDocMapper, IrrigationProcessStandardDocPo> implements IIrrigationProcessStandardDocService {

    @Resource
    private IIrrigationProcessFileService processFileService;

    @Override
    public List<IrrigationProcessStandardDocVo> queryList(IrrigationProcessStandardDocPo irrigationProcessStandardDoc) {
        LambdaQueryWrapper<IrrigationProcessStandardDocPo> lqw = Wrappers.lambdaQuery();
        if (irrigationProcessStandardDoc.getStandardId() != null){
            lqw.eq(IrrigationProcessStandardDocPo::getStandardId ,irrigationProcessStandardDoc.getStandardId());
        }
        if (StringUtils.isNotBlank(irrigationProcessStandardDoc.getDocName())){
            lqw.like(IrrigationProcessStandardDocPo::getDocName ,irrigationProcessStandardDoc.getDocName());
        }
        if (irrigationProcessStandardDoc.getSortOrder() != null){
            lqw.eq(IrrigationProcessStandardDocPo::getSortOrder ,irrigationProcessStandardDoc.getSortOrder());
        }
        List<IrrigationProcessStandardDocVo> irrigationProcessStandardDocVos= BeanUtil.copyToList(this.list(lqw), IrrigationProcessStandardDocVo.class);
        return irrigationProcessStandardDocVos;
    }
    /**
     * 查询工程运行-标准化-文档
     *
     * @param docId 工程运行-标准化-文档主键
     * @return 工程运行-标准化-文档
     */
    @Override
    public IrrigationProcessStandardDocVo selectIrrigationProcessStandardDocByDocId(Long docId)
    {
        return baseMapper.selectIrrigationProcessStandardDocByDocId(docId);
    }

    /**
     * 查询工程运行-标准化-文档列表
     *
     * @param irrigationProcessStandardDoc 工程运行-标准化-文档
     * @return 工程运行-标准化-文档
     */
    @Override
    public List<IrrigationProcessStandardDocVo> selectIrrigationProcessStandardDocList(IrrigationProcessStandardDocParam irrigationProcessStandardDoc)
    {
        List<IrrigationProcessStandardDocVo> irrigationProcessStandardDocVos = baseMapper.selectIrrigationProcessStandardDocList(irrigationProcessStandardDoc);
        for (IrrigationProcessStandardDocVo irrigationProcessStandardDocVo : irrigationProcessStandardDocVos) {
            irrigationProcessStandardDocVo.setDocFile(processFileService.selectIrrigationProcessFileList
                    (new IrrigationProcessFileParam().setModuleId(irrigationProcessStandardDocVo.getDocId())));
        }

        return irrigationProcessStandardDocVos;
    }

    /**
     * 新增工程运行-标准化-文档
     *
     * @param irrigationProcessStandardDocAddParam 工程运行-标准化-文档
     * @return 结果
     */
    @Override
    public int insertIrrigationProcessStandardDoc(IrrigationProcessStandardDocAddParam irrigationProcessStandardDocAddParam)
    {


        IrrigationProcessStandardDocPo irrigationProcessStandardDoc=new IrrigationProcessStandardDocPo();
        BeanUtil.copyProperties(irrigationProcessStandardDocAddParam,irrigationProcessStandardDoc);
        irrigationProcessStandardDoc.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insert(irrigationProcessStandardDoc);
    }

    /**
     * 修改工程运行-标准化-文档
     *
     * @param irrigationProcessStandardDocEditParam 工程运行-标准化-文档
     * @return 结果
     */
    @Override
    public int updateIrrigationProcessStandardDoc(IrrigationProcessStandardDocEditParam irrigationProcessStandardDocEditParam)
    {
        IrrigationProcessStandardDocPo irrigationProcessStandardDoc=new IrrigationProcessStandardDocPo();
        BeanUtil.copyProperties(irrigationProcessStandardDocEditParam,irrigationProcessStandardDoc);
        return baseMapper.updateById(irrigationProcessStandardDoc);
    }

    /**
     * 批量删除工程运行-标准化-文档
     *
     * @param docIds 需要删除的工程运行-标准化-文档主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationProcessStandardDocByDocIds(Long[] docIds)
    {
        return baseMapper.deleteIrrigationProcessStandardDocByDocIds(docIds);
    }

    /**
     * 删除工程运行-标准化-文档信息
     *
     * @param docId 工程运行-标准化-文档主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationProcessStandardDocByDocId(Long docId)
    {
        return baseMapper.deleteIrrigationProcessStandardDocByDocId(docId);
    }
}
