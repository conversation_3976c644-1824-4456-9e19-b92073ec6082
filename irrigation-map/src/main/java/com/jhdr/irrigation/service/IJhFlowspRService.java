package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.JhFlowspRPo;
import com.jhdr.irrigation.entity.param.JhFlowspRParam;
import com.jhdr.irrigation.entity.param.JhFlowspRAddParam;
import com.jhdr.irrigation.entity.param.JhFlowspREditParam;
import com.jhdr.irrigation.entity.vo.JhFlowspRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 流量站时序数据Service接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface IJhFlowspRService extends IService<JhFlowspRPo>
{

    /**
     * 查询流量站时序数据列表
     *
     * @param jhFlowspR 流量站时序数据
     * @return 流量站时序数据集合
     */
    public List<JhFlowspRVo> queryList(JhFlowspRPo jhFlowspR);

    /**
     * 查询流量站时序数据
     *
     * @param STCD 流量站时序数据主键
     * @return 流量站时序数据
     */
    public JhFlowspRVo selectJhFlowspRBySTCD(String STCD);

    /**
     * 查询流量站时序数据列表
     *
     * @param jhFlowspR 流量站时序数据
     * @return 流量站时序数据集合
     */
    public List<JhFlowspRVo> selectJhFlowspRList(JhFlowspRParam jhFlowspR);

    /**
     * 新增流量站时序数据
     *
     * @param jhFlowspR 流量站时序数据
     * @return 结果
     */
    public int insertJhFlowspR(JhFlowspRAddParam jhFlowspR);

    /**
     * 修改流量站时序数据
     *
     * @param jhFlowspR 流量站时序数据
     * @return 结果
     */
    public int updateJhFlowspR(JhFlowspREditParam jhFlowspR);

    /**
     * 批量删除流量站时序数据
     *
     * @param STCDs 需要删除的流量站时序数据主键集合
     * @return 结果
     */
    public int deleteJhFlowspRBySTCDs(String[] STCDs);

    /**
     * 删除流量站时序数据信息
     *
     * @param STCD 流量站时序数据主键
     * @return 结果
     */
    public int deleteJhFlowspRBySTCD(String STCD);

}
