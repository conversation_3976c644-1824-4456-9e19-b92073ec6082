package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jhdr.irrigation.entity.param.IrrigationProcessWorkParam;
import com.jhdr.irrigation.entity.vo.*;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhSlcrsRRealPo;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealEditParam;
import com.jhdr.irrigation.mapper.JhSlcrsRRealMapper;
import com.jhdr.irrigation.service.IJhSlcrsRRealService;

import java.math.BigDecimal;
import java.util.ArrayList;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 闸站运行状况快照Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service
public class JhSlcrsRRealServiceImpl extends ServiceImpl<JhSlcrsRRealMapper, JhSlcrsRRealPo> implements IJhSlcrsRRealService {


    /**
     * 查询闸站运行状况快照
     *
     * @param prjnmcd 闸站运行状况快照主键
     * @return 闸站运行状况快照
     */
    @Override
    public JhSlcrsRRealVo selectJhSlcrsRRealByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhSlcrsRRealByPrjnmcd(prjnmcd);
    }

    /**
     * 查询闸站运行状况快照列表
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 闸站运行状况快照
     */
    @Override
    public List<JhSlcrsRRealVo> selectJhSlcrsRRealList(JhSlcrsRRealParam jhSlcrsRReal)
    {
        return baseMapper.selectJhSlcrsRRealList(jhSlcrsRReal);
    }

    /**
     * 新增闸站运行状况快照
     *
     * @param jhSlcrsRRealAddParam 闸站运行状况快照
     * @return 结果
     */
    @Override
    public int insertJhSlcrsRReal(JhSlcrsRRealAddParam jhSlcrsRRealAddParam)
    {

        JhSlcrsRRealPo jhSlcrsRReal=new JhSlcrsRRealPo();
        BeanUtil.copyProperties(jhSlcrsRRealAddParam,jhSlcrsRReal);
        return baseMapper.insert(jhSlcrsRReal);
    }

    /**
     * 修改闸站运行状况快照
     *
     * @param jhSlcrsRRealEditParam 闸站运行状况快照
     * @return 结果
     */
    @Override
    public int updateJhSlcrsRReal(JhSlcrsRRealEditParam jhSlcrsRRealEditParam)
    {
        JhSlcrsRRealPo jhSlcrsRReal=new JhSlcrsRRealPo();
        BeanUtil.copyProperties(jhSlcrsRRealEditParam,jhSlcrsRReal);
        return baseMapper.updateById(jhSlcrsRReal);
    }

    /**
     * 批量删除闸站运行状况快照
     *
     * @param prjnmcds 需要删除的闸站运行状况快照主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsRRealByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhSlcrsRRealByPrjnmcds(prjnmcds);
    }

    /**
     * 删除闸站运行状况快照信息
     *
     * @param prjnmcd 闸站运行状况快照主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsRRealByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhSlcrsRRealByPrjnmcd(prjnmcd);
    }

    @Override
    public List<JhSlcrsDtlRInfoVo> getSlcrsRRealInfoVos(String prjnmcd) {
        return baseMapper.getSlcrsRRealInfoVos(prjnmcd);
    }

    @Override
    public Integer getGateStationOpenNum() {
        return baseMapper.getGateStationOpenNum();
    }

    @Override
    public List<EngineerGateStationCountVo> engineerGateStationCount() {
        return baseMapper.engineerGateStationCount();
    }

    @Override
    public JhSlcrsRRealTemporaryDataVo temporaryData(String prjnmcd) {
        return baseMapper.temporaryData(prjnmcd);
    }

    @Override
    public List<IrrigationProcessGateVo> gateList(IrrigationProcessWorkParam processWorkParam) {
        // 获取闸门数量
        List<IrrigationProcessGateVo> processGateVos =baseMapper.gateListCount(processWorkParam);
        // 获取闸门状态
        List<IrrigationProcessGateVo> irrigationProcessGateVos = baseMapper.gateList(processWorkParam);
        if (ObjectUtil.isNotNull(irrigationProcessGateVos)) {
            for (IrrigationProcessGateVo irrigationProcessGateVo : irrigationProcessGateVos) {
                if (ObjectUtil.isNotNull(irrigationProcessGateVo.getGateCondition())) {
                    // 遍历 jhSlcrsDtlRInfoVos，判断 gtoph 是否大于 0，如果是则将 om 设置为 "1"，否则设置为 "0"
                    for (JhSlcrsDtlRInfoVo jhSlcrsDtlRInfoVo : irrigationProcessGateVo.getGateCondition()) {
                        if (ObjectUtil.isNotEmpty(jhSlcrsDtlRInfoVo.getGtoph()) && jhSlcrsDtlRInfoVo.getGtoph().compareTo(new BigDecimal(0)) > 0) {
                            jhSlcrsDtlRInfoVo.setOm("1");
                        } else {
                            jhSlcrsDtlRInfoVo.setOm("0");
                        }
                    }
                }
            }
        }

        // 创建一个 Map 用来存储 processGateVos 中的对象，以 strCode 为键
        Map<String, IrrigationProcessGateVo> processGateMap = new HashMap<>();
        for (IrrigationProcessGateVo vo : processGateVos) {
            processGateMap.put(vo.getStrCode(), vo);
        }

        // 遍历 irrigationProcessGateVos，更新 processGateVos 中对应对象的属性
        for (IrrigationProcessGateVo vo : irrigationProcessGateVos) {
            IrrigationProcessGateVo targetVo = processGateMap.get(vo.getStrCode());
            if (targetVo != null) {
                // 更新目标对象的属性
                targetVo.setStrName(vo.getStrName());
                targetVo.setSlupsz(vo.getSlupsz());
                targetVo.setSldsz(vo.getSldsz());
                targetVo.setThrslq(vo.getThrslq());
                targetVo.setStatus(vo.getStatus());
                targetVo.setGateCondition(vo.getGateCondition());
            }
        }
        return processGateVos;
    }

    @Override
    public BigDecimal yearAccq(String prjnmcd) {
        return baseMapper.yearAccq(prjnmcd);
    }
}
