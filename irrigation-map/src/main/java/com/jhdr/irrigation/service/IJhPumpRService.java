package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.JhPumpRPo;
import com.jhdr.irrigation.entity.param.JhPumpRParam;
import com.jhdr.irrigation.entity.param.JhPumpRAddParam;
import com.jhdr.irrigation.entity.param.JhPumpREditParam;
import com.jhdr.irrigation.entity.vo.JhPumpRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 泵站运行状况Service接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface IJhPumpRService extends IService<JhPumpRPo>
{

    /**
     * 查询泵站运行状况列表
     *
     * @param jhPumpR 泵站运行状况
     * @return 泵站运行状况集合
     */
    public List<JhPumpRVo> queryList(JhPumpRPo jhPumpR);

    /**
     * 查询泵站运行状况
     *
     * @param prjnmcd 泵站运行状况主键
     * @return 泵站运行状况
     */
    public JhPumpRVo selectJhPumpRByPrjnmcd(String prjnmcd);

    /**
     * 查询泵站运行状况列表
     *
     * @param jhPumpR 泵站运行状况
     * @return 泵站运行状况集合
     */
    public List<JhPumpRVo> selectJhPumpRList(JhPumpRParam jhPumpR);

    /**
     * 新增泵站运行状况
     *
     * @param jhPumpR 泵站运行状况
     * @return 结果
     */
    public int insertJhPumpR(JhPumpRAddParam jhPumpR);

    /**
     * 修改泵站运行状况
     *
     * @param jhPumpR 泵站运行状况
     * @return 结果
     */
    public int updateJhPumpR(JhPumpREditParam jhPumpR);

    /**
     * 批量删除泵站运行状况
     *
     * @param prjnmcds 需要删除的泵站运行状况主键集合
     * @return 结果
     */
    public int deleteJhPumpRByPrjnmcds(String[] prjnmcds);

    /**
     * 删除泵站运行状况信息
     *
     * @param prjnmcd 泵站运行状况主键
     * @return 结果
     */
    public int deleteJhPumpRByPrjnmcd(String prjnmcd);

}
