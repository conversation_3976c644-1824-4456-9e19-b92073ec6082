package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhstWarnrecordRPo;
import com.jhdr.irrigation.entity.param.JhstWarnrecordRParam;
import com.jhdr.irrigation.entity.param.JhstWarnrecordRAddParam;
import com.jhdr.irrigation.entity.param.JhstWarnrecordREditParam;
import com.jhdr.irrigation.entity.vo.JhstWarnrecordRVo;
import com.jhdr.irrigation.mapper.JhstWarnrecordRMapper;
import com.jhdr.irrigation.service.IJhstWarnrecordRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 告警记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-19
 */
@Service
public class JhstWarnrecordRServiceImpl extends ServiceImpl<JhstWarnrecordRMapper, JhstWarnrecordRPo> implements IJhstWarnrecordRService {

    @Override
    public List<JhstWarnrecordRVo> queryList(JhstWarnrecordRPo jhstWarnrecordR) {
        LambdaQueryWrapper<JhstWarnrecordRPo> lqw = Wrappers.lambdaQuery();
        if (jhstWarnrecordR.getStWarnTm() != null){
            lqw.eq(JhstWarnrecordRPo::getStWarnTm ,jhstWarnrecordR.getStWarnTm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnNm())){
            lqw.eq(JhstWarnrecordRPo::getWarnNm ,jhstWarnrecordR.getWarnNm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnType())){
            lqw.eq(JhstWarnrecordRPo::getWarnType ,jhstWarnrecordR.getWarnType());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnGrade())){
            lqw.eq(JhstWarnrecordRPo::getWarnGrade ,jhstWarnrecordR.getWarnGrade());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnSource())){
            lqw.eq(JhstWarnrecordRPo::getWarnSource ,jhstWarnrecordR.getWarnSource());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnStatus())){
            lqw.eq(JhstWarnrecordRPo::getWarnStatus ,jhstWarnrecordR.getWarnStatus());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getStcd())){
            lqw.eq(JhstWarnrecordRPo::getStcd ,jhstWarnrecordR.getStcd());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getStnm())){
            lqw.eq(JhstWarnrecordRPo::getStnm ,jhstWarnrecordR.getStnm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getBsnm())){
            lqw.eq(JhstWarnrecordRPo::getBsnm ,jhstWarnrecordR.getBsnm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getHnnm())){
            lqw.eq(JhstWarnrecordRPo::getHnnm ,jhstWarnrecordR.getHnnm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getRvnm())){
            lqw.eq(JhstWarnrecordRPo::getRvnm ,jhstWarnrecordR.getRvnm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getStlc())){
            lqw.eq(JhstWarnrecordRPo::getStlc ,jhstWarnrecordR.getStlc());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getAddvcd())){
            lqw.eq(JhstWarnrecordRPo::getAddvcd ,jhstWarnrecordR.getAddvcd());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getCountyNm())){
            lqw.eq(JhstWarnrecordRPo::getCountyNm ,jhstWarnrecordR.getCountyNm());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getTownNm())){
            lqw.eq(JhstWarnrecordRPo::getTownNm ,jhstWarnrecordR.getTownNm());
        }
        if (jhstWarnrecordR.getLgtd() != null){
            lqw.eq(JhstWarnrecordRPo::getLgtd ,jhstWarnrecordR.getLgtd());
        }
        if (jhstWarnrecordR.getLttd() != null){
            lqw.eq(JhstWarnrecordRPo::getLttd ,jhstWarnrecordR.getLttd());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnC1())){
            lqw.eq(JhstWarnrecordRPo::getWarnC1 ,jhstWarnrecordR.getWarnC1());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnC2())){
            lqw.eq(JhstWarnrecordRPo::getWarnC2 ,jhstWarnrecordR.getWarnC2());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnC3())){
            lqw.eq(JhstWarnrecordRPo::getWarnC3 ,jhstWarnrecordR.getWarnC3());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getWarnSummary())){
            lqw.eq(JhstWarnrecordRPo::getWarnSummary ,jhstWarnrecordR.getWarnSummary());
        }
        if (StringUtils.isNotBlank(jhstWarnrecordR.getSender())){
            lqw.eq(JhstWarnrecordRPo::getSender ,jhstWarnrecordR.getSender());
        }
        if (jhstWarnrecordR.getSenderTm() != null){
            lqw.eq(JhstWarnrecordRPo::getSenderTm ,jhstWarnrecordR.getSenderTm());
        }
        if (jhstWarnrecordR.getCtm() != null){
            lqw.eq(JhstWarnrecordRPo::getCtm ,jhstWarnrecordR.getCtm());
        }
        List<JhstWarnrecordRVo> jhstWarnrecordRVos= BeanUtil.copyToList(this.list(lqw), JhstWarnrecordRVo.class);
        return jhstWarnrecordRVos;
    }
    /**
     * 查询告警记录
     *
     * @param id 告警记录主键
     * @return 告警记录
     */
    @Override
    public JhstWarnrecordRVo selectJhstWarnrecordRById(Long id)
    {
        return baseMapper.selectJhstWarnrecordRById(id);
    }

    /**
     * 查询告警记录列表
     *
     * @param jhstWarnrecordR 告警记录
     * @return 告警记录
     */
    @Override
    public List<JhstWarnrecordRVo> selectJhstWarnrecordRList(JhstWarnrecordRParam jhstWarnrecordR)
    {
        return baseMapper.selectJhstWarnrecordRList(jhstWarnrecordR);
    }

    /**
     * 新增告警记录
     *
     * @param jhstWarnrecordRAddParam 告警记录
     * @return 结果
     */
    @Override
    public int insertJhstWarnrecordR(JhstWarnrecordRAddParam jhstWarnrecordRAddParam)
    {

        JhstWarnrecordRPo jhstWarnrecordR=new JhstWarnrecordRPo();
        BeanUtil.copyProperties(jhstWarnrecordRAddParam,jhstWarnrecordR);
        return baseMapper.insert(jhstWarnrecordR);
    }

    /**
     * 修改告警记录
     *
     * @param jhstWarnrecordREditParam 告警记录
     * @return 结果
     */
    @Override
    public int updateJhstWarnrecordR(JhstWarnrecordREditParam jhstWarnrecordREditParam)
    {
        JhstWarnrecordRPo jhstWarnrecordR=new JhstWarnrecordRPo();
        BeanUtil.copyProperties(jhstWarnrecordREditParam,jhstWarnrecordR);
        return baseMapper.updateById(jhstWarnrecordR);
    }

    /**
     * 批量删除告警记录
     *
     * @param ids 需要删除的告警记录主键
     * @return 结果
     */
    @Override
    public int deleteJhstWarnrecordRByIds(Long[] ids)
    {
        return baseMapper.deleteJhstWarnrecordRByIds(ids);
    }

    /**
     * 删除告警记录信息
     *
     * @param id 告警记录主键
     * @return 结果
     */
    @Override
    public int deleteJhstWarnrecordRById(Long id)
    {
        return baseMapper.deleteJhstWarnrecordRById(id);
    }
}
