package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.vo.JhSlcrsDtlRInfoVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhSlcrsDtlRPo;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlRParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlRAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsDtlREditParam;
import com.jhdr.irrigation.entity.vo.JhSlcrsDtlRVo;
import com.jhdr.irrigation.mapper.JhSlcrsDtlRMapper;
import com.jhdr.irrigation.service.IJhSlcrsDtlRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 水闸运行明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class JhSlcrsDtlRServiceImpl extends ServiceImpl<JhSlcrsDtlRMapper, JhSlcrsDtlRPo> implements IJhSlcrsDtlRService {

    @Override
    public List<JhSlcrsDtlRVo> queryList(JhSlcrsDtlRPo jhSlcrsDtlR) {
        LambdaQueryWrapper<JhSlcrsDtlRPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhSlcrsDtlR.getClltm())){
            lqw.eq(JhSlcrsDtlRPo::getClltm ,jhSlcrsDtlR.getClltm());
        }
        if (jhSlcrsDtlR.getGpcd() != null){
            lqw.eq(JhSlcrsDtlRPo::getGpcd ,jhSlcrsDtlR.getGpcd());
        }
        if (jhSlcrsDtlR.getGtoph() != null){
            lqw.eq(JhSlcrsDtlRPo::getGtoph ,jhSlcrsDtlR.getGtoph());
        }
        if (jhSlcrsDtlR.getGtq() != null){
            lqw.eq(JhSlcrsDtlRPo::getGtq ,jhSlcrsDtlR.getGtq());
        }
        if (jhSlcrsDtlR.getGtup() != null){
            lqw.eq(JhSlcrsDtlRPo::getGtup ,jhSlcrsDtlR.getGtup());
        }
        if (jhSlcrsDtlR.getGtdw() != null){
            lqw.eq(JhSlcrsDtlRPo::getGtdw ,jhSlcrsDtlR.getGtdw());
        }
        if (jhSlcrsDtlR.getGtaop() != null){
            lqw.eq(JhSlcrsDtlRPo::getGtaop ,jhSlcrsDtlR.getGtaop());
        }
        if (jhSlcrsDtlR.getGtaco() != null){
            lqw.eq(JhSlcrsDtlRPo::getGtaco ,jhSlcrsDtlR.getGtaco());
        }
        List<JhSlcrsDtlRVo> jhSlcrsDtlRVos= BeanUtil.copyToList(this.list(lqw), JhSlcrsDtlRVo.class);
        return jhSlcrsDtlRVos;
    }
    /**
     * 查询水闸运行明细
     *
     * @param prjnmcd 水闸运行明细主键
     * @return 水闸运行明细
     */
    @Override
    public JhSlcrsDtlRVo selectJhSlcrsDtlRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhSlcrsDtlRByPrjnmcd(prjnmcd);
    }

    /**
     * 查询水闸运行明细列表
     *
     * @param jhSlcrsDtlR 水闸运行明细
     * @return 水闸运行明细
     */
    @Override
    public List<JhSlcrsDtlRVo> selectJhSlcrsDtlRList(JhSlcrsDtlRParam jhSlcrsDtlR)
    {
        return baseMapper.selectJhSlcrsDtlRList(jhSlcrsDtlR);
    }

    /**
     * 新增水闸运行明细
     *
     * @param jhSlcrsDtlRAddParam 水闸运行明细
     * @return 结果
     */
    @Override
    public int insertJhSlcrsDtlR(JhSlcrsDtlRAddParam jhSlcrsDtlRAddParam)
    {

        JhSlcrsDtlRPo jhSlcrsDtlR=new JhSlcrsDtlRPo();
        BeanUtil.copyProperties(jhSlcrsDtlRAddParam,jhSlcrsDtlR);
        return baseMapper.insert(jhSlcrsDtlR);
    }

    /**
     * 修改水闸运行明细
     *
     * @param jhSlcrsDtlREditParam 水闸运行明细
     * @return 结果
     */
    @Override
    public int updateJhSlcrsDtlR(JhSlcrsDtlREditParam jhSlcrsDtlREditParam)
    {
        JhSlcrsDtlRPo jhSlcrsDtlR=new JhSlcrsDtlRPo();
        BeanUtil.copyProperties(jhSlcrsDtlREditParam,jhSlcrsDtlR);
        return baseMapper.updateById(jhSlcrsDtlR);
    }

    /**
     * 批量删除水闸运行明细
     *
     * @param prjnmcds 需要删除的水闸运行明细主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsDtlRByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhSlcrsDtlRByPrjnmcds(prjnmcds);
    }

    /**
     * 删除水闸运行明细信息
     *
     * @param prjnmcd 水闸运行明细主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsDtlRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhSlcrsDtlRByPrjnmcd(prjnmcd);
    }

    /**
     * 获取水闸运行状况基本信息 传gis数据中的CD
     * @param prjnmcd
     * @return
     */
    @Override
    public List<JhSlcrsDtlRInfoVo> getSlcrsRInfoVos(String prjnmcd) {

        return baseMapper.getSlcrsRInfoVos(prjnmcd);
    }
}
