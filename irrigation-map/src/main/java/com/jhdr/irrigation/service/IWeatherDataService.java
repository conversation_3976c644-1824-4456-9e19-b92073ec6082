package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.WeatherDataPo;
import com.jhdr.irrigation.entity.param.WeatherDataParam;
import com.jhdr.irrigation.entity.param.WeatherDataAddParam;
import com.jhdr.irrigation.entity.param.WeatherDataEditParam;
import com.jhdr.irrigation.entity.vo.WeatherDataVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 天气数据Service接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface IWeatherDataService extends IService<WeatherDataPo>
{

    /**
     * 查询天气数据列表
     *
     * @param weatherData 天气数据
     * @return 天气数据集合
     */
    public List<WeatherDataVo> queryList(WeatherDataPo weatherData);

    /**
     * 查询天气数据
     *
     * @param id 天气数据主键
     * @return 天气数据
     */
    public WeatherDataVo selectWeatherDataById(Long id);

    /**
     * 查询天气数据列表
     *
     * @param weatherData 天气数据
     * @return 天气数据集合
     */
    public List<WeatherDataVo> selectWeatherDataList(WeatherDataParam weatherData);

    /**
     * 新增天气数据
     *
     * @param weatherData 天气数据
     * @return 结果
     */
    public int insertWeatherData(WeatherDataAddParam weatherData);

    /**
     * 修改天气数据
     *
     * @param weatherData 天气数据
     * @return 结果
     */
    public int updateWeatherData(WeatherDataEditParam weatherData);

    /**
     * 批量删除天气数据
     *
     * @param ids 需要删除的天气数据主键集合
     * @return 结果
     */
    public int deleteWeatherDataByIds(Long[] ids);

    /**
     * 删除天气数据信息
     *
     * @param id 天气数据主键
     * @return 结果
     */
    public int deleteWeatherDataById(Long id);

    void saveWeatherData(WeatherDataParam weatherDataParam);
}
