package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.StWasRPo;
import com.jhdr.irrigation.entity.param.StWasRParam;
import com.jhdr.irrigation.entity.param.StWasRAddParam;
import com.jhdr.irrigation.entity.param.StWasREditParam;
import com.jhdr.irrigation.entity.vo.StWasRVo;
import com.jhdr.irrigation.mapper.StWasRMapper;
import com.jhdr.irrigation.service.IStWasRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 堰闸水情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-21
 */
@Service
public class StWasRServiceImpl extends ServiceImpl<StWasRMapper, StWasRPo> implements IStWasRService {


    /**
     * 查询堰闸水情
     *
     * @param STCD 堰闸水情主键
     * @return 堰闸水情
     */
    @Override
    public StWasRVo selectStWasRBySTCD(String STCD)
    {
        return baseMapper.selectStWasRBySTCD(STCD);
    }

    /**
     * 查询堰闸水情列表
     *
     * @param stWasR 堰闸水情
     * @return 堰闸水情
     */
    @Override
    public List<StWasRVo> selectStWasRList(StWasRParam stWasR)
    {
        return baseMapper.selectStWasRList(stWasR);
    }

    /**
     * 新增堰闸水情
     *
     * @param stWasRAddParam 堰闸水情
     * @return 结果
     */
    @Override
    public int insertStWasR(StWasRAddParam stWasRAddParam)
    {

        StWasRPo stWasR=new StWasRPo();
        BeanUtil.copyProperties(stWasRAddParam,stWasR);
        return baseMapper.insert(stWasR);
    }

    /**
     * 修改堰闸水情
     *
     * @param stWasREditParam 堰闸水情
     * @return 结果
     */
    @Override
    public int updateStWasR(StWasREditParam stWasREditParam)
    {
        StWasRPo stWasR=new StWasRPo();
        BeanUtil.copyProperties(stWasREditParam,stWasR);
        return baseMapper.updateById(stWasR);
    }

    /**
     * 批量删除堰闸水情
     *
     * @param STCDs 需要删除的堰闸水情主键
     * @return 结果
     */
    @Override
    public int deleteStWasRBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteStWasRBySTCDs(STCDs);
    }

    /**
     * 删除堰闸水情信息
     *
     * @param STCD 堰闸水情主键
     * @return 结果
     */
    @Override
    public int deleteStWasRBySTCD(String STCD)
    {
        return baseMapper.deleteStWasRBySTCD(STCD);
    }
}
