package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.vo.JhirPustSummaryVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhirPustBPo;
import com.jhdr.irrigation.entity.param.JhirPustBParam;
import com.jhdr.irrigation.entity.param.JhirPustBAddParam;
import com.jhdr.irrigation.entity.param.JhirPustBEditParam;
import com.jhdr.irrigation.entity.vo.JhirPustBVo;
import com.jhdr.irrigation.mapper.JhirPustBMapper;
import com.jhdr.irrigation.service.IJhirPustBService;

import java.util.ArrayList;

import java.util.List;

/**
 * 泵站基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class JhirPustBServiceImpl extends ServiceImpl<JhirPustBMapper, JhirPustBPo> implements IJhirPustBService {

    @Override
    public List<JhirPustBVo> queryList(JhirPustBPo jhirPustB) {
        LambdaQueryWrapper<JhirPustBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhirPustB.getStrName())){
            lqw.like(JhirPustBPo::getStrName ,jhirPustB.getStrName());
        }
        if (StringUtils.isNotBlank(jhirPustB.getPustType())){
            lqw.eq(JhirPustBPo::getPustType ,jhirPustB.getPustType());
        }
        if (StringUtils.isNotBlank(jhirPustB.getAddress())){
            lqw.eq(JhirPustBPo::getAddress ,jhirPustB.getAddress());
        }
        if (StringUtils.isNotBlank(jhirPustB.getEnWait())){
            lqw.eq(JhirPustBPo::getEnWait ,jhirPustB.getEnWait());
        }
        if (StringUtils.isNotBlank(jhirPustB.getEnSize())){
            lqw.eq(JhirPustBPo::getEnSize ,jhirPustB.getEnSize());
        }
        if (StringUtils.isNotBlank(jhirPustB.getImpType())){
            lqw.eq(JhirPustBPo::getImpType ,jhirPustB.getImpType());
        }
        if (StringUtils.isNotBlank(jhirPustB.getCompleteTime())){
            lqw.eq(JhirPustBPo::getCompleteTime ,jhirPustB.getCompleteTime());
        }
        if (StringUtils.isNotBlank(jhirPustB.getInsFlow())){
            lqw.eq(JhirPustBPo::getInsFlow ,jhirPustB.getInsFlow());
        }
        if (StringUtils.isNotBlank(jhirPustB.getPumpNum())){
            lqw.eq(JhirPustBPo::getPumpNum ,jhirPustB.getPumpNum());
        }
        if (StringUtils.isNotBlank(jhirPustB.getDesFlow())){
            lqw.eq(JhirPustBPo::getDesFlow ,jhirPustB.getDesFlow());
        }
        if (StringUtils.isNotBlank(jhirPustB.getDesHead())){
            lqw.eq(JhirPustBPo::getDesHead ,jhirPustB.getDesHead());
        }
        if (StringUtils.isNotBlank(jhirPustB.getHeadwaters())){
            lqw.eq(JhirPustBPo::getHeadwaters ,jhirPustB.getHeadwaters());
        }
        if (StringUtils.isNotBlank(jhirPustB.getDeviseIrr())){
            lqw.eq(JhirPustBPo::getDeviseIrr ,jhirPustB.getDeviseIrr());
        }
        if (StringUtils.isNotBlank(jhirPustB.getDeviseDra())){
            lqw.eq(JhirPustBPo::getDeviseDra ,jhirPustB.getDeviseDra());
        }
        if (StringUtils.isNotBlank(jhirPustB.getDeviseIrrFlow())){
            lqw.eq(JhirPustBPo::getDeviseIrrFlow ,jhirPustB.getDeviseIrrFlow());
        }
        if (StringUtils.isNotBlank(jhirPustB.getKishibetsu())){
            lqw.eq(JhirPustBPo::getKishibetsu ,jhirPustB.getKishibetsu());
        }
        if (StringUtils.isNotBlank(jhirPustB.getSelfIrr())){
            lqw.eq(JhirPustBPo::getSelfIrr ,jhirPustB.getSelfIrr());
        }
        if (StringUtils.isNotBlank(jhirPustB.getPumpIrr())){
            lqw.eq(JhirPustBPo::getPumpIrr ,jhirPustB.getPumpIrr());
        }
        if (StringUtils.isNotBlank(jhirPustB.getIrrType())){
            lqw.eq(JhirPustBPo::getIrrType ,jhirPustB.getIrrType());
        }
        if (StringUtils.isNotBlank(jhirPustB.getInstallType())){
            lqw.eq(JhirPustBPo::getInstallType ,jhirPustB.getInstallType());
        }
        if (StringUtils.isNotBlank(jhirPustB.getLatd())){
            lqw.eq(JhirPustBPo::getLatd ,jhirPustB.getLatd());
        }
        if (StringUtils.isNotBlank(jhirPustB.getLgtd())){
            lqw.eq(JhirPustBPo::getLgtd ,jhirPustB.getLgtd());
        }
        if (StringUtils.isNotBlank(jhirPustB.getInstaCapicaty())){
            lqw.eq(JhirPustBPo::getInstaCapicaty ,jhirPustB.getInstaCapicaty());
        }
        if (jhirPustB.getDesInEl() != null){
            lqw.eq(JhirPustBPo::getDesInEl ,jhirPustB.getDesInEl());
        }
        if (jhirPustB.getTopInEl() != null){
            lqw.eq(JhirPustBPo::getTopInEl ,jhirPustB.getTopInEl());
        }
        if (jhirPustB.getLowInEl() != null){
            lqw.eq(JhirPustBPo::getLowInEl ,jhirPustB.getLowInEl());
        }
        if (jhirPustB.getNormalInEl() != null){
            lqw.eq(JhirPustBPo::getNormalInEl ,jhirPustB.getNormalInEl());
        }
        if (jhirPustB.getDesOutEl() != null){
            lqw.eq(JhirPustBPo::getDesOutEl ,jhirPustB.getDesOutEl());
        }
        if (jhirPustB.getTopOutEl() != null){
            lqw.eq(JhirPustBPo::getTopOutEl ,jhirPustB.getTopOutEl());
        }
        if (jhirPustB.getLowOutEl() != null){
            lqw.eq(JhirPustBPo::getLowOutEl ,jhirPustB.getLowOutEl());
        }
        if (jhirPustB.getNormalOutEl() != null){
            lqw.eq(JhirPustBPo::getNormalOutEl ,jhirPustB.getNormalOutEl());
        }
        if (StringUtils.isNotBlank(jhirPustB.getNote())){
            lqw.eq(JhirPustBPo::getNote ,jhirPustB.getNote());
        }
        if (StringUtils.isNotBlank(jhirPustB.getUrl())){
            lqw.eq(JhirPustBPo::getUrl ,jhirPustB.getUrl());
        }
        if (StringUtils.isNotBlank(jhirPustB.getZpll())){
            lqw.eq(JhirPustBPo::getZpll ,jhirPustB.getZpll());
        }
        if (StringUtils.isNotBlank(jhirPustB.getCpll())){
            lqw.eq(JhirPustBPo::getCpll ,jhirPustB.getCpll());
        }
        if (StringUtils.isNotBlank(jhirPustB.getAddvcd())){
            lqw.eq(JhirPustBPo::getAddvcd ,jhirPustB.getAddvcd());
        }
        if (StringUtils.isNotBlank(jhirPustB.getPidCode())){
            lqw.eq(JhirPustBPo::getPidCode ,jhirPustB.getPidCode());
        }
        if (StringUtils.isNotBlank(jhirPustB.getVmBCd())){
            lqw.eq(JhirPustBPo::getVmBCd ,jhirPustB.getVmBCd());
        }
        if (jhirPustB.getWwZ() != null){
            lqw.eq(JhirPustBPo::getWwZ ,jhirPustB.getWwZ());
        }
        if (jhirPustB.getSwZ() != null){
            lqw.eq(JhirPustBPo::getSwZ ,jhirPustB.getSwZ());
        }
        if (StringUtils.isNotBlank(jhirPustB.getRaincd())){
            lqw.eq(JhirPustBPo::getRaincd ,jhirPustB.getRaincd());
        }
        if (StringUtils.isNotBlank(jhirPustB.getWdwcd())){
            lqw.eq(JhirPustBPo::getWdwcd ,jhirPustB.getWdwcd());
        }
        if (StringUtils.isNotBlank(jhirPustB.getWupcd())){
            lqw.eq(JhirPustBPo::getWupcd ,jhirPustB.getWupcd());
        }
        if (StringUtils.isNotBlank(jhirPustB.getHisMaxZ())){
            lqw.eq(JhirPustBPo::getHisMaxZ ,jhirPustB.getHisMaxZ());
        }
        if (StringUtils.isNotBlank(jhirPustB.getAllAvgZ())){
            lqw.eq(JhirPustBPo::getAllAvgZ ,jhirPustB.getAllAvgZ());
        }
        if (StringUtils.isNotBlank(jhirPustB.getStDesc())){
            lqw.eq(JhirPustBPo::getStDesc ,jhirPustB.getStDesc());
        }
        if (jhirPustB.getHisRt() != null){
            lqw.eq(JhirPustBPo::getHisRt ,jhirPustB.getHisRt());
        }
        if (jhirPustB.getSumRt() != null){
            lqw.eq(JhirPustBPo::getSumRt ,jhirPustB.getSumRt());
        }
        if (jhirPustB.getNowRt() != null){
            lqw.eq(JhirPustBPo::getNowRt ,jhirPustB.getNowRt());
        }
        if (jhirPustB.getHisFw() != null){
            lqw.eq(JhirPustBPo::getHisFw ,jhirPustB.getHisFw());
        }
        if (jhirPustB.getSumFw() != null){
            lqw.eq(JhirPustBPo::getSumFw ,jhirPustB.getSumFw());
        }
        if (jhirPustB.getNowFw() != null){
            lqw.eq(JhirPustBPo::getNowFw ,jhirPustB.getNowFw());
        }
        List<JhirPustBVo> jhirPustBVos= BeanUtil.copyToList(this.list(lqw), JhirPustBVo.class);
        return jhirPustBVos;
    }
    /**
     * 查询泵站基本信息
     *
     * @param strCode 泵站基本信息主键
     * @return 泵站基本信息
     */
    @Override
    public JhirPustBVo selectJhirPustBByStrCode(String strCode)
    {
        return baseMapper.selectJhirPustBByStrCode(strCode);
    }

    /**
     * 查询泵站基本信息列表
     *
     * @param jhirPustB 泵站基本信息
     * @return 泵站基本信息
     */
    @Override
    public List<JhirPustBVo> selectJhirPustBList(JhirPustBParam jhirPustB)
    {
        return baseMapper.selectJhirPustBList(jhirPustB);
    }

    /**
     * 新增泵站基本信息
     *
     * @param jhirPustBAddParam 泵站基本信息
     * @return 结果
     */
    @Override
    public int insertJhirPustB(JhirPustBAddParam jhirPustBAddParam)
    {

        JhirPustBPo jhirPustB=new JhirPustBPo();
        BeanUtil.copyProperties(jhirPustBAddParam,jhirPustB);
        return baseMapper.insert(jhirPustB);
    }

    /**
     * 修改泵站基本信息
     *
     * @param jhirPustBEditParam 泵站基本信息
     * @return 结果
     */
    @Override
    public int updateJhirPustB(JhirPustBEditParam jhirPustBEditParam)
    {
        JhirPustBPo jhirPustB=new JhirPustBPo();
        BeanUtil.copyProperties(jhirPustBEditParam,jhirPustB);
        return baseMapper.updateById(jhirPustB);
    }

    /**
     * 批量删除泵站基本信息
     *
     * @param strCodes 需要删除的泵站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirPustBByStrCodes(String[] strCodes)
    {
        return baseMapper.deleteJhirPustBByStrCodes(strCodes);
    }

    /**
     * 删除泵站基本信息信息
     *
     * @param strCode 泵站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirPustBByStrCode(String strCode)
    {
        return baseMapper.deleteJhirPustBByStrCode(strCode);
    }

    @Override
    public JhirPustSummaryVo selectPustNumByGis() {
        return baseMapper.selectPustNumByGis();
    }
}
