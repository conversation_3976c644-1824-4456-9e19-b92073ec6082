package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationProcessStandardPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessStandardEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessStandardVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 工程运行-标准化Service接口
 *
 * <AUTHOR>
 * @date 2024-09-26
 */
public interface IIrrigationProcessStandardService extends IService<IrrigationProcessStandardPo>
{

    /**
     * 查询工程运行-标准化列表
     *
     * @param irrigationProcessStandard 工程运行-标准化
     * @return 工程运行-标准化集合
     */
    public List<IrrigationProcessStandardVo> queryList(IrrigationProcessStandardPo irrigationProcessStandard);

    /**
     * 查询工程运行-标准化
     *
     * @param standardId 工程运行-标准化主键
     * @return 工程运行-标准化
     */
    public IrrigationProcessStandardVo selectIrrigationProcessStandardByStandardId(Long standardId);

    /**
     * 查询工程运行-标准化列表
     *
     * @param irrigationProcessStandard 工程运行-标准化
     * @return 工程运行-标准化集合
     */
    public List<IrrigationProcessStandardVo> selectIrrigationProcessStandardList(IrrigationProcessStandardParam irrigationProcessStandard);

    /**
     * 新增工程运行-标准化
     *
     * @param irrigationProcessStandard 工程运行-标准化
     * @return 结果
     */
    public int insertIrrigationProcessStandard(IrrigationProcessStandardAddParam irrigationProcessStandard);

    /**
     * 修改工程运行-标准化
     *
     * @param irrigationProcessStandard 工程运行-标准化
     * @return 结果
     */
    public int updateIrrigationProcessStandard(IrrigationProcessStandardEditParam irrigationProcessStandard);

    /**
     * 批量删除工程运行-标准化
     *
     * @param standardIds 需要删除的工程运行-标准化主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessStandardByStandardIds(Long[] standardIds);

    /**
     * 删除工程运行-标准化信息
     *
     * @param standardId 工程运行-标准化主键
     * @return 结果
     */
    public int deleteIrrigationProcessStandardByStandardId(Long standardId);

}
