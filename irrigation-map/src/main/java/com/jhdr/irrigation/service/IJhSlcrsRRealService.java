package com.jhdr.irrigation.service;

import java.math.BigDecimal;
import java.util.List;

import com.jhdr.irrigation.entity.param.IrrigationProcessWorkParam;
import com.jhdr.irrigation.entity.po.JhSlcrsRRealPo;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRRealEditParam;
import com.jhdr.irrigation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 闸站运行状况快照Service接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface IJhSlcrsRRealService extends IService<JhSlcrsRRealPo>
{



    /**
     * 查询闸站运行状况快照
     *
     * @param prjnmcd 闸站运行状况快照主键
     * @return 闸站运行状况快照
     */
    public JhSlcrsRRealVo selectJhSlcrsRRealByPrjnmcd(String prjnmcd);

    /**
     * 查询闸站运行状况快照列表
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 闸站运行状况快照集合
     */
    public List<JhSlcrsRRealVo> selectJhSlcrsRRealList(JhSlcrsRRealParam jhSlcrsRReal);

    /**
     * 新增闸站运行状况快照
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 结果
     */
    public int insertJhSlcrsRReal(JhSlcrsRRealAddParam jhSlcrsRReal);

    /**
     * 修改闸站运行状况快照
     *
     * @param jhSlcrsRReal 闸站运行状况快照
     * @return 结果
     */
    public int updateJhSlcrsRReal(JhSlcrsRRealEditParam jhSlcrsRReal);

    /**
     * 批量删除闸站运行状况快照
     *
     * @param prjnmcds 需要删除的闸站运行状况快照主键集合
     * @return 结果
     */
    public int deleteJhSlcrsRRealByPrjnmcds(String[] prjnmcds);

    /**
     * 删除闸站运行状况快照信息
     *
     * @param prjnmcd 闸站运行状况快照主键
     * @return 结果
     */
    public int deleteJhSlcrsRRealByPrjnmcd(String prjnmcd);

    /**
     * 获取闸站运行状况明细快照
     * @param prjnmcd
     * @return
     */
    List<JhSlcrsDtlRInfoVo> getSlcrsRRealInfoVos(String prjnmcd);

    /**
     * 获取闸站开启数量
     * @return
     */
    Integer getGateStationOpenNum();

    /**
     * 获取工情-闸站 站点启动信息
     * @param
     * @return
     */
    List<EngineerGateStationCountVo> engineerGateStationCount();

    /**
     * 获取临时数据
     * @param prjnmcd
     * @return
     */
    JhSlcrsRRealTemporaryDataVo temporaryData(String prjnmcd);

    List<IrrigationProcessGateVo> gateList(IrrigationProcessWorkParam processWorkParam);

    BigDecimal yearAccq(String prjnmcd);
}
