package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhFlowspRPo;
import com.jhdr.irrigation.entity.param.JhFlowspRParam;
import com.jhdr.irrigation.entity.param.JhFlowspRAddParam;
import com.jhdr.irrigation.entity.param.JhFlowspREditParam;
import com.jhdr.irrigation.entity.vo.JhFlowspRVo;
import com.jhdr.irrigation.mapper.JhFlowspRMapper;
import com.jhdr.irrigation.service.IJhFlowspRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 流量站时序数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Service
public class JhFlowspRServiceImpl extends ServiceImpl<JhFlowspRMapper, JhFlowspRPo> implements IJhFlowspRService {

    @Override
    public List<JhFlowspRVo> queryList(JhFlowspRPo jhFlowspR) {
        LambdaQueryWrapper<JhFlowspRPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhFlowspR.getSTCD())){
            lqw.eq(JhFlowspRPo::getSTCD ,jhFlowspR.getSTCD());
        }
        if (jhFlowspR.getTM() != null){
            lqw.eq(JhFlowspRPo::getTM ,jhFlowspR.getTM());
        }
        if (StringUtils.isNotBlank(jhFlowspR.getQTYPE())){
            lqw.eq(JhFlowspRPo::getQTYPE ,jhFlowspR.getQTYPE());
        }
        if (jhFlowspR.getPSQ() != null){
            lqw.eq(JhFlowspRPo::getPSQ ,jhFlowspR.getPSQ());
        }
        List<JhFlowspRVo> jhFlowspRVos= BeanUtil.copyToList(this.list(lqw), JhFlowspRVo.class);
        return jhFlowspRVos;
    }
    /**
     * 查询流量站时序数据
     *
     * @param STCD 流量站时序数据主键
     * @return 流量站时序数据
     */
    @Override
    public JhFlowspRVo selectJhFlowspRBySTCD(String STCD)
    {
        return baseMapper.selectJhFlowspRBySTCD(STCD);
    }

    /**
     * 查询流量站时序数据列表
     *
     * @param jhFlowspR 流量站时序数据
     * @return 流量站时序数据
     */
    @Override
    public List<JhFlowspRVo> selectJhFlowspRList(JhFlowspRParam jhFlowspR)
    {
        return baseMapper.selectJhFlowspRList(jhFlowspR);
    }

    /**
     * 新增流量站时序数据
     *
     * @param jhFlowspRAddParam 流量站时序数据
     * @return 结果
     */
    @Override
    public int insertJhFlowspR(JhFlowspRAddParam jhFlowspRAddParam)
    {

        JhFlowspRPo jhFlowspR=new JhFlowspRPo();
        BeanUtil.copyProperties(jhFlowspRAddParam,jhFlowspR);
        return baseMapper.insert(jhFlowspR);
    }

    /**
     * 修改流量站时序数据
     *
     * @param jhFlowspREditParam 流量站时序数据
     * @return 结果
     */
    @Override
    public int updateJhFlowspR(JhFlowspREditParam jhFlowspREditParam)
    {
        JhFlowspRPo jhFlowspR=new JhFlowspRPo();
        BeanUtil.copyProperties(jhFlowspREditParam,jhFlowspR);
        return baseMapper.updateById(jhFlowspR);
    }

    /**
     * 批量删除流量站时序数据
     *
     * @param STCDs 需要删除的流量站时序数据主键
     * @return 结果
     */
    @Override
    public int deleteJhFlowspRBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteJhFlowspRBySTCDs(STCDs);
    }

    /**
     * 删除流量站时序数据信息
     *
     * @param STCD 流量站时序数据主键
     * @return 结果
     */
    @Override
    public int deleteJhFlowspRBySTCD(String STCD)
    {
        return baseMapper.deleteJhFlowspRBySTCD(STCD);
    }
}
