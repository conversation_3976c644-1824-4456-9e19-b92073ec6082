package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationProcessFilePo;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessFileEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessFileVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 工程运行-文件管理Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IIrrigationProcessFileService extends IService<IrrigationProcessFilePo>
{



    /**
     * 查询工程运行-文件管理
     *
     * @param id 工程运行-文件管理主键
     * @return 工程运行-文件管理
     */
    public IrrigationProcessFileVo selectIrrigationProcessFileById(Long id);

    /**
     * 查询工程运行-文件管理列表
     *
     * @param irrigationProcessFile 工程运行-文件管理
     * @return 工程运行-文件管理集合
     */
    public List<IrrigationProcessFileVo> selectIrrigationProcessFileList(IrrigationProcessFileParam irrigationProcessFile);

    /**
     * 新增工程运行-文件管理
     *
     * @param irrigationProcessFile 工程运行-文件管理
     * @return 结果
     */
    public int insertIrrigationProcessFile(IrrigationProcessFileAddParam irrigationProcessFile);

    /**
     * 修改工程运行-文件管理
     *
     * @param irrigationProcessFile 工程运行-文件管理
     * @return 结果
     */
    public int updateIrrigationProcessFile(IrrigationProcessFileEditParam irrigationProcessFile);

    /**
     * 批量删除工程运行-文件管理
     *
     * @param ids 需要删除的工程运行-文件管理主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessFileByIds(Long[] ids);

    /**
     * 删除工程运行-文件管理信息
     *
     * @param id 工程运行-文件管理主键
     * @return 结果
     */
    public int deleteIrrigationProcessFileById(Long id);

}
