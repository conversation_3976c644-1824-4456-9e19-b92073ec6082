package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.StRvfcchBPo;
import com.jhdr.irrigation.entity.param.StRvfcchBParam;
import com.jhdr.irrigation.entity.param.StRvfcchBAddParam;
import com.jhdr.irrigation.entity.param.StRvfcchBEditParam;
import com.jhdr.irrigation.entity.vo.StRvfcchBVo;
import com.jhdr.irrigation.mapper.StRvfcchBMapper;
import com.jhdr.irrigation.service.IStRvfcchBService;

import java.util.ArrayList;

import java.util.List;

/**
 * 河道防洪指标Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Service
public class StRvfcchBServiceImpl extends ServiceImpl<StRvfcchBMapper, StRvfcchBPo> implements IStRvfcchBService {

    @Override
    public List<StRvfcchBVo> queryList(StRvfcchBPo stRvfcchB) {
        LambdaQueryWrapper<StRvfcchBPo> lqw = Wrappers.lambdaQuery();
        if (stRvfcchB.getLDKEL() != null){
            lqw.eq(StRvfcchBPo::getLDKEL ,stRvfcchB.getLDKEL());
        }
        if (stRvfcchB.getRDKEL() != null){
            lqw.eq(StRvfcchBPo::getRDKEL ,stRvfcchB.getRDKEL());
        }
        if (stRvfcchB.getWRZ() != null){
            lqw.eq(StRvfcchBPo::getWRZ ,stRvfcchB.getWRZ());
        }
        if (stRvfcchB.getWRQ() != null){
            lqw.eq(StRvfcchBPo::getWRQ ,stRvfcchB.getWRQ());
        }
        if (stRvfcchB.getGRZ() != null){
            lqw.eq(StRvfcchBPo::getGRZ ,stRvfcchB.getGRZ());
        }
        if (stRvfcchB.getGRQ() != null){
            lqw.eq(StRvfcchBPo::getGRQ ,stRvfcchB.getGRQ());
        }
        if (stRvfcchB.getFLPQ() != null){
            lqw.eq(StRvfcchBPo::getFLPQ ,stRvfcchB.getFLPQ());
        }
        if (stRvfcchB.getOBHTZ() != null){
            lqw.eq(StRvfcchBPo::getOBHTZ ,stRvfcchB.getOBHTZ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getOBHTZTM())){
            lqw.eq(StRvfcchBPo::getOBHTZTM ,stRvfcchB.getOBHTZTM());
        }
        if (stRvfcchB.getIVHZ() != null){
            lqw.eq(StRvfcchBPo::getIVHZ ,stRvfcchB.getIVHZ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getIVHZTM())){
            lqw.eq(StRvfcchBPo::getIVHZTM ,stRvfcchB.getIVHZTM());
        }
        if (stRvfcchB.getOBMXQ() != null){
            lqw.eq(StRvfcchBPo::getOBMXQ ,stRvfcchB.getOBMXQ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getOBMXQTM())){
            lqw.eq(StRvfcchBPo::getOBMXQTM ,stRvfcchB.getOBMXQTM());
        }
        if (stRvfcchB.getIVMXQ() != null){
            lqw.eq(StRvfcchBPo::getIVMXQ ,stRvfcchB.getIVMXQ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getIVMXQTM())){
            lqw.eq(StRvfcchBPo::getIVMXQTM ,stRvfcchB.getIVMXQTM());
        }
        if (stRvfcchB.getHMXS() != null){
            lqw.eq(StRvfcchBPo::getHMXS ,stRvfcchB.getHMXS());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getHMXSTM())){
            lqw.eq(StRvfcchBPo::getHMXSTM ,stRvfcchB.getHMXSTM());
        }
        if (stRvfcchB.getHMXAVV() != null){
            lqw.eq(StRvfcchBPo::getHMXAVV ,stRvfcchB.getHMXAVV());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getHMXAVVTM())){
            lqw.eq(StRvfcchBPo::getHMXAVVTM ,stRvfcchB.getHMXAVVTM());
        }
        if (stRvfcchB.getHLZ() != null){
            lqw.eq(StRvfcchBPo::getHLZ ,stRvfcchB.getHLZ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getHLZTM())){
            lqw.eq(StRvfcchBPo::getHLZTM ,stRvfcchB.getHLZTM());
        }
        if (stRvfcchB.getHMNQ() != null){
            lqw.eq(StRvfcchBPo::getHMNQ ,stRvfcchB.getHMNQ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getHMNQTM())){
            lqw.eq(StRvfcchBPo::getHMNQTM ,stRvfcchB.getHMNQTM());
        }
        if (stRvfcchB.getTAZ() != null){
            lqw.eq(StRvfcchBPo::getTAZ ,stRvfcchB.getTAZ());
        }
        if (stRvfcchB.getTAQ() != null){
            lqw.eq(StRvfcchBPo::getTAQ ,stRvfcchB.getTAQ());
        }
        if (stRvfcchB.getLAZ() != null){
            lqw.eq(StRvfcchBPo::getLAZ ,stRvfcchB.getLAZ());
        }
        if (stRvfcchB.getLAQ() != null){
            lqw.eq(StRvfcchBPo::getLAQ ,stRvfcchB.getLAQ());
        }
        if (stRvfcchB.getSFZ() != null){
            lqw.eq(StRvfcchBPo::getSFZ ,stRvfcchB.getSFZ());
        }
        if (stRvfcchB.getSFQ() != null){
            lqw.eq(StRvfcchBPo::getSFQ ,stRvfcchB.getSFQ());
        }
        if (StringUtils.isNotBlank(stRvfcchB.getMODITIME())){
            lqw.eq(StRvfcchBPo::getMODITIME ,stRvfcchB.getMODITIME());
        }
        List<StRvfcchBVo> stRvfcchBVos= BeanUtil.copyToList(this.list(lqw), StRvfcchBVo.class);
        return stRvfcchBVos;
    }
    /**
     * 查询河道防洪指标
     *
     * @param STCD 河道防洪指标主键
     * @return 河道防洪指标
     */
    @Override
    public StRvfcchBVo selectStRvfcchBBySTCD(String STCD)
    {
        return baseMapper.selectStRvfcchBBySTCD(STCD);
    }

    /**
     * 查询河道防洪指标列表
     *
     * @param stRvfcchB 河道防洪指标
     * @return 河道防洪指标
     */
    @Override
    public List<StRvfcchBVo> selectStRvfcchBList(StRvfcchBParam stRvfcchB)
    {
        return baseMapper.selectStRvfcchBList(stRvfcchB);
    }

    /**
     * 新增河道防洪指标
     *
     * @param stRvfcchBAddParam 河道防洪指标
     * @return 结果
     */
    @Override
    public int insertStRvfcchB(StRvfcchBAddParam stRvfcchBAddParam)
    {

        StRvfcchBPo stRvfcchB=new StRvfcchBPo();
        BeanUtil.copyProperties(stRvfcchBAddParam,stRvfcchB);
        return baseMapper.insert(stRvfcchB);
    }

    /**
     * 修改河道防洪指标
     *
     * @param stRvfcchBEditParam 河道防洪指标
     * @return 结果
     */
    @Override
    public int updateStRvfcchB(StRvfcchBEditParam stRvfcchBEditParam)
    {
        StRvfcchBPo stRvfcchB=new StRvfcchBPo();
        BeanUtil.copyProperties(stRvfcchBEditParam,stRvfcchB);
        return baseMapper.updateById(stRvfcchB);
    }

    /**
     * 批量删除河道防洪指标
     *
     * @param STCDs 需要删除的河道防洪指标主键
     * @return 结果
     */
    @Override
    public int deleteStRvfcchBBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteStRvfcchBBySTCDs(STCDs);
    }

    /**
     * 删除河道防洪指标信息
     *
     * @param STCD 河道防洪指标主键
     * @return 结果
     */
    @Override
    public int deleteStRvfcchBBySTCD(String STCD)
    {
        return baseMapper.deleteStRvfcchBBySTCD(STCD);
    }
}
