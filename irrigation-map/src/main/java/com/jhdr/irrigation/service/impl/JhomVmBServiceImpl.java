package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jhdr.irrigation.entity.param.JhomVmBAddParam;
import com.jhdr.irrigation.entity.param.JhomVmBEditParam;
import com.jhdr.irrigation.entity.param.JhomVmBParam;
import com.jhdr.irrigation.entity.po.JhomVmBPo;
import com.jhdr.irrigation.entity.vo.JhomVmBVo;
import com.jhdr.irrigation.entity.vo.VideoRegionDataVo;
import com.jhdr.irrigation.mapper.JhomVmBMapper;
import com.jhdr.irrigation.service.IJhomVmBService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频监控站点基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Service
public class JhomVmBServiceImpl extends ServiceImpl<JhomVmBMapper, JhomVmBPo> implements IJhomVmBService {


    /**
     * 查询视频监控站点基础信息
     *
     * @param cd 视频监控站点基础信息主键
     * @return 视频监控站点基础信息
     */
    @Override
    public JhomVmBVo selectJhomVmBByCd(String cd)
    {
        return baseMapper.selectJhomVmBByCd(cd);
    }

    /**
     * 查询视频监控站点基础信息列表
     *
     * @param jhomVmB 视频监控站点基础信息
     * @return 视频监控站点基础信息
     */
    @Override
    public List<JhomVmBVo> selectJhomVmBList(JhomVmBParam jhomVmB)
    {
        return baseMapper.selectJhomVmBList(jhomVmB);
    }

    /**
     * 新增视频监控站点基础信息
     *
     * @param jhomVmBAddParam 视频监控站点基础信息
     * @return 结果
     */
    @Override
    public int insertJhomVmB(JhomVmBAddParam jhomVmBAddParam)
    {

        JhomVmBPo jhomVmB=new JhomVmBPo();
        BeanUtil.copyProperties(jhomVmBAddParam,jhomVmB);
        return baseMapper.insert(jhomVmB);
    }

    /**
     * 修改视频监控站点基础信息
     *
     * @param jhomVmBEditParam 视频监控站点基础信息
     * @return 结果
     */
    @Override
    public int updateJhomVmB(JhomVmBEditParam jhomVmBEditParam)
    {
        JhomVmBPo jhomVmB=new JhomVmBPo();
        BeanUtil.copyProperties(jhomVmBEditParam,jhomVmB);
        return baseMapper.updateById(jhomVmB);
    }

    /**
     * 批量删除视频监控站点基础信息
     *
     * @param cds 需要删除的视频监控站点基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhomVmBByCds(String[] cds)
    {
        return baseMapper.deleteJhomVmBByCds(cds);
    }

    /**
     * 删除视频监控站点基础信息信息
     *
     * @param cd 视频监控站点基础信息主键
     * @return 结果
     */
    @Override
    public int deleteJhomVmBByCd(String cd)
    {
        return baseMapper.deleteJhomVmBByCd(cd);
    }


    @Override
    public List<VideoRegionDataVo> selectRegionTreeList() {
        List<VideoRegionDataVo> video = baseMapper.selectRegionTreeList();
        List<VideoRegionDataVo> menusList = new ArrayList<>();

        for (VideoRegionDataVo vo : video) {
            //设置一级菜单
            if (ObjectUtil.isNotEmpty(vo.getParentIndexCode())&&vo.getParentIndexCode().equals("-1")){
                menusList.add(vo);
            }
            if (ObjectUtil.isNotEmpty(vo)&&ObjectUtil.isNotEmpty(vo.getSource())){
                vo.setName(vo.getName()+(vo.getSource().equals("30")?"[金海迪尔]":"[南瑞]"));
            }
        }

        //设置一级菜单的子集菜单
        for (VideoRegionDataVo menusVo : menusList) {
            menusVo.setChildren(getChildren(menusVo.getIndexCode(),video));

        }
        return menusList;
    }

    @Override
    public Integer onlineNum() {
        return baseMapper.onlineNum();
    }


    private List<VideoRegionDataVo> getChildren(String id, List<VideoRegionDataVo> video) {
        //定义集合
        List<VideoRegionDataVo> childList = new ArrayList<>();
        //遍历子集菜单
        for (VideoRegionDataVo menu : video) {
            //父级id与子集id比较
            if (menu.getParentIndexCode().equals(id)){
                childList.add(menu);
            }
        }
        //循环子集菜单
        for (VideoRegionDataVo menusVo : childList) {
            menusVo.setChildren(getChildren(menusVo.getIndexCode(),video));
        }

        //递归条件退出
        if (childList.size() ==0){
            return null;
        }
        return childList;
    }





}
