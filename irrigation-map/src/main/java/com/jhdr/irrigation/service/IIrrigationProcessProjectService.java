package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationProcessProjectPo;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectAddParam;
import com.jhdr.irrigation.entity.param.IrrigationProcessProjectEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationProcessProjectInfoVo;
import com.jhdr.irrigation.entity.vo.IrrigationProcessProjectVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 工程运行-项目管理Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IIrrigationProcessProjectService extends IService<IrrigationProcessProjectPo>
{



    /**
     * 查询工程运行-项目管理
     *
     * @param id 工程运行-项目管理主键
     * @return 工程运行-项目管理
     */
    public IrrigationProcessProjectInfoVo selectIrrigationProcessProjectById(Long id);

    /**
     * 查询工程运行-项目管理列表
     *
     * @param irrigationProcessProject 工程运行-项目管理
     * @return 工程运行-项目管理集合
     */
    public List<IrrigationProcessProjectVo> selectIrrigationProcessProjectList(IrrigationProcessProjectParam irrigationProcessProject);

    /**
     * 新增工程运行-项目管理
     *
     * @param irrigationProcessProject 工程运行-项目管理
     * @return 结果
     */
    public int insertIrrigationProcessProject(IrrigationProcessProjectAddParam irrigationProcessProject);

    /**
     * 修改工程运行-项目管理
     *
     * @param irrigationProcessProject 工程运行-项目管理
     * @return 结果
     */
    public int updateIrrigationProcessProject(IrrigationProcessProjectEditParam irrigationProcessProject);

    /**
     * 批量删除工程运行-项目管理
     *
     * @param ids 需要删除的工程运行-项目管理主键集合
     * @return 结果
     */
    public int deleteIrrigationProcessProjectByIds(Long[] ids);

    /**
     * 删除工程运行-项目管理信息
     *
     * @param id 工程运行-项目管理主键
     * @return 结果
     */
    public int deleteIrrigationProcessProjectById(Long id);

}
