package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.StPptnRPo;
import com.jhdr.irrigation.entity.param.StPptnRParam;
import com.jhdr.irrigation.entity.param.StPptnRAddParam;
import com.jhdr.irrigation.entity.param.StPptnREditParam;
import com.jhdr.irrigation.entity.vo.StPptnRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 降水量Service接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface IStPptnRService extends IService<StPptnRPo>
{

    /**
     * 查询降水量列表
     *
     * @param stPptnR 降水量
     * @return 降水量集合
     */
    public List<StPptnRVo> queryList(StPptnRPo stPptnR);

    /**
     * 查询降水量
     *
     * @param STCD 降水量主键
     * @return 降水量
     */
    public StPptnRVo selectStPptnRBySTCD(String STCD);

    /**
     * 查询降水量列表
     *
     * @param stPptnR 降水量
     * @return 降水量集合
     */
    public List<StPptnRVo> selectStPptnRList(StPptnRParam stPptnR);

    /**
     * 新增降水量
     *
     * @param stPptnR 降水量
     * @return 结果
     */
    public int insertStPptnR(StPptnRAddParam stPptnR);

    /**
     * 修改降水量
     *
     * @param stPptnR 降水量
     * @return 结果
     */
    public int updateStPptnR(StPptnREditParam stPptnR);

    /**
     * 批量删除降水量
     *
     * @param STCDs 需要删除的降水量主键集合
     * @return 结果
     */
    public int deleteStPptnRBySTCDs(String[] STCDs);

    /**
     * 删除降水量信息
     *
     * @param STCD 降水量主键
     * @return 结果
     */
    public int deleteStPptnRBySTCD(String STCD);

}
