package com.jhdr.irrigation.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.jhdr.irrigation.entity.po.JhFlowycRPo;
import com.jhdr.irrigation.entity.param.JhFlowycRParam;
import com.jhdr.irrigation.entity.param.JhFlowycRAddParam;
import com.jhdr.irrigation.entity.param.JhFlowycREditParam;
import com.jhdr.irrigation.entity.vo.FlowCrossSectionVo;
import com.jhdr.irrigation.entity.vo.JhFlowtnRVo;
import com.jhdr.irrigation.entity.vo.JhFlowycRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 原始水量数据Service接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface IJhFlowycRService extends IService<JhFlowycRPo>
{



    /**
     * 查询原始水量数据
     *
     * @param STCD 原始水量数据主键
     * @return 原始水量数据
     */
    public JhFlowycRVo selectJhFlowycRBySTCD(String STCD);

    /**
     * 查询原始水量数据列表
     *
     * @param jhFlowycR 原始水量数据
     * @return 原始水量数据集合
     */
    public List<JhFlowtnRVo> selectJhFlowycRList(JhFlowycRParam jhFlowycR);

    /**
     * 新增原始水量数据
     *
     * @param jhFlowycR 原始水量数据
     * @return 结果
     */
    public int insertJhFlowycR(JhFlowycRAddParam jhFlowycR);

    /**
     * 修改原始水量数据
     *
     * @param jhFlowycR 原始水量数据
     * @return 结果
     */
    public int updateJhFlowycR(JhFlowycREditParam jhFlowycR);

    /**
     * 批量删除原始水量数据
     *
     * @param STCDs 需要删除的原始水量数据主键集合
     * @return 结果
     */
    public int deleteJhFlowycRBySTCDs(String[] STCDs);

    /**
     * 删除原始水量数据信息
     *
     * @param STCD 原始水量数据主键
     * @return 结果
     */
    public int deleteJhFlowycRBySTCD(String STCD);

    /**
     * 获取流量站日水情
     * @param stcd
     * @return
     */
    JhFlowtnRVo getOnePsqTn(String stcd);

    /**
     * 获取流量站断面信息
     * @param stcd
     * @return
     */
    List<FlowCrossSectionVo> selectFlowCrossSectionList(String stcd);

    BigDecimal yearAccq(String stcd);

    //月度流量
    List<JhFlowtnRVo> selectJhFlowycRMonthList(JhFlowycRParam jhFlowycRParam);

    Map<String, BigDecimal> batchYearAccq(List<String> cdList);
}
