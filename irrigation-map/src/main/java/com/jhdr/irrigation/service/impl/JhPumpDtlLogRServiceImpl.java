package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhPumpDtlLogRPo;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogRParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogRAddParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlLogREditParam;
import com.jhdr.irrigation.entity.vo.JhPumpDtlLogRVo;
import com.jhdr.irrigation.mapper.JhPumpDtlLogRMapper;
import com.jhdr.irrigation.service.IJhPumpDtlLogRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 泵机运行操作记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class JhPumpDtlLogRServiceImpl extends ServiceImpl<JhPumpDtlLogRMapper, JhPumpDtlLogRPo> implements IJhPumpDtlLogRService {

    @Override
    public List<JhPumpDtlLogRVo> queryList(JhPumpDtlLogRPo jhPumpDtlLogR) {
        LambdaQueryWrapper<JhPumpDtlLogRPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(jhPumpDtlLogR.getClltm())){
            lqw.eq(JhPumpDtlLogRPo::getClltm ,jhPumpDtlLogR.getClltm());
        }
        if (StringUtils.isNotBlank(jhPumpDtlLogR.getOm())){
            lqw.eq(JhPumpDtlLogRPo::getOm ,jhPumpDtlLogR.getOm());
        }
        if (jhPumpDtlLogR.getEe() != null){
            lqw.eq(JhPumpDtlLogRPo::getEe ,jhPumpDtlLogR.getEe());
        }
        if (jhPumpDtlLogR.getQ() != null){
            lqw.eq(JhPumpDtlLogRPo::getQ ,jhPumpDtlLogR.getQ());
        }
        if (StringUtils.isNotBlank(jhPumpDtlLogR.getOperator())){
            lqw.eq(JhPumpDtlLogRPo::getOperator ,jhPumpDtlLogR.getOperator());
        }
        List<JhPumpDtlLogRVo> jhPumpDtlLogRVos= BeanUtil.copyToList(this.list(lqw), JhPumpDtlLogRVo.class);
        return jhPumpDtlLogRVos;
    }
    /**
     * 查询泵机运行操作记录
     *
     * @param prjnmcd 泵机运行操作记录主键
     * @return 泵机运行操作记录
     */
    @Override
    public JhPumpDtlLogRVo selectJhPumpDtlLogRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhPumpDtlLogRByPrjnmcd(prjnmcd);
    }

    /**
     * 查询泵机运行操作记录列表
     *
     * @param jhPumpDtlLogR 泵机运行操作记录
     * @return 泵机运行操作记录
     */
    @Override
    public List<JhPumpDtlLogRVo> selectJhPumpDtlLogRList(JhPumpDtlLogRParam jhPumpDtlLogR)
    {
        return baseMapper.selectJhPumpDtlLogRList(jhPumpDtlLogR);
    }

    /**
     * 新增泵机运行操作记录
     *
     * @param jhPumpDtlLogRAddParam 泵机运行操作记录
     * @return 结果
     */
    @Override
    public int insertJhPumpDtlLogR(JhPumpDtlLogRAddParam jhPumpDtlLogRAddParam)
    {

        JhPumpDtlLogRPo jhPumpDtlLogR=new JhPumpDtlLogRPo();
        BeanUtil.copyProperties(jhPumpDtlLogRAddParam,jhPumpDtlLogR);
        return baseMapper.insert(jhPumpDtlLogR);
    }

    /**
     * 修改泵机运行操作记录
     *
     * @param jhPumpDtlLogREditParam 泵机运行操作记录
     * @return 结果
     */
    @Override
    public int updateJhPumpDtlLogR(JhPumpDtlLogREditParam jhPumpDtlLogREditParam)
    {
        JhPumpDtlLogRPo jhPumpDtlLogR=new JhPumpDtlLogRPo();
        BeanUtil.copyProperties(jhPumpDtlLogREditParam,jhPumpDtlLogR);
        return baseMapper.updateById(jhPumpDtlLogR);
    }

    /**
     * 批量删除泵机运行操作记录
     *
     * @param prjnmcds 需要删除的泵机运行操作记录主键
     * @return 结果
     */
    @Override
    public int deleteJhPumpDtlLogRByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhPumpDtlLogRByPrjnmcds(prjnmcds);
    }

    /**
     * 删除泵机运行操作记录信息
     *
     * @param prjnmcd 泵机运行操作记录主键
     * @return 结果
     */
    @Override
    public int deleteJhPumpDtlLogRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhPumpDtlLogRByPrjnmcd(prjnmcd);
    }
}
