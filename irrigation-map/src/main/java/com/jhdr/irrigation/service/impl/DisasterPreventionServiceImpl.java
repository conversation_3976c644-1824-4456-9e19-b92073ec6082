package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.*;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 水旱灾害防御
 */
@Component
public class DisasterPreventionServiceImpl {
    @Resource
    private IHubBasicInformationService hubBasicInformationService;
    @Autowired
    private IJhSlcrsRService jhSlcrsRService;
    @Resource
    private IJhSlcrsRService slcrsRService;
    @Resource
    private IJhPumpRRealService pumpRRealService;
    @Resource
    private IStPptnRService stPptnRService;
    @Resource
    private IJhomVmBService jhomVmBService;
    @Resource
    private IJhirWagaBService jhirWagaBService;
    @Resource
    private HikvisionVideoTool hikvisionVideoTool;
    @Autowired
    private IJhSlcrsRRealService jhSlcrsRRealService;
    @Autowired
    private IJhPumpDtlRRealService pumpDtlRRealService;
    @Autowired
    private IJhPumpRRealService jhPumpRRealService;
    @Autowired
    private IJhirPustBService jhirPustBService;
    @Autowired
    private IStRiverRService stRiverRService;
    @Resource
    private IStWasRService stWasRService;
    @Autowired
    private IStStbprpBService stStbprpBService;
    @Autowired
    private IStRvfcchBService stRvfcchBService;

    @Autowired
    private IJhFlowycRService jhFlowycRService;
    @Autowired
    private IGisDatasDService gisDatasDService;


    /**
     * 调度运用、地图、河道水清-枢纽信息 汇总
     */
    public List<PreventionHubVo> getHubs() {
        String hubAllNum = "chp001,ch001,kt001,sq001";
        List<PreventionHubVo> hubs = new ArrayList<>();
        for (String hubCd : hubAllNum.split(",")) {
            PreventionHubVo hubVo = getHub(hubCd);
            hubs.add(hubVo);
        }
        return hubs;
    }

    /**
     * 枢纽信息
     */
    public PreventionHubVo getHub(String hubCd) {
        PreventionHubVo hubVo = new PreventionHubVo();

        HubBasicInformationPo hubInfo = hubBasicInformationService.getOne(new QueryWrapper<HubBasicInformationPo>()
                .eq("hub_code", hubCd).last("limit 1"), false);
        if (ObjectUtil.isNotEmpty(hubInfo) && ObjectUtil.isNotEmpty(hubInfo.getGataCode())) {

            String gateCode = hubInfo.getGataCode().toString();
            JhSlcrsRPo slcrsRPo = slcrsRService.getOne(new QueryWrapper<JhSlcrsRPo>()
                    .eq("prjnmcd", gateCode)
                    .orderBy(true, false, "clltm").last("limit 1"), false);
            JhirWagaBPo waga = jhirWagaBService.getById(gateCode);
            hubVo = BeanUtil.toBean(slcrsRPo, PreventionHubVo.class);
            if (hubVo == null) {
                hubVo = new PreventionHubVo();
            }
            hubVo.setAdminArea(hubInfo.getAdminArea());
            hubVo.setLgtd(hubInfo.getLgtd());
            hubVo.setLttd(hubInfo.getLttd());
            hubVo.setGataCode(gateCode);
            hubVo.setPustCode(hubInfo.getPustCode());

            hubVo.setHubStatus("0");
            hubVo.setSldszStatus("0");
            hubVo.setSlupszStatus("0");
            JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(gateCode);
            if (ObjectUtil.isNotEmpty(temporaryData)) {
                hubVo.setSldsz(temporaryData.getSldsz());
                hubVo.setSlupsz(temporaryData.getSlupsz());
                hubVo.setUpswtp(temporaryData.getUpswtp());
                hubVo.setDswtp(temporaryData.getDswtp());
            } else {
                hubVo.setSldsz(null);
                hubVo.setSlupsz(null);
                hubVo.setUpswtp(null);
                hubVo.setDswtp(null);
            }
            if (ObjectUtil.isNotEmpty(waga) && ObjectUtil.isNotEmpty(waga.getWwZ())) {

                if (ObjectUtil.isNotEmpty(hubVo.getSlupsz()) && hubVo.getSlupsz().compareTo(waga.getWwZ()) > 0) {
                    hubVo.setSlupszStatus("1");
                }
                if (ObjectUtil.isNotEmpty(hubVo.getSldsz()) && hubVo.getSldsz().compareTo(waga.getWwZ()) > 0) {
                    hubVo.setSldszStatus("1");
                }
            }
            hubVo.setHubStatus("0");
            if (ObjectUtil.isNotEmpty(hubVo.getGtopn()) && hubVo.getGtopn().compareTo(BigDecimal.ZERO) > 0) {
                hubVo.setHubStatus("1");
            }
        }
        if (ObjectUtil.isNotEmpty(hubInfo)) {
            hubVo.setName(hubInfo.getName());
            hubVo.setHubCode(hubInfo.getHubCode());
        }
        if (ObjectUtil.isNotEmpty(hubInfo) && ObjectUtil.isNotEmpty(hubInfo.getPustCode())) {
            String pustCode = hubInfo.getPustCode();
            JhPumpRRealPo one = pumpRRealService.getOne(new QueryWrapper<JhPumpRRealPo>()
                    .eq("prjnmcd", pustCode)
                    .orderBy(true, false, "clltm").last("limit 1"), false);
            if (one != null) {
                hubVo.setPmpq(one.getPmpq() == null ? new BigDecimal(0) : one.getPmpq());
                hubVo.setOmcn(one.getOmcn());
                hubVo.setPpcn(one.getPpcn());
                if (ObjectUtil.isNotEmpty(one.getOmcn()) && one.getOmcn().compareTo(BigDecimal.ZERO) > 0) {
                    hubVo.setHubStatus("1");
                }
            }
        }

        return hubVo;
    }

    /**
     * 工程信息-枢纽信息
     */
    public List<PreventionProjectVo> getProjectInfo() {

        String hubAllNum = "chp001,ch001,kt001,sq001";
        List<PreventionProjectVo> projectInfos = new ArrayList<>();
        for (String hubCd : hubAllNum.split(",")) {
            PreventionProjectVo hubVo = hubBasicInformationService.getProjectInfo(hubCd);
            projectInfos.add(hubVo);
        }
        return projectInfos;

    }

    /**
     * 视频监控
     */
    public List<PreventionVideoVo> getVideoInfo() {

        List<String> hubCodes = Stream.of("ch001", "kt001", "sq001", "chp001").collect(Collectors.toList());
        List<HubBasicInformationPo> hubs = hubBasicInformationService.list(
                new QueryWrapper<HubBasicInformationPo>().in("hub_code", hubCodes).eq("status", 1));


        if (ObjectUtil.isNotEmpty(hubs)) {
            // 定义hubCode的优先顺序
            Map<String, Integer> orderMap = Collections.unmodifiableMap(new HashMap<String, Integer>() {{
                put("sq001", 1);
                put("kt001", 2);
            }});

// 使用stream对hubs进行排序
            List<HubBasicInformationPo> sortedHubs = hubs.stream()
                    .sorted((h1, h2) -> {
                        int order1 = orderMap.getOrDefault(h1.getHubCode(), Integer.MAX_VALUE);
                        int order2 = orderMap.getOrDefault(h2.getHubCode(), Integer.MAX_VALUE);
                        return Integer.compare(order1, order2);
                    })
                    .collect(Collectors.toList());
            List<PreventionVideoVo> videoInfo = BeanUtil.copyToList(sortedHubs, PreventionVideoVo.class);
            videoInfo.forEach(vo -> {
                if (ObjectUtil.isNotEmpty(vo.getVmBCd())) {
                    vo.setUrl(getVideoUrl(vo.getVmBCd()));
                }
            });
            return videoInfo;
        }

        return null;
    }

    /**
     * 获取视频播放地址
     */
    private String getVideoUrl(String vmBCd) {

        String url = "";
        JhomVmBPo video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                .eq("cd", vmBCd).last("limit 1"), false);
        if (ObjectUtil.isNotEmpty(video) && ObjectUtil.isNotEmpty(video.getCameraCode())) {
            //todo 根据cameraCode获取播放地址
            url = hikvisionVideoTool.getVideoPlayUrl(video.getCameraCode());
        }

        return url;
    }

    //雨量统计-枢纽
    public List<PreventionRainVo> getRainInfos() {

        //枢纽编码 "chp001", "ch001", "kt001", "sq001" "chp001, ch001, kt001, sq001"
        List<String> hubCodes = Stream.of("chp001", "ch001", "kt001", "sq001").collect(Collectors.toList());
        List<PreventionRainVo> rainInfos = hubBasicInformationService.getRainInfos(hubCodes);
        if (ObjectUtil.isNotEmpty(rainInfos)) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            // 创建日期时间格式器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 格式化当前时间
            String nowStr = now.format(formatter);
            // 计算七天后的时间
            LocalDateTime sevenDaysOld = now.minusDays(7);
            // 格式化七天后的时间
            String sevenDaysOldStr = sevenDaysOld.format(formatter);
            rainInfos.forEach(vo -> {
                if (ObjectUtil.isNotEmpty(vo.getHubCode())) {
                    List<StPptnRVo> list = stPptnRService.selectStPptnRList(new StPptnRParam().
                            setSTCD(vo.getRaincd()).setTMStart(sevenDaysOldStr).
                            setTMEnd(nowStr).setType(1));
                    vo.setSevenDays(list);
                }
            });
        }
        return rainInfos;
    }


    /**
     * 水情信息-枢纽
     */
    public List<PreventionWaterVo> getWaterInfos() {
        //枢纽编码 "chp001", "ch001", "kt001", "sq001" "chp001, ch001, kt001, sq001"
        List<String> hubCodes = Stream.of("chp001", "ch001", "kt001", "sq001").collect(Collectors.toList());
        List<PreventionWaterVo> waterInfos = hubBasicInformationService.getWaterInfos(hubCodes);

        if (ObjectUtil.isNotEmpty(waterInfos)) {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            // 创建日期时间格式器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 格式化当前时间
            String nowStr = now.format(formatter);
            // 计算几天后的时间
            LocalDateTime daysOld = now.minusDays(3);
            // 格式化几天后的时间
            String daysOldStr = daysOld.format(formatter);
            waterInfos.forEach(vo -> {
                vo.setWaterVos(jhSlcrsRService.selectJhSlcrsRList(
                        new JhSlcrsRParam().setPrjnmcd(vo.getGataCode())
                                .setClltmStart(daysOldStr).setClltmEnd(nowStr)));
            });
        }

        return waterInfos;
    }

    //超限站点 弹窗
    public List<PreventionWarnVo> getWarnList() {

        List<String> hubCodes = Stream.of("chp001", "ch001", "kt001", "sq001").collect(Collectors.toList());
        List<HubBasicInformationPo> hubs = hubBasicInformationService.list(
                new QueryWrapper<HubBasicInformationPo>().in("hub_code", hubCodes).eq("status", 1));
        List<PreventionWarnVo> warnList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(hubs)) {
            hubs.forEach(vo -> {
                if (ObjectUtil.isNotEmpty(vo) && ObjectUtil.isNotEmpty(vo.getGataCode())) {

                    String gateCode = vo.getGataCode().toString();
                    JhSlcrsRPo slcrsRPo = slcrsRService.getOne(new QueryWrapper<JhSlcrsRPo>()
                            .eq("prjnmcd", gateCode)
                            .orderBy(true, false, "clltm").last("limit 1"), false);
                    JhirWagaBPo waga = jhirWagaBService.getById(gateCode);
                    if (slcrsRPo != null) {
                        if (ObjectUtil.isNotEmpty(waga) && ObjectUtil.isNotEmpty(waga.getWwZ())) {
                            if (ObjectUtil.isNotEmpty(slcrsRPo.getSlupsz()) && slcrsRPo.getSlupsz().compareTo(waga.getWwZ()) > 0) {
                                PreventionWarnVo hubVo = new PreventionWarnVo();
                                hubVo.setId(IdUtil.getSnowflakeNextId());
                                hubVo.setWarnNm(waga.getStrName());
                                hubVo.setStWarnTm(slcrsRPo.getClltm());
                                hubVo.setWarnSummary("闸上水位：" + slcrsRPo.getSlupsz() + "m 超限");
                                hubVo.setWarnStatus("超警戒");
                                warnList.add(hubVo);

                            }
                            if (ObjectUtil.isNotEmpty(slcrsRPo.getSldsz()) && slcrsRPo.getSldsz().compareTo(waga.getWwZ()) > 0) {
                                PreventionWarnVo hubVo = new PreventionWarnVo();
                                hubVo.setId(IdUtil.getSnowflakeNextId());
                                hubVo.setWarnNm(waga.getStrName());
                                hubVo.setStWarnTm(slcrsRPo.getClltm());
                                hubVo.setWarnSummary("闸下水位：" + slcrsRPo.getSldsz() + "m 超限");
                                hubVo.setWarnStatus("超警戒");
                                warnList.add(hubVo);
                            }
                        }
                    }
                }
            });
        }
        return warnList;
    }

    public JhSlcrsRBaseVo getGataBaseInfo(String prjnmcd) {
        //todo 闸站信息逻辑问题
        JhSlcrsRRealVo jhSlcrsRVo = jhSlcrsRRealService.selectJhSlcrsRRealByPrjnmcd(prjnmcd);
        JhSlcrsRBaseVo baseVo = BeanUtil.toBean(jhSlcrsRVo, JhSlcrsRBaseVo.class);
        if (ObjectUtil.isEmpty(baseVo)) {
            baseVo = new JhSlcrsRBaseVo();
            baseVo.setPrjnmcd(prjnmcd);
        }
        //闸站对应闸门开度列表
        List<JhSlcrsDtlRInfoVo> slcrsDtlRInfoVos = jhSlcrsRRealService.getSlcrsRRealInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(slcrsDtlRInfoVos)) {
            baseVo.setGateCondition(slcrsDtlRInfoVos);
            slcrsDtlRInfoVos.size();
            baseVo.setGtcn(new BigDecimal(slcrsDtlRInfoVos.size()));
            baseVo.setGtopn(new BigDecimal(slcrsDtlRInfoVos.stream().filter(vo -> ObjectUtil.isNotEmpty(vo.getOm()) && vo.getOm().equals("1")).collect(Collectors.toList()).size()));

        }
        if (ObjectUtil.isNotEmpty(baseVo)) {
            JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(prjnmcd);
            if (ObjectUtil.isNotEmpty(temporaryData)) {
                baseVo.setSldsz(temporaryData.getSldsz());
                baseVo.setSlupsz(temporaryData.getSlupsz());
                baseVo.setUpswtp(temporaryData.getUpswtp());
                baseVo.setDswtp(temporaryData.getDswtp());
            } else {
                baseVo.setSldsz(null);
                baseVo.setSlupsz(null);
                baseVo.setUpswtp(null);
                baseVo.setDswtp(null);
            }
        }
        baseVo.setAccqYear(jhSlcrsRRealService.yearAccq(prjnmcd)) ;

        return baseVo;
    }

    public JhirWagaBVo selectJhirWagaBByStrCode(String strCode) {

        return jhirWagaBService.selectJhirWagaBByStrCode(strCode);
    }

    public JhPumpRBaseVo getPumpBaseInfo(String prjnmcd) {

        JhPumpRRealVo jhPumpRVo = jhPumpRRealService.selectJhPumpRRealByPrjnmcd(prjnmcd);
        JhPumpRBaseVo baseVo = BeanUtil.toBean(jhPumpRVo, JhPumpRBaseVo.class);
        if (ObjectUtil.isNotEmpty(baseVo) && ObjectUtil.isEmpty(baseVo.getPmpq())) {
            baseVo.setPmpq(BigDecimal.valueOf(0));
        }

        if (ObjectUtil.isEmpty(jhPumpRVo)) {
            baseVo = new JhPumpRBaseVo();
            baseVo.setPrjnmcd(prjnmcd);
        }
        if (ObjectUtil.isNotEmpty(baseVo)) {
            JhSlcrsRRealTemporaryDataVo temporaryData = jhSlcrsRRealService.temporaryData(prjnmcd);

            if (ObjectUtil.isNotEmpty(temporaryData)) {
                baseVo.setPpupz(temporaryData.getSlupsz());
                baseVo.setPpdwz(temporaryData.getSldsz());
                baseVo.setPpupwptn(temporaryData.getUpswtp());
                baseVo.setPpdwwptn(temporaryData.getDswtp());
            } else {
                baseVo.setPpupz(null);
                baseVo.setPpdwz(null);
                baseVo.setPpupwptn(null);
                baseVo.setPpdwwptn(null);
            }
        }
        List<JhPumpDtlRInfoVo> pumpDtlRInfoVos = pumpDtlRRealService.getPumpDtlRInfoVos(prjnmcd);
        if (ObjectUtil.isNotEmpty(pumpDtlRInfoVos)) {
            baseVo.setPumpCondition(pumpDtlRInfoVos);
        }
        baseVo.setAccqYear(jhPumpRRealService.yearAccq(prjnmcd))   ;
        return baseVo;
    }

    public JhirPustBVo selectJhirPustBByStrCode(String strCode) {

        return jhirPustBService.selectJhirPustBByStrCode(strCode);
    }

    public List<StRiverRVo> selectStRiverRList(StRiverRParam stRiverRParam) {

        List<StRiverRVo> list = stRiverRService.selectStRiverRListEight(stRiverRParam);
        return list;
    }

    public FlowSectionVo getSectionInfo(String stcd) {
        FlowSectionVo flowSectionVo = new FlowSectionVo();
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(stcd);
        if (ObjectUtil.isNotEmpty(stRvfcchBVo)) {
            flowSectionVo.setGrz(stRvfcchBVo.getGrz());
            flowSectionVo.setWrz(stRvfcchBVo.getWrz());
            flowSectionVo.setObhtz(stRvfcchBVo.getObhtz());
        }

        StRiverRPo one = stRiverRService.getOne(new QueryWrapper<StRiverRPo>()
                .eq("STCD", stcd)
                .orderBy(true, false, "TM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)) {
            flowSectionVo.setZ(one.getZ());
        }

        return flowSectionVo;

    }

    public WaterLevelVo getRiverBaseInfo(String stcd) {

        WaterLevelVo baseVo = new WaterLevelVo();
        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(stcd);
        StStbprpFlowVo stStbprpFlowVo = BeanUtil.toBean(stStbprpBVo, StStbprpFlowVo.class);
        baseVo.setStbprpFlowVo(stStbprpFlowVo);
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(stcd);
        baseVo.setRvfcchBVo(stRvfcchBVo);
        if (ObjectUtil.isNotEmpty(stStbprpBVo) && ObjectUtil.isNotEmpty(stStbprpBVo.getIswz()) && 1 == stStbprpBVo.getIswz()) {
            //最新水位，同样时间查询，六小时没数据，则为空
            StWasRPo one = stWasRService.getOne(new QueryWrapper<StWasRPo>().
                    eq("STCD", stcd).apply("tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )")
                    .orderBy(true, false, "TM").last(" limit 1"), false);

            if (ObjectUtil.isNotNull(one)) {
                baseVo.setZ(one.getUpz());
                baseVo.setTm(one.getTm());
            }
        } else {
            //最新水位，同样时间查询，六小时没数据，则为空
            StRiverRPo one = stRiverRService.getOne(new QueryWrapper<StRiverRPo>().
                    eq("STCD", stcd).apply("tm >= ADDTIME ( sysdate, INTERVAL '-6' HOUR )")
                    .orderBy(true, false, "TM").last(" limit 1"), false);
            if (ObjectUtil.isNotNull(one)) {
                baseVo.setZ(one.getZ());
                baseVo.setTm(one.getTm());
            }

        }
        return baseVo;
    }

    public FlowVo getFlowBaseInfo(String stcd) {

        FlowVo baseVo = new FlowVo();
        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(stcd);
        StStbprpFlowVo stStbprpFlowVo = BeanUtil.toBean(stStbprpBVo, StStbprpFlowVo.class);
        baseVo.setStbprpFlowVo(stStbprpFlowVo);
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(stcd);
        baseVo.setRvfcchBVo(stRvfcchBVo);
        JhFlowycRPo one = jhFlowycRService.getOne(new QueryWrapper<JhFlowycRPo>()
                .eq("STCD", stcd).orderBy(true, false, "DATATM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)) {
            baseVo.setQ(one.getQ() == null ? new BigDecimal(0) : one.getQ());
            baseVo.setZ(one.getZ());
            baseVo.setTm(one.getDatatm());
        }
        return baseVo;
    }


    public List<JhFlowtnRVo> selectJhFlowycRList(JhFlowycRParam jhFlowycRParam) {

        List<JhFlowtnRVo> list = jhFlowycRService.selectJhFlowycRList(jhFlowycRParam);
        return list;
    }

    public FlowSectionVo getFlowSectionInfo(String stcd) {

        FlowSectionVo flowSectionVo = new FlowSectionVo();
        StRvfcchBVo stRvfcchBVo = stRvfcchBService.selectStRvfcchBBySTCD(stcd);
        if (ObjectUtil.isNotEmpty(stRvfcchBVo)) {
            flowSectionVo.setGrz(stRvfcchBVo.getGrz());
            flowSectionVo.setWrz(stRvfcchBVo.getWrz());
            flowSectionVo.setObhtz(stRvfcchBVo.getObhtz());
        }
        JhFlowycRPo one = jhFlowycRService.getOne(new QueryWrapper<JhFlowycRPo>()
                .eq("STCD", stcd).orderBy(true, false, "DATATM").last("limit 1"), false);
        if (ObjectUtil.isNotNull(one)) {
            flowSectionVo.setZ(one.getZ());
        }
        BigDecimal bigDecimal = jhFlowycRService.yearAccq(stcd);
        if (ObjectUtil.isNotEmpty(bigDecimal)){

            bigDecimal = bigDecimal.stripTrailingZeros();
        }

        flowSectionVo.setAccqYear(bigDecimal);
        return flowSectionVo;
    }

    public StStbprpBaseVo getPptnBaseInfo(String stcd) {

        StStbprpBVo stStbprpBVo = stStbprpBService.selectStStbprpBBySTCD(stcd);
        StStbprpBaseVo baseVo = BeanUtil.toBean(stStbprpBVo, StStbprpBaseVo.class);
        if (ObjectUtil.isNotEmpty(baseVo)) {
            //有歧义注意
            StPptnRVo pptnRVo = stPptnRService.selectStPptnRBySTCD(stcd);
            if (ObjectUtil.isNotEmpty(pptnRVo)) {
                baseVo.setDrp(pptnRVo.getDrp());
            }
        }
        return baseVo;
    }

    public List<StPptnRVo> getPptnRainInfo(StPptnRParam stPptnRParam) {
        return stPptnRService.selectStPptnRList(stPptnRParam);
    }

    public JhomVmBVo getVmInfo(String cd) {

        return jhomVmBService.selectJhomVmBByCd(cd);
    }

    public VideoDataVo getSeeInfo(String cd) {
        VideoDataVo videoDataVo = new VideoDataVo();
        String url = "";
        JhomVmBPo video = null;
        video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                .eq("cd", cd).last("limit 1"), false);
        if (ObjectUtil.isEmpty(video)) {
            video = jhomVmBService.getOne(new QueryWrapper<JhomVmBPo>()
                    .eq("camera_code", cd).last("limit 1"), false);
        }

        if (ObjectUtil.isNotEmpty(video) && ObjectUtil.isNotEmpty(video.getCameraCode())) {
            //todo 根据cameraCode获取播放地址
            url = hikvisionVideoTool.getVideoPlayUrl(video.getCameraCode());
            videoDataVo.setCameraCode(video.getCameraCode());
            videoDataVo.setUrl(url);
            videoDataVo.setNm(video.getNm());
        }
        return videoDataVo;
    }

    public IrrigationBriefingVo getIrrigationBriefing(String reportDate) {
        return gisDatasDService.getIrrigationBriefing( reportDate);
    }
}
