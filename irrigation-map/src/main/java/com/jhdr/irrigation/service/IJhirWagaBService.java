package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.JhirWagaBPo;
import com.jhdr.irrigation.entity.param.JhirWagaBParam;
import com.jhdr.irrigation.entity.param.JhirWagaBAddParam;
import com.jhdr.irrigation.entity.param.JhirWagaBEditParam;
import com.jhdr.irrigation.entity.vo.JhirWagaBVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 闸站基础信息Service接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface IJhirWagaBService extends IService<JhirWagaBPo>
{

    /**
     * 查询闸站基础信息列表
     *
     * @param jhirWagaB 闸站基础信息
     * @return 闸站基础信息集合
     */
    public List<JhirWagaBVo> queryList(JhirWagaBPo jhirWagaB);

    /**
     * 查询闸站基础信息
     *
     * @param strCode 闸站基础信息主键
     * @return 闸站基础信息
     */
    public JhirWagaBVo selectJhirWagaBByStrCode(String strCode);

    /**
     * 查询闸站基础信息列表
     *
     * @param jhirWagaB 闸站基础信息
     * @return 闸站基础信息集合
     */
    public List<JhirWagaBVo> selectJhirWagaBList(JhirWagaBParam jhirWagaB);

    /**
     * 新增闸站基础信息
     *
     * @param jhirWagaB 闸站基础信息
     * @return 结果
     */
    public int insertJhirWagaB(JhirWagaBAddParam jhirWagaB);

    /**
     * 修改闸站基础信息
     *
     * @param jhirWagaB 闸站基础信息
     * @return 结果
     */
    public int updateJhirWagaB(JhirWagaBEditParam jhirWagaB);

    /**
     * 批量删除闸站基础信息
     *
     * @param strCodes 需要删除的闸站基础信息主键集合
     * @return 结果
     */
    public int deleteJhirWagaBByStrCodes(String[] strCodes);

    /**
     * 删除闸站基础信息信息
     *
     * @param strCode 闸站基础信息主键
     * @return 结果
     */
    public int deleteJhirWagaBByStrCode(String strCode);

}
