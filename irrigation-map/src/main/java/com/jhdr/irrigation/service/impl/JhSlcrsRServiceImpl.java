package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.vo.JhSlcrsRWaterVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhSlcrsRPo;
import com.jhdr.irrigation.entity.param.JhSlcrsRParam;
import com.jhdr.irrigation.entity.param.JhSlcrsRAddParam;
import com.jhdr.irrigation.entity.param.JhSlcrsREditParam;
import com.jhdr.irrigation.entity.vo.JhSlcrsRVo;
import com.jhdr.irrigation.mapper.JhSlcrsRMapper;
import com.jhdr.irrigation.service.IJhSlcrsRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 水闸运行状况Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class JhSlcrsRServiceImpl extends ServiceImpl<JhSlcrsRMapper, JhSlcrsRPo> implements IJhSlcrsRService {

    @Override
    public List<JhSlcrsRVo> queryList(JhSlcrsRPo jhSlcrsR) {
        LambdaQueryWrapper<JhSlcrsRPo> lqw = Wrappers.lambdaQuery();
        if (jhSlcrsR.getClltm() != null){
            lqw.eq(JhSlcrsRPo::getClltm ,jhSlcrsR.getClltm());
        }
        if (jhSlcrsR.getSldsz() != null){
            lqw.eq(JhSlcrsRPo::getSldsz ,jhSlcrsR.getSldsz());
        }
        if (jhSlcrsR.getThrslq() != null){
            lqw.eq(JhSlcrsRPo::getThrslq ,jhSlcrsR.getThrslq());
        }
        if (jhSlcrsR.getGtopn() != null){
            lqw.eq(JhSlcrsRPo::getGtopn ,jhSlcrsR.getGtopn());
        }
        if (jhSlcrsR.getUpswtp() != null){
            lqw.eq(JhSlcrsRPo::getUpswtp ,jhSlcrsR.getUpswtp());
        }
        if (jhSlcrsR.getDswtp() != null){
            lqw.eq(JhSlcrsRPo::getDswtp ,jhSlcrsR.getDswtp());
        }
        if (StringUtils.isNotBlank(jhSlcrsR.getRpdpcd())){
            lqw.eq(JhSlcrsRPo::getRpdpcd ,jhSlcrsR.getRpdpcd());
        }
        if (StringUtils.isNotBlank(jhSlcrsR.getReporter())){
            lqw.eq(JhSlcrsRPo::getReporter ,jhSlcrsR.getReporter());
        }
        if (StringUtils.isNotBlank(jhSlcrsR.getRpttlnmb())){
            lqw.eq(JhSlcrsRPo::getRpttlnmb ,jhSlcrsR.getRpttlnmb());
        }
        if (StringUtils.isNotBlank(jhSlcrsR.getRmk())){
            lqw.eq(JhSlcrsRPo::getRmk ,jhSlcrsR.getRmk());
        }
        if (jhSlcrsR.getGtcn() != null){
            lqw.eq(JhSlcrsRPo::getGtcn ,jhSlcrsR.getGtcn());
        }
        List<JhSlcrsRVo> jhSlcrsRVos= BeanUtil.copyToList(this.list(lqw), JhSlcrsRVo.class);
        return jhSlcrsRVos;
    }
    /**
     * 查询水闸运行状况
     *
     * @param prjnmcd 水闸运行状况主键
     * @return 水闸运行状况
     */
    @Override
    public JhSlcrsRVo selectJhSlcrsRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhSlcrsRByPrjnmcd(prjnmcd);
    }

    /**
     * 查询水闸运行状况列表
     *
     * @param jhSlcrsR 水闸运行状况
     * @return 水闸运行状况
     */
    @Override
    public List<JhSlcrsRWaterVo> selectJhSlcrsRList(JhSlcrsRParam jhSlcrsR)
    {
        return baseMapper.selectJhSlcrsRList(jhSlcrsR);
    }

    /**
     * 新增水闸运行状况
     *
     * @param jhSlcrsRAddParam 水闸运行状况
     * @return 结果
     */
    @Override
    public int insertJhSlcrsR(JhSlcrsRAddParam jhSlcrsRAddParam)
    {

        JhSlcrsRPo jhSlcrsR=new JhSlcrsRPo();
        BeanUtil.copyProperties(jhSlcrsRAddParam,jhSlcrsR);
        return baseMapper.insert(jhSlcrsR);
    }

    /**
     * 修改水闸运行状况
     *
     * @param jhSlcrsREditParam 水闸运行状况
     * @return 结果
     */
    @Override
    public int updateJhSlcrsR(JhSlcrsREditParam jhSlcrsREditParam)
    {
        JhSlcrsRPo jhSlcrsR=new JhSlcrsRPo();
        BeanUtil.copyProperties(jhSlcrsREditParam,jhSlcrsR);
        return baseMapper.updateById(jhSlcrsR);
    }

    /**
     * 批量删除水闸运行状况
     *
     * @param prjnmcds 需要删除的水闸运行状况主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsRByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhSlcrsRByPrjnmcds(prjnmcds);
    }

    /**
     * 删除水闸运行状况信息
     *
     * @param prjnmcd 水闸运行状况主键
     * @return 结果
     */
    @Override
    public int deleteJhSlcrsRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhSlcrsRByPrjnmcd(prjnmcd);
    }
}
