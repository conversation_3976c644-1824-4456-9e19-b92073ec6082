package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.JhPumpDtlRPo;
import com.jhdr.irrigation.entity.param.JhPumpDtlRParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlRAddParam;
import com.jhdr.irrigation.entity.param.JhPumpDtlREditParam;
import com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo;
import com.jhdr.irrigation.entity.vo.JhPumpDtlRVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 泵站运行明细Service接口
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
public interface IJhPumpDtlRService extends IService<JhPumpDtlRPo>
{

    /**
     * 查询泵站运行明细列表
     *
     * @param jhPumpDtlR 泵站运行明细
     * @return 泵站运行明细集合
     */
    public List<JhPumpDtlRVo> queryList(JhPumpDtlRPo jhPumpDtlR);

    /**
     * 查询泵站运行明细
     *
     * @param prjnmcd 泵站运行明细主键
     * @return 泵站运行明细
     */
    public JhPumpDtlRVo selectJhPumpDtlRByPrjnmcd(String prjnmcd);

    /**
     * 查询泵站运行明细列表
     *
     * @param jhPumpDtlR 泵站运行明细
     * @return 泵站运行明细集合
     */
    public List<JhPumpDtlRVo> selectJhPumpDtlRList(JhPumpDtlRParam jhPumpDtlR);

    /**
     * 新增泵站运行明细
     *
     * @param jhPumpDtlR 泵站运行明细
     * @return 结果
     */
    public int insertJhPumpDtlR(JhPumpDtlRAddParam jhPumpDtlR);

    /**
     * 修改泵站运行明细
     *
     * @param jhPumpDtlR 泵站运行明细
     * @return 结果
     */
    public int updateJhPumpDtlR(JhPumpDtlREditParam jhPumpDtlR);

    /**
     * 批量删除泵站运行明细
     *
     * @param prjnmcds 需要删除的泵站运行明细主键集合
     * @return 结果
     */
    public int deleteJhPumpDtlRByPrjnmcds(String[] prjnmcds);

    /**
     * 删除泵站运行明细信息
     *
     * @param prjnmcd 泵站运行明细主键
     * @return 结果
     */
    public int deleteJhPumpDtlRByPrjnmcd(String prjnmcd);

    /**
     * 获取泵站运行明细详细信息
     * @param prjnmcd
     * @return
     */
    List<JhPumpDtlRInfoVo> getPumpDtlRInfoVos(String prjnmcd);
}
