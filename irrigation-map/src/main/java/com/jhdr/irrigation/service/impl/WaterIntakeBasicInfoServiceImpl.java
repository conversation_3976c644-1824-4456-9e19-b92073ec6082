package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.WaterIntakeBasicInfoPo;
import com.jhdr.irrigation.entity.param.WaterIntakeBasicInfoParam;
import com.jhdr.irrigation.entity.param.WaterIntakeBasicInfoAddParam;
import com.jhdr.irrigation.entity.param.WaterIntakeBasicInfoEditParam;
import com.jhdr.irrigation.entity.vo.WaterIntakeBasicInfoVo;
import com.jhdr.irrigation.mapper.WaterIntakeBasicInfoMapper;
import com.jhdr.irrigation.service.IWaterIntakeBasicInfoService;

import java.util.ArrayList;

import java.util.List;

/**
 * 取水口基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class WaterIntakeBasicInfoServiceImpl extends ServiceImpl<WaterIntakeBasicInfoMapper, WaterIntakeBasicInfoPo> implements IWaterIntakeBasicInfoService {

    @Override
    public List<WaterIntakeBasicInfoVo> queryList(WaterIntakeBasicInfoPo waterIntakeBasicInfo) {
        LambdaQueryWrapper<WaterIntakeBasicInfoPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(waterIntakeBasicInfo.getIntakeName())){
            lqw.like(WaterIntakeBasicInfoPo::getIntakeName ,waterIntakeBasicInfo.getIntakeName());
        }
        if (StringUtils.isNotBlank(waterIntakeBasicInfo.getAdminDistrict())){
            lqw.eq(WaterIntakeBasicInfoPo::getAdminDistrict ,waterIntakeBasicInfo.getAdminDistrict());
        }
        if (waterIntakeBasicInfo.getDailyCapacity() != null){
            lqw.eq(WaterIntakeBasicInfoPo::getDailyCapacity ,waterIntakeBasicInfo.getDailyCapacity());
        }
        if (waterIntakeBasicInfo.getAnnualCapacity() != null){
            lqw.eq(WaterIntakeBasicInfoPo::getAnnualCapacity ,waterIntakeBasicInfo.getAnnualCapacity());
        }
        if (StringUtils.isNotBlank(waterIntakeBasicInfo.getBankSide())){
            lqw.eq(WaterIntakeBasicInfoPo::getBankSide ,waterIntakeBasicInfo.getBankSide());
        }
        if (StringUtils.isNotBlank(waterIntakeBasicInfo.getLocation())){
            lqw.eq(WaterIntakeBasicInfoPo::getLocation ,waterIntakeBasicInfo.getLocation());
        }
        if (StringUtils.isNotBlank(waterIntakeBasicInfo.getImageUrl())){
            lqw.eq(WaterIntakeBasicInfoPo::getImageUrl ,waterIntakeBasicInfo.getImageUrl());
        }
        List<WaterIntakeBasicInfoVo> waterIntakeBasicInfoVos= BeanUtil.copyToList(this.list(lqw), WaterIntakeBasicInfoVo.class);
        return waterIntakeBasicInfoVos;
    }
    /**
     * 查询取水口基本信息
     *
     * @param id 取水口基本信息主键
     * @return 取水口基本信息
     */
    @Override
    public WaterIntakeBasicInfoVo selectWaterIntakeBasicInfoById(Long id)
    {
        return baseMapper.selectWaterIntakeBasicInfoById(id);
    }

    /**
     * 查询取水口基本信息列表
     *
     * @param waterIntakeBasicInfo 取水口基本信息
     * @return 取水口基本信息
     */
    @Override
    public List<WaterIntakeBasicInfoVo> selectWaterIntakeBasicInfoList(WaterIntakeBasicInfoParam waterIntakeBasicInfo)
    {
        return baseMapper.selectWaterIntakeBasicInfoList(waterIntakeBasicInfo);
    }

    /**
     * 新增取水口基本信息
     *
     * @param waterIntakeBasicInfoAddParam 取水口基本信息
     * @return 结果
     */
    @Override
    public int insertWaterIntakeBasicInfo(WaterIntakeBasicInfoAddParam waterIntakeBasicInfoAddParam)
    {

        WaterIntakeBasicInfoPo waterIntakeBasicInfo=new WaterIntakeBasicInfoPo();
        BeanUtil.copyProperties(waterIntakeBasicInfoAddParam,waterIntakeBasicInfo);
        return baseMapper.insert(waterIntakeBasicInfo);
    }

    /**
     * 修改取水口基本信息
     *
     * @param waterIntakeBasicInfoEditParam 取水口基本信息
     * @return 结果
     */
    @Override
    public int updateWaterIntakeBasicInfo(WaterIntakeBasicInfoEditParam waterIntakeBasicInfoEditParam)
    {
        WaterIntakeBasicInfoPo waterIntakeBasicInfo=new WaterIntakeBasicInfoPo();
        BeanUtil.copyProperties(waterIntakeBasicInfoEditParam,waterIntakeBasicInfo);
        return baseMapper.updateById(waterIntakeBasicInfo);
    }

    /**
     * 批量删除取水口基本信息
     *
     * @param ids 需要删除的取水口基本信息主键
     * @return 结果
     */
    @Override
    public int deleteWaterIntakeBasicInfoByIds(Long[] ids)
    {
        return baseMapper.deleteWaterIntakeBasicInfoByIds(ids);
    }

    /**
     * 删除取水口基本信息信息
     *
     * @param id 取水口基本信息主键
     * @return 结果
     */
    @Override
    public int deleteWaterIntakeBasicInfoById(Long id)
    {
        return baseMapper.deleteWaterIntakeBasicInfoById(id);
    }
}
