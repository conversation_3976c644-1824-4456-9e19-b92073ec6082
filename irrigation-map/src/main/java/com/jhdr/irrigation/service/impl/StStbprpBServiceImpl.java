package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.jhdr.irrigation.entity.vo.StRiverRegionDataVo;
import com.jhdr.irrigation.entity.vo.VideoRegionDataVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.StStbprpBPo;
import com.jhdr.irrigation.entity.param.StStbprpBParam;
import com.jhdr.irrigation.entity.param.StStbprpBAddParam;
import com.jhdr.irrigation.entity.param.StStbprpBEditParam;
import com.jhdr.irrigation.entity.vo.StStbprpBVo;
import com.jhdr.irrigation.mapper.StStbprpBMapper;
import com.jhdr.irrigation.service.IStStbprpBService;

import java.util.ArrayList;

import java.util.Collections;
import java.util.List;

/**
 * 测站基本属性Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-22
 */
@Service
public class StStbprpBServiceImpl extends ServiceImpl<StStbprpBMapper, StStbprpBPo> implements IStStbprpBService {

    @Override
    public List<StStbprpBVo> queryList(StStbprpBPo stStbprpB) {
        LambdaQueryWrapper<StStbprpBPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(stStbprpB.getSTNM())){
            lqw.eq(StStbprpBPo::getSTNM ,stStbprpB.getSTNM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getRVNM())){
            lqw.eq(StStbprpBPo::getRVNM ,stStbprpB.getRVNM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getHNNM())){
            lqw.eq(StStbprpBPo::getHNNM ,stStbprpB.getHNNM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getBSNM())){
            lqw.eq(StStbprpBPo::getBSNM ,stStbprpB.getBSNM());
        }
        if (stStbprpB.getLGTD() != null){
            lqw.eq(StStbprpBPo::getLGTD ,stStbprpB.getLGTD());
        }
        if (stStbprpB.getLTTD() != null){
            lqw.eq(StStbprpBPo::getLTTD ,stStbprpB.getLTTD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSTLC())){
            lqw.eq(StStbprpBPo::getSTLC ,stStbprpB.getSTLC());
        }
        if (StringUtils.isNotBlank(stStbprpB.getADDVCD())){
            lqw.eq(StStbprpBPo::getADDVCD ,stStbprpB.getADDVCD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getDTMNM())){
            lqw.eq(StStbprpBPo::getDTMNM ,stStbprpB.getDTMNM());
        }
        if (stStbprpB.getDTMEL() != null){
            lqw.eq(StStbprpBPo::getDTMEL ,stStbprpB.getDTMEL());
        }
        if (stStbprpB.getDTPR() != null){
            lqw.eq(StStbprpBPo::getDTPR ,stStbprpB.getDTPR());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSTTP())){
            lqw.eq(StStbprpBPo::getSTTP ,stStbprpB.getSTTP());
        }
        if (StringUtils.isNotBlank(stStbprpB.getFRGRD())){
            lqw.eq(StStbprpBPo::getFRGRD ,stStbprpB.getFRGRD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getESSTYM())){
            lqw.eq(StStbprpBPo::getESSTYM ,stStbprpB.getESSTYM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getBGFRYM())){
            lqw.eq(StStbprpBPo::getBGFRYM ,stStbprpB.getBGFRYM());
        }
        if (StringUtils.isNotBlank(stStbprpB.getATCUNIT())){
            lqw.eq(StStbprpBPo::getATCUNIT ,stStbprpB.getATCUNIT());
        }
        if (StringUtils.isNotBlank(stStbprpB.getADMAUTH())){
            lqw.eq(StStbprpBPo::getADMAUTH ,stStbprpB.getADMAUTH());
        }
        if (StringUtils.isNotBlank(stStbprpB.getLOCALITY())){
            lqw.eq(StStbprpBPo::getLOCALITY ,stStbprpB.getLOCALITY());
        }
        if (StringUtils.isNotBlank(stStbprpB.getSTBK())){
            lqw.eq(StStbprpBPo::getSTBK ,stStbprpB.getSTBK());
        }
        if (stStbprpB.getSTAZT() != null){
            lqw.eq(StStbprpBPo::getSTAZT ,stStbprpB.getSTAZT());
        }
        if (stStbprpB.getDSTRVM() != null){
            lqw.eq(StStbprpBPo::getDSTRVM ,stStbprpB.getDSTRVM());
        }
        if (stStbprpB.getDRNA() != null){
            lqw.eq(StStbprpBPo::getDRNA ,stStbprpB.getDRNA());
        }
        if (StringUtils.isNotBlank(stStbprpB.getPHCD())){
            lqw.eq(StStbprpBPo::getPHCD ,stStbprpB.getPHCD());
        }
        if (StringUtils.isNotBlank(stStbprpB.getUSFL())){
            lqw.eq(StStbprpBPo::getUSFL ,stStbprpB.getUSFL());
        }
        if (StringUtils.isNotBlank(stStbprpB.getCOMMENTS())){
            lqw.eq(StStbprpBPo::getCOMMENTS ,stStbprpB.getCOMMENTS());
        }
        if (StringUtils.isNotBlank(stStbprpB.getMODITIME())){
            lqw.eq(StStbprpBPo::getMODITIME ,stStbprpB.getMODITIME());
        }
        List<StStbprpBVo> stStbprpBVos= BeanUtil.copyToList(this.list(lqw), StStbprpBVo.class);
        return stStbprpBVos;
    }
    /**
     * 查询测站基本属性
     *
     * @param STCD 测站基本属性主键
     * @return 测站基本属性
     */
    @Override
    public StStbprpBVo selectStStbprpBBySTCD(String STCD)
    {
        return baseMapper.selectStStbprpBBySTCD(STCD);
    }

    /**
     * 查询测站基本属性列表
     *
     * @param stStbprpB 测站基本属性
     * @return 测站基本属性
     */
    @Override
    public List<StStbprpBVo> selectStStbprpBList(StStbprpBParam stStbprpB)
    {
        return baseMapper.selectStStbprpBList(stStbprpB);
    }

    /**
     * 新增测站基本属性
     *
     * @param stStbprpBAddParam 测站基本属性
     * @return 结果
     */
    @Override
    public int insertStStbprpB(StStbprpBAddParam stStbprpBAddParam)
    {

        StStbprpBPo stStbprpB=new StStbprpBPo();
        BeanUtil.copyProperties(stStbprpBAddParam,stStbprpB);
        return baseMapper.insert(stStbprpB);
    }

    /**
     * 修改测站基本属性
     *
     * @param stStbprpBEditParam 测站基本属性
     * @return 结果
     */
    @Override
    public int updateStStbprpB(StStbprpBEditParam stStbprpBEditParam)
    {
        StStbprpBPo stStbprpB=new StStbprpBPo();
        BeanUtil.copyProperties(stStbprpBEditParam,stStbprpB);
        return baseMapper.updateById(stStbprpB);
    }

    /**
     * 批量删除测站基本属性
     *
     * @param STCDs 需要删除的测站基本属性主键
     * @return 结果
     */
    @Override
    public int deleteStStbprpBBySTCDs(String[] STCDs)
    {
        return baseMapper.deleteStStbprpBBySTCDs(STCDs);
    }

    /**
     * 删除测站基本属性信息
     *
     * @param STCD 测站基本属性主键
     * @return 结果
     */
    @Override
    public int deleteStStbprpBBySTCD(String STCD)
    {
        return baseMapper.deleteStStbprpBBySTCD(STCD);
    }

    @Override
    public List<StRiverRegionDataVo> selectRegionTreeList(String sttp) {
        List<StRiverRegionDataVo> regionTreeList= baseMapper.selectRegionTreeList(sttp);

        List<StRiverRegionDataVo> menusList = new ArrayList<>();

        for (StRiverRegionDataVo vo : regionTreeList) {
            //设置一级菜单
            if (vo.getAddvcd().equals("0")){
                menusList.add(vo);
            }
        }

        //设置一级菜单
//        StRiverRegionDataVo menus = new StRiverRegionDataVo();
//        menus.setAddvcd("1").setStnm("安徽茨淮新河").setStcd("0");
//        menusList.add(menus);

        //设置一级菜单的子集菜单
        for (StRiverRegionDataVo menusVo : menusList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }
        return menusList;

    }

    @Override
    public List<StRiverRegionDataVo> selectRegionWaterTreeList() {
        List<StRiverRegionDataVo> regionTreeList= baseMapper.selectRegionWaterTreeList();

        List<StRiverRegionDataVo> menusList = new ArrayList<>();

        for (StRiverRegionDataVo vo : regionTreeList) {
            //设置一级菜单
            if (vo.getAddvcd().equals("0")){
                menusList.add(vo);
            }
        }

        //设置一级菜单
//        StRiverRegionDataVo menus = new StRiverRegionDataVo();
//        menus.setAddvcd("1").setStnm("安徽茨淮新河").setStcd("0");
//        menusList.add(menus);

        //设置一级菜单的子集菜单
        for (StRiverRegionDataVo menusVo : menusList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }
        return menusList;

    }


    private List<StRiverRegionDataVo> getChildren(String id, List<StRiverRegionDataVo> regionTreeList) {
        //定义集合
        List<StRiverRegionDataVo> childList = new ArrayList<>();
        //遍历子集菜单
        for (StRiverRegionDataVo menu : regionTreeList) {
            //父级id与子集id比较
            if (menu.getAddvcd().equals(id)){
                childList.add(menu);
            }
        }
        //循环子集菜单
        for (StRiverRegionDataVo menusVo : childList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }

        //递归条件退出
        if (childList.size() ==0){
            return null;
        }
        return childList;
    }
}
