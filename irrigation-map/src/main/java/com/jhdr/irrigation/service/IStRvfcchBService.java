package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.StRvfcchBPo;
import com.jhdr.irrigation.entity.param.StRvfcchBParam;
import com.jhdr.irrigation.entity.param.StRvfcchBAddParam;
import com.jhdr.irrigation.entity.param.StRvfcchBEditParam;
import com.jhdr.irrigation.entity.vo.StRvfcchBVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 河道防洪指标Service接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface IStRvfcchBService extends IService<StRvfcchBPo>
{

    /**
     * 查询河道防洪指标列表
     *
     * @param stRvfcchB 河道防洪指标
     * @return 河道防洪指标集合
     */
    public List<StRvfcchBVo> queryList(StRvfcchBPo stRvfcchB);

    /**
     * 查询河道防洪指标
     *
     * @param STCD 河道防洪指标主键
     * @return 河道防洪指标
     */
    public StRvfcchBVo selectStRvfcchBBySTCD(String STCD);

    /**
     * 查询河道防洪指标列表
     *
     * @param stRvfcchB 河道防洪指标
     * @return 河道防洪指标集合
     */
    public List<StRvfcchBVo> selectStRvfcchBList(StRvfcchBParam stRvfcchB);

    /**
     * 新增河道防洪指标
     *
     * @param stRvfcchB 河道防洪指标
     * @return 结果
     */
    public int insertStRvfcchB(StRvfcchBAddParam stRvfcchB);

    /**
     * 修改河道防洪指标
     *
     * @param stRvfcchB 河道防洪指标
     * @return 结果
     */
    public int updateStRvfcchB(StRvfcchBEditParam stRvfcchB);

    /**
     * 批量删除河道防洪指标
     *
     * @param STCDs 需要删除的河道防洪指标主键集合
     * @return 结果
     */
    public int deleteStRvfcchBBySTCDs(String[] STCDs);

    /**
     * 删除河道防洪指标信息
     *
     * @param STCD 河道防洪指标主键
     * @return 结果
     */
    public int deleteStRvfcchBBySTCD(String STCD);

}
