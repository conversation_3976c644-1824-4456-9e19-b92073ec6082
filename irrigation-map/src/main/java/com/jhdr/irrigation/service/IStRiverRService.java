package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.StRiverRPo;
import com.jhdr.irrigation.entity.param.StRiverRParam;
import com.jhdr.irrigation.entity.param.StRiverRAddParam;
import com.jhdr.irrigation.entity.param.StRiverREditParam;
import com.jhdr.irrigation.entity.vo.StRiverRVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.irrigation.entity.vo.StRiverRegionDataVo;

/**
 * 河道水情Service接口
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
public interface IStRiverRService extends IService<StRiverRPo>
{



    /**
     * 查询河道水情
     *
     * @param STCD 河道水情主键
     * @return 河道水情
     */
    public StRiverRVo selectStRiverRBySTCD(String STCD);

    /**
     * 查询河道水情列表
     *
     * @param stRiverR 河道水情
     * @return 河道水情集合
     */
    public List<StRiverRVo> selectStRiverRList(StRiverRParam stRiverR);

    /**
     * 新增河道水情
     *
     * @param stRiverR 河道水情
     * @return 结果
     */
    public int insertStRiverR(StRiverRAddParam stRiverR);

    /**
     * 修改河道水情
     *
     * @param stRiverR 河道水情
     * @return 结果
     */
    public int updateStRiverR(StRiverREditParam stRiverR);

    /**
     * 批量删除河道水情
     *
     * @param STCDs 需要删除的河道水情主键集合
     * @return 结果
     */
    public int deleteStRiverRBySTCDs(String[] STCDs);

    /**
     * 删除河道水情信息
     *
     * @param STCD 河道水情主键
     * @return 结果
     */
    public int deleteStRiverRBySTCD(String STCD);


    /**
     * 获取gis 中水位站有数据的站点数量
     * @param
     * @return
     */
    Integer waterOpenNum();

    List<StRiverRVo> selectStRiverRListEight(StRiverRParam stRiverRParam);
}
