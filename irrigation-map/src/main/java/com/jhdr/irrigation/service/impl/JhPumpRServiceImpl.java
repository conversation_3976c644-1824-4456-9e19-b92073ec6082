package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhPumpRPo;
import com.jhdr.irrigation.entity.param.JhPumpRParam;
import com.jhdr.irrigation.entity.param.JhPumpRAddParam;
import com.jhdr.irrigation.entity.param.JhPumpREditParam;
import com.jhdr.irrigation.entity.vo.JhPumpRVo;
import com.jhdr.irrigation.mapper.JhPumpRMapper;
import com.jhdr.irrigation.service.IJhPumpRService;

import java.util.ArrayList;

import java.util.List;

/**
 * 泵站运行状况Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-20
 */
@Service
public class JhPumpRServiceImpl extends ServiceImpl<JhPumpRMapper, JhPumpRPo> implements IJhPumpRService {

    @Override
    public List<JhPumpRVo> queryList(JhPumpRPo jhPumpR) {
        LambdaQueryWrapper<JhPumpRPo> lqw = Wrappers.lambdaQuery();
        if (jhPumpR.getClltm() != null){
            lqw.eq(JhPumpRPo::getClltm ,jhPumpR.getClltm());
        }
        if (jhPumpR.getPpupz() != null){
            lqw.eq(JhPumpRPo::getPpupz ,jhPumpR.getPpupz());
        }
        if (jhPumpR.getPpdwz() != null){
            lqw.eq(JhPumpRPo::getPpdwz ,jhPumpR.getPpdwz());
        }
        if (jhPumpR.getOmcn() != null){
            lqw.eq(JhPumpRPo::getOmcn ,jhPumpR.getOmcn());
        }
        if (jhPumpR.getOmpwr() != null){
            lqw.eq(JhPumpRPo::getOmpwr ,jhPumpR.getOmpwr());
        }
        if (jhPumpR.getPmpq() != null){
            lqw.eq(JhPumpRPo::getPmpq ,jhPumpR.getPmpq());
        }
        if (jhPumpR.getWchrcd() != null){
            lqw.eq(JhPumpRPo::getWchrcd ,jhPumpR.getWchrcd());
        }
        if (jhPumpR.getPpupwptn() != null){
            lqw.eq(JhPumpRPo::getPpupwptn ,jhPumpR.getPpupwptn());
        }
        if (jhPumpR.getPpdwwptn() != null){
            lqw.eq(JhPumpRPo::getPpdwwptn ,jhPumpR.getPpdwwptn());
        }
        if (jhPumpR.getMsqmt() != null){
            lqw.eq(JhPumpRPo::getMsqmt ,jhPumpR.getMsqmt());
        }
        if (jhPumpR.getPdchcd() != null){
            lqw.eq(JhPumpRPo::getPdchcd ,jhPumpR.getPdchcd());
        }
        if (jhPumpR.getPpee() != null){
            lqw.eq(JhPumpRPo::getPpee ,jhPumpR.getPpee());
        }
        if (jhPumpR.getPpcn() != null){
            lqw.eq(JhPumpRPo::getPpcn ,jhPumpR.getPpcn());
        }
        if (jhPumpR.getPpuab() != null){
            lqw.eq(JhPumpRPo::getPpuab ,jhPumpR.getPpuab());
        }
        if (jhPumpR.getPpubc() != null){
            lqw.eq(JhPumpRPo::getPpubc ,jhPumpR.getPpubc());
        }
        if (jhPumpR.getPpuca() != null){
            lqw.eq(JhPumpRPo::getPpuca ,jhPumpR.getPpuca());
        }
        if (jhPumpR.getPpia() != null){
            lqw.eq(JhPumpRPo::getPpia ,jhPumpR.getPpia());
        }
        if (jhPumpR.getPpib() != null){
            lqw.eq(JhPumpRPo::getPpib ,jhPumpR.getPpib());
        }
        if (jhPumpR.getPpic() != null){
            lqw.eq(JhPumpRPo::getPpic ,jhPumpR.getPpic());
        }
        if (jhPumpR.getPpapwr() != null){
            lqw.eq(JhPumpRPo::getPpapwr ,jhPumpR.getPpapwr());
        }
        if (jhPumpR.getPprpwr() != null){
            lqw.eq(JhPumpRPo::getPprpwr ,jhPumpR.getPprpwr());
        }
        if (jhPumpR.getPpcos() != null){
            lqw.eq(JhPumpRPo::getPpcos ,jhPumpR.getPpcos());
        }
        List<JhPumpRVo> jhPumpRVos= BeanUtil.copyToList(this.list(lqw), JhPumpRVo.class);
        return jhPumpRVos;
    }
    /**
     * 查询泵站运行状况
     *
     * @param prjnmcd 泵站运行状况主键
     * @return 泵站运行状况
     */
    @Override
    public JhPumpRVo selectJhPumpRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.selectJhPumpRByPrjnmcd(prjnmcd);
    }

    /**
     * 查询泵站运行状况列表
     *
     * @param jhPumpR 泵站运行状况
     * @return 泵站运行状况
     */
    @Override
    public List<JhPumpRVo> selectJhPumpRList(JhPumpRParam jhPumpR)
    {
        return baseMapper.selectJhPumpRList(jhPumpR);
    }

    /**
     * 新增泵站运行状况
     *
     * @param jhPumpRAddParam 泵站运行状况
     * @return 结果
     */
    @Override
    public int insertJhPumpR(JhPumpRAddParam jhPumpRAddParam)
    {

        JhPumpRPo jhPumpR=new JhPumpRPo();
        BeanUtil.copyProperties(jhPumpRAddParam,jhPumpR);
        return baseMapper.insert(jhPumpR);
    }

    /**
     * 修改泵站运行状况
     *
     * @param jhPumpREditParam 泵站运行状况
     * @return 结果
     */
    @Override
    public int updateJhPumpR(JhPumpREditParam jhPumpREditParam)
    {
        JhPumpRPo jhPumpR=new JhPumpRPo();
        BeanUtil.copyProperties(jhPumpREditParam,jhPumpR);
        return baseMapper.updateById(jhPumpR);
    }

    /**
     * 批量删除泵站运行状况
     *
     * @param prjnmcds 需要删除的泵站运行状况主键
     * @return 结果
     */
    @Override
    public int deleteJhPumpRByPrjnmcds(String[] prjnmcds)
    {
        return baseMapper.deleteJhPumpRByPrjnmcds(prjnmcds);
    }

    /**
     * 删除泵站运行状况信息
     *
     * @param prjnmcd 泵站运行状况主键
     * @return 结果
     */
    @Override
    public int deleteJhPumpRByPrjnmcd(String prjnmcd)
    {
        return baseMapper.deleteJhPumpRByPrjnmcd(prjnmcd);
    }
}
