package com.jhdr.irrigation;

import com.jhdr.common.security.annotation.EnableCustomConfig;
import com.jhdr.common.security.annotation.EnableRyFeignClients;
import com.jhdr.common.security.aspect.PreAuthorizeAspect;
import com.jhdr.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 灌区一张图模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@EnableScheduling
//本地调试时候切换这个，取消权限校验 todo
//@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, PreAuthorizeAspect.class})
public class IrrigationMapApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(IrrigationMapApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  灌区一张图启动成功   ლ(´ڡ`ლ)ﾞ  \n" );
    }
}
