package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 水旱防御-视频
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水旱防御-视频")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class PreventionVideoVo
{
    

    /** 枢纽编码 */
    @ApiModelProperty(value = "枢纽编码")
    private String hubCode;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    private String name;

    @ApiModelProperty(value = "视频cd",hidden = true)
    private String vmBCd;

    @ApiModelProperty(value = "视频地址")
    private String url;


}
