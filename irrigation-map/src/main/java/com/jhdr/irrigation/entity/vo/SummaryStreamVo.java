package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 河流信息对象 jh_river_b
 *
 * <AUTHOR>
 * @date 2024-06-19
 */

@Data
@ApiModel(description = "河流汇总信息")
@Accessors(chain = true)
public class SummaryStreamVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "茨淮新河长度:公里")
    private BigDecimal chxhMnstln;

    @ApiModelProperty(value = "茨淮新河流域面积:平方公里")
    private BigDecimal chxhTtdrbsar;

    @ApiModelProperty(value = "西淝河长度:公里")
    private BigDecimal xfhMnstln;

    @ApiModelProperty(value = "西淝河流域面积:平方公里")
    private BigDecimal xfhTtdrbsar;

    @ApiModelProperty(value = "流域面积50平方公里以上的河流数量")
    private Integer streamNum;

    @ApiModelProperty(value = "全灌溉区多年平均降雨量 ")
    private String yearAverageRain;

}
