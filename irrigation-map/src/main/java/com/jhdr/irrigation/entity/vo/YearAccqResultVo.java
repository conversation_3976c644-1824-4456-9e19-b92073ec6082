package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "流量站年水量")
@Accessors(chain = true)
@JsonInclude()
public class YearAccqResultVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    private String stcd;
    private BigDecimal yearAccq;



}
