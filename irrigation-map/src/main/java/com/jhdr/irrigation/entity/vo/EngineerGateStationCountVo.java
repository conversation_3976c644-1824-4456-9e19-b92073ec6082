package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工情-运行统计-闸站
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "工情-运行统计-闸站")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class EngineerGateStationCountVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**闸站编码 */
    @ApiModelProperty(value = "闸站编码")
    private String gateStationCd;

    /** 闸站名称 */
    @ApiModelProperty(value = "闸站名称")
    private String gateStationName;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    @TableId
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    @Excel(name = "闸上水势")
    private BigDecimal upswtp;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    @Excel(name = "闸下水势")
    private BigDecimal dswtp;

    /** 闸站状态 0关闭 1开启 */
    @ApiModelProperty(value = "0关闭 1开启")
    private String status;

}
