package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 降水量对象 st_pptn_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "降水量")
@Accessors(chain = true)
public class StPptnRVo
{

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String stcd;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)		// 反序列化
    @JsonSerialize(using = LocalDateTimeSerializer.class)		// 序列化
    private LocalDateTime tm;


    @ApiModelProperty(value = "降水量")
    @Excel(name = "降水量")
    private BigDecimal drp;


    @ApiModelProperty(value = "累计降水量")
    @Excel(name = "累计时段降水量")
    private BigDecimal totalDrp;






}
