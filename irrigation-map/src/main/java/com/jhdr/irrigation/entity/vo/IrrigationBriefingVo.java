package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import com.jhdr.irrigation.entity.po.*;
import java.io.Serializable;
import java.util.List;
import java.util.Date;

/**
 * 灌区简报
 *
 * <AUTHOR>
 * @date 2025/02/24
 */
@Data
@ApiModel(description = "灌区简报")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationBriefingVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "简报ID")
    private Long id;

    @ApiModelProperty(value = "简报第多少期", example = "1")
    private Integer period;

    @ApiModelProperty(value = "报告日期", example = "2025-02-24T08:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportDate;

    @ApiModelProperty(value = "天气信息")
    private IrrigationWeatherDayVo weatherInfo;

    @ApiModelProperty(value = "上桥枢纽水情工情")
    private  PivotHubVo sqHubSituation;

    @ApiModelProperty(value = "阚疃枢纽水情工情")
    private PivotHubVo ktHubSituation ;
    @ApiModelProperty(value = "插花枢纽水情工情")
    private PivotHubVo chHubSituation ;
    @ApiModelProperty(value = "茨河铺枢纽水情工情")
    private PivotHubVo chpHubSituation ;

    @ApiModelProperty(value = "雨量简报")
    private GisRainDVo rainBriefing;

    @ApiModelProperty(value = "沿河泵闸站")
    private SituationAllNumVo situationsAllNum;


    @ApiModelProperty(value = "创建时间", example = "2025-02-24T08:30:00")
    private Date createTime;
}
