package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 堰闸水情对象 st_was_r
 *
 * <AUTHOR>
 * @date 2024-06-21
 */

@Data
@ApiModel(description = "堰闸水情")
@Accessors(chain = true)
@TableName(value ="st_was_r")
public class StWasRPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String stcd;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    @Excel(name = "闸上水位")
    private BigDecimal upz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal dwz;

    /** 总过闸流量 */
    @ApiModelProperty(value = "总过闸流量")
    @Excel(name = "总过闸流量")
    private BigDecimal tgtq;

    /** 闸水特征码 */
    @ApiModelProperty(value = "闸水特征码")
    @Excel(name = "闸水特征码")
    private String swchrcd;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    @Excel(name = "闸上水势")
    private String supwptn;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    @Excel(name = "闸下水势")
    private String sdwwptn;

    /** 测流方法 */
    @ApiModelProperty(value = "测流方法")
    @Excel(name = "测流方法")
    private String msqmt;

}
