package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 水闸运行明细对象 jh_slcrs_dtl_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水闸运行明细")
@Accessors(chain = true)
@JsonInclude()
public class JhSlcrsDtlRInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 闸门编码 */
    @ApiModelProperty(value = "闸门编码")
    private BigDecimal gpcd;

    @ApiModelProperty(value = "开启状态 0关 1开")
    private String om;

    /** 闸门开度 */
    @ApiModelProperty(value = "闸门开度")
    private BigDecimal gtoph;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal gtq;


    @ApiModelProperty(value = "累计流量")
    private BigDecimal accq;

}
