package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 泵站基本信息对象 jhir_pust_b
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "泵站汇总信息")
@Accessors(chain = true)
public class JhirPustSummaryVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "大型泵站")
    private String bigPump;

    @ApiModelProperty(value = "中心泵站")
    private String centrePump;

    @ApiModelProperty(value = "小型泵站")
    private String smallPump;

    @ApiModelProperty(value = "泵站数量")
    private String pumpNum;

    @ApiModelProperty(value = "装机总流量")
    private String insFlowAll;

}
