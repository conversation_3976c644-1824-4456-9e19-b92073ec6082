package com.jhdr.irrigation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-标准化-文档对象 irrigation_process_standard_doc
 *
 * <AUTHOR>
 * @date 2024-09-26
 */

@Data
@ApiModel(description = "工程运行-标准化-文档")
@Accessors(chain = true)
public class IrrigationProcessStandardDocParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 标准 ID */
    @ApiModelProperty(value = "标准 ID")
    @Excel(name = "标准 ID")
    private Long standardId;

}
