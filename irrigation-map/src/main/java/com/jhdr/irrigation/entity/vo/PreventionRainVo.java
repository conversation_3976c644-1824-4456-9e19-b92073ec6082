package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 水旱防御-雨量统计
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水旱防御-雨量统计")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class PreventionRainVo
{
    /** 枢纽编码 */
    @ApiModelProperty(value = "枢纽编码")
    private String hubCode;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    private String name;

    @ApiModelProperty(value = "关联雨量编码")
    private String raincd;

    @ApiModelProperty(value = "雨量七天")
    private List<StPptnRVo> sevenDays;

}
