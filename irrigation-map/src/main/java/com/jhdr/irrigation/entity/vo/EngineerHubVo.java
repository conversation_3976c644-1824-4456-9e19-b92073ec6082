package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 工情-枢纽信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "工情-枢纽信息")
@Accessors(chain = true)
@JsonInclude//返回值如果为空也返回字段
public class EngineerHubVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    @ApiModelProperty(value = "闸门情况")
    private List<JhSlcrsDtlRInfoVo> gateCondition;

    /** 抽水流量 */
    @ApiModelProperty(value = "机组流量")
    @Excel(name = "机组流量")
    private BigDecimal pmpq;

    @ApiModelProperty(value = "泵机情况")
    private List<JhPumpDtlRInfoVo> pumpCondition;





}
