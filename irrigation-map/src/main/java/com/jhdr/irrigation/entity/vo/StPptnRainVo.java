package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 降水量对象 st_pptn_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "降水量")
@Accessors(chain = true)
public class StPptnRainVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 时间 */
    @ApiModelProperty(value = "时间")
   private Date TM;

    /** 时段降水量 */
    @ApiModelProperty(value = "时段降水量")
    @Excel(name = "时段降水量")
    private BigDecimal DRP;


}
