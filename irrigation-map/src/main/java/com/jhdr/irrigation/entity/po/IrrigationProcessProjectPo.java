package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-项目管理对象 irrigation_process_project
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "工程运行-项目管理")
@Accessors(chain = true)
@TableName(value ="irrigation_process_project")
public class IrrigationProcessProjectPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 项目 ID */
    @ApiModelProperty(value = "项目 ID")
    @TableId
    private Long id;

    /** 项目名称 */
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称")
    private String projectName;

    /** 招标代理机构 */
    @ApiModelProperty(value = "招标代理机构")
    @Excel(name = "招标代理机构")
    private String biddingAgent;

    /** 承包单位 */
    @ApiModelProperty(value = "承包单位")
    @Excel(name = "承包单位")
    private String contractor;

    /** 预算批复 */
    @ApiModelProperty(value = "预算批复")
    @Excel(name = "预算批复")
    private BigDecimal budgetApproval;

    /** 合同金额 */
    @ApiModelProperty(value = "合同金额")
    @Excel(name = "合同金额")
    private BigDecimal contractAmount;

    /** 工程结算金额 */
    @ApiModelProperty(value = "工程结算金额")
    @Excel(name = "工程结算金额")
    private BigDecimal settlementAmount;

    /** 合同签订日期 */
    @ApiModelProperty(value = "合同签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "合同签订日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date contractSigningDate;

    /** 采购方式 */
    @ApiModelProperty(value = "采购方式")
    @Excel(name = "采购方式")
    private String procurementMethod;

    /** 分部验收情况 */
    @ApiModelProperty(value = "分部验收情况")
    @Excel(name = "分部验收情况")
    private String acceptanceSituation;

    /** 现场管理单位 */
    @ApiModelProperty(value = "现场管理单位")
    @Excel(name = "现场管理单位")
    private String siteManagementUnit;

    /** 开工日期 */
    @ApiModelProperty(value = "开工日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开工日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /** 竣工日期 */
    @ApiModelProperty(value = "竣工日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "竣工日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completionDate;

    /** 主要实施内容 */
    @ApiModelProperty(value = "主要实施内容")
    @Excel(name = "主要实施内容")
    private String mainContent;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
