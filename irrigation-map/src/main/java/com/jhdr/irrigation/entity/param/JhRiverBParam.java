package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河流信息对象 jh_river_b
 *
 * <AUTHOR>
 * @date 2024-06-19
 */

@Data
@ApiModel(description = "河流信息")
@Accessors(chain = true)
public class JhRiverBParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 河流代码 */
    @ApiModelProperty(value = "河流代码")
    @TableId
    private String ennmcd;

    /** 河流名称 */
    @ApiModelProperty(value = "河流名称")
    @Excel(name = "河流名称")
    private String rvnm;

    /** 河流类别:河道/干渠/支渠/斗渠/农渠（毛渠 */
    @ApiModelProperty(value = "河流类别:河道/干渠/支渠/斗渠/农渠（毛渠")
    @Excel(name = "河流类别:河道/干渠/支渠/斗渠/农渠", readConverterExp = "河流类别:河道/干渠/支渠/斗渠/农渠（毛渠")
    private String rvtp;

}
