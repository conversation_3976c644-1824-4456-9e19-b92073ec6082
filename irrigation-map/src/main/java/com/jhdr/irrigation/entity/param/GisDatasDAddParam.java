package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * GIS数据对象 gis_datas_d
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "GIS数据")
@Accessors(chain = true)
public class GisDatasDAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 数据ID */
    @ApiModelProperty(value = "数据ID")
    @TableId
    private String GUID;

    /** 数据编码 */
    @ApiModelProperty(value = "数据编码")
    @Excel(name = "数据编码")
    private String CD;

    /** 数据名称 */
    @ApiModelProperty(value = "数据名称")
    @Excel(name = "数据名称")
    private String NM;

    /** GIS数据 */
    @ApiModelProperty(value = "GIS数据")
    @Excel(name = "GIS数据")
    private String GISDATA;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息")
    @Excel(name = "扩展信息")
    private String GISSIGN;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal LGTD;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private BigDecimal LTTD;

}
