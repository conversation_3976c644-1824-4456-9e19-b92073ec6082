package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 降水量对象 st_pptn_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "降水量")
@Accessors(chain = true)
public class StPptnRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String STCD;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String TMStart;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String TMEnd;

    @ApiModelProperty(value = "查询类别 type 1 日期 type 2时分")
    private Integer type;
}
