package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Data
@ApiModel(description = "泵闸站")
@JsonInclude()
public class SituationCountyNumVo {


    @ApiModelProperty(value = "泵闸站开启 泵机或闸机数量")
    private int num;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量m³/s")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    /** 抽水流量 */
    @ApiModelProperty(value = "泵站抽水流量m³/s")
    @Excel(name = "抽水流量")
    private BigDecimal pmpq;

}
