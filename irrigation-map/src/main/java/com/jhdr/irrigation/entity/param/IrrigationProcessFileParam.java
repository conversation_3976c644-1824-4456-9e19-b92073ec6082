package com.jhdr.irrigation.entity.param;

import java.io.Serializable;
import java.util.Date;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-文件管理对象 irrigation_process_file
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "工程运行-文件管理")
@Accessors(chain = true)
public class IrrigationProcessFileParam implements Serializable
{
    private static final long serialVersionUID = 1L;



    /** 文件名称 */
    @ApiModelProperty(value = "文件名称")
    @Excel(name = "文件名称")
    private String fileName;



    /** 模块类型 1立项依据 2过程资料 3规章制度 9其他 */
    @ApiModelProperty(value = "模块类型 1立项依据 2过程资料 3规章制度 9其他")
    @Excel(name = "模块类型 1立项依据 2过程资料 3规章制度 9其他")
    private String moduleType;

    @ApiModelProperty(value = "开始时间 例如：yyyy-MM-dd HH:mm:ss")
    private String startTime; // 开始时间
    @ApiModelProperty(value = "结束时间 例如：yyyy-MM-dd HH:mm:ss")
    private String endTime; // 结束时间

    /** 模块id */
    @ApiModelProperty(value = "模块id")
    @Excel(name = "模块id")
    private Long moduleId;

}
