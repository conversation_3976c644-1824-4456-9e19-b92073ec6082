package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 水旱防御-枢纽信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水旱防御-枢纽信息")
@Accessors(chain = true)
@JsonInclude//返回值如果为空也返回字段
public class PreventionHubVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 枢纽编码 */
    @ApiModelProperty(value = "枢纽编码")
    private String hubCode;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    private String name;

    @ApiModelProperty(value = "枢纽状态 0关 1开")
    private String hubStatus;

    /** 所属行政名称 */
    @ApiModelProperty(value = "所属行政名称")
    @Excel(name = "所属行政名称")
    private String adminArea;

    /** 闸站编码 */
    @ApiModelProperty(value = "闸站编码")
    @Excel(name = "闸站编码")
    private String gataCode;

    /** 泵站编码 */
    @ApiModelProperty(value = "泵站编码")
    @Excel(name = "泵站编码")
    private String pustCode;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private BigDecimal lttd;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;


    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    private BigDecimal slupsz;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位状态 0未超限 1超限")
    private String slupszStatus;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    private BigDecimal sldsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位状态 0未超限 1超限")
    private String sldszStatus;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    @Excel(name = "闸上水势")
    private BigDecimal upswtp;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    @Excel(name = "闸下水势")
    private BigDecimal dswtp;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;
    /** 开启孔数 */
    @ApiModelProperty(value = "闸门开启孔数 大于0开启")
    @Excel(name = "开启孔数")
    private BigDecimal gtopn;

    /** 闸门数量 */
    @ApiModelProperty(value = "闸门数量")
    @Excel(name = "闸门数量")
    private BigDecimal gtcn;

    /** 开机台数 */
    @ApiModelProperty(value = "泵机开机台数 大于0开启")
    @Excel(name = "开机台数")
    private BigDecimal omcn;


    /** 机组数量 */
    @ApiModelProperty(value = "泵机机组数量")
    @Excel(name = "机组数量")
    private BigDecimal ppcn;

    /** 抽水流量 */
    @ApiModelProperty(value = "机组流量")
    @Excel(name = "机组流量")
    private BigDecimal pmpq;






}
