package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工情-数量统计
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "工情-数量统计")
@Accessors(chain = true)
@JsonInclude()
public class EngineerCountingVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "枢纽开启数量")
    private String hubOpenNum;

    @ApiModelProperty(value = "枢纽总数量")
    private String hubAllNum;

    @ApiModelProperty(value = "泵站开启数量")
    private String pumpOpenNum;

    @ApiModelProperty(value = "泵站总数量")
    private String pumpAllNum;

    @ApiModelProperty(value = "闸站开启数量")
    private String gateStationOpenNum;

    @ApiModelProperty(value = "闸站总数量")
    private String gateStationAllNum;
}
