package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 泵站运行状况对象 jh_pump_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "泵站运行状况")
@Accessors(chain = true)
public class JhPumpRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    /** 站上水位 */
    @ApiModelProperty(value = "站上水位")
    @Excel(name = "站上水位")
    private BigDecimal ppupz;

    /** 站下水位 */
    @ApiModelProperty(value = "站下水位")
    @Excel(name = "站下水位")
    private BigDecimal ppdwz;

    /** 开机台数 */
    @ApiModelProperty(value = "开机台数")
    @Excel(name = "开机台数")
    private BigDecimal omcn;

    /** 开机功率 */
    @ApiModelProperty(value = "开机功率")
    @Excel(name = "开机功率")
    private BigDecimal ompwr;

    /** 抽水流量 */
    @ApiModelProperty(value = "抽水流量")
    @Excel(name = "抽水流量")
    private BigDecimal pmpq;

    /** 水情特征码 */
    @ApiModelProperty(value = "水情特征码")
    @Excel(name = "水情特征码")
    private BigDecimal wchrcd;

    /** 站上水势 */
    @ApiModelProperty(value = "站上水势")
    @Excel(name = "站上水势")
    private BigDecimal ppupwptn;

    /** 站下水势 */
    @ApiModelProperty(value = "站下水势")
    @Excel(name = "站下水势")
    private BigDecimal ppdwwptn;

    /** 测流方法 */
    @ApiModelProperty(value = "测流方法")
    @Excel(name = "测流方法")
    private BigDecimal msqmt;

    /** 引流特征码 */
    @ApiModelProperty(value = "引流特征码")
    @Excel(name = "引流特征码")
    private BigDecimal pdchcd;

    /** 累计电能 */
    @ApiModelProperty(value = "累计电能")
    @Excel(name = "累计电能")
    private BigDecimal ppee;

    /** 机组数量 */
    @ApiModelProperty(value = "机组数量")
    @Excel(name = "机组数量")
    private BigDecimal ppcn;

    /** 站内ab相电压 */
    @ApiModelProperty(value = "站内ab相电压")
    @Excel(name = "站内ab相电压")
    private BigDecimal ppuab;

    /** 站内bc相电压 */
    @ApiModelProperty(value = "站内bc相电压")
    @Excel(name = "站内bc相电压")
    private BigDecimal ppubc;

    /** 站内ca相电压 */
    @ApiModelProperty(value = "站内ca相电压")
    @Excel(name = "站内ca相电压")
    private BigDecimal ppuca;

    /** 站内a相电流 */
    @ApiModelProperty(value = "站内a相电流")
    @Excel(name = "站内a相电流")
    private BigDecimal ppia;

    /** 站内b相电流 */
    @ApiModelProperty(value = "站内b相电流")
    @Excel(name = "站内b相电流")
    private BigDecimal ppib;

    /** 站内c相电流 */
    @ApiModelProperty(value = "站内c相电流")
    @Excel(name = "站内c相电流")
    private BigDecimal ppic;

    /** 站内有功功率 */
    @ApiModelProperty(value = "站内有功功率")
    @Excel(name = "站内有功功率")
    private BigDecimal ppapwr;

    /** 站内无功功率 */
    @ApiModelProperty(value = "站内无功功率")
    @Excel(name = "站内无功功率")
    private BigDecimal pprpwr;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal ppcos;

}
