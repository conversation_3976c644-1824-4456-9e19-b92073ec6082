package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 水旱防御-水情信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水旱防御-水情信息")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class PreventionWaterVo
{
    /** 枢纽编码 */
    @ApiModelProperty(value = "枢纽编码")
    private String hubCode;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    private String name;

    @ApiModelProperty(value = "闸站编码",hidden = true)
    private String gataCode;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal wwZ;

    @ApiModelProperty(value = "水位三天")
    private List<JhSlcrsRWaterVo> waterVos;

}
