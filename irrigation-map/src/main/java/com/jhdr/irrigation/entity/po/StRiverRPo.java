package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河道水情对象 st_river_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "河道水情")
@Accessors(chain = true)
@TableName(value ="st_river_r")
public class StRiverRPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @Excel(name = "测站编码")
    private String stcd;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private BigDecimal z;

    /** 流量 */
    @ApiModelProperty(value = "流量")
    @Excel(name = "流量")
    private BigDecimal q;

    /** 断面过水面积 */
    @ApiModelProperty(value = "断面过水面积")
    @Excel(name = "断面过水面积")
    private BigDecimal xsa;

    /** 断面平均流速 */
    @ApiModelProperty(value = "断面平均流速")
    @Excel(name = "断面平均流速")
    private BigDecimal xsavv;

    /** 断面最大流速 */
    @ApiModelProperty(value = "断面最大流速")
    @Excel(name = "断面最大流速")
    private BigDecimal xsmxv;

    /** 河水特征码 */
    @ApiModelProperty(value = "河水特征码")
    @Excel(name = "河水特征码")
    private String flwchrcd;

    /** 水势 */
    @ApiModelProperty(value = "水势")
    @Excel(name = "水势")
    private String wptn;

    /** 测流方法 */
    @ApiModelProperty(value = "测流方法")
    @Excel(name = "测流方法")
    private String msqmt;

    /** 测积方法 */
    @ApiModelProperty(value = "测积方法")
    @Excel(name = "测积方法")
    private String msamt;

    /** 测速方法 */
    @ApiModelProperty(value = "测速方法")
    @Excel(name = "测速方法")
    private String msvmt;

}
