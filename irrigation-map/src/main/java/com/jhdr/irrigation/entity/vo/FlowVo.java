package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "雨量站基本属性")
@Accessors(chain = true)
@JsonInclude()
public class FlowVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "基础信息")
    private StStbprpFlowVo stbprpFlowVo;


    @ApiModelProperty(value = "防洪指标信息")
    private StRvfcchBVo rvfcchBVo;


    /** 流量 */
    @ApiModelProperty(value = "流量")
    private BigDecimal q;

    @ApiModelProperty(value = "水位")
    private BigDecimal z;


    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    @ApiModelProperty(value = "日流量")
    private BigDecimal psq;

}
