package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 泵站运行明细对象 jh_pump_dtl_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "泵站运行明细")
@Accessors(chain = true)
public class JhPumpDtlRAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @Excel(name = "采集时间")
    private String clltm;

    /** 机组编码 */
    @ApiModelProperty(value = "机组编码")
    @Excel(name = "机组编码")
    private BigDecimal pmpcd;

    /** 运行标识 */
    @ApiModelProperty(value = "运行标识")
    @Excel(name = "运行标识")
    private BigDecimal om;

    /** ab相电压 */
    @ApiModelProperty(value = "ab相电压")
    @Excel(name = "ab相电压")
    private BigDecimal uab;

    /** bc相电压 */
    @ApiModelProperty(value = "bc相电压")
    @Excel(name = "bc相电压")
    private BigDecimal ubc;

    /** ca相电压 */
    @ApiModelProperty(value = "ca相电压")
    @Excel(name = "ca相电压")
    private BigDecimal uca;

    /** a相电流 */
    @ApiModelProperty(value = "a相电流")
    @Excel(name = "a相电流")
    private BigDecimal ia;

    /** b相电流 */
    @ApiModelProperty(value = "b相电流")
    @Excel(name = "b相电流")
    private BigDecimal ib;

    /** c相电流 */
    @ApiModelProperty(value = "c相电流")
    @Excel(name = "c相电流")
    private BigDecimal ic;

    /** 有功功率 */
    @ApiModelProperty(value = "有功功率")
    @Excel(name = "有功功率")
    private BigDecimal apwr;

    /** 无功功率 */
    @ApiModelProperty(value = "无功功率")
    @Excel(name = "无功功率")
    private BigDecimal rpwr;

    /** 功率因数 */
    @ApiModelProperty(value = "功率因数")
    @Excel(name = "功率因数")
    private BigDecimal cos;

    /** 累计电能 */
    @ApiModelProperty(value = "累计电能")
    @Excel(name = "累计电能")
    private BigDecimal ee;

    /** 排水量 */
    @ApiModelProperty(value = "排水量")
    @Excel(name = "排水量")
    private BigDecimal q;

}
