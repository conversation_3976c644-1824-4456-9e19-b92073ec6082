package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河道防洪指标对象 st_rvfcch_b
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "河道防洪指标")
@Accessors(chain = true)
public class StRvfcchBAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String STCD;

    /** 左堤高程 */
    @ApiModelProperty(value = "左堤高程")
    @Excel(name = "左堤高程")
    private BigDecimal LDKEL;

    /** 右堤高程 */
    @ApiModelProperty(value = "右堤高程")
    @Excel(name = "右堤高程")
    private BigDecimal RDKEL;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal WRZ;

    /** 警戒流量 */
    @ApiModelProperty(value = "警戒流量")
    @Excel(name = "警戒流量")
    private BigDecimal WRQ;

    /** 保证水位 */
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal GRZ;

    /** 保证流量 */
    @ApiModelProperty(value = "保证流量")
    @Excel(name = "保证流量")
    private BigDecimal GRQ;

    /** 平滩流量 */
    @ApiModelProperty(value = "平滩流量")
    @Excel(name = "平滩流量")
    private BigDecimal FLPQ;

    /** 实测最高水位 */
    @ApiModelProperty(value = "实测最高水位")
    @Excel(name = "实测最高水位")
    private BigDecimal OBHTZ;

    /** 实测最高水位出现时间 */
    @ApiModelProperty(value = "实测最高水位出现时间")
    @Excel(name = "实测最高水位出现时间")
    private String OBHTZTM;

    /** 调查最高水位 */
    @ApiModelProperty(value = "调查最高水位")
    @Excel(name = "调查最高水位")
    private BigDecimal IVHZ;

    /** 调查最高水位出现时间 */
    @ApiModelProperty(value = "调查最高水位出现时间")
    @Excel(name = "调查最高水位出现时间")
    private String IVHZTM;

    /** 实测最大流量 */
    @ApiModelProperty(value = "实测最大流量")
    @Excel(name = "实测最大流量")
    private BigDecimal OBMXQ;

    /** 实测最大流量出现时间 */
    @ApiModelProperty(value = "实测最大流量出现时间")
    @Excel(name = "实测最大流量出现时间")
    private String OBMXQTM;

    /** 调查最大流量 */
    @ApiModelProperty(value = "调查最大流量")
    @Excel(name = "调查最大流量")
    private BigDecimal IVMXQ;

    /** 调查最大流量出现时间 */
    @ApiModelProperty(value = "调查最大流量出现时间")
    @Excel(name = "调查最大流量出现时间")
    private String IVMXQTM;

    /** 历史最大含沙量 */
    @ApiModelProperty(value = "历史最大含沙量")
    @Excel(name = "历史最大含沙量")
    private BigDecimal HMXS;

    /** 历史最大含沙量出现时间 */
    @ApiModelProperty(value = "历史最大含沙量出现时间")
    @Excel(name = "历史最大含沙量出现时间")
    private String HMXSTM;

    /** 历史最大断面平均流速 */
    @ApiModelProperty(value = "历史最大断面平均流速")
    @Excel(name = "历史最大断面平均流速")
    private BigDecimal HMXAVV;

    /** 历史最大断面平均流速出现时间 */
    @ApiModelProperty(value = "历史最大断面平均流速出现时间")
    @Excel(name = "历史最大断面平均流速出现时间")
    private String HMXAVVTM;

    /** 历史最低水位 */
    @ApiModelProperty(value = "历史最低水位")
    @Excel(name = "历史最低水位")
    private BigDecimal HLZ;

    /** 历史最低水位出现时间 */
    @ApiModelProperty(value = "历史最低水位出现时间")
    @Excel(name = "历史最低水位出现时间")
    private String HLZTM;

    /** 历史最小流量 */
    @ApiModelProperty(value = "历史最小流量")
    @Excel(name = "历史最小流量")
    private BigDecimal HMNQ;

    /** 历史最小流量出现时间 */
    @ApiModelProperty(value = "历史最小流量出现时间")
    @Excel(name = "历史最小流量出现时间")
    private String HMNQTM;

    /** 高水位告警值 */
    @ApiModelProperty(value = "高水位告警值")
    @Excel(name = "高水位告警值")
    private BigDecimal TAZ;

    /** 大流量告警值 */
    @ApiModelProperty(value = "大流量告警值")
    @Excel(name = "大流量告警值")
    private BigDecimal TAQ;

    /** 低水位告警值 */
    @ApiModelProperty(value = "低水位告警值")
    @Excel(name = "低水位告警值")
    private BigDecimal LAZ;

    /** 小流量告警值 */
    @ApiModelProperty(value = "小流量告警值")
    @Excel(name = "小流量告警值")
    private BigDecimal LAQ;

    /** 启动预报水位标准 */
    @ApiModelProperty(value = "启动预报水位标准")
    @Excel(name = "启动预报水位标准")
    private BigDecimal SFZ;

    /** 启动预报流量标准 */
    @ApiModelProperty(value = "启动预报流量标准")
    @Excel(name = "启动预报流量标准")
    private BigDecimal SFQ;

    /** 时间戳 */
    @ApiModelProperty(value = "时间戳")
    @Excel(name = "时间戳")
    private String MODITIME;

}
