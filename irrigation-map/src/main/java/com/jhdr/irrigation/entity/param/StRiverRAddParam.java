package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河道水情对象 st_river_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "河道水情")
@Accessors(chain = true)
public class StRiverRAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @Excel(name = "测站编码")
    private String STCD;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date TM;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private BigDecimal Z;

    /** 流量 */
    @ApiModelProperty(value = "流量")
    @Excel(name = "流量")
    private BigDecimal Q;

    /** 断面过水面积 */
    @ApiModelProperty(value = "断面过水面积")
    @Excel(name = "断面过水面积")
    private BigDecimal XSA;

    /** 断面平均流速 */
    @ApiModelProperty(value = "断面平均流速")
    @Excel(name = "断面平均流速")
    private BigDecimal XSAVV;

    /** 断面最大流速 */
    @ApiModelProperty(value = "断面最大流速")
    @Excel(name = "断面最大流速")
    private BigDecimal XSMXV;

    /** 河水特征码 */
    @ApiModelProperty(value = "河水特征码")
    @Excel(name = "河水特征码")
    private String FLWCHRCD;

    /** 水势 */
    @ApiModelProperty(value = "水势")
    @Excel(name = "水势")
    private String WPTN;

    /** 测流方法 */
    @ApiModelProperty(value = "测流方法")
    @Excel(name = "测流方法")
    private String MSQMT;

    /** 测积方法 */
    @ApiModelProperty(value = "测积方法")
    @Excel(name = "测积方法")
    private String MSAMT;

    /** 测速方法 */
    @ApiModelProperty(value = "测速方法")
    @Excel(name = "测速方法")
    private String MSVMT;

}
