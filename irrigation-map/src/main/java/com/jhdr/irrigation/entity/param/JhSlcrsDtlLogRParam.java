package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水闸运行操作日志对象 jh_slcrs_dtl_log_r
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "水闸运行操作日志")
@Accessors(chain = true)
public class JhSlcrsDtlLogRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间")
    private String clltmStart;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间")
    private String clltmEnd;

}
