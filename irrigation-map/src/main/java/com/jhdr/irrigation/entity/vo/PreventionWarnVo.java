package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 水旱防御-水情信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水旱防御-超限站点")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class PreventionWarnVo
{
    /** 自动编码 */
    @ApiModelProperty(value = "自动编码")
    @TableId
    private Long id;

    /** 告警名称 */
    @ApiModelProperty(value = "告警名称")
    private String warnNm;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date stWarnTm;

    /** 报警信息 */
    @ApiModelProperty(value = "报警信息")
    private String warnSummary;

    /** 告警状态 */
    @ApiModelProperty(value = "告警状态")
    private String warnStatus;

}
