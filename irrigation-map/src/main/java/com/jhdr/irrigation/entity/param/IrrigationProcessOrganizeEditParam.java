package com.jhdr.irrigation.entity.param;

import java.io.Serializable;
import java.util.Date;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-组织架构对象 irrigation_process_organize
 *
 * <AUTHOR>
 * @date 2024-09-26
 */

@Data
@ApiModel(description = "工程运行-组织架构")
@Accessors(chain = true)
public class IrrigationProcessOrganizeEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 组织 ID */
    @ApiModelProperty(value = "组织 ID")
    @TableId
    private Long id;

    /** 姓名 */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名")
    private String name;

    /** 队内职务 */
    @ApiModelProperty(value = "队内职务")
    @Excel(name = "队内职务")
    private String duty;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
