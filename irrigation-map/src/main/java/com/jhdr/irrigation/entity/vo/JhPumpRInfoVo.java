package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 泵站运行状况对象 jh_pump_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "泵站运行状况")
@Accessors(chain = true)
@JsonInclude()
public class JhPumpRInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;


    /** 站上水位 */
    @ApiModelProperty(value = "站上水位")
    @Excel(name = "站上水位")
    private BigDecimal ppupz;

    /** 站下水位 */
    @ApiModelProperty(value = "站下水位")
    @Excel(name = "站下水位")
    private BigDecimal ppdwz;

    /** 开机台数 */
    @ApiModelProperty(value = "开机台数")
    @Excel(name = "开机台数")
    private BigDecimal omcn;

    /** 开机功率 */
    @ApiModelProperty(value = "开机功率")
    @Excel(name = "开机功率")
    private BigDecimal ompwr;

    /** 抽水流量 */
    @ApiModelProperty(value = "抽水流量")
    @Excel(name = "抽水流量")
    private BigDecimal pmpq;

    @ApiModelProperty(value = "累计流量 万m³")
    private BigDecimal accq;

    /** 站上水势 */
    @ApiModelProperty(value = "站上水势")
    @Excel(name = "站上水势")
    private BigDecimal ppupwptn;

    /** 站下水势 */
    @ApiModelProperty(value = "站下水势")
    @Excel(name = "站下水势")
    private BigDecimal ppdwwptn;


    /** 机组数量 */
    @ApiModelProperty(value = "机组数量")
    @Excel(name = "机组数量")
    private BigDecimal ppcn;


    @ApiModelProperty(value = "泵的情况")
    @Excel(name = "泵的情况")
    private List<JhPumpDtlRInfoVo> pumpDtlRInfoVos;

}
