package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 泵机运行操作记录对象 jh_pump_dtl_log_r
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "泵机运行操作记录")
@Accessors(chain = true)
@TableName(value ="jh_pump_dtl_log_r")
public class JhPumpDtlLogRPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @Excel(name = "采集时间")
    private String clltm;

    /** 机组编码 */
    @ApiModelProperty(value = "机组编码")
    private BigDecimal pmpcd;

    /** 运行标识 0关 1开 */
    @ApiModelProperty(value = "运行标识 0关 1开")
    @Excel(name = "运行标识 0关 1开")
    private String om;

    /** 累计电能 */
    @ApiModelProperty(value = "累计电能")
    @Excel(name = "累计电能")
    private BigDecimal ee;

    /** 排水量 */
    @ApiModelProperty(value = "排水量")
    @Excel(name = "排水量")
    private BigDecimal q;

    /** 操作员 */
    @ApiModelProperty(value = "操作员")
    @Excel(name = "操作员")
    private String operator;

}
