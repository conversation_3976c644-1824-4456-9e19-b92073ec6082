package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.beans.Transient;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 水旱防御-工程信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水旱防御-枢纽信息")
@Accessors(chain = true)
@JsonInclude//返回值如果为空也返回字段
public class PreventionProjectVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 枢纽编码 */
    @ApiModelProperty(value = "枢纽编码")
    private String hubCode;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    private String name;

    /** 闸孔数量 */
    @ApiModelProperty(value = "闸孔数量")
    @Excel(name = "闸孔数量")
    private BigDecimal gaorNum;

    /** 自排流量 */
    @ApiModelProperty(value = "闸站设计自排流量")
    private String zpll;

    /** 测站类型 */
    @ApiModelProperty(value = "闸站类型")
    private String wagaType;

    /** 水泵数量 */
    @ApiModelProperty(value = "水泵数量")
    @Excel(name = "水泵数量")
    private String pumpNum;

    /** 设计流量 */
    @ApiModelProperty(value = "泵站设计流量")
    private String desFlow;

    /** 主要建筑物级别 */
    @ApiModelProperty(value = "泵站工程规模类型")
    private String enSize;

    /** 调度单位 */
    @ApiModelProperty(value = "调度单位")
    private String dispatchUnit;

    /** 地址 */
    @ApiModelProperty(value = "所在地址")
    private String address;


}
