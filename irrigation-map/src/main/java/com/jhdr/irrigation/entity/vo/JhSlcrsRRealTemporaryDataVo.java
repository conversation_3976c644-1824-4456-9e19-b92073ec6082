package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 闸站运行状况快照对象 jh_slcrs_r_real
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "闸站水位数据表")
@Accessors(chain = true)
@JsonInclude()
public class JhSlcrsRRealTemporaryDataVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    @TableId
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    private BigDecimal upswtp;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    private BigDecimal dswtp;
}
