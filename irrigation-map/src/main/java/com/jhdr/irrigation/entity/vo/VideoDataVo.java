package com.jhdr.irrigation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 视频监控站点基础信息对象 jhom_vm_b
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "视频区域层级")
@Accessors(chain = true)
public class VideoDataVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "视频url")
    private String url;

    @ApiModelProperty(value = "相机编码")
    private String cameraCode;

    @ApiModelProperty(value = "通道名称（监控设备名称）")
    private String nm;



}
