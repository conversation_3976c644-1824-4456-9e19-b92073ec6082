package com.jhdr.irrigation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 工程运行-文件管理对象 irrigation_process_file
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "工程运行-文件管理")
@Accessors(chain = true)
public class IrrigationProcessFileInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 文件名称 */
    @ApiModelProperty(value = "文件名称")
    @Excel(name = "文件名称")
    private String fileName;

    /** 文件路径 */
    @ApiModelProperty(value = "文件路径")
    @Excel(name = "文件路径")
    private String fileUrl;

    /** 文件类型 */
    @ApiModelProperty(value = "文件类型 pdf")
    @Excel(name = "文件类型")
    private String fileType;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
