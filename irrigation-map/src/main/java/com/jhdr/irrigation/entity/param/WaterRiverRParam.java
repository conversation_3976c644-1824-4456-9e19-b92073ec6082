package com.jhdr.irrigation.entity.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 河道水情对象 st_river_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "水雨情-河道水情")
@Accessors(chain = true)
public class WaterRiverRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String TmStart;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String TmEnd;




}
