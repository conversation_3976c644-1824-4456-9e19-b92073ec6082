package com.jhdr.irrigation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "测站（水位，）层级")
@Accessors(chain = true)
public class StRiverRegionDataVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "当前区域编码")
    private String stcd;

    @ApiModelProperty(value = "名称")
    private String stnm;

    @ApiModelProperty(value = "父区域编码")
    private String addvcd;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private BigDecimal lttd;


    @ApiModelProperty("子集菜单")
    private List<StRiverRegionDataVo> children;

}
