package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 原始水量数据对象 jh_flowyc_r
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "原始水量数据")
@Accessors(chain = true)
public class JhFlowycREditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String STCD;

    /** 数据时间 */
    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "数据时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date DATATM;

    /** 入库时间 */
    @ApiModelProperty(value = "入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date RECVTM;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private BigDecimal Z;

    /** 流量 */
    @ApiModelProperty(value = "流量")
    @Excel(name = "流量")
    private BigDecimal Q;

    /** 断面流速 */
    @ApiModelProperty(value = "断面流速")
    @Excel(name = "断面流速")
    private BigDecimal SP;

    /** 指标流苏 */
    @ApiModelProperty(value = "指标流苏")
    @Excel(name = "指标流苏")
    private BigDecimal VS;

    /** 累计水量 */
    @ApiModelProperty(value = "累计水量")
    @Excel(name = "累计水量")
    private BigDecimal FLOWD;

    /** 时段水量 */
    @ApiModelProperty(value = "时段水量")
    @Excel(name = "时段水量")
    private BigDecimal DTRF;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String YCBM;

}
