package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 流量站时序数据对象 jh_flowsp_r
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "流量站时序数据")
@Accessors(chain = true)
public class JhFlowspRZVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String STCD;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date TM;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位(m)")
    private String z;

    @ApiModelProperty(value = "日水势")
    private String wptnexDay;



}
