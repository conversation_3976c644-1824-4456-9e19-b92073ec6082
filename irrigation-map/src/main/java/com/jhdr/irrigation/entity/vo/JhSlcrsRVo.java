package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水闸运行状况对象 jh_slcrs_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水闸运行状况")
@Accessors(chain = true)
public class JhSlcrsRVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    @TableId
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    /** 开启孔数 */
    @ApiModelProperty(value = "开启孔数")
    @Excel(name = "开启孔数")
    private BigDecimal gtopn;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    @Excel(name = "闸上水势")
    private BigDecimal upswtp;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    @Excel(name = "闸下水势")
    private BigDecimal dswtp;

    /** 填报单位代码 */
    @ApiModelProperty(value = "填报单位代码")
    @Excel(name = "填报单位代码")
    private String rpdpcd;

    /** 填报人 */
    @ApiModelProperty(value = "填报人")
    @Excel(name = "填报人")
    private String reporter;

    /** 填报人联系方式 */
    @ApiModelProperty(value = "填报人联系方式")
    @Excel(name = "填报人联系方式")
    private String rpttlnmb;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String rmk;

    /** 闸门数量 */
    @ApiModelProperty(value = "闸门数量")
    @Excel(name = "闸门数量")
    private BigDecimal gtcn;

}
