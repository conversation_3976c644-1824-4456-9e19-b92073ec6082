package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 水位站-水位站详情列表
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水位站-水位站详情列表")
@Accessors(chain = true)
@JsonInclude()
public class WaterSituationInfoListVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal wrz;

    /** 保证水位 */
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal grz;

    /** 实测最高水位 */
    @ApiModelProperty(value = "实测最高水位")
    @Excel(name = "实测最高水位")
    private BigDecimal obhtz;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private List<StRiverRVo> waterList;



}
