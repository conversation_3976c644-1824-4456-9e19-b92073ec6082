package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区基本信息对象 irrigation_district_info
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "灌区基本信息")
@Accessors(chain = true)
@TableName(value ="irrigation_district_info")
public class IrrigationDistrictInfoPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @TableId
    private Long id;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String name;

    /** 所属行政区名称 */
    @ApiModelProperty(value = "所属行政区名称")
    @Excel(name = "所属行政区名称")
    private String adminArea;

    /** 设计灌溉总面积（单位：万亩） */
    @ApiModelProperty(value = "设计灌溉总面积（单位：万亩）")
    @Excel(name = "设计灌溉总面积", readConverterExp = "单=位：万亩")
    private BigDecimal designedIrrigationArea;

    /** 有效灌溉面积（单位：万亩） */
    @ApiModelProperty(value = "有效灌溉面积（单位：万亩）")
    @Excel(name = "有效灌溉面积", readConverterExp = "单=位：万亩")
    private BigDecimal effectiveIrrigationArea;

    /** 耕地总面积（单位：万亩） */
    @ApiModelProperty(value = "耕地总面积（单位：万亩）")
    @Excel(name = "耕地总面积", readConverterExp = "单=位：万亩")
    private BigDecimal croplandArea;

    /** 灌区简要介绍 */
    @ApiModelProperty(value = "灌区简要介绍")
    @Excel(name = "灌区简要介绍")
    private String profile;

}
