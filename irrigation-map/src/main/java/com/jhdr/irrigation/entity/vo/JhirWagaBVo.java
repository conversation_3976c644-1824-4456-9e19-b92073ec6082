package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 闸站基础信息对象 jhir_waga_b
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "闸站基础信息")
@Accessors(chain = true)
public class JhirWagaBVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 建筑物编码 */
    @ApiModelProperty(value = "建筑物编码")
    @TableId
    private String strCode;

    /** 名称 */
    @ApiModelProperty(value = "名称")
    @Excel(name = "名称")
    private String strName;

    /** 地址 */
    @ApiModelProperty(value = "地址")
    @Excel(name = "地址")
    private String address;

    /** 测站类型 */
    @ApiModelProperty(value = "测站类型")
    @Excel(name = "测站类型")
    private String wagaType;

    /** 工程等别 */
    @ApiModelProperty(value = "工程等别")
    @Excel(name = "工程等别")
    private String engWait;

    /** 主要建筑物级别 */
    @ApiModelProperty(value = "主要建筑物级别")
    @Excel(name = "主要建筑物级别")
    private String majorType;

    /** 建成时间 */
    @ApiModelProperty(value = "建成时间")
    @Excel(name = "建成时间")
    private String completeTime;

    /** 闸孔数量 */
    @ApiModelProperty(value = "闸孔数量")
    @Excel(name = "闸孔数量")
    private BigDecimal gaorNum;

    /** 设计过闸时间 */
    @ApiModelProperty(value = "设计过闸时间")
    @Excel(name = "设计过闸时间")
    private BigDecimal desLockDisc;

    /** 闸孔总净宽 */
    @ApiModelProperty(value = "闸孔总净宽")
    @Excel(name = "闸孔总净宽")
    private BigDecimal gaorTotNetWid;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String note;

    /** 图片地址 */
    @ApiModelProperty(value = "图片地址")
    @Excel(name = "图片地址")
    private String url;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private String latd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private String lgtd;

    /** 工程规模 */
    @ApiModelProperty(value = "工程规模")
    @Excel(name = "工程规模")
    private String gcgm;

    /** 左右岸 */
    @ApiModelProperty(value = "左右岸")
    @Excel(name = "左右岸")
    private String zya;

    /** 自排流量 */
    @ApiModelProperty(value = "自排流量")
    @Excel(name = "自排流量")
    private String zpll;

    /** 自灌流量 */
    @ApiModelProperty(value = "自灌流量")
    @Excel(name = "自灌流量")
    private String zgll;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划")
    @Excel(name = "行政区划")
    private String addvcd;


    /** 关联雨量编码  st_pptn_r  关联STCD */
    @ApiModelProperty(value = "关联雨量编码  st_pptn_r  关联STCD",hidden = true)
    @Excel(name = "关联雨量编码  st_pptn_r  关联STCD")
    private String raincd;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal wwZ;

    /** 保证水位 */
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal swZ;

    /** 历史最高水位 */
    @ApiModelProperty(value = "历史最高水位")
    @Excel(name = "历史最高水位")
    private BigDecimal hisMaxZ;

    /** 常年平均水位 */
    @ApiModelProperty(value = "常年平均水位")
    @Excel(name = "常年平均水位")
    private BigDecimal allAvgZ;

    /** 视频站 jhom_vm_b 关联CD */
    @ApiModelProperty(value = "视频站 jhom_vm_b 关联CD",hidden = true)
    @Excel(name = "视频站 jhom_vm_b 关联CD")
    private String vmBCd;

    /** 站点简介 */
    @ApiModelProperty(value = "站点简介")
    @Excel(name = "站点简介")
    private String stDesc;

    /** 关联工情站编码 */
    @ApiModelProperty(value = "关联工情站编码",hidden = true)
    @Excel(name = "关联工情站编码")
    private String pidCode;

    /** 历史运行时间 */
    @ApiModelProperty(value = "历史运行时间",hidden = true)
    @Excel(name = "历史运行时间")
    private BigDecimal hisRt;

    /** 累计运行时间 */
    @ApiModelProperty(value = "累计运行时间",hidden = true)
    @Excel(name = "累计运行时间")
    private BigDecimal sumRt;

    /** 本次运行时间 */
    @ApiModelProperty(value = "本次运行时间",hidden = true)
    @Excel(name = "本次运行时间")
    private BigDecimal nowRt;

    /** 历史运行流量 */
    @ApiModelProperty(value = "历史运行流量",hidden = true)
    @Excel(name = "历史运行流量")
    private BigDecimal hisFw;

    /** 累计运行流量 */
    @ApiModelProperty(value = "累计运行流量",hidden = true)
    @Excel(name = "累计运行流量")
    private BigDecimal sumFw;

    /** 本次运行流量 */
    @ApiModelProperty(value = "本次运行流量",hidden = true)
    @Excel(name = "本次运行流量")
    private BigDecimal nowFw;

    @ApiModelProperty(value = "通讯方式 有线 无线")
    private String method;
}
