package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-标准化对象 irrigation_process_standard
 *
 * <AUTHOR>
 * @date 2024-09-26
 */

@Data
@ApiModel(description = "工程运行-标准化")
@Accessors(chain = true)
public class IrrigationProcessStandardParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 标准类型 1组织管理 2安全管理 3运行管理 4经济管理 5目标管理 6 制度化管理 7教育培训 8现场管理 9安全风险管控及隐患排查治理 10应急管理 11事故管理 12持续改进 */
    @ApiModelProperty(value = "标准类型 1组织管理 2安全管理 3运行管理 4经济管理 5目标管理 6 制度化管理 7教育培训 8现场管理 9安全风险管控及隐患排查治理 10应急管理 11事故管理 12持续改进")
    @Excel(name = "标准类型 1组织管理 2安全管理 3运行管理 4经济管理 5目标管理 6 制度化管理 7教育培训 8现场管理 9安全风险管控及隐患排查治理 10应急管理 11事故管理 12持续改进")
    private String standardType;

    /** 标准名称 */
    @ApiModelProperty(value = "标准名称")
    @Excel(name = "标准名称")
    private String standardName;

}
