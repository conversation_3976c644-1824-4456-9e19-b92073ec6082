package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河道水情对象 st_river_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "河道水情")
@Accessors(chain = true)
public class StRiverRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @Excel(name = "测站编码")
    private String STCD;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String TMStart;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String TMEnd;


    //状态 1 st_river_r 状态2 st_was_r
    private int status;


}
