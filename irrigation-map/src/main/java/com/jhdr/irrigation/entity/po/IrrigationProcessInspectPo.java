package com.jhdr.irrigation.entity.po;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-检查对象 irrigation_process_inspect
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "工程运行-检查")
@Accessors(chain = true)
@TableName(value ="irrigation_process_inspect")
public class IrrigationProcessInspectPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 排查 ID */
    @ApiModelProperty(value = "排查 ID")
    @TableId
    private Long id;

    /** 排查单位 */
    @ApiModelProperty(value = "排查单位")
    @Excel(name = "排查单位")
    private String inspectionUnit;

    /** 被检查单位 1上桥抽水站 2上桥节制闸 3上桥船闸4荆山湖进洪闸 5荆山湖退洪闸 6上桥灌引涵 7芡河排涝涵 8跃进沟地下涵 9安全检查 */
    @ApiModelProperty(value = "被检查单位 1上桥抽水站 2上桥节制闸 3上桥船闸4荆山湖进洪闸 5荆山湖退洪闸 6上桥灌引涵 7芡河排涝涵 8跃进沟地下涵 9安全检查")
    @Excel(name = "被检查单位 1上桥抽水站 2上桥节制闸 3上桥船闸4荆山湖进洪闸 5荆山湖退洪闸 6上桥灌引涵 7芡河排涝涵 8跃进沟地下涵 9安全检查")
    private String inspectedUnit;

    /** 检查类型 1日常检查  2专项检查 */
    @ApiModelProperty(value = "检查类型 1日常检查  2专项检查")
    @Excel(name = "检查类型 1日常检查  2专项检查")
    private String inspectionType;

    /** 排查日期 */
    @ApiModelProperty(value = "排查日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "排查日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date inspectionDate;

    /** 录入日期 */
    @ApiModelProperty(value = "录入日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "录入日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date entryDate;

    /** 是否有隐患 */
    @ApiModelProperty(value = "是否有隐患")
    @Excel(name = "是否有隐患")
    private String hasHazards;

    /** 检查事项 */
    @ApiModelProperty(value = "检查事项")
    @Excel(name = "检查事项")
    private String inspectionItems;

    /** 隐患内容 */
    @ApiModelProperty(value = "隐患内容")
    @Excel(name = "隐患内容")
    private String hazardContent;

    /** 隐患级别 */
    @ApiModelProperty(value = "隐患级别")
    @Excel(name = "隐患级别")
    private String hazardLevel;

    /** 隐患类型 */
    @ApiModelProperty(value = "隐患类型")
    @Excel(name = "隐患类型")
    private String hazardType;

    /** 隐患状态 */
    @ApiModelProperty(value = "隐患状态")
    @Excel(name = "隐患状态")
    private String hazardStatus;

    /** 督办状态 */
    @ApiModelProperty(value = "督办状态")
    @Excel(name = "督办状态")
    private String supervisionStatus;

    /** 要求整改时间 */
    @ApiModelProperty(value = "要求整改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "要求整改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date rectificationDeadline;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
