package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 告警记录对象 jhst_warnrecord_r
 *
 * <AUTHOR>
 * @date 2024-07-19
 */

@Data
@ApiModel(description = "告警记录")
@Accessors(chain = true)
@TableName(value ="jhst_warnrecord_r")
public class JhstWarnrecordRPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 自动编码 */
    @ApiModelProperty(value = "自动编码")
    @TableId
    private Long id;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date stWarnTm;

    /** 告警名称 */
    @ApiModelProperty(value = "告警名称")
    @Excel(name = "告警名称")
    private String warnNm;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String warnType;

    /** 告警等级 */
    @ApiModelProperty(value = "告警等级")
    @Excel(name = "告警等级")
    private String warnGrade;

    /** 告警来源 */
    @ApiModelProperty(value = "告警来源")
    @Excel(name = "告警来源")
    private String warnSource;

    /** 告警状态 */
    @ApiModelProperty(value = "告警状态")
    @Excel(name = "告警状态")
    private String warnStatus;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @Excel(name = "站点编码")
    private String stcd;

    /** 站点名称 */
    @ApiModelProperty(value = "站点名称")
    @Excel(name = "站点名称")
    private String stnm;

    /** 流域 */
    @ApiModelProperty(value = "流域")
    @Excel(name = "流域")
    private String bsnm;

    /** 水系 */
    @ApiModelProperty(value = "水系")
    @Excel(name = "水系")
    private String hnnm;

    /** 河流 */
    @ApiModelProperty(value = "河流")
    @Excel(name = "河流")
    private String rvnm;

    /** 站址 */
    @ApiModelProperty(value = "站址")
    @Excel(name = "站址")
    private String stlc;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划")
    @Excel(name = "行政区划")
    private String addvcd;

    /** 县区名 */
    @ApiModelProperty(value = "县区名")
    @Excel(name = "县区名")
    private String countyNm;

    /** 乡镇名 */
    @ApiModelProperty(value = "乡镇名")
    @Excel(name = "乡镇名")
    private String townNm;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;

    /** 维度 */
    @ApiModelProperty(value = "维度")
    @Excel(name = "维度")
    private BigDecimal lttd;

    /** 报警参数1 */
    @ApiModelProperty(value = "报警参数1")
    @Excel(name = "报警参数1")
    private String warnC1;

    /** 报警参数2 */
    @ApiModelProperty(value = "报警参数2")
    @Excel(name = "报警参数2")
    private String warnC2;

    /** 报警参数3 */
    @ApiModelProperty(value = "报警参数3")
    @Excel(name = "报警参数3")
    private String warnC3;

    /** 报警信息 */
    @ApiModelProperty(value = "报警信息")
    @Excel(name = "报警信息")
    private String warnSummary;

    /** 发送人 */
    @ApiModelProperty(value = "发送人")
    @Excel(name = "发送人")
    private String sender;

    /** 发送时间 */
    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date senderTm;

    /** 时间标记 */
    @ApiModelProperty(value = "时间标记")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间标记", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ctm;

}
