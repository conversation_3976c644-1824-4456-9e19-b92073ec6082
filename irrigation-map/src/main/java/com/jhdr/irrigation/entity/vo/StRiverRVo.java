package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河道水情对象 st_river_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "河道水情")
@Accessors(chain = true)
public class StRiverRVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @Excel(name = "测站编码")
    private String stcd;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位(m)")
    private BigDecimal z;
//
//    /** 流量 */
//    @ApiModelProperty(value = "流量")
//    @Excel(name = "流量")
//    private BigDecimal q;

}
