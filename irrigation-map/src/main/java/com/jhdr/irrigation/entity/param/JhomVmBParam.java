package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 视频监控站点基础信息对象 jhom_vm_b
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "视频监控站点基础信息")
@Accessors(chain = true)
public class JhomVmBParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 设备编号 */
    @ApiModelProperty(value = "设备编号")
    @Excel(name = "设备编号")
    private String cd;

    /** 设备序列号 */
    @ApiModelProperty(value = "设备序列号")
    @Excel(name = "设备序列号")
    private String sernum;

    /** 设备ID */
    @ApiModelProperty(value = "设备ID")
    @Excel(name = "设备ID")
    private String deviceid;

    /** 设备型号 */
    @ApiModelProperty(value = "设备型号")
    @Excel(name = "设备型号")
    private String model;

    /** 设备厂商 */
    @ApiModelProperty(value = "设备厂商")
    @Excel(name = "设备厂商")
    private String oem;

    /** 分辨率 */
    @ApiModelProperty(value = "分辨率")
    @Excel(name = "分辨率")
    private BigDecimal resolution;

    /** 视角范围 */
    @ApiModelProperty(value = "视角范围")
    @Excel(name = "视角范围")
    private BigDecimal viewang;

    /** 所属硬盘录像机 */
    @ApiModelProperty(value = "所属硬盘录像机")
    @Excel(name = "所属硬盘录像机")
    private String nvr;

    /** 通道名称 */
    @ApiModelProperty(value = "通道名称")
    @Excel(name = "通道名称")
    private String nm;

    /** 通道号 */
    @ApiModelProperty(value = "通道号")
    @Excel(name = "通道号")
    private String cn;

    /** 协议类型 */
    @ApiModelProperty(value = "协议类型")
    @Excel(name = "协议类型")
    private String protocol;

    /** IP */
    @ApiModelProperty(value = "IP")
    @Excel(name = "IP")
    private String ip;

    /** IP端口号 */
    @ApiModelProperty(value = "IP端口号")
    @Excel(name = "IP端口号")
    private BigDecimal ipport;

    /** 设备通道号 */
    @ApiModelProperty(value = "设备通道号")
    @Excel(name = "设备通道号")
    private BigDecimal sbcn;

    /** 管理端口 */
    @ApiModelProperty(value = "管理端口")
    @Excel(name = "管理端口")
    private BigDecimal mport;

    /** 设备状态 */
    @ApiModelProperty(value = "设备状态")
    @Excel(name = "设备状态")
    private String status;

    /** 所属项目 */
    @ApiModelProperty(value = "所属项目")
    @Excel(name = "所属项目")
    private String project;

    /** 安装单位 */
    @ApiModelProperty(value = "安装单位")
    @Excel(name = "安装单位")
    private String instUnit;

    /** 安装日期 */
    @ApiModelProperty(value = "安装日期")
    @Excel(name = "安装日期")
    private String instdate;

    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String mangUnit;

    /** 维护单位 */
    @ApiModelProperty(value = "维护单位")
    @Excel(name = "维护单位")
    private String maitUnit;

    /** 维保信息 */
    @ApiModelProperty(value = "维保信息")
    @Excel(name = "维保信息")
    private String maitTel;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划")
    @Excel(name = "行政区划")
    private String addvcd;

    /** 安装地址 */
    @ApiModelProperty(value = "安装地址")
    @Excel(name = "安装地址")
    private String stlc;

    /** 安装图片 */
    @ApiModelProperty(value = "安装图片")
    @Excel(name = "安装图片")
    private String instPic;

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    @Excel(name = "用户名")
    private String username;

    /** 密码 */
    @ApiModelProperty(value = "密码")
    @Excel(name = "密码")
    private String password;

    /** 上次维护时间 */
    @ApiModelProperty(value = "上次维护时间")
    @Excel(name = "上次维护时间")
    private String lastmaittm;

    /** 下次维护时间 */
    @ApiModelProperty(value = "下次维护时间")
    @Excel(name = "下次维护时间")
    private String nextmaittm;

    /** 状态监测时间 */
    @ApiModelProperty(value = "状态监测时间")
    @Excel(name = "状态监测时间")
    private String checktm;

    /** 是否录像 */
    @ApiModelProperty(value = "是否录像")
    @Excel(name = "是否录像")
    private BigDecimal recyn;

    /** 最大录像时长 */
    @ApiModelProperty(value = "最大录像时长")
    @Excel(name = "最大录像时长")
    private BigDecimal recmaxhours;

    /** 位置经度 */
    @ApiModelProperty(value = "位置经度")
    @Excel(name = "位置经度")
    private String lgtd;

    /** 位置纬度 */
    @ApiModelProperty(value = "位置纬度")
    @Excel(name = "位置纬度")
    private String lttd;

    /** 主控版本 */
    @ApiModelProperty(value = "主控版本")
    @Excel(name = "主控版本")
    private String masterVersion;

    /** 编码版本 */
    @ApiModelProperty(value = "编码版本")
    @Excel(name = "编码版本")
    private String codeVersion;

    /** web版本 */
    @ApiModelProperty(value = "web版本")
    @Excel(name = "web版本")
    private String webVersion;

    /** plugin版本 */
    @ApiModelProperty(value = "plugin版本")
    @Excel(name = "plugin版本")
    private String pluginVersion;

    /** 通道个数 */
    @ApiModelProperty(value = "通道个数")
    @Excel(name = "通道个数")
    private String channelNum;

    /** 硬盘个数 */
    @ApiModelProperty(value = "硬盘个数")
    @Excel(name = "硬盘个数")
    private Long diskNum;

    /** 报警输入个数 */
    @ApiModelProperty(value = "报警输入个数")
    @Excel(name = "报警输入个数")
    private String alarmEnter;

    /** 报警输出个数 */
    @ApiModelProperty(value = "报警输出个数")
    @Excel(name = "报警输出个数")
    private String alarmOutput;

    /** 通讯方式 */
    @ApiModelProperty(value = "通讯方式")
    @Excel(name = "通讯方式")
    private String method;

    /** 安全性 */
    @ApiModelProperty(value = "安全性")
    @Excel(name = "安全性")
    private String security;

}
