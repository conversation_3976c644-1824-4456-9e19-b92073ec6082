package com.jhdr.irrigation.entity.param;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 泵机运行操作记录对象 jh_pump_dtl_log_r
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "泵机运行操作记录")
@Accessors(chain = true)
public class JhPumpDtlLogRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String clltmStart;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String clltmEnd;



}
