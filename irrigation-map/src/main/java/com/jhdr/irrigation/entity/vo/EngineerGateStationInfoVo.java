package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 水闸运行状况对象 jh_slcrs_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "闸站运行状况")
@Accessors(chain = true)
@JsonInclude()
public class EngineerGateStationInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;


    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    /** 开启孔数 */
    @ApiModelProperty(value = "开启孔数")
    @Excel(name = "开启孔数")
    private BigDecimal gtopn;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    @Excel(name = "闸上水势")
    private BigDecimal upswtp;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    @Excel(name = "闸下水势")
    private BigDecimal dswtp;


    /** 闸门数量 */
    @ApiModelProperty(value = "闸门数量")
    @Excel(name = "闸门数量")
    private BigDecimal gtcn;

    @ApiModelProperty(value = "闸门运行明细")
    @Excel(name = "闸门运行明细")
    private List<JhSlcrsDtlRInfoVo> slcrsRInfoVos;




}
