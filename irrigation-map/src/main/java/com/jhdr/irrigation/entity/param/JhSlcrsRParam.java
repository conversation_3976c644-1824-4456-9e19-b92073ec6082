package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水闸运行状况对象 jh_slcrs_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水闸运行状况")
@Accessors(chain = true)
public class JhSlcrsRParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String clltmStart;

    @ApiModelProperty(value = "采集时间结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String clltmEnd;


}
