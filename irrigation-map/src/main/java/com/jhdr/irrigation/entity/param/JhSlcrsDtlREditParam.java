package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水闸运行明细对象 jh_slcrs_dtl_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水闸运行明细")
@Accessors(chain = true)
public class JhSlcrsDtlREditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @Excel(name = "采集时间")
    private String clltm;

    /** 闸门编码 */
    @ApiModelProperty(value = "闸门编码")
    @Excel(name = "闸门编码")
    private BigDecimal gpcd;

    /** 闸门开度 */
    @ApiModelProperty(value = "闸门开度")
    @Excel(name = "闸门开度")
    private BigDecimal gtoph;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal gtq;

    /** 闸门上升信号 */
    @ApiModelProperty(value = "闸门上升信号")
    @Excel(name = "闸门上升信号")
    private BigDecimal gtup;

    /** 闸门下降信号 */
    @ApiModelProperty(value = "闸门下降信号")
    @Excel(name = "闸门下降信号")
    private BigDecimal gtdw;

    /** 闸门全开 */
    @ApiModelProperty(value = "闸门全开")
    @Excel(name = "闸门全开")
    private BigDecimal gtaop;

    /** 闸门全关 */
    @ApiModelProperty(value = "闸门全关")
    @Excel(name = "闸门全关")
    private BigDecimal gtaco;

}
