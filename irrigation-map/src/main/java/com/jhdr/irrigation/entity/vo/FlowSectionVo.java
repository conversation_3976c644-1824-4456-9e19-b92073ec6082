package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "雨量站基本属性")
@Accessors(chain = true)
@JsonInclude()
public class FlowSectionVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal wrz;

    /** 保证水位 */
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal grz;

    /** 实测最高水位 */
    @ApiModelProperty(value = "实测最高水位")
    @Excel(name = "实测最高水位")
    private BigDecimal obhtz;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private BigDecimal z;


    @ApiModelProperty(value = "实时流量")
    private BigDecimal q;

    @ApiModelProperty(value = "年累计取水量 万m³")
    private BigDecimal accqYear;

}
