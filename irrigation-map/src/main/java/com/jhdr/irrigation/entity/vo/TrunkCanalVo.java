package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 河流信息对象 jh_river_b
 *
 * <AUTHOR>
 * @date 2024-06-19
 */

@Data
@ApiModel(description = "干渠或者支渠汇总信息")
@Accessors(chain = true)
public class TrunkCanalVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "干渠或者支渠数量")
    private Integer num;

    @ApiModelProperty(value = "干渠或者支渠长度")
    private String length;

}
