package com.jhdr.irrigation.entity.vo;

import java.io.Serializable;
import java.util.Date;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 天气数据对象 weather_data
 *
 * <AUTHOR>
 * @date 2024-07-30
 */

@Data
@ApiModel(description = "天气数据")
@Accessors(chain = true)
public class WeatherDataVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 天气数据id */
    @ApiModelProperty(value = "天气数据id")
    @TableId
    private Long id;

    /** 地区 */
    @ApiModelProperty(value = "地区")
    @Excel(name = "地区")
    private String region;

    /** 位置，经纬度或者其他标识 */
    @ApiModelProperty(value = "位置，经纬度或者其他标识")
    @Excel(name = "位置，经纬度或者其他标识")
    private String location;

    /** 天气数据json */
    @ApiModelProperty(value = "天气数据json")
    @Excel(name = "天气数据json")
    private String weatherJson;

    /** 0关闭 1启用 */
    @ApiModelProperty(value = "0关闭 1启用")
    @Excel(name = "0关闭 1启用")
    private Long status;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
