package com.jhdr.irrigation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 海康视频区域层级对象 hikvision_video_region
 *
 * <AUTHOR>
 * @date 2024-07-04
 */

@Data
@ApiModel(description = "海康视频区域层级")
@Accessors(chain = true)
public class HikvisionVideoRegionEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 视频区域层级id */
    @ApiModelProperty(value = "视频区域层级id")
    @TableId
    private Long id;

    /** 区域完整路径，含本节点，@进行分割，上级节点在前 */
    @ApiModelProperty(value = "区域完整路径，含本节点，@进行分割，上级节点在前")
    @Excel(name = "区域完整路径，含本节点，@进行分割，上级节点在前")
    private String regionPath;

    /** 区域标识
0：本级
1：级联
2：混合，下级推送给上级的本级点（杭州本级有滨江，然后下级滨江又把自己推送上来了，滨江是混合区域节点）
入参cascadeFlag与返回值对应：
cascadeFlag=0：返回0、1、2
cascadeFlag=1：返回0、2 cascadeFlag=2：返回1、2 */
    @ApiModelProperty(value = "区域标识\n" +
            "0：本级\n" +
            "1：级联\n" +
            "2：混合，下级推送给上级的本级点（杭州本级有滨江，然后下级滨江又把自己推送上来了，滨江是混合区域节点）\n" +
            "入参cascadeFlag与返回值对应：\n" +
            "cascadeFlag=0：返回0、1、2\n" +
            "cascadeFlag=1：返回0、2 cascadeFlag=2：返回1、2")
    private String cascadeType;

    /** 区域类型
0: 国标区域
1: 雪亮工程区域
2: 司法行政区域
9: 自定义区域
10: 历史兼容版本占用普通区域
11: 历史兼容版本占用级联区域
12: 楼栋单元 */
    @ApiModelProperty(value = "区域类型\n" +
            "0: 国标区域\n" +
            "1: 雪亮工程区域\n" +
            "2: 司法行政区域\n" +
            "9: 自定义区域\n" +
            "10: 历史兼容版本占用普通区域\n" +
            "11: 历史兼容版本占用级联区域\n" +
            "12: 楼栋单元\")\n" +
            "    @Excel(name = \"区域类型\n" +
            "0: 国标区域\n" +
            "1: 雪亮工程区域\n" +
            "2: 司法行政区域\n" +
            "9: 自定义区域\n" +
            "10: 历史兼容版本占用普通区域\n" +
            "11: 历史兼容版本占用级联区域\n" +
            "12: 楼栋单元")
    private String catalogType;

    /** 级联平台标识，多个级联编号以@分隔，本级区域默认值“0” */
    @ApiModelProperty(value = "级联平台标识，多个级联编号以@分隔，本级区域默认值“0”")
    @Excel(name = "级联平台标识，多个级联编号以@分隔，本级区域默认值“0”")
    private String cascadeCode;

    /** 用于标识区域节点是否有权限操作，true：有权限 false：无权限 */
    @ApiModelProperty(value = "用于标识区域节点是否有权限操作，true：有权限 false：无权限")
    @Excel(name = "用于标识区域节点是否有权限操作，true：有权限 false：无权限")
    private String available;

    /** 区域编码 */
    @ApiModelProperty(value = "区域编码")
    @Excel(name = "区域编码")
    private String indexCode;

    /** 创建时间，要求遵守ISO8601标准，如2018-07-26T21:30:08.322+08:00 */
    @ApiModelProperty(value = "创建时间，要求遵守ISO8601标准，如2018-07-26T21:30:08.322+08:00")
    private String updateTime;

    /** 同级区域顺序 */
    @ApiModelProperty(value = "同级区域顺序")
    @Excel(name = "同级区域顺序")
    private String sort;

    /** true:是叶子节点，表示该区域下面未挂区域 false:不是叶子节点，表示该区域下面挂有区域 */
    @ApiModelProperty(value = "true:是叶子节点，表示该区域下面未挂区域 false:不是叶子节点，表示该区域下面挂有区域")
    @Excel(name = "true:是叶子节点，表示该区域下面未挂区域 false:不是叶子节点，表示该区域下面挂有区域")
    private String leaf;

    /** 外码编码，regionType为12时有效，regionType为10时忽略该值 */
    @ApiModelProperty(value = "外码编码，regionType为12时有效，regionType为10时忽略该值")
    @Excel(name = "外码编码，regionType为12时有效，regionType为10时忽略该值")
    private String regionCode;

    /** 创建时间，要求遵守ISO8601标准，如2018-07-26T21:30:08.322+08:00 */
    @ApiModelProperty(value = "创建时间，要求遵守ISO8601标准，如2018-07-26T21:30:08.322+08:00")
    private String createTime;

    /** 区域名称 */
    @ApiModelProperty(value = "区域名称")
    @Excel(name = "区域名称")
    private String name;

    /** 父区域唯一标识码 */
    @ApiModelProperty(value = "父区域唯一标识码")
    @Excel(name = "父区域唯一标识码")
    private String parentIndexCode;

}
