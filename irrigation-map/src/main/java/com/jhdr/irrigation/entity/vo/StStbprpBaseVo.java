package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "雨量站")
public class StStbprpBaseVo
{

        /** 测站编码 */
        @ApiModelProperty(value = "测站编码")
        private String stcd;

        /** 测站名称 */
        @ApiModelProperty(value = "测站名称")
        private String stnm;

        /** 河流名称 */
        @ApiModelProperty(value = "河流名称")
        private String rvnm;

        /** 水系名称 */
        @ApiModelProperty(value = "水系名称")
        private String hnnm;

        /** 流域名称 */
        @ApiModelProperty(value = "流域名称")
        private String bsnm;

        /** 站址 */
        @ApiModelProperty(value = "站址")
        private String stlc;

        /** 行政区划码 */
        @ApiModelProperty(value = "行政区划码")
        private String addvcd;

        /** 基面名称 */
        @ApiModelProperty(value = "基面名称")
        private String dtmnm;

        /** 基面高程 */
        @ApiModelProperty(value = "基面高程")
        private BigDecimal dtmel;

        /** 基面修正值 */
        @ApiModelProperty(value = "基面修正值")
        private BigDecimal dtpr;


    /** 建站年月 */
    @ApiModelProperty(value = "建站年月")
    private String esstym;

    /** 始报年月 */
    @ApiModelProperty(value = "始报年月")
    private String bgfrym;

    /** 隶属行业单位 */
    @ApiModelProperty(value = "隶属行业单位")
    private String atcunit;

    /** 信息管理单位 */
    @ApiModelProperty(value = "信息管理单位")
    private String admauth;

    /** 交换管理单位 */
    @ApiModelProperty(value = "交换管理单位")
    private String locality;

    /** 测站岸别 */
    @ApiModelProperty(value = "测站岸别")
    private String stbk;

    /** 测站方位 */
    @ApiModelProperty(value = "测站方位")
    private BigDecimal stazt;

    /** 至河口距离 */
    @ApiModelProperty(value = "至河口距离")
    private BigDecimal dstrvm;

    /** 集水面积 */
    @ApiModelProperty(value = "集水面积")
    private BigDecimal drna;

    /** 拼音码 */
    @ApiModelProperty(value = "拼音码")
    private String phcd;

    /** 时段降水量 */
    @ApiModelProperty(value = "时段降水量")
    private BigDecimal drp;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;


}
