package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "流量站断面")
@Accessors(chain = true)
@JsonInclude()
public class FlowCrossSectionVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "编码")
    private String stcd;

    @ApiModelProperty(value = "x坐标")
    private BigDecimal xAxis;

    @ApiModelProperty(value = "y坐标")
    private BigDecimal yAxis;

    @ApiModelProperty(value = "断面流水面积m²")
    private String   streamArea;

    @ApiModelProperty(value = "断面平均流速")
    private String   flowRate;
    @ApiModelProperty(value = "断面最大流速")
    private String   maxRate;






}
