package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "流量站日 水量")
@Accessors(chain = true)
@JsonInclude()
public class JhFlowtnRVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 时间 */
    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date tm;

    @ApiModelProperty(value = "日水量  /月流量m³")
    private BigDecimal psq;

    @ApiModelProperty(value = "累计日水量/月流量 m³")
    private BigDecimal totalPsq;



}
