package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 降水量对象 st_pptn_r
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "降水量")
@Accessors(chain = true)
public class StPptnREditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    @TableId
    private String STCD;

    /** 时间 */
    @ApiModelProperty(value = "时间")
   private Date TM;

    /** 时段降水量 */
    @ApiModelProperty(value = "时段降水量")
    @Excel(name = "时段降水量")
    private BigDecimal DRP;

    /** 时段长 */
    @ApiModelProperty(value = "时段长")
    @Excel(name = "时段长")
    private BigDecimal INTV;

    /** 降水历时 */
    @ApiModelProperty(value = "降水历时")
    @Excel(name = "降水历时")
    private BigDecimal PDR;

    /** 日降水量 */
    @ApiModelProperty(value = "日降水量")
    @Excel(name = "日降水量")
    private BigDecimal DYP;

    /** 天气状况 */
    @ApiModelProperty(value = "天气状况")
    @Excel(name = "天气状况")
    private String WTH;

}
