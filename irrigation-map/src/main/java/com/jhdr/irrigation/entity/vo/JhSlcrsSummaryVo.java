package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 *闸站汇总信息
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "闸站汇总信息")
@Accessors(chain = true)
public class JhSlcrsSummaryVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "大型闸站")
    private String bigStation;

    @ApiModelProperty(value = "中心闸站")
    private String centreStation;

    @ApiModelProperty(value = "小型闸站")
    private String smallStation;

    @ApiModelProperty(value = "闸站数量")
    private String stationNum;
}
