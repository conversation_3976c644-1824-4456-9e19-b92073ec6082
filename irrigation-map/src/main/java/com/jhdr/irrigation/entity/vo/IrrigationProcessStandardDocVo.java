package com.jhdr.irrigation.entity.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程运行-标准化-文档对象 irrigation_process_standard_doc
 *
 * <AUTHOR>
 * @date 2024-09-26
 */

@Data
@ApiModel(description = "工程运行-标准化-文档")
@Accessors(chain = true)
public class IrrigationProcessStandardDocVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 文档 ID */
    @ApiModelProperty(value = "文档 ID")
    @TableId
    private Long docId;

    /** 标准 ID */
    @ApiModelProperty(value = "标准 ID")
    @Excel(name = "标准 ID")
    private Long standardId;

    /** 文档名称 */
    @ApiModelProperty(value = "文档名称")
    @Excel(name = "文档名称")
    private String docName;

    @ApiModelProperty(value = "文件列表")
    private List<IrrigationProcessFileVo> docFile;

    /** 排序 */
    @ApiModelProperty(value = "排序")
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
