package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 插画和茨河铺枢纽信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "插画和茨河铺枢纽信息")
@Accessors(chain = true)
public class PivotChaHuaVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    @TableId
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    /** 开启孔数 */
    @ApiModelProperty(value = "闸门开启孔数")
    @Excel(name = "开启孔数")
    private BigDecimal gtopn;

    /** 闸门数量 */
    @ApiModelProperty(value = "闸门数量")
    @Excel(name = "闸门数量")
    private BigDecimal gtcn;


    @ApiModelProperty(value = "上桥节制闸编码")
    private String pslcrsCd;

}
