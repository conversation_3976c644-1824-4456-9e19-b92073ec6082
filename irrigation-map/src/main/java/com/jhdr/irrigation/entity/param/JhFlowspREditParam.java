package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 流量站时序数据对象 jh_flowsp_r
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "流量站时序数据")
@Accessors(chain = true)
public class JhFlowspREditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String STCD;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date TM;

    /** 类型 */
    @ApiModelProperty(value = "类型")
    @Excel(name = "类型 ")
    private String QTYPE;

    /** 流量 */
    @ApiModelProperty(value = "流量")
    @Excel(name = "流量")
    private BigDecimal PSQ;

}
