package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工情-运行统计-枢纽
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "工情-运行统计-枢纽")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class EngineerHubCountVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**枢纽编码 */
    @ApiModelProperty(value = "枢纽编码")
    private String hubCd;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    /** 枢纽名称 */
    @ApiModelProperty(value = "枢纽名称")
    private String hubName;

    /** 枢纽状态 0关闭 1开启 */
    @ApiModelProperty(value = "0关闭 1开启")
    private String status;

}
