package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工情-运行统计-泵站
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "工情-运行统计-泵站")
@Accessors(chain = true)
@JsonInclude()//返回值如果为空也返回字段
public class EngineerPumpCountVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "泵站编码")
    private String pumpCd;

    @ApiModelProperty(value = "泵站名称")
    private String pumpName;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    /** 站上水位 */
    @ApiModelProperty(value = "站上水位")
    @Excel(name = "站上水位")
    private BigDecimal ppupz;

    /** 站下水位 */
    @ApiModelProperty(value = "站下水位")
    @Excel(name = "站下水位")
    private BigDecimal ppdwz;


    /** 抽水流量 */
    @ApiModelProperty(value = "抽水流量")
    @Excel(name = "抽水流量")
    private BigDecimal pmpq;


    /** 站上水势 */
    @ApiModelProperty(value = "站上水势")
    @Excel(name = "站上水势")
    private BigDecimal ppupwptn;

    /** 站下水势 */
    @ApiModelProperty(value = "站下水势")
    @Excel(name = "站下水势")
    private BigDecimal ppdwwptn;


    /** 泵站状态 0关闭 1开启 */
    @ApiModelProperty(value = "0关闭 1开启")
    private String status;

    @ApiModelProperty(value = "泵机情况")
    private List<JhPumpDtlRInfoVo> pumpCondition;
}
