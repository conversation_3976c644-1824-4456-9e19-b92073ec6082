package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 水闸运行状况对象 jh_slcrs_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "水闸运行状况")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationProcessGateVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String strCode;

    /** 站点编码 */
    @ApiModelProperty(value = "站点名称")
    private String strName;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    @Excel(name = "闸下水位")
    private BigDecimal sldsz;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量m³")
    @Excel(name = "过闸流量")
    private BigDecimal thrslq;

    /** 闸站状态 0关闭 1开启 */
    @ApiModelProperty(value = "0关闭 1开启")
    private String status;

    @ApiModelProperty(value = "闸门情况")
    private List<JhSlcrsDtlRInfoVo> gateCondition;


}
