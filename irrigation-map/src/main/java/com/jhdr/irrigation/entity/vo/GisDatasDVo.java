package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * GIS数据对象 gis_datas_d
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "GIS数据")
@JsonInclude()
public class GisDatasDVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 数据ID */
    @ApiModelProperty(value = "数据ID")
    @TableId
    private String guid;

    /** 数据编码 */
    @ApiModelProperty(value = "数据编码")
    @Excel(name = "数据编码")
    private String cd;

    /** 数据名称 */
    @ApiModelProperty(value = "数据名称")
    @Excel(name = "数据名称")
    private String nm;

    /** GIS数据 */
    @ApiModelProperty(value = "GIS数据")
    @Excel(name = "GIS数据")
    private String gisdata;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息")
    @Excel(name = "扩展信息")
    private String gissign;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    @Excel(name = "纬度")
    private BigDecimal lttd;


    @ApiModelProperty(value = "雨量")
    private String drp;

    @ApiModelProperty(value = "水位")
    private String z;

    @ApiModelProperty(value = "日水势")
    private String wptnexDay;

    @ApiModelProperty(value = "流量m³/s")
    private String q;

    @ApiModelProperty(value = "日水量 m³")
    private String psq;

   /** 时间 */
    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    @ApiModelProperty(value = "天气图标")
    private String iconDay ;
    @ApiModelProperty(value = "最高温度")
    private String tempMax; // 最高温度
    @ApiModelProperty(value = "最低温度")
    private String tempMin; // 最低温度

    @ApiModelProperty(value = "承建单位 10：省水文局,20：南瑞集团,30：金海迪尔,40：省气象局")
    private String source;

    /** 泵闸站状态 0关闭 1开启 */
    @ApiModelProperty(value = "0关闭 1开启")
    private String status;

}
