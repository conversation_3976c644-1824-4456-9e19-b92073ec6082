package com.jhdr.irrigation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.io.Serializable;



/**
 * 灌区简报表对象 irrigation_briefing
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "灌区简报表")
@Accessors(chain = true)
@TableName(value ="irrigation_briefing")
public class IrrigationBriefingPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    @TableId
    private Long id;

    /** 报告日期 */
    @ApiModelProperty(value = "报告日期")
    private Date reportDate;

    /** 当前天气信息(JSON) */
    @ApiModelProperty(value = "当前天气信息(JSON)")
    private String weatherInfo;

    /** 上桥枢纽水情工情(JSON) */
    @ApiModelProperty(value = "上桥枢纽水情工情(JSON)")
    private String sqHubSituation;

    /** 阚疃枢纽工情信息(JSON) */
    @ApiModelProperty(value = "阚疃枢纽工情信息(JSON)")
    private String ktHubSituation;

    /** 插花枢纽工情信息(JSON) */
    @ApiModelProperty(value = "插花枢纽工情信息(JSON)")
    private String chHubSituation;

    /** 茨河铺枢纽工情信息(JSON) */
    @ApiModelProperty(value = "茨河铺枢纽工情信息(JSON)")
    private String chpHubSituation;

    /** 雨量信息(JSON) */
    @ApiModelProperty(value = "雨量信息(JSON)")
    private String rainBriefing;

    /** 泵站开启数(JSON) */
    @ApiModelProperty(value = "泵站开启数(JSON)")
    private String pumpStationCounts;

    /** 闸站开启数(JSON) */
    @ApiModelProperty(value = "闸站开启数(JSON)")
    private String sluiceStationCounts;

    /** 流量站累计值 */
    @ApiModelProperty(value = "流量站累计值")
    private String totalFlow;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
