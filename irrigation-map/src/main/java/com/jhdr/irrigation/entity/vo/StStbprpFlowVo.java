package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 测站基本属性对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2024-06-22
 */

@Data
@ApiModel(description = "雨量站基本属性")
@Accessors(chain = true)
public class StStbprpFlowVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @ApiModelProperty(value = "测站编码")
    private String stcd;

    /** 测站名称 */
    @ApiModelProperty(value = "测站名称")
    @Excel(name = "测站名称")
    private String stnm;

    /** 河流名称 */
    @ApiModelProperty(value = "河流名称")
    @Excel(name = "河流名称")
    private String rvnm;

    /** 水系名称 */
    @ApiModelProperty(value = "水系名称")
    @Excel(name = "水系名称")
    private String hnnm;

    /** 流域名称 */
    @ApiModelProperty(value = "流域名称")
    @Excel(name = "流域名称")
    private String bsnm;

    /** 站址 */
    @ApiModelProperty(value = "站址")
    @Excel(name = "站址")
    private String stlc;

    /** 基面名称 */
    @ApiModelProperty(value = "基面名称")
    @Excel(name = "基面名称")
    private String dtmnm;

    /** 建站年月 */
    @ApiModelProperty(value = "建站年月")
    @Excel(name = "建站年月")
    private String esstym;

    /** 始报年月 */
    @ApiModelProperty(value = "始报年月")
    @Excel(name = "始报年月")
    private String bgfrym;

    /** 隶属行业单位 */
    @ApiModelProperty(value = "隶属行业单位")
    @Excel(name = "隶属行业单位")
    private String atcunit;

    /** 信息管理单位 */
    @ApiModelProperty(value = "信息管理单位")
    @Excel(name = "信息管理单位")
    private String admauth;

    /** 交换管理单位 */
    @ApiModelProperty(value = "交换管理单位")
    @Excel(name = "交换管理单位")
    private String locality;

    /** 集水面积 */
    @ApiModelProperty(value = "集水面积")
    @Excel(name = "集水面积")
    private BigDecimal drna;



}
