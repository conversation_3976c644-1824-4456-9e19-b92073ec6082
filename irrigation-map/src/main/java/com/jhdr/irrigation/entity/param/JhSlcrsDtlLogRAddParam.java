package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水闸运行操作日志对象 jh_slcrs_dtl_log_r
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "水闸运行操作日志")
@Accessors(chain = true)
public class JhSlcrsDtlLogRAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 采集时间 */
    @ApiModelProperty(value = "采集时间")
    @Excel(name = "采集时间")
    private String clltm;

    /** 闸门编码 */
    @ApiModelProperty(value = "闸门编码")
    @TableId
    private BigDecimal gpcd;

    /** 操作员 */
    @ApiModelProperty(value = "操作员")
    @Excel(name = "操作员")
    private String operator;

    /** 闸门状态 0关 1开 */
    @ApiModelProperty(value = "闸门状态 0关 1开")
    @Excel(name = "闸门状态 0关 1开")
    private String om;

}
