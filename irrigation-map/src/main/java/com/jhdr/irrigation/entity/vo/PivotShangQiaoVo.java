package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 上桥枢纽信息
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "上桥枢纽信息")
@Accessors(chain = true)
@JsonInclude()
public class PivotShangQiaoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 站点编码 */
    @ApiModelProperty(value = "站点编码")
    @TableId
    private String prjnmcd;

    /** 闸上水位 */
    @ApiModelProperty(value = "闸上水位")
    private BigDecimal slupsz;

    /** 闸下水位 */
    @ApiModelProperty(value = "闸下水位")
    private BigDecimal sldsz;

    /** 闸上水势 */
    @ApiModelProperty(value = "闸上水势")
    private BigDecimal upswtp;

    /** 闸下水势 */
    @ApiModelProperty(value = "闸下水势")
    private BigDecimal dswtp;

    /** 过闸流量 */
    @ApiModelProperty(value = "过闸流量m³")
    private BigDecimal thrslq;

    @ApiModelProperty(value = "累计流量 万m³")
    private BigDecimal accq;

    @ApiModelProperty(value = "闸门情况")
    private List<JhSlcrsDtlRInfoVo> gateCondition;

    /** 开启孔数 */
    @ApiModelProperty(value = "闸门开启孔数")
    private BigDecimal gtopn;

    /** 闸门数量 */
    @ApiModelProperty(value = "闸门数量")
    private BigDecimal gtcn;

    /** 开机台数 */
    @ApiModelProperty(value = "泵机开机台数")
    private BigDecimal omcn;


    /** 机组数量 */
    @ApiModelProperty(value = "泵机机组数量")
    private BigDecimal ppcn;


    /** 抽水流量 */
    @ApiModelProperty(value = "机组流量")
    private BigDecimal pmpq;


    @ApiModelProperty(value = "泵机情况")
    private List<JhPumpDtlRInfoVo> pumpCondition;

    @ApiModelProperty(value = "上桥节制闸编码")
    private String pslcrsCd;

    @ApiModelProperty(value = "上桥抽水泵站编码")
    private String pumpCd;

}
