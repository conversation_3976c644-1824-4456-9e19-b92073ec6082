package com.jhdr.irrigation.entity.po;

import lombok.Data;
import java.io.Serializable;

@Data
public class WaterLevelInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    // 站点编码
    private String stationCode;
    // 站点名称 
    private String stationName;
    // 闸上水位
    private Double upperWaterLevel;
    // 闸下水位
    private Double lowerWaterLevel;
    // 水势(上涨/下降/持平)
    private String waterTrend;
} 