package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 河流信息对象 jh_river_b
 *
 * <AUTHOR>
 * @date 2024-06-19
 */

@Data
@ApiModel(description = "河流汇总信息")
@Accessors(chain = true)
public class JhRiverSummaryVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 河流代码 */
    @ApiModelProperty(value = "河流汇总信息")
    private SummaryStreamVo summaryStreamVo;

    @ApiModelProperty(value = "干渠汇总信息")
    private TrunkCanalVo trunkCanalVo;

    @ApiModelProperty(value = "支渠汇总信息")
    private TrunkCanalVo branchCanalVo;



}
