package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 河道防洪指标对象 st_rvfcch_b
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "河道防洪指标")
@Accessors(chain = true)
public class StRvfcchBVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 左堤高程 */
    @ApiModelProperty(value = "左堤高程")
    @Excel(name = "左堤高程")
    private BigDecimal ldkel;

    /** 右堤高程 */
    @ApiModelProperty(value = "右堤高程")
    @Excel(name = "右堤高程")
    private BigDecimal rdkel;

    /** 警戒水位 */
    @ApiModelProperty(value = "警戒水位")
    @Excel(name = "警戒水位")
    private BigDecimal wrz;

    /** 保证水位 */
    @ApiModelProperty(value = "保证水位")
    @Excel(name = "保证水位")
    private BigDecimal grz;


    /** 警戒流量 */
    @ApiModelProperty(value = "警戒流量")
    @Excel(name = "警戒流量")
    private BigDecimal wrq;


    /** 保证流量 */
    @ApiModelProperty(value = "保证流量")
    @Excel(name = "保证流量")
    private BigDecimal grq;

    /** 平滩流量 */
    @ApiModelProperty(value = "平滩流量")
    @Excel(name = "平滩流量")
    private BigDecimal flpq;

    /** 实测最高水位 */
    @ApiModelProperty(value = "实测最高水位")
    @Excel(name = "实测最高水位")
    private BigDecimal obhtz;

    /** 实测最高水位出现时间 */
    @ApiModelProperty(value = "实测最高水位出现时间")
    @Excel(name = "实测最高水位出现时间")
    private String obhtztm;

    /** 调查最高水位 */
    @ApiModelProperty(value = "调查最高水位")
    @Excel(name = "调查最高水位")
    private BigDecimal ivhz;

    /** 调查最高水位出现时间 */
    @ApiModelProperty(value = "调查最高水位出现时间")
    @Excel(name = "调查最高水位出现时间")
    private String ivhztm;

    /** 实测最大流量 */
    @ApiModelProperty(value = "实测最大流量")
    @Excel(name = "实测最大流量")
    private BigDecimal obmxq;

    /** 实测最大流量出现时间 */
    @ApiModelProperty(value = "实测最大流量出现时间")
    @Excel(name = "实测最大流量出现时间")
    private String obmxqtm;

    /** 调查最大流量 */
    @ApiModelProperty(value = "调查最大流量")
    @Excel(name = "调查最大流量")
    private BigDecimal ivmxq;

    /** 调查最大流量出现时间 */
    @ApiModelProperty(value = "调查最大流量出现时间")
    @Excel(name = "调查最大流量出现时间")
    private String ivmxqtm;

    /** 历史最大含沙量 */
    @ApiModelProperty(value = "历史最大含沙量")
    @Excel(name = "历史最大含沙量")
    private BigDecimal hmxs;

    /** 历史最大含沙量出现时间 */
    @ApiModelProperty(value = "历史最大含沙量出现时间")
    @Excel(name = "历史最大含沙量出现时间")
    private String hmxstm;

    /** 历史最大断面平均流速 */
    @ApiModelProperty(value = "历史最大断面平均流速")
    @Excel(name = "历史最大断面平均流速")
    private BigDecimal hmxavv;

    /** 历史最大断面平均流速出现时间 */
    @ApiModelProperty(value = "历史最大断面平均流速出现时间")
    @Excel(name = "历史最大断面平均流速出现时间")
    private String hmxavvtm;

    /** 历史最低水位 */
    @ApiModelProperty(value = "历史最低水位")
    @Excel(name = "历史最低水位")
    private BigDecimal hlz;

    /** 历史最低水位出现时间 */
    @ApiModelProperty(value = "历史最低水位出现时间")
    @Excel(name = "历史最低水位出现时间")
    private String hlztm;

    /** 历史最小流量 */
    @ApiModelProperty(value = "历史最小流量")
    @Excel(name = "历史最小流量")
    private BigDecimal hmnq;

    /** 历史最小流量出现时间 */
    @ApiModelProperty(value = "历史最小流量出现时间")
    @Excel(name = "历史最小流量出现时间")
    private String hmnqtm;

    /** 高水位告警值 */
    @ApiModelProperty(value = "高水位告警值")
    @Excel(name = "高水位告警值")
    private BigDecimal taz;

    /** 大流量告警值 */
    @ApiModelProperty(value = "大流量告警值")
    @Excel(name = "大流量告警值")
    private BigDecimal taq;

    /** 低水位告警值 */
    @ApiModelProperty(value = "低水位告警值")
    @Excel(name = "低水位告警值")
    private BigDecimal laz;

    /** 小流量告警值 */
    @ApiModelProperty(value = "小流量告警值")
    @Excel(name = "小流量告警值")
    private BigDecimal laq;

    /** 启动预报水位标准 */
    @ApiModelProperty(value = "启动预报水位标准")
    @Excel(name = "启动预报水位标准")
    private BigDecimal sfz;

    /** 启动预报流量标准 */
    @ApiModelProperty(value = "启动预报流量标准")
    @Excel(name = "启动预报流量标准")
    private BigDecimal sfq;

    /** 时间戳 */
    @ApiModelProperty(value = "时间戳")
    @Excel(name = "时间戳")
    private String moditime;

}
