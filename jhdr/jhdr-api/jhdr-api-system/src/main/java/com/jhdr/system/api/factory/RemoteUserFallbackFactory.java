package com.jhdr.system.api.factory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import com.jhdr.common.core.domain.R;
import com.jhdr.system.api.RemoteUserService;
import com.jhdr.system.api.domain.SysUser;
import com.jhdr.system.api.model.LoginUser;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteUserService()
        {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public R<LoginUser> getCasUserInfo(String username, String source)
            {
                return R.fail("获取cas用户失败:" + throwable.getMessage());
            }
            @Override
            public R<String> getOaUserName(String username, String source) {
                return R.fail("获取oa用户失败:" + throwable.getMessage());
            }
            @Override
            public R<Boolean> registerUserInfo(SysUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }
        };
    }
}
