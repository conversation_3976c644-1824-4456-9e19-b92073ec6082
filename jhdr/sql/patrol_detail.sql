/*
 Navicat Premium Data Transfer

 Source Server         : 远程公司sqlserver
 Source Server Type    : SQL Server
 Source Server Version : 10501600
 Source Host           : ************:1433
 Source Catalog        : gcxj
 Source Schema         : dbo

 Target Server Type    : SQL Server
 Target Server Version : 10501600
 File Encoding         : 65001

 Date: 18/04/2024 15:24:11
*/


-- ----------------------------
-- Table structure for patrol_detail
-- ----------------------------
IF EXISTS (SELECT * FROM sys.all_objects WHERE object_id = OBJECT_ID(N'[dbo].[patrol_detail]') AND type IN ('U'))
	DROP TABLE [dbo].[patrol_detail]
GO

CREATE TABLE [dbo].[patrol_detail] (
  [id] bigint  IDENTITY(1,1) NOT NULL,
  [task_code] int  NULL,
  [longitude] decimal(10,6)  NULL,
  [latitude] decimal(10,6)  NULL,
  [remark] varchar(512) COLLATE Chinese_PRC_CI_AS  NULL,
  [path] varchar(512) COLLATE Chinese_PRC_CI_AS  NULL,
  [classify] int  NULL,
  [add_time] datetime  NULL
)
GO

ALTER TABLE [dbo].[patrol_detail] SET (LOCK_ESCALATION = TABLE)
GO

EXEC sp_addextendedproperty
'MS_Description', N'任务编号',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'task_code'
GO

EXEC sp_addextendedproperty
'MS_Description', N'经度',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'longitude'
GO

EXEC sp_addextendedproperty
'MS_Description', N'纬度',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'latitude'
GO

EXEC sp_addextendedproperty
'MS_Description', N'文字',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'remark'
GO

EXEC sp_addextendedproperty
'MS_Description', N'图片',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'path'
GO

EXEC sp_addextendedproperty
'MS_Description', N'类别 (0打卡点，1轨迹)',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'classify'
GO

EXEC sp_addextendedproperty
'MS_Description', N'上传时间',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail',
'COLUMN', N'add_time'
GO

EXEC sp_addextendedproperty
'MS_Description', N'巡检详情',
'SCHEMA', N'dbo',
'TABLE', N'patrol_detail'
GO


-- ----------------------------
-- Records of [patrol_detail]
-- ----------------------------
SET IDENTITY_INSERT [dbo].[patrol_detail] ON
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'1', N'1', N'117.459937', N'31.207913', N'0', NULL, N'2', N'2024-04-01 16:28:45.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'2', N'1', N'117.505903', N'31.200185', N'0', N'http:', N'1', N'2024-04-01 16:28:50.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'3', N'1', N'117.520366', N'31.197567', N'0', NULL, N'0', N'2024-04-01 16:29:22.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'4', N'1', N'117.532811', N'31.189134', N'0', N'http:', N'1', N'2024-04-01 16:29:50.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'5', N'1', N'117.554708', N'31.167336', N'0', NULL, N'0', N'2024-04-01 16:30:22.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'6', N'1', N'117.548199', N'31.150250', N'0', NULL, N'1', N'2024-04-01 17:01:33.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'7', N'1', N'117.517937', N'31.152209', N'0', NULL, N'1', N'2024-04-01 17:01:42.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'8', N'1', N'117.494556', N'31.153391', N'0', NULL, N'1', N'2024-04-01 17:01:45.000')
GO

INSERT INTO [dbo].[patrol_detail] ([id], [task_code], [longitude], [latitude], [remark], [path], [classify], [add_time]) VALUES (N'9', N'1', N'117.474093', N'31.156530', N'0', NULL, N'1', N'2024-04-01 17:01:47.000')
GO

SET IDENTITY_INSERT [dbo].[patrol_detail] OFF
GO


-- ----------------------------
-- Primary Key structure for table patrol_detail
-- ----------------------------
ALTER TABLE [dbo].[patrol_detail] ADD CONSTRAINT [PK_PATROL_DETAIL] PRIMARY KEY CLUSTERED ([id])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)  
ON [PRIMARY]
GO

