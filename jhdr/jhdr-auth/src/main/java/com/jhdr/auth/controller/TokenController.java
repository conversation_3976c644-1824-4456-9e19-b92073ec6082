package com.jhdr.auth.controller;
import javax.servlet.http.HttpServletRequest;
import com.jhdr.auth.form.CasLoginBody;
import com.jhdr.auth.form.OaLoginBody;
import com.jhdr.system.api.domain.SysUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.jhdr.auth.form.LoginBody;
import com.jhdr.auth.form.RegisterBody;
import com.jhdr.auth.service.SysLoginService;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.JwtUtils;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.common.security.auth.AuthUtil;
import com.jhdr.common.security.service.TokenService;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.system.api.model.LoginUser;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @PostMapping("casLogin")
    public R<?> casLogin(@RequestBody CasLoginBody form)
    {
        LoginUser userInfo = sysLoginService.loadUserDetails(form);

        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }
    @PostMapping("oaLogin")
    public String oaLogin(@RequestBody OaLoginBody form)
    {
        return sysLoginService.oaLoginUserName(form);
    }

    @PostMapping("testLogin")
    public R<?> testLogin(@RequestBody CasLoginBody form)
    {
        SysUser userInfo = sysLoginService.testUserDetails(form);

        return R.ok(userInfo);
    }

    @PostMapping("login")
    public R<?> login(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }
}
