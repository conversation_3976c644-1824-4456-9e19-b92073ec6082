package com.jhdr.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 部门使用率查询
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "部门使用率查询")
@Accessors(chain = true)
public class DeptUseRateParam implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "部门名称")
    private String deptName;
    
    @ApiModelProperty(value = "父部门ID")
    private Long parentId;

    /** 数据时间 */
    @ApiModelProperty(value = "数据时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timeStart;

    /** 数据时间 */
    @ApiModelProperty(value = "数据时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String timeEnd;



}
