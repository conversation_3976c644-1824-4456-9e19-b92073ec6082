package com.jhdr.system.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jhdr.system.api.domain.SysLogininfor;
import com.jhdr.system.domain.AccessStatisticsParam;
import com.jhdr.system.domain.vo.AccessStatisticsVo;
import com.jhdr.system.mapper.SysLogininforMapper;
import com.jhdr.system.service.ISysLogininforService;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysLogininforServiceImpl implements ISysLogininforService
{

    @Autowired
    private SysLogininforMapper logininforMapper;

    /**
     * 新增系统登录日志
     *
     * @param logininfor 访问日志对象
     */
    @Override
    public int insertLogininfor(SysLogininfor logininfor)
    {
        return logininforMapper.insertLogininfor(logininfor);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    @Override
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
    {
        return logininforMapper.selectLogininforList(logininfor);
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    @Override
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    @Override
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }

    /**
     * 查询按日期分组的访问统计
     *
     * @param param 查询参数
     * @return 按日期分组的访问统计
     */
    @Override
    public List<AccessStatisticsVo> selectAccessStatistics(AccessStatisticsParam param)
    {
        List<Map<String, Object>> statistics = logininforMapper.selectAccessStatistics(param);
        List<AccessStatisticsVo> result = new ArrayList<>();

        int cumulativeCount = 0;
        for (Map<String, Object> stat : statistics)
        {
            AccessStatisticsVo vo = new AccessStatisticsVo();

            // 设置日期
            Date accessDate = (Date) stat.get("access_date");
            vo.setAccessDate(accessDate);

            // 设置单日访问量
            Integer dailyCount = Integer.parseInt(stat.get("daily_count").toString());
            vo.setDailyCount(dailyCount);

            // 计算并设置累计访问量
            cumulativeCount += dailyCount;
            vo.setCumulativeCount(cumulativeCount);

            result.add(vo);
        }

        return result;
    }
}
