package com.jhdr.system.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.system.domain.AccessStatisticsParam;
import com.jhdr.system.domain.DeptUseRateParam;
import com.jhdr.system.domain.vo.AccessStatisticsVo;
import com.jhdr.system.domain.vo.DeptUseRateVo;
import com.jhdr.system.service.ISysDeptService;
import com.jhdr.system.service.ISysLogininforService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 部门使用率
 *
 * <AUTHOR>
 * @date 2024-06-25
 */
@Api(tags = "部门使用率")
@RestController
@RequestMapping("/dept/use")
public class DeptUseRateController extends BaseController {

    @Resource
    private ISysDeptService deptService;

    @Resource
    private ISysLogininforService logininforService;

    /**
     * 获取部门使用率列表
     */
    @ApiOperation(value = "获取部门使用率列表")
    @GetMapping(value = "/rate")
    public R<List<DeptUseRateVo>> getDeptUseRate(DeptUseRateParam updateParam) {

        List<DeptUseRateVo> list = deptService.getDeptUseRate(updateParam);
        return R.ok(list);
    }

    /**
     * 获取用户访问统计数据
     */
    @ApiOperation(value = "获取用户访问统计数据")
    @GetMapping(value = "/access/statistics")
    public R<List<AccessStatisticsVo>> getAccessStatistics(AccessStatisticsParam param) {
        List<AccessStatisticsVo> list = logininforService.selectAccessStatistics(param);
        return R.ok(list);
    }
}
