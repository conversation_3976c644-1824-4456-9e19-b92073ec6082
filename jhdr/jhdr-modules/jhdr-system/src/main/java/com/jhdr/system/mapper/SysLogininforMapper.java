package com.jhdr.system.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.jhdr.system.api.domain.SysLogininfor;
import com.jhdr.system.domain.AccessStatisticsParam;
import org.apache.ibatis.annotations.Param;

/**
 * 系统访问日志情况信息 数据层
 *
 * <AUTHOR>
 */
public interface SysLogininforMapper
{
    /**
     * 新增系统登录日志
     *
     * @param logininfor 访问日志对象
     */
    public int insertLogininfor(SysLogininfor logininfor);

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor);

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return 结果
     */
    public int deleteLogininforByIds(Long[] infoIds);

    /**
     * 清空系统登录日志
     *
     * @return 结果
     */
    public int cleanLogininfor();

    /**
     * 查询按日期分组的访问统计
     *
     * @param param 查询参数
     * @return 按日期分组的访问统计
     */
    public List<Map<String, Object>> selectAccessStatistics(AccessStatisticsParam param);
}
