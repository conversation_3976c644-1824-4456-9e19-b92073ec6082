package com.jhdr.system.domain.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 部门使用率
 *
 * <AUTHOR>
 * @date 2024/06/24
 */
@Data
@ApiModel(description = "部门使用率")
@Accessors(chain = true)
@JsonInclude()
public class DeptUseRateVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门名称")
    private String deptName;


    @ApiModelProperty(value = "部门用户总数")
    private String deptUserCount;

    @ApiModelProperty(value = "总登录天次")
    private String totalLoginsCount;
    
    @ApiModelProperty(value = "工作日总数")
    private String totalWorkDays;

//    @ApiModelProperty(value = "登录人数")
//    private String loginUsersCount;

    @ApiModelProperty(value = "部门使用率")
    private String loginPercentage;

    //部门名称  部门用户总数  登录人数  总登录天次  人均登录天次

}
