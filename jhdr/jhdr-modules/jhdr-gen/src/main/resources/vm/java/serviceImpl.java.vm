package ${packageName}.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
#if($table.sub)
import java.util.ArrayList;
import com.jhdr.common.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.entity.po.${ClassName}Po;
import ${packageName}.entity.param.${ClassName}Param;
import ${packageName}.entity.param.${ClassName}AddParam;
import ${packageName}.entity.param.${ClassName}EditParam;
import ${packageName}.entity.vo.${ClassName}Vo;
import ${packageName}.mapper.${ClassName}Mapper;
import ${packageName}.service.I${ClassName}Service;

import java.util.ArrayList;

import java.util.List;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Service
public class ${ClassName}ServiceImpl extends ServiceImpl<${ClassName}Mapper, ${ClassName}Po> implements I${ClassName}Service {

    @Override
    public List<${ClassName}Vo> queryList(${ClassName}Po ${className}) {
        LambdaQueryWrapper<${ClassName}Po> lqw = Wrappers.lambdaQuery();
#foreach($column in $columns)
#set($queryType=$column.queryType)
#set($javaField=$column.javaField)
#set($javaType=$column.javaType)
#set($columnName=$column.columnName)
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
#if($column.query)
#if($column.queryType == "EQ")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.eq(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.eq(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "NE")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.ne(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.ne(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "GT")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.gt(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.gt(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "GTE")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.ge(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.ge(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "LT")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.lt(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.lt(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "LTE")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.le(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.le(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "LIKE")
#if($javaType == 'String')
        if (StringUtils.isNotBlank(${className}.get$AttrName())){
            lqw.like(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#else
        if (${className}.get$AttrName() != null){
            lqw.like(${ClassName}Po::get$AttrName ,${className}.get$AttrName());
        }
#end
#elseif($queryType == "BETWEEN")
        Map<String, Object> params = ${className}.getParams();
        if (params.get("begin$AttrName") != null && params.get("end$AttrName") != null) {
            lqw.between(${ClassName}Po::get$AttrName ,params.get("begin$AttrName"),params.get("end$AttrName"));
        }
#end
#end
#end
        List<${ClassName}Vo> ${className}Vos= BeanUtil.copyToList(this.list(lqw), ${ClassName}Vo.class);
        return ${className}Vos;
    }
    /**
     * 查询${functionName}
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return ${functionName}
     */
    @Override
    public ${ClassName}Vo select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        return baseMapper.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }

    /**
     * 查询${functionName}列表
     *
     * @param ${className} ${functionName}
     * @return ${functionName}
     */
    @Override
    public List<${ClassName}Vo> select${ClassName}List(${ClassName}Param ${className})
    {
        return baseMapper.select${ClassName}List(${className});
    }

    /**
     * 新增${functionName}
     *
     * @param ${className}AddParam ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int insert${ClassName}(${ClassName}AddParam ${className}AddParam)
    {
        #foreach ($column in $columns)
            #if($column.javaField == 'createTime')
                ${className}.setCreateTime(DateUtils.getNowDate());
            #end
        #end

        ${ClassName}Po ${className}=new ${ClassName}Po();
        BeanUtil.copyProperties(${className}AddParam,${className});
        return baseMapper.insert(${className});
    }

    /**
     * 修改${functionName}
     *
     * @param ${className}EditParam ${functionName}
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int update${ClassName}(${ClassName}EditParam ${className}EditParam)
    {
        #foreach ($column in $columns)
            #if($column.javaField == 'updateTime')
                ${className}.setUpdateTime(DateUtils.getNowDate());
            #end
        #end
        ${ClassName}Po ${className}=new ${ClassName}Po();
        BeanUtil.copyProperties(${className}EditParam,${className});
        return baseMapper.updateById(${className});
    }

    /**
     * 批量删除${functionName}
     *
     * @param ${pkColumn.javaField}s 需要删除的${functionName}主键
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        #if($table.sub)
                baseMapper.delete${subClassName}By${subTableFkClassName}s(${pkColumn.javaField}s);
        #end
        return baseMapper.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s);
    }

    /**
     * 删除${functionName}信息
     *
     * @param ${pkColumn.javaField} ${functionName}主键
     * @return 结果
     */
        #if($table.sub)
        @Transactional
        #end
    @Override
    public int delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField})
    {
        #if($table.sub)
                baseMapper.delete${subClassName}By${subTableFkClassName}(${pkColumn.javaField});
        #end
        return baseMapper.delete${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }
    #if($table.sub)

        /**
         * 新增${subTable.functionName}信息
         *
         * @param ${className} ${functionName}对象
         */
        public void insert${subClassName}(${ClassName}AddParam ${className})
        {
            List<${subClassName}> ${subclassName}List = ${className}.get${subClassName}List();
            ${pkColumn.javaType} ${pkColumn.javaField} = ${className}.get${pkColumn.capJavaField}();
            if (StringUtils.isNotNull(${subclassName}List))
            {
                List<${subClassName}> list = new ArrayList<${subClassName}>();
                for (${subClassName} ${subclassName} : ${subclassName}List)
                {
                    ${subclassName}.set${subTableFkClassName}(${pkColumn.javaField});
                    list.add(${subclassName});
                }
                if (list.size() > 0)
                {
                        baseMapper.batch${subClassName}(list);
                }
            }
        }
    #end
}
