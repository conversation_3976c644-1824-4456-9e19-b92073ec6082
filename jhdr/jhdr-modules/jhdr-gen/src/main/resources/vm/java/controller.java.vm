package ${packageName}.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import ${packageName}.entity.po.${ClassName}Po;
import ${packageName}.entity.param.${ClassName}Param;
import ${packageName}.entity.param.${ClassName}AddParam;
import ${packageName}.entity.param.${ClassName}EditParam;
import ${packageName}.entity.vo.${ClassName}Vo;
import ${packageName}.service.I${ClassName}Service;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
#if($table.crud || $table.sub)
import com.jhdr.common.core.web.page.TableDataInfo;
#elseif($table.tree)
#end

/**
 * ${functionName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Api(tags = "${functionName}")
@RestController
@RequestMapping("/${businessName}")
public class ${ClassName}Controller extends BaseController
{
    @Autowired
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    @ApiOperation(value = "查询${functionName}列表",notes="${permissionPrefix}:list")
    @RequiresPermissions("${permissionPrefix}:list")
    @GetMapping("/list")
#if($table.crud || $table.sub)
    public TableDataInfo<List<${ClassName}Vo>> list(${ClassName}Param ${className}Param)
    {
        startPage();
        List<${ClassName}Vo> list = ${className}Service.select${ClassName}List(${className}Param);
        return getDataTable(list);
    }
#elseif($table.tree)
    public R list(${ClassName} ${className})
    {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return R.ok(list);
    }
#end

    /**
     * 导出${functionName}列表
     */
    @ApiOperation(value = "导出${functionName}列表",notes="${permissionPrefix}:export")
    @RequiresPermissions("${permissionPrefix}:export")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ${ClassName}Param ${className})
    {
        List<${ClassName}Vo> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}Vo> util = new ExcelUtil<${ClassName}Vo>(${ClassName}Vo.class);
        util.exportExcel(response, list, "${functionName}数据");
    }

    /**
     * 获取${functionName}详细信息
     */
    @ApiOperation(value = "获取${functionName}详细信息",notes="${permissionPrefix}:query")
    @RequiresPermissions("${permissionPrefix}:query")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public R<${ClassName}Vo> getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField})
    {

        return R.ok(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }

    /**
     * 新增${functionName}
     */
    @ApiOperation(value = "新增${functionName}",notes="${permissionPrefix}:add")
    @RequiresPermissions("${permissionPrefix}:add")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody ${ClassName}AddParam ${className})
    {
        return toAjaxR(${className}Service.insert${ClassName}(${className}),"新增");
    }

    /**
     * 修改${functionName}
     */
    @ApiOperation(value = "修改${functionName}",notes="${permissionPrefix}:edit")
    @RequiresPermissions("${permissionPrefix}:edit")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody ${ClassName}EditParam ${className})
    {
        return toAjaxR(${className}Service.update${ClassName}(${className}),"修改");
    }

    /**
     * 删除${functionName}
     */
    @ApiOperation(value = "删除${functionName}",notes="${permissionPrefix}:remove")
    @RequiresPermissions("${permissionPrefix}:remove")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
	@DeleteMapping("/{${pkColumn.javaField}s}")
    public R remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s)
    {
        return toAjaxR(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s),"删除");
    }
}
