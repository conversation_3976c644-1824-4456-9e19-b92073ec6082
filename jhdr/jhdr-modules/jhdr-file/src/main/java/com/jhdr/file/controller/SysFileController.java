package com.jhdr.file.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.file.FileUtils;
import com.jhdr.file.service.ISysFileService;
import com.jhdr.system.api.domain.SysFile;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@Api(tags = "文件上传")
@RestController
public class SysFileController
{
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private ISysFileService sysFileService;

    /**
     * 文件上传请求
     */
    @ApiOperation(value = "公共上传入口")
    @PostMapping("upload")
    public R<SysFile> upload( @RequestPart("file")MultipartFile file)
    {
        try
        {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setType(FileUtils.getType(sysFile.getName()));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }
}
