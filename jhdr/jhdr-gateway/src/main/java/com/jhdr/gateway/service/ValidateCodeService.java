package com.jhdr.gateway.service;

import java.io.IOException;
import com.jhdr.common.core.exception.CaptchaException;
import com.jhdr.common.core.web.domain.AjaxResult;

/**
 * 验证码处理
 *
 * <AUTHOR>
 */
public interface ValidateCodeService
{
    /**
     * 生成验证码
     */
    public AjaxResult createCaptcha() throws IOException, CaptchaException;

    /**
     * 校验验证码
     */
    public void checkCaptcha(String key, String value) throws CaptchaException;

    /**
     * 临时使用 公司信息，公司头像和项目名称
     */
    public AjaxResult companyInfo() throws IOException;
}
