version : '3.8'
services:
  jhdr-nacos:
    container_name: jhdr-nacos
    image: nacos/nacos-server
    build:
      context: ./nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs/:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    depends_on:
      - jhdr-mysql
  jhdr-mysql:
    container_name: jhdr-mysql
    image: mysql:5.7
    build:
      context: ./mysql
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/logs:/logs
      - ./mysql/data:/var/lib/mysql
    command: [
          'mysqld',
          '--innodb-buffer-pool-size=80M',
          '--character-set-server=utf8mb4',
          '--collation-server=utf8mb4_unicode_ci',
          '--default-time-zone=+8:00',
          '--lower-case-table-names=1'
        ]
    environment:
      MYSQL_DATABASE: 'ry-cloud'
      MYSQL_ROOT_PASSWORD: password
  jhdr-redis:
    container_name: jhdr-redis
    image: redis
    build:
      context: ./redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      - ./redis/data:/data
    command: redis-server /home/<USER>/redis/redis.conf
  jhdr-nginx:
    container_name: jhdr-nginx
    image: nginx
    build:
      context: ./nginx
    ports:
      - "80:80"
    volumes:
      - ./nginx/html/dist:/home/<USER>/projects/jhdr-ui
      - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - jhdr-gateway
    links:
      - jhdr-gateway
  jhdr-gateway:
    container_name: jhdr-gateway
    build:
      context: ./jhdr/gateway
      dockerfile: dockerfile
    ports:
      - "8080:8080"
    depends_on:
      - jhdr-redis
    links:
      - jhdr-redis
  jhdr-auth:
    container_name: jhdr-auth
    build:
      context: ./jhdr/auth
      dockerfile: dockerfile
    ports:
      - "9200:9200"
    depends_on:
      - jhdr-redis
    links:
      - jhdr-redis
  jhdr-modules-system:
    container_name: jhdr-modules-system
    build:
      context: ./jhdr/modules/system
      dockerfile: dockerfile
    ports:
      - "9201:9201"
    depends_on:
      - jhdr-redis
      - jhdr-mysql
    links:
      - jhdr-redis
      - jhdr-mysql
  jhdr-modules-gen:
    container_name: jhdr-modules-gen
    build:
      context: ./jhdr/modules/gen
      dockerfile: dockerfile
    ports:
      - "9202:9202"
    depends_on:
      - jhdr-mysql
    links:
      - jhdr-mysql
  jhdr-modules-job:
    container_name: jhdr-modules-job
    build:
      context: ./jhdr/modules/job
      dockerfile: dockerfile
    ports:
      - "9203:9203"
    depends_on:
      - jhdr-mysql
    links:
      - jhdr-mysql
  jhdr-modules-file:
    container_name: jhdr-modules-file
    build:
      context: ./jhdr/modules/file
      dockerfile: dockerfile
    ports:
      - "9300:9300"
    volumes:
    - ./jhdr/uploadPath:/home/<USER>/uploadPath
  jhdr-visual-monitor:
    container_name: jhdr-visual-monitor
    build:
      context: ./jhdr/visual/monitor
      dockerfile: dockerfile
    ports:
      - "9100:9100"
