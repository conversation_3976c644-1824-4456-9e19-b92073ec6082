#!/bin/sh

# 复制项目的文件到对应docker路径，便于一键生成镜像。
usage() {
	echo "Usage: sh copy.sh"
	exit 1
}


# copy sql
echo "begin copy sql "
cp ../sql/ry_20231130.sql ./mysql/db
cp ../sql/ry_config_20231204.sql ./mysql/db

# copy html
echo "begin copy html "
cp -r ../jhdr-ui/dist/** ./nginx/html/dist


# copy jar
echo "begin copy jhdr-gateway "
cp ../jhdr-gateway/target/jhdr-gateway.jar ./ruoyi/gateway/jar

echo "begin copy jhdr-auth "
cp ../jhdr-auth/target/jhdr-auth.jar ./ruoyi/auth/jar

echo "begin copy jhdr-visual "
cp ../jhdr-visual/jhdr-monitor/target/jhdr-visual-monitor.jar  ./ruoyi/visual/monitor/jar

echo "begin copy jhdr-modules-system "
cp ../jhdr-modules/jhdr-system/target/jhdr-modules-system.jar ./ruoyi/modules/system/jar

echo "begin copy jhdr-modules-file "
cp ../jhdr-modules/jhdr-file/target/jhdr-modules-file.jar ./ruoyi/modules/file/jar

echo "begin copy jhdr-modules-job "
cp ../jhdr-modules/jhdr-job/target/jhdr-modules-job.jar ./ruoyi/modules/job/jar

echo "begin copy jhdr-modules-gen "
cp ../jhdr-modules/jhdr-gen/target/jhdr-modules-gen.jar ./ruoyi/modules/gen/jar

