<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.jhdr</groupId>
        <artifactId>jhdr</artifactId>
        <version>3.6.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>jhdr-common-log</module>
        <module>jhdr-common-core</module>
        <module>jhdr-common-redis</module>
        <module>jhdr-common-seata</module>
        <module>jhdr-common-swagger</module>
        <module>jhdr-common-security</module>
        <module>jhdr-common-sensitive</module>
        <module>jhdr-common-datascope</module>
        <module>jhdr-common-datasource</module>
    </modules>

    <artifactId>jhdr-common</artifactId>
    <packaging>pom</packaging>

    <description>
        jhdr-common通用模块
    </description>

</project>
