# Tomcat
server:
  port: 9100

# Spring
spring:
  application:
    # 应用名称
    name: jhdr-monitor
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        namespace: 9002 #改成自己nacos命名id
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        namespace: 9002 #改成自己nacos命名id
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
