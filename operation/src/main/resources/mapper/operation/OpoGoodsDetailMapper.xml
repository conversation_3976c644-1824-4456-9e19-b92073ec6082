<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoGoodsDetailMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoGoodsDetailPo" id="OpoGoodsDetailResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="model"    column="model"    />
        <result property="unit"    column="unit"    />
        <result property="amount"    column="amount"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="stationId"    column="station_id"    />
    </resultMap>

    <sql id="selectOpoGoodsDetailVo">
        select id, name, model, unit, amount, is_delete, station_id from opo_goods_detail
    </sql>

    <select id="selectOpoGoodsDetailList"  resultType="com.jhdr.operation.entity.vo.OpoGoodsDetailVo">
        <include refid="selectOpoGoodsDetailVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="stationId != null  and stationId != ''"> and station_id = #{stationId}</if>
        </where>
    </select>

    <select id="selectOpoGoodsDetailById"  resultType="com.jhdr.operation.entity.vo.OpoGoodsDetailVo">
            <include refid="selectOpoGoodsDetailVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoGoodsDetail" parameterType="com.jhdr.operation.entity.param.OpoGoodsDetailAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_goods_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="name != null">name,</if>
                    <if test="model != null">model,</if>
                    <if test="unit != null">unit,</if>
                    <if test="amount != null">amount,</if>
                    <if test="isDelete != null">is_delete,</if>
                    <if test="stationId != null">station_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="name != null">#{name},</if>
                    <if test="model != null">#{model},</if>
                    <if test="unit != null">#{unit},</if>
                    <if test="amount != null">#{amount},</if>
                    <if test="isDelete != null">#{isDelete},</if>
                    <if test="stationId != null">#{stationId},</if>
        </trim>
    </insert>

    <update id="updateOpoGoodsDetail" parameterType="com.jhdr.operation.entity.param.OpoGoodsDetailEditParam">
        update opo_goods_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">name = #{name},</if>
                    <if test="model != null">model = #{model},</if>
                    <if test="unit != null">unit = #{unit},</if>
                    <if test="amount != null">amount = #{amount},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
                    <if test="stationId != null">station_id = #{stationId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoGoodsDetailById" parameterType="Integer">
        delete from opo_goods_detail where id = #{id}
    </delete>

    <delete id="deleteOpoGoodsDetailByIds" parameterType="String">
        delete from opo_goods_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
