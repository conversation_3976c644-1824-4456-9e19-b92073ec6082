<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoMachineRoomDetailMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoMachineRoomDetailPo" id="OpoMachineRoomDetailResult">
        <result property="id"    column="id"    />
        <result property="subName"    column="sub_name"    />
        <result property="wyid"    column="wyid"    />
        <result property="deviceId"    column="device_id"    />
        <result property="ip"    column="ip"    />
        <result property="value"    column="value"    />
        <result property="dw"    column="dw"    />
        <result property="subid"    column="subid"    />
        <result property="time"    column="time"    />
    </resultMap>

    <sql id="selectOpoMachineRoomDetailVo">
        select id, sub_name, wyid, device_id, ip, value, dw, subid, time from opo_machine_room_detail
    </sql>

    <select id="selectOpoMachineRoomDetailList"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomDetailVo">
        <include refid="selectOpoMachineRoomDetailVo"/>
        <where>
            <if test="subName != null  and subName != ''"> and sub_name like concat('%', #{subName}, '%')</if>
            <if test="wyid != null  and wyid != ''"> and wyid = #{wyid}</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="dw != null  and dw != ''"> and dw = #{dw}</if>
            <if test="subid != null "> and subid = #{subid}</if>
            <if test="time != null "> and time = #{time}</if>
        </where>
    </select>

    <select id="selectOpoMachineRoomDetailById"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomDetailVo">
            <include refid="selectOpoMachineRoomDetailVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoMachineRoomDetail" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomDetailAddParam">
        insert into opo_machine_room_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="subName != null">sub_name,</if>
                    <if test="wyid != null">wyid,</if>
                    <if test="deviceId != null">device_id,</if>
                    <if test="ip != null">ip,</if>
                    <if test="value != null">value,</if>
                    <if test="dw != null">dw,</if>
                    <if test="subid != null">subid,</if>
                    <if test="time != null">time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="subName != null">#{subName},</if>
                    <if test="wyid != null">#{wyid},</if>
                    <if test="deviceId != null">#{deviceId},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="value != null">#{value},</if>
                    <if test="dw != null">#{dw},</if>
                    <if test="subid != null">#{subid},</if>
                    <if test="time != null">#{time},</if>
        </trim>
    </insert>

    <update id="updateOpoMachineRoomDetail" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomDetailEditParam">
        update opo_machine_room_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="subName != null">sub_name = #{subName},</if>
                    <if test="wyid != null">wyid = #{wyid},</if>
                    <if test="deviceId != null">device_id = #{deviceId},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="value != null">value = #{value},</if>
                    <if test="dw != null">dw = #{dw},</if>
                    <if test="subid != null">subid = #{subid},</if>
                    <if test="time != null">time = #{time},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoMachineRoomDetailById" parameterType="Long">
        delete from opo_machine_room_detail where id = #{id}
    </delete>

    <delete id="deleteOpoMachineRoomDetailByIds" parameterType="String">
        delete from opo_machine_room_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
