<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.PatrolTaskMapper">

    <resultMap type="com.jhdr.operation.entity.po.PatrolTaskPo" id="PatrolTaskResult">
        <result property="id"    column="id"    />
        <result property="patrolCode"    column="patrol_code"    />
        <result property="patrolName"    column="patrol_name"    />
        <result property="content"    column="content"    />
        <result property="patrolType"    column="patrol_type"    />
        <result property="patrolScope"    column="patrol_scope"    />
        <result property="cycle"    column="cycle"    />
        <result property="planTime"    column="plan_time"    />
        <result property="result"    column="result"    />
        <result property="state"    column="state"    />
        <result property="patrolTime"    column="patrol_time"    />
        <result property="person"    column="person"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectPatrolTaskVo">
        select id,patrol_code, patrol_id, patrol_name, content, patrol_type, patrol_scope, cycle, plan_time, result, state, patrol_time, person, is_delete from opo_patrol_task
    </sql>

    <select id="selectPatrolTaskList"  resultType="com.jhdr.operation.entity.vo.PatrolTaskVo">
        <include refid="selectPatrolTaskVo"/>
        <where>
            <if test="patrolType != null  and patrolType != ''"> and patrol_type = #{patrolType}</if>
            <if test="startTime != null "> and patrol_time between #{startTime} and #{endTime}</if>
            and is_delete=0
        </where>
        order by  patrol_time desc
    </select>

    <select id="selectPatrolTaskById"  resultType="com.jhdr.operation.entity.vo.PatrolOneTaskVo">
            <include refid="selectPatrolTaskVo"/>
            where id = #{id}
    </select>
    <select id="selectAlarmDetails" resultType="com.jhdr.operation.entity.vo.PatrolTaskDetailVo">
        select * from opo_patrol_task_detail  where status='异常'  and task_code=#{id}
    </select>
    <select id="findUseAmount" resultType="java.lang.Integer">
        select count(*) from opo_patrol_task_detail  where status='正常' and task_code=#{id}
    </select>
    <select id="findAlarmAmount" resultType="java.lang.Integer">
        select count(*) from opo_patrol_task_detail  where status='异常' and task_code=#{id}
    </select>
    <select id="findFristTask" resultType="com.jhdr.operation.entity.po.PatrolTaskPo">
        select top 1 * from opo_patrol_task where patrol_type='自动巡检' and TO_CHAR(patrol_time, 'YYYY-MM-DD')= TO_CHAR(SYSDATE, 'YYYY-MM-DD')
        order by patrol_time desc
    </select>

    <select id="selectPatrolResult" resultType="com.jhdr.operation.entity.vo.PatrolResultVo">
        select type ,staName,status,content from all_device_state
        where type=#{scope}
    </select>
    <select id="selectAllResult" resultType="com.jhdr.operation.entity.vo.PatrolResultVo">
       select type ,staName,status,content from all_device_state
    </select>
    <select id="findNotify" resultType="java.lang.String">
        SELECT person FROM opo_patrol_plan_detail where plan_id=#{patrolId}
    </select>
    <select id="selectResultByScope" resultType="com.jhdr.operation.entity.vo.PatrolResultVo">
        select staName,status from all_device_state where type=#{scope} and status='异常'
    </select>
    <select id="selectAmountByScope" resultType="java.lang.Integer">
        select count(1) from all_device_state where type = #{scope} and status = '异常'
    </select>
    <select id="findProgress" resultType="java.lang.String">
        select  top 1 progress from opo_patrol_progress
        order by add_time desc
    </select>


    <insert id="insertPatrolTask" parameterType="com.jhdr.operation.entity.param.PatrolTaskAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_patrol_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="patrolId != null">patrol_id,</if>
                    <if test="patrolCode != null">patrol_code,</if>
                    <if test="patrolName != null">patrol_name,</if>
                    <if test="content != null">content,</if>
                    <if test="patrolType != null">patrol_type,</if>
                    <if test="patrolScope != null">patrol_scope,</if>
                    <if test="cycle != null">cycle,</if>
                    <if test="planTime != null">plan_time,</if>
                    <if test="result != null">result,</if>
                    <if test="state != null">state,</if>
                    <if test="patrolTime != null">patrol_time,</if>
                    <if test="person != null">person,</if>
                    <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="patrolId != null">#{patrolId},</if>
                    <if test="patrolCode != null">#{patrolCode},</if>
                    <if test="patrolName != null">#{patrolName},</if>
                    <if test="content != null">#{content},</if>
                    <if test="patrolType != null">#{patrolType},</if>
                    <if test="patrolScope != null">#{patrolScope},</if>
                    <if test="cycle != null">#{cycle},</if>
                    <if test="planTime != null">#{planTime},</if>
                    <if test="result != null">#{result},</if>
                    <if test="state != null">#{state},</if>
                    <if test="patrolTime != null">#{patrolTime},</if>
                    <if test="person != null">#{person},</if>
                    <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>


    <update id="updatePatrolTask" parameterType="com.jhdr.operation.entity.param.PatrolTaskEditParam">
        update opo_patrol_task
        <trim prefix="SET" suffixOverrides=",">
                    <if test="patrolId != null">patrol_id = #{patrolId},</if>
                    <if test="patrolCode != null">patrol_code = #{patrolCode},</if>
                    <if test="patrolName != null">patrol_name = #{patrolName},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="patrolType != null">patrol_type = #{patrolType},</if>
                    <if test="patrolScope != null">patrol_scope = #{patrolScope},</if>
                    <if test="cycle != null">cycle = #{cycle},</if>
                    <if test="planTime != null">plan_time = #{planTime},</if>
                    <if test="result != null">result = #{result},</if>
                    <if test="state != null">state = #{state},</if>
                    <if test="patrolTime != null">patrol_time = #{patrolTime},</if>
                    <if test="person != null">person = #{person},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatrolTaskById" parameterType="Long">
        delete from opo_patrol_task where id = #{id}
    </delete>

    <delete id="deletePatrolTaskByIds" parameterType="String">
        delete from opo_patrol_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="finish">
        delete from opo_patrol_progress
    </delete>
</mapper>
