<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.JhPumpDtlRMapper">


    <select id="selectRealPump" resultType="com.jhdr.operation.entity.po.JhPumpDtlRealPo">
        select * from jh_pump_dtl_r_real where pmpcd =#{pmpcd} and prjnmcd=#{prjnmcd}
    </select>
    <select id="selectDtlPump" resultType="com.jhdr.operation.entity.po.JhPumpDtlRPo">
        select * from jh_pump_dtl_r where pmpcd =#{pmpcd} and clltm=#{clltm} and prjnmcd=#{prjnmcd}
    </select>
    <select id="selectRealPumpTest" resultType="com.jhdr.operation.entity.po.JhPumpDtlRealPo">
        select * from jh_pump_dtl_r_real where pmpcd =#{pmpcd} and clltm=#{clltm} and prjnmcd=#{prjnmcd}
    </select>
</mapper>
