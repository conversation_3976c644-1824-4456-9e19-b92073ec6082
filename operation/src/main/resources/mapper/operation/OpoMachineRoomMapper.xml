<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoMachineRoomMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoMachineRoomPo" id="OpoMachineRoomResult">
        <result property="id"    column="id"    />
        <result property="roomName"    column="room_name"    />
        <result property="temp"    column="temp"    />
        <result property="humidity"    column="humidity"    />
        <result property="airState"    column="air_state"    />
        <result property="smogState"    column="smog_state"    />
    </resultMap>

    <sql id="selectOpoMachineRoomVo">
        select id, room_name, temp, humidity, air_state, smog_state from opo_machine_room
    </sql>

    <select id="selectOpoMachineRoomList"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomVo">
        <include refid="selectOpoMachineRoomVo"/>
        <where>
            <if test="roomName != null  and roomName != ''"> and room_name like concat('%', #{roomName}, '%')</if>
            <if test="temp != null  and temp != ''"> and temp = #{temp}</if>
            <if test="humidity != null  and humidity != ''"> and humidity = #{humidity}</if>
            <if test="airState != null  and airState != ''"> and air_state = #{airState}</if>
            <if test="smogState != null  and smogState != ''"> and smog_state = #{smogState}</if>
        </where>
    </select>

    <select id="selectOpoMachineRoomById"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomVo">
            <include refid="selectOpoMachineRoomVo"/>
            where id = #{id}
    </select>

    <select id="findFwqCount" resultType="java.lang.Integer">
        select count(1) from opo_server_station where is_delete=0
    </select>
    <select id="findNetSafeCount" resultType="java.lang.Integer">
        select count(1) from opo_netsafe_station where is_delete=0
    </select>
    <select id="findNetworkCount" resultType="java.lang.Integer">
        SELECT count(1)  from (
        select  device_name from  opo_network_station  where   is_delete=0 UNION all
        select  device_name from  opo_netsafe_station  where   is_delete=0 )
    </select>
    <select id="findCountStateByState" resultType="java.lang.Integer">
        SELECT count(1)  from (
        select  device_name from  opo_network_station  where  device_state=#{state} and is_delete=0 UNION all
        select  device_name from  opo_netsafe_station  where  device_state=#{state} and is_delete=0 )
    </select>
    <select id="findDeviceAlarm" resultType="com.jhdr.operation.entity.vo.MachineAlarmVo">
        SELECT top 1  * from (
        select  device_name,cabinet_name ,alarm_type from  opo_network_station  where  device_state='异常' and is_delete=0 UNION all
        select  device_name,cabinet_name ,alarm_type from  opo_netsafe_station  where  device_state='异常' and is_delete=0 )
    </select>
    <select id="findMachineValue" resultType="com.jhdr.operation.entity.vo.OpoRoomValueVo">
        SELECT '西侧空调上' as deviceName, CONCAT(value, '', dw) as value FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5003:1_11' union all
        SELECT '西侧空调下' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5003:4_11' union all
        SELECT '后门湿度' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5006:1_1' union all
        SELECT '后门温度' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5006:1_2' union all
        SELECT '前门湿度' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5006:2_1' union all
        SELECT '前门温度' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5006:2_2' union all
        SELECT '东侧空调上' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5004:1_27' union all
        SELECT '东侧空调下' as deviceName, CONCAT(value, '', dw) as value  FROM opo_machine_room_detail WHERE wyid='127.0.0.1:5004:2_27'

    </select>
    <select id="findBaseCabinet" resultType="com.jhdr.operation.entity.vo.OpoCabinetVo">
        SELECT id,cabinet_name,temp,humidity,device_amount from opo_cabinet
        WHERE cabinet_name=#{name} and type=1
    </select>
    <select id="findMachineByCode" resultType="java.lang.String">
        SELECT  CONCAT(value, '', dw) as value FROM opo_machine_room_detail WHERE wyid=#{code}
    </select>
    <select id="findCabinetDevice" resultType="com.jhdr.operation.entity.vo.OpoCabinetDeviceVo">
        SELECT * FROM (SELECT device_name,device_type,device_model as deviceModel ,ip,rank,u_site,run_state as deviceState from opo_server_station where cabinet_name=#{name}	union all
        SELECT device_name,device_type,CONCAT(device_model, '', brand) as deviceModel,ip,rank,u_site,device_state from opo_netsafe_station where cabinet_name=#{name}	union all
        SELECT device_name,device_type,CONCAT(device_model, '', brand) as deviceModel,ip,rank,u_site,device_state from opo_network_station where cabinet_name=#{name}	)
        ORDER BY rank desc
    </select>
    <select id="findCabinetName" resultType="java.lang.String">
        SELECT cabinet_name FROM opo_cabinet where type=1
        ORDER BY cd
    </select>


    <insert id="insertOpoMachineRoom" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_machine_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="roomName != null">room_name,</if>
                    <if test="temp != null">temp,</if>
                    <if test="humidity != null">humidity,</if>
                    <if test="airState != null">air_state,</if>
                    <if test="smogState != null">smog_state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="roomName != null">#{roomName},</if>
                    <if test="temp != null">#{temp},</if>
                    <if test="humidity != null">#{humidity},</if>
                    <if test="airState != null">#{airState},</if>
                    <if test="smogState != null">#{smogState},</if>
        </trim>
    </insert>

    <update id="updateOpoMachineRoom" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomEditParam">
        update opo_machine_room
        <trim prefix="SET" suffixOverrides=",">
                    <if test="roomName != null">room_name = #{roomName},</if>
                    <if test="temp != null">temp = #{temp},</if>
                    <if test="humidity != null">humidity = #{humidity},</if>
                    <if test="airState != null">air_state = #{airState},</if>
                    <if test="smogState != null">smog_state = #{smogState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoMachineRoomById" parameterType="Long">
        delete from opo_machine_room where id = #{id}
    </delete>

    <delete id="deleteOpoMachineRoomByIds" parameterType="String">
        delete from opo_machine_room where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
