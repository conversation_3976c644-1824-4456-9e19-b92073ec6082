<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoMessageSendMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoMessageSendPo" id="OpoMessageSendResult">
        <result property="id"    column="id"    />
        <result property="people"    column="people"    />
        <result property="content"    column="content"    />
        <result property="person"    column="person"    />
        <result property="phone"    column="phone"    />
        <result property="addTime"    column="add_time"    />
    </resultMap>

    <sql id="selectOpoMessageSendVo">
        select id, people, content, person, phone, add_time,status from opo_message_send
    </sql>

    <select id="selectOpoMessageSendList"  resultType="com.jhdr.operation.entity.vo.OpoMessageSendVo">
        <include refid="selectOpoMessageSendVo"/>
        <where>
            <if test="person != null  and person != ''"> and person like concat('%', #{person}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone like concat('%', #{phone}, '%')</if>
        </where>
        order by id desc
    </select>


    <select id="getNoSendMessage" resultType="com.jhdr.operation.entity.vo.OpoNoSendVo">
        select  people, content, person, phone from opo_message_send where status=0
    </select>

    <select id="selectOpoMessageSendById"  resultType="com.jhdr.operation.entity.vo.OpoMessageSendVo">
            <include refid="selectOpoMessageSendVo"/>
            where id = #{id}
    </select>


    <insert id="insertOpoMessageSend" parameterType="com.jhdr.operation.entity.param.OpoMessageSendAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_message_send
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="people != null">people,</if>
                    <if test="content != null">content,</if>
                    <if test="person != null">person,</if>
                    <if test="phone != null">phone,</if>
                    <if test="addTime != null">add_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="people != null">#{people},</if>
                    <if test="content != null">#{content},</if>
                    <if test="person != null">#{person},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="addTime != null">#{addTime},</if>
        </trim>
    </insert>

    <update id="updateOpoMessageSend" parameterType="com.jhdr.operation.entity.param.OpoMessageSendEditParam">
        update opo_message_send
        <trim prefix="SET" suffixOverrides=",">
                    <if test="people != null">people = #{people},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="person != null">person = #{person},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="addTime != null">add_time = #{addTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoMessageSendById" parameterType="Integer">
        delete from opo_message_send where id = #{id}
    </delete>


</mapper>
