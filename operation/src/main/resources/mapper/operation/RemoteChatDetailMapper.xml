<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.RemoteChatDetailMapper">

    <resultMap type="com.jhdr.operation.entity.po.RemoteChatDetailPo" id="RemoteChatDetailResult">
        <result property="id"    column="id"    />
        <result property="chatId"    column="chat_id"    />
        <result property="roleName"    column="role_name"    />
        <result property="personId"    column="person_id"    />
        <result property="person"    column="person"    />
        <result property="addTime"    column="add_time"    />
        <result property="content"    column="content"    />
        <result property="status"    column="status"    />
        <result property="filePath"    column="file_path"    />
        <result property="fileName"    column="file_name"    />
    </resultMap>

    <sql id="selectRemoteChatDetailVo">
        select id, chat_id, role_name, person_id, person, add_time, content, status, file_path, file_name from remote_chat_detail
    </sql>

    <select id="selectRemoteChatDetailList"  resultType="com.jhdr.operation.entity.vo.RemoteChatDetailVo">
        <include refid="selectRemoteChatDetailVo"/>
        <where>

        </where>
    </select>

    <select id="selectRemoteChatDetailById"  resultType="com.jhdr.operation.entity.vo.RemoteChatDetailVo">
            <include refid="selectRemoteChatDetailVo"/>
            where id = #{id}
    </select>

    <insert id="insertRemoteChatDetail" parameterType="com.jhdr.operation.entity.param.RemoteChatDetailAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into remote_chat_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="chatId != null">chat_id,</if>
                    <if test="roleName != null">role_name,</if>
                    <if test="personId != null">person_id,</if>
                    <if test="person != null">person,</if>
                    <if test="addTime != null">add_time,</if>
                    <if test="content != null">content,</if>
                    <if test="status != null">status,</if>
                    <if test="filePath != null">file_path,</if>
                    <if test="fileName != null">file_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="chatId != null">#{chatId},</if>
                    <if test="roleName != null">#{roleName},</if>
                    <if test="personId != null">#{personId},</if>
                    <if test="person != null">#{person},</if>
                    <if test="addTime != null">#{addTime},</if>
                    <if test="content != null">#{content},</if>
                    <if test="status != null">#{status},</if>
                    <if test="filePath != null">#{filePath},</if>
                    <if test="fileName != null">#{fileName},</if>
        </trim>
    </insert>

    <update id="updateRemoteChatDetail" parameterType="com.jhdr.operation.entity.param.RemoteChatDetailEditParam">
        update remote_chat_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="chatId != null">chat_id = #{chatId},</if>
                    <if test="roleName != null">role_name = #{roleName},</if>
                    <if test="personId != null">person_id = #{personId},</if>
                    <if test="person != null">person = #{person},</if>
                    <if test="addTime != null">add_time = #{addTime},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="filePath != null">file_path = #{filePath},</if>
                    <if test="fileName != null">file_name = #{fileName},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateBaseStatus">
        update remote_chat set status=1 where id=#{chatId}
    </update>

    <delete id="deleteRemoteChatDetailById" parameterType="Integer">
        delete from remote_chat_detail where id = #{id}
    </delete>

    <delete id="deleteRemoteChatDetailByIds" parameterType="String">
        delete from remote_chat_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
