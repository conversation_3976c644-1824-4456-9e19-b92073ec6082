<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.ShareDataMapper">
    <update id="updateContrastTime">
        update jh_code_contrast set update_time = #{datatm} where jh_code=#{jhCode}
    </update>

    <select id="findRainStcds" resultType="java.lang.String">
        select STCD from st_stbprp_b where isdrp=1
    </select>
    <select id="findRainDataByStcd" resultType="com.jhdr.operation.entity.po.StPptnRPo">
        select  top 1 STCD,TM,DRP,INTV,PDR,DYP,WTH from ST_PPTN_R where stcd=#{stcd}
        order by TM desc
    </select>
    <select id="findRainRealData" resultType="com.jhdr.operation.entity.po.StPptnRPo">
        select STCD,TM,DRP,INTV,PDR,DYP,WTH from ST_PPTN_R where stcd=#{stcd} and TM=#{tm}
    </select>
    <select id="findFlowStcds" resultType="java.lang.String">
        select STCD from st_stbprp_b where STTP = 'ZQ' and source=10
    </select>

    <select id="findFlowDataByStcd" resultType="com.jhdr.operation.entity.po.StRiverRPo">
        select top 1 STCD,TM,Z,Q,XSA,XSAVV,XSMXV,FLWCHRCD,WPTN,MSQMT,MSAMT,MSVMT from ST_RIVER_R
        where stcd =#{stcd}
        order by TM desc
    </select>
    <select id="findFlowRealData" resultType="com.jhdr.operation.entity.po.StRiverRPo">
        select STCD,TM,Z,Q,XSA,XSAVV,XSMXV,FLWCHRCD,WPTN,MSQMT,MSAMT,MSVMT
        from st_river_r where STCD=#{stcd} and TM=#{tm}
    </select>
    <select id="findWaterLineStcds" resultType="java.lang.String">
        select STCD from st_stbprp_b where source ='10' and isz=1
    </select>

    <select id="findFlowAccumStcds" resultType="com.jhdr.operation.entity.vo.FlowAccumStcdVo">
        select nrcode,jh_code FROM jh_code_contrast WHERE jh_code !='' and station_type ='水量' and code_type='水文'
    </select>

    <select id="findFlowAccumByStcd" resultType="com.jhdr.operation.entity.po.JhFlowspRPo">
        SELECT FACTV as psq , time as tm  FROM wds.wds.REAL WHERE SENID=#{nrcode}
    </select>
    <select id="findFlowAccumData" resultType="com.jhdr.operation.entity.po.JhFlowspRPo">
        select stcd,tm FROM  jh_flowsp_r where  stcd=#{jhCode} and tm=#{tm}
    </select>

    <select id="findAllRainfall" resultType="com.jhdr.operation.entity.po.StPptnRPo">
        select   STCD,TM,DRP,INTV,PDR,DYP,WTH from ST_PPTN_R where stcd=#{stcd} and tm=#{date}
    </select>
    <select id="findShareAllRainTime" resultType="java.util.Date">
        SELECT tm FROM st_pptn_r WHERE stcd=#{stcd}
    </select>
    <select id="findAllRain" resultType="com.jhdr.operation.entity.po.StPptnRPo">
        select   stcd,tm,drp,intv,pdr,dyp,wth from ST_PPTN_R where stcd=#{stcd}
    </select>
    <select id="findShareAllRain" resultType="com.jhdr.operation.entity.po.StPptnRPo">
        select  STCD,TM,DRP,INTV,PDR,DYP,WTH from ST_PPTN_R where stcd=#{stcd}
        and tm between '2024-05-01' and '2024-06-15'
    </select>
    <select id="findRainsByStcd" resultType="com.jhdr.operation.entity.po.StPptnRPo">
        select  top 3 STCD,TM,DRP,INTV,PDR,DYP,WTH from ST_PPTN_R where stcd=#{stcd}
        order by TM desc
    </select>
    <select id="findAllFlow" resultType="com.jhdr.operation.entity.vo.FlowStcdVo">
        SELECT nrcode,jh_code ,update_time,station_name
        from jh_code_contrast
        where station_type='流量' and use_type=1 and code_type='水文'
    </select>

    <select id="findNewAllFlow" resultType="com.jhdr.operation.entity.vo.FlowStcdVo">
        SELECT nrcode,jh_code ,update_time,station_name,new_type
        from jh_code_contrast
        where station_type='水量' and use_type=1
    </select>
    <select id="selectFlowByCode" resultType="com.jhdr.operation.entity.po.JhFlowycRPo">
        select  time as datatm ,factv as q from wds.wds.Real where senid=#{nrcode} and time > #{updateTime}
    </select>
    <select id="findCodeByType" resultType="java.lang.String">
        select nrcode from jh_code_contrast where jh_code=#{jhCode} and station_type=#{type} and code_type='水文'
    </select>
    <select id="findDataByCode" resultType="java.lang.Double">
        select factv from wds.wds.Real where senid=#{nrcode}
    </select>
    <select id="findNowFlowByCd" resultType="java.lang.Double">
        SELECT (MAX(FACTV) - MIN(FACTV)) AS psq FROM wds.wds.RTSQ WHERE
        SENID=#{nrcode}  and CONVERT(varchar(13),time, 120) =CONVERT(varchar(13),GETDATE(), 120)
        and FACTV !=0
    </select>
    <select id="findMonthFlowByNrCode" resultType="java.lang.Double">
        SELECT ISNULL((MAX(FACTV) - MIN(FACTV)),0.0) AS psq FROM wds.wds.RTSQ WHERE SENID=#{nrcode}
        and datediff(MM,TIME,getdate())=0
    </select>
    <select id="findYearPoByTime" resultType="com.jhdr.operation.entity.po.StStationWaterYearPo">
        select * from st_station_water_year where station_code=#{stationCode} and station_year=#{year}
        and station_month=#{month}
    </select>
    <select id="findAllMonthFlow" resultType="com.jhdr.operation.entity.vo.FindMonthFlowVo">
        SELECT ISNULL((MAX(FACTV) - MIN(FACTV)),0.0) AS flowAccum,max(time) as tm
        FROM wds.wds.RTSQ WHERE SENID=#{nrCode}
        group by CONVERT(varchar(7),time, 120)
    </select>

</mapper>
