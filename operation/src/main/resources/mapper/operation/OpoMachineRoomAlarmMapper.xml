<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoMachineRoomAlarmMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoMachineRoomAlarmPo" id="OpoMachineRoomAlarmResult">
        <result property="id"    column="id"    />
        <result property="alarmid"    column="alarmid"    />
        <result property="lscid"    column="lscid"    />
        <result property="wyid"    column="wyid"    />
        <result property="siteName"    column="site_name"    />
        <result property="name"    column="name"    />
        <result property="isad"    column="isad"    />
        <result property="type"    column="type"    />
        <result property="ip"    column="ip"    />
        <result property="value"    column="value"    />
        <result property="dw"    column="dw"    />
        <result property="itemLevels"    column="item_levels"    />
        <result property="ts"    column="ts"    />
    </resultMap>

    <sql id="selectOpoMachineRoomAlarmVo">
        select id, alarmid, lscid, wyid, site_name, name, isad, type, ip, value, dw, item_levels, ts from opo_machine_room_alarm
    </sql>

    <select id="selectOpoMachineRoomAlarmList"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomAlarmVo">
        <include refid="selectOpoMachineRoomAlarmVo"/>
        <where>
            <if test="alarmid != null  and alarmid != ''"> and alarmid = #{alarmid}</if>
            <if test="lscid != null "> and lscid = #{lscid}</if>
            <if test="wyid != null  and wyid != ''"> and wyid = #{wyid}</if>
            <if test="siteName != null  and siteName != ''"> and site_name like concat('%', #{siteName}, '%')</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isad != null  and isad != ''"> and isad = #{isad}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="dw != null  and dw != ''"> and dw = #{dw}</if>
            <if test="itemLevels != null "> and item_levels = #{itemLevels}</if>
            <if test="ts != null "> and ts = #{ts}</if>
        </where>
    </select>

    <select id="selectOpoMachineRoomAlarmById"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomAlarmVo">
            <include refid="selectOpoMachineRoomAlarmVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoMachineRoomAlarm" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomAlarmAddParam">
        insert into opo_machine_room_alarm
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="alarmid != null">alarmid,</if>
                    <if test="lscid != null">lscid,</if>
                    <if test="wyid != null">wyid,</if>
                    <if test="siteName != null">site_name,</if>
                    <if test="name != null">name,</if>
                    <if test="isad != null">isad,</if>
                    <if test="type != null">type,</if>
                    <if test="ip != null">ip,</if>
                    <if test="value != null">value,</if>
                    <if test="dw != null">dw,</if>
                    <if test="itemLevels != null">item_levels,</if>
                    <if test="ts != null">ts,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="alarmid != null">#{alarmid},</if>
                    <if test="lscid != null">#{lscid},</if>
                    <if test="wyid != null">#{wyid},</if>
                    <if test="siteName != null">#{siteName},</if>
                    <if test="name != null">#{name},</if>
                    <if test="isad != null">#{isad},</if>
                    <if test="type != null">#{type},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="value != null">#{value},</if>
                    <if test="dw != null">#{dw},</if>
                    <if test="itemLevels != null">#{itemLevels},</if>
                    <if test="ts != null">#{ts},</if>
        </trim>
    </insert>

    <update id="updateOpoMachineRoomAlarm" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomAlarmEditParam">
        update opo_machine_room_alarm
        <trim prefix="SET" suffixOverrides=",">
                    <if test="alarmid != null">alarmid = #{alarmid},</if>
                    <if test="lscid != null">lscid = #{lscid},</if>
                    <if test="wyid != null">wyid = #{wyid},</if>
                    <if test="siteName != null">site_name = #{siteName},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="isad != null">isad = #{isad},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="value != null">value = #{value},</if>
                    <if test="dw != null">dw = #{dw},</if>
                    <if test="itemLevels != null">item_levels = #{itemLevels},</if>
                    <if test="ts != null">ts = #{ts},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoMachineRoomAlarmById" parameterType="Long">
        delete from opo_machine_room_alarm where id = #{id}
    </delete>

    <delete id="deleteOpoMachineRoomAlarmByIds" parameterType="String">
        delete from opo_machine_room_alarm where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
