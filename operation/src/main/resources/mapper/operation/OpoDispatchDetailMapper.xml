<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoDispatchDetailMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoDispatchDetailPo" id="OpoDispatchDetailResult">
        <result property="id"    column="id"    />
        <result property="personId"    column="person_id"    />
        <result property="person"    column="person"    />
        <result property="addTime"    column="add_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="taskId"    column="task_id"    />
    </resultMap>

    <sql id="selectOpoDispatchDetailVo">
        select id, person_id, person, add_time, is_delete,status, task_id from opo_dispatch_detail
    </sql>

    <select id="selectOpoDispatchDetailList"  resultType="com.jhdr.operation.entity.vo.OpoDispatchDetailVo">
        <include refid="selectOpoDispatchDetailVo"/>
        <where>
            <if test="personId != null  and personId != ''"> and person_id = #{personId}</if>
            <if test="person != null  and person != ''"> and person = #{person}</if>
            <if test="addTime != null "> and add_time = #{addTime}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
        </where>
    </select>

    <select id="selectOpoDispatchDetailById"  resultType="com.jhdr.operation.entity.vo.OpoDispatchDetailVo">
            <include refid="selectOpoDispatchDetailVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoDispatchDetail" parameterType="com.jhdr.operation.entity.param.OpoDispatchDetailAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_dispatch_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="personId != null">person_id,</if>
                    <if test="person != null">person,</if>
                    <if test="addTime != null">add_time,</if>
                    <if test="isDelete != null">is_delete,</if>
                    <if test="taskId != null">task_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="personId != null">#{personId},</if>
                    <if test="person != null">#{person},</if>
                    <if test="addTime != null">#{addTime},</if>
                    <if test="isDelete != null">#{isDelete},</if>
                    <if test="taskId != null">#{taskId},</if>
        </trim>
    </insert>

    <update id="updateOpoDispatchDetail" parameterType="com.jhdr.operation.entity.param.OpoDispatchDetailEditParam">
        update opo_dispatch_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="personId != null">person_id = #{personId},</if>
                    <if test="person != null">person = #{person},</if>
                    <if test="addTime != null">add_time = #{addTime},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
                    <if test="taskId != null">task_id = #{taskId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoDispatchDetailById" parameterType="Integer">
        delete from opo_dispatch_detail where id = #{id}
    </delete>

    <delete id="deleteOpoDispatchDetailByIds" parameterType="String">
        delete from opo_dispatch_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
