<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoAlarmRecordMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoAlarmRecordPo" id="OpoAlarmRecordResult">
        <result property="id"    column="id"    />
        <result property="alarmCode"    column="alarm_code"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceCode"    column="device_code"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="addTime"    column="add_time"    />
        <result property="alarmState"    column="alarm_state"    />
        <result property="content"    column="content"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectOpoAlarmRecordVo">
        select id,device_type, alarm_code, device_name, device_code, alarm_type, add_time, alarm_state,
        content, is_delete,site,person,result,update_time from opo_alarm_record
    </sql>

    <select id="selectOpoAlarmRecordList"  resultType="com.jhdr.operation.entity.vo.OpoAlarmRecordVo">
        <include refid="selectOpoAlarmRecordVo"/>
        <where>
            <if test="alarmType != null  and alarmType != ''"> and alarm_type = #{alarmType}</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceCode != null  and deviceCode != ''"> and device_code = #{deviceCode}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="startTime != null and startTime != ''"> and add_time between #{startTime} and #{endTime}</if>
            <if test="alarmState != null and alarmState != ''"> and alarm_state = #{alarmState} </if>
            and   is_delete=0
        </where>

        order by add_time desc
    </select>

    <select id="selectOpoAlarmRecordById"  resultType="com.jhdr.operation.entity.vo.OpoAlarmRecordVo">
            <include refid="selectOpoAlarmRecordVo"/>
            where id = #{id}
    </select>
    <select id="findAlarmType" resultType="java.lang.String">
        select alarm_type from opo_alarm_record where alarm_type is not null
        GROUP BY alarm_type
    </select>
    <select id="findStrAlarmRecord" resultType="com.jhdr.operation.entity.vo.OpoAlarmRecordVo">
        select id,device_name,alarm_type,add_time,regain_time,content from opo_alarm_record
        <where>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%')</if>
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="startTime != null and startTime != ''"> and add_time  between #{startTime} and #{endTime}</if>
        </where>
        order by add_time desc
    </select>
    <select id="findDeviceType" resultType="java.lang.String">
        select device_type from opo_alarm_record where device_type is not null
        GROUP BY device_type
    </select>
    <select id="getMaxOneByCode" resultType="com.jhdr.operation.entity.po.OpoAlarmRecordPo">
        select top 1 * from opo_alarm_record where is_delete=0 and device_name =#{deviceName}
    </select>

    <insert id="insertOpoAlarmRecord" parameterType="com.jhdr.operation.entity.param.OpoAlarmRecordAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_alarm_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="alarmCode != null">alarm_code,</if>
                    <if test="deviceName != null">device_name,</if>
                    <if test="deviceCode != null">device_code,</if>
                    <if test="alarmType != null">alarm_type,</if>
                    <if test="addTime != null">add_time,</if>
                    <if test="alarmState != null">alarm_state,</if>
                    <if test="content != null">content,</if>
                    <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="alarmCode != null">#{alarmCode},</if>
                    <if test="deviceName != null">#{deviceName},</if>
                    <if test="deviceCode != null">#{deviceCode},</if>
                    <if test="alarmType != null">#{alarmType},</if>
                    <if test="addTime != null">#{addTime},</if>
                    <if test="alarmState != null">#{alarmState},</if>
                    <if test="content != null">#{content},</if>
                    <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateOpoAlarmRecord" parameterType="com.jhdr.operation.entity.param.OpoAlarmRecordEditParam">
        update opo_alarm_record
        <trim prefix="SET" suffixOverrides=",">
                    <if test="alarmCode != null">alarm_code = #{alarmCode},</if>
                    <if test="deviceName != null">device_name = #{deviceName},</if>
                    <if test="deviceCode != null">device_code = #{deviceCode},</if>
                    <if test="alarmType != null">alarm_type = #{alarmType},</if>
                    <if test="addTime != null">add_time = #{addTime},</if>
                    <if test="alarmState != null">alarm_state = #{alarmState},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoAlarmRecordById" parameterType="Long">
        delete from opo_alarm_record where id = #{id}
    </delete>

    <delete id="deleteOpoAlarmRecordByIds" parameterType="String">
        delete from opo_alarm_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
