<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoNetworkStationMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoNetworkStationPo" id="OpoNetworkStationResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceModel"    column="device_model"    />
        <result property="deviceType"    column="device_type"    />
        <result property="ip"    column="ip"    />
        <result property="mac"    column="mac"    />
        <result property="site"    column="site"    />
        <result property="deviceState"    column="device_state"    />
        <result property="netState"    column="net_state"    />
        <result property="alarmTime"    column="alarm_time"    />
    </resultMap>

    <sql id="selectOpoNetworkStationVo">
        select id, device_name, brand ,device_model, device_type, ip, mac,gateway,mask,dns, site, device_state, net_state, alarm_time,update_time,remark,cabinet_name,
        cpu_use,nc_use,is_mon from opo_network_station
    </sql>

    <select id="selectOpoNetworkStationList"  resultType="com.jhdr.operation.entity.vo.OpoNetworkStationVo">
        <include refid="selectOpoNetworkStationVo"/>
        where  is_delete=0
            <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%') </if>
            <if test="deviceState != null  and deviceState != ''"> and device_state = #{deviceState}</if>
            <if test="netState != null  and netState != ''"> and net_state = #{netState}</if>
    </select>

    <select id="selectOpoNetworkStationById"  resultType="com.jhdr.operation.entity.vo.OpoNetworkStationVo">
            <include refid="selectOpoNetworkStationVo"/>
            where id = #{id}
    </select>
    <select id="findWorkByState" resultType="com.jhdr.operation.entity.vo.DeviceIpMonVo">
        select id,device_name,ip,cabinet_name as site from opo_network_station
        where is_delete=0 and mon_type=1 and ip is not null and device_state=#{state}
    </select>

    <select id="findTopologyState" resultType="com.jhdr.operation.entity.vo.NetStateVo">
        select  device_name,device_state as status from opo_network_station where is_delete=0  UNION all
        select  device_name,device_state as status from opo_netsafe_station where is_delete=0
    </select>
    <select id="getWorkStateAmount" resultType="java.lang.Integer">
        select  count(1) from opo_network_station  where  is_delete=0  and is_mon=1 and device_state = #{status}
    </select>

    <insert id="insertOpoNetworkStation" parameterType="com.jhdr.operation.entity.param.OpoNetworkStationAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_network_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="deviceName != null">device_name,</if>
                    <if test="deviceModel != null">device_model,</if>
                    <if test="deviceType != null">device_type,</if>
                    <if test="ip != null">ip,</if>
                    <if test="mac != null">mac,</if>
                    <if test="site != null">site,</if>
                    <if test="deviceState != null">device_state,</if>
                    <if test="netState != null">net_state,</if>
                    <if test="alarmTime != null">alarm_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="deviceName != null">#{deviceName},</if>
                    <if test="deviceModel != null">#{deviceModel},</if>
                    <if test="deviceType != null">#{deviceType},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="mac != null">#{mac},</if>
                    <if test="site != null">#{site},</if>
                    <if test="deviceState != null">#{deviceState},</if>
                    <if test="netState != null">#{netState},</if>
                    <if test="alarmTime != null">#{alarmTime},</if>
        </trim>
    </insert>

    <update id="updateOpoNetworkStation" parameterType="com.jhdr.operation.entity.param.OpoNetworkStationEditParam">
        update opo_network_station
        <trim prefix="SET" suffixOverrides=",">
                    <if test="deviceName != null">device_name = #{deviceName},</if>
                    <if test="deviceModel != null">device_model = #{deviceModel},</if>
                    <if test="deviceType != null">device_type = #{deviceType},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="mac != null">mac = #{mac},</if>
                    <if test="site != null">site = #{site},</if>
                    <if test="deviceState != null">device_state = #{deviceState},</if>
                    <if test="netState != null">net_state = #{netState},</if>
                    <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateTopologyState">
        update opo_network_topology set status =#{status},update_time=now()
        where up_name=#{deviceName} or down_name=#{deviceName}
    </update>
    <update id="updateNetWork">
        update opo_network_station set update_time=now(),alarm_time=#{time},
        device_state=#{state},alarm_type=#{alarmType} where id=#{id}
    </update>

    <delete id="deleteOpoNetworkStationById" parameterType="Long">
        delete from opo_network_station where id = #{id}
    </delete>

    <delete id="deleteOpoNetworkStationByIds" parameterType="String">
        delete from opo_network_station where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
