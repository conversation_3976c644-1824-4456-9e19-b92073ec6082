<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.PatrolPlanMapper">

    <resultMap type="com.jhdr.operation.entity.po.PatrolPlanPo" id="PatrolPlanResult">
        <result property="id"    column="id"    />
        <result property="patrolName"    column="patrol_name"    />
        <result property="patrolType"    column="patrol_type"    />
        <result property="patrolScope"    column="patrol_scope"    />
        <result property="content"    column="content"    />
        <result property="person"    column="person"    />
        <result property="cycle"    column="cycle"    />
        <result property="patrolTime"    column="patrol_time"    />
        <result property="informTime"    column="inform_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectPatrolPlanVo">
        select id, patrol_name, patrol_type, patrol_scope, content, person, cycle, patrol_time, inform_time, is_delete from opo_patrol_plan
    </sql>

    <select id="selectPatrolPlanList"  resultType="com.jhdr.operation.entity.vo.PatrolPlanVo">
        <include refid="selectPatrolPlanVo"/>
        order by  id desc
    </select>

    <select id="selectPatrolPlanById"  resultType="com.jhdr.operation.entity.vo.PatrolOnePlanVo">
            <include refid="selectPatrolPlanVo"/>
            where id = #{id}
    </select>
    <select id="findPlanDetailByCode" resultType="com.jhdr.operation.entity.vo.PatrolPlanDetailVo">
        select * from opo_patrol_plan_detail where plan_id=#{id}
    </select>

    <insert id="insertPatrolPlan" parameterType="com.jhdr.operation.entity.param.PatrolPlanAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_patrol_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">

                    <if test="patrolName != null">patrol_name,</if>
                    <if test="patrolType != null">patrol_type,</if>
                    <if test="patrolScope != null">patrol_scope,</if>
                    <if test="content != null">content,</if>
                    <if test="person != null">person,</if>
                    <if test="cycle != null">cycle,</if>
                    <if test="patrolTime != null">patrol_time,</if>
                    <if test="informTime != null">inform_time,</if>
                    <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="patrolCode != null">#{patrolCode},</if>
                    <if test="patrolName != null">#{patrolName},</if>
                    <if test="patrolType != null">#{patrolType},</if>
                    <if test="patrolScope != null">#{patrolScope},</if>
                    <if test="content != null">#{content},</if>
                    <if test="person != null">#{person},</if>
                    <if test="cycle != null">#{cycle},</if>
                    <if test="patrolTime != null">#{patrolTime},</if>
                    <if test="informTime != null">#{informTime},</if>
                    <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updatePatrolPlan" parameterType="com.jhdr.operation.entity.param.PatrolPlanEditParam">
        update opo_patrol_plan
        <trim prefix="SET" suffixOverrides=",">
                    <if test="patrolCode != null">patrol_code = #{patrolCode},</if>
                    <if test="patrolName != null">patrol_name = #{patrolName},</if>
                    <if test="patrolType != null">patrol_type = #{patrolType},</if>
                    <if test="patrolScope != null">patrol_scope = #{patrolScope},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="person != null">person = #{person},</if>
                    <if test="cycle != null">cycle = #{cycle},</if>
                    <if test="patrolTime != null">patrol_time = #{patrolTime},</if>
                    <if test="informTime != null">inform_time = #{informTime},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatrolPlanById" parameterType="Long">
        delete from opo_patrol_plan where id = #{id}
    </delete>

    <delete id="deletePatrolPlanByIds" parameterType="String">
        delete from opo_patrol_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDetails">
        delete from opo_patrol_plan_detail where plan_id=#{id}
    </delete>
</mapper>
