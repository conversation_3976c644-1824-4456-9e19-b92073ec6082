<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoMachineRoomDeviceMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoMachineRoomDevicePo" id="OpoMachineRoomDeviceResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="subClass"    column="sub_class"    />
        <result property="ip"    column="ip"    />
        <result property="name"    column="name"    />
        <result property="netStatus"    column="net_status"    />
        <result property="subtype"    column="subtype"    />
    </resultMap>

    <sql id="selectOpoMachineRoomDeviceVo">
        select id, parent_id, sub_class, ip, name, net_status, subtype from opo_machine_room_device
    </sql>

    <select id="selectOpoMachineRoomDeviceList"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomDeviceVo">
        <include refid="selectOpoMachineRoomDeviceVo"/>
        <where>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="subClass != null "> and sub_class = #{subClass}</if>
            <if test="ip != null  and ip != ''"> and ip = #{ip}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="netStatus != null "> and net_status = #{netStatus}</if>
            <if test="subtype != null "> and subtype = #{subtype}</if>
        </where>
    </select>

    <select id="selectOpoMachineRoomDeviceById"  resultType="com.jhdr.operation.entity.vo.OpoMachineRoomDeviceVo">
            <include refid="selectOpoMachineRoomDeviceVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoMachineRoomDevice" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomDeviceAddParam">
        insert into opo_machine_room_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="parentId != null">parent_id,</if>
                    <if test="subClass != null">sub_class,</if>
                    <if test="ip != null">ip,</if>
                    <if test="name != null">name,</if>
                    <if test="netStatus != null">net_status,</if>
                    <if test="subtype != null">subtype,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="parentId != null">#{parentId},</if>
                    <if test="subClass != null">#{subClass},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="name != null">#{name},</if>
                    <if test="netStatus != null">#{netStatus},</if>
                    <if test="subtype != null">#{subtype},</if>
        </trim>
    </insert>

    <update id="updateOpoMachineRoomDevice" parameterType="com.jhdr.operation.entity.param.OpoMachineRoomDeviceEditParam">
        update opo_machine_room_device
        <trim prefix="SET" suffixOverrides=",">
                    <if test="parentId != null">parent_id = #{parentId},</if>
                    <if test="subClass != null">sub_class = #{subClass},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="name != null">name = #{name},</if>
                    <if test="netStatus != null">net_status = #{netStatus},</if>
                    <if test="subtype != null">subtype = #{subtype},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoMachineRoomDeviceById" parameterType="Long">
        delete from opo_machine_room_device where id = #{id}
    </delete>

    <delete id="deleteOpoMachineRoomDeviceByIds" parameterType="String">
        delete from opo_machine_room_device where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
