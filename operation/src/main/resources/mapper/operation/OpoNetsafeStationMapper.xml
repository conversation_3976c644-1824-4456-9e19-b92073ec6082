<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoNetsafeStationMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoNetsafeStationPo" id="OpoNetsafeStationResult">
        <result property="id"    column="id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceModel"    column="device_model"    />
        <result property="deviceType"    column="device_type"    />
        <result property="ip"    column="ip"    />
        <result property="mac"    column="mac"    />
        <result property="site"    column="site"    />
        <result property="deviceState"    column="device_state"    />
        <result property="netState"    column="net_state"    />
        <result property="alarmType"    column="alarm_type"    />
        <result property="alarmTime"    column="alarm_time"    />
    </resultMap>

    <sql id="selectOpoNetsafeStationVo">
        select id, device_name, brand,device_model, device_type, ip, mac,gateway,mask,dns, site, device_state, net_state,
        alarm_type, alarm_time,cabinet_name,update_time ,cpu_use,nc_use,is_mon from opo_netsafe_station
    </sql>

    <select id="selectOpoNetsafeStationList"  resultType="com.jhdr.operation.entity.vo.OpoNetsafeStationVo">
        <include refid="selectOpoNetsafeStationVo"/>
            where is_delete=0
        <if test="deviceName != null  and deviceName != ''"> and device_name like concat('%', #{deviceName}, '%') </if>
        <if test="deviceType != null  and deviceType != ''"> and device_type = #{deviceType}</if>
            <if test="deviceState != null  and deviceState != ''"> and device_state = #{deviceState}</if>
            <if test="netState != null  and netState != ''"> and net_state = #{netState}</if>
    </select>

    <select id="selectOpoNetsafeStationById"  resultType="com.jhdr.operation.entity.vo.OpoNetsafeStationVo">
            <include refid="selectOpoNetsafeStationVo"/>
            where id = #{id}
    </select>
    <select id="findSafeByState" resultType="com.jhdr.operation.entity.vo.DeviceIpMonVo">
        select id,device_name,ip from opo_netsafe_station
        where is_delete=0 and mon_type=1 and ip is not null and device_state=#{state}
    </select>
    <select id="getSafeStateAmount" resultType="java.lang.Integer">
        select count(1) from opo_netsafe_station  where  is_delete=0 and device_state =#{status} and is_mon=1
    </select>

    <insert id="insertOpoNetsafeStation" parameterType="com.jhdr.operation.entity.param.OpoNetsafeStationAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_netsafe_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="deviceName != null">device_name,</if>
                    <if test="deviceModel != null">device_model,</if>
                    <if test="deviceType != null">device_type,</if>
                    <if test="ip != null">ip,</if>
                    <if test="mac != null">mac,</if>
                    <if test="site != null">site,</if>
                    <if test="deviceState != null">device_state,</if>
                    <if test="netState != null">net_state,</if>
                    <if test="alarmType != null">alarm_type,</if>
                    <if test="alarmTime != null">alarm_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="deviceName != null">#{deviceName},</if>
                    <if test="deviceModel != null">#{deviceModel},</if>
                    <if test="deviceType != null">#{deviceType},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="mac != null">#{mac},</if>
                    <if test="site != null">#{site},</if>
                    <if test="deviceState != null">#{deviceState},</if>
                    <if test="netState != null">#{netState},</if>
                    <if test="alarmType != null">#{alarmType},</if>
                    <if test="alarmTime != null">#{alarmTime},</if>
        </trim>
    </insert>

    <update id="updateOpoNetsafeStation" parameterType="com.jhdr.operation.entity.param.OpoNetsafeStationEditParam">
        update opo_netsafe_station
        <trim prefix="SET" suffixOverrides=",">
                    <if test="deviceName != null">device_name = #{deviceName},</if>
                    <if test="deviceModel != null">device_model = #{deviceModel},</if>
                    <if test="deviceType != null">device_type = #{deviceType},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="mac != null">mac = #{mac},</if>
                    <if test="site != null">site = #{site},</if>
                    <if test="deviceState != null">device_state = #{deviceState},</if>
                    <if test="netState != null">net_state = #{netState},</if>
                    <if test="alarmType != null">alarm_type = #{alarmType},</if>
                    <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateNetWork">
        update opo_netsafe_station set update_time=now(),alarm_time=#{time},
        device_state=#{state},alarm_type=#{alarmType} where id=#{id}
    </update>

    <delete id="deleteOpoNetsafeStationById" parameterType="Long">
        delete from opo_netsafe_station where id = #{id}
    </delete>

    <delete id="deleteOpoNetsafeStationByIds" parameterType="String">
        delete from opo_netsafe_station where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
