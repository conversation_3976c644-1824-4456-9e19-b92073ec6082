<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.CommonMapper">
    <insert id="insertWaterLine">
        INSERT INTO st_river_r (stcd,z,tm) values(#{code}, #{waterLine}, #{addTime})
    </insert>
    <insert id="insertFlow">
        INSERT INTO st_river_r (stcd,q,tm) values(#{code}, #{flow}, #{addTime})
    </insert>



    <select id="findAllWaterLine" resultType="com.jhdr.operation.entity.vo.FindWaterLineVo">
        SELECT a.SENID as code,a.FACTV as waterLine,a.TIME as addTime FROM wds.wds.Real a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b WHERE b.dtype=2 and b.DCHAR=201
        and a.SENID=b.ID
        and a.SENID in ('10110203500201','10110203700201','10110201100201')
        and  DAY(a.Time)= day(GETDATE())
    </select>
    <select id="findLineDataByCodeTime" resultType="com.jhdr.operation.entity.vo.FindWaterLineVo">
        select top 1 stcd as code,tm as addTime from st_river_r
        WHERE stcd=#{code} and tm=#{addTime}
        order by tm desc
    </select>
    <select id="findAllFlow" resultType="com.jhdr.operation.entity.vo.FindFlowVo">
        SELECT a.SENID as code,a.FACTV as waterLine,a.TIME as addTime
        FROM wds.wds.Real a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b WHERE b.dtype in (5,1,2) and b.DCHAR in (700,701,600)
        and a.SENID=b.ID and  DAY(a.Time)= day(GETDATE())
    </select>
    <select id="findFlowDataByCodeTime" resultType="com.jhdr.operation.entity.vo.FindFlowVo">
        select top 1 stcd as code,tm as addTime from st_river_r
        WHERE stcd=#{code} and tm=#{addTime}
        order by tm desc
    </select>
    <select id="findAllPumpReal" resultType="com.jhdr.operation.entity.vo.PumpRealVo">
        SELECT a.SENID as id ,a.FACTV as data,a.TIME as addTime FROM wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID and  b.PID =#{pid} and
        DAY(a.Time)= day(GETDATE())
    </select>
    <select id="findNewPumpDtl" resultType="com.jhdr.operation.entity.po.JhPumpDtlRPo">
        select * from jh_pump_dtl_r
        where prjnmcd=#{pid} and pmpcd=#{id} and clltm =#{addTime}
    </select>
    <select id="findAllPid" resultType="java.lang.String">
        select str_code from jhir_pust_b where  source='20'
    </select>
    <select id="findGateById" resultType="com.jhdr.operation.entity.vo.GateRealVo">
        select a.SENID as id ,a.FACTV as data ,a.TIME as addTime from wds.wds.REAL a,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID
        and datediff(dd,TIME,getdate())=0
        and  a.SENID like  '%' + #{pid} + '%'
        and  b.NAME like  '%' + '开度' + '%'
    </select>
    <select id="findGate1ById" resultType="com.jhdr.operation.entity.vo.GateRealVo">
        select a.SENID as id ,a.FACTV as data ,a.TIME as addTime from wds.wds.REAL a,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID
        and datediff(dd,TIME,getdate())=0
        and  a.SENID like  '%' + #{pid} + '%'
        and  b.NAME like  '%' + '全开' + '%'
    </select>

    <select id="findGate2ById" resultType="com.jhdr.operation.entity.vo.GateRealVo">
        select a.SENID as id ,a.FACTV as data ,a.TIME as addTime from wds.wds.REAL a,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID
        and datediff(dd,TIME,getdate())=0
        and  a.SENID like  '%' + #{pid} + '%'
        and  b.NAME like  '%' + '全关' + '%'
    </select>
    <select id="find1GateById" resultType="com.jhdr.operation.entity.vo.GateRealVo">
        select a.SENID as id ,a.FACTV as data ,a.TIME as addTime from wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID and datediff(dd,TIME,getdate())=0 and  a.SENID =#{id}
    </select>
    <select id="findGateNameType" resultType="com.jhdr.operation.entity.vo.GateRealVo">
        select a.SENID as id ,a.FACTV as data ,a.TIME as addTime,b.pid as pid from wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID
        and  b.NAME like  '%' + #{name} + '%'
        and  b.NAME like  '%' + #{type} + '%'
    </select>
    <select id="findSlcrsByPid" resultType="com.jhdr.operation.entity.po.JhSlcrsDtlRPo">
        select * from jh_slcrs_dtl_r where prjnmcd=#{pid} and clltm=#{addTime} and gpcd=#{id}
    </select>
    <select id="findAllPumoState" resultType="java.lang.Integer">
        select count (1) from wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID  and b.PID=#{pid}
        and b.name  like '%'+ '机组运行' +'%'
    </select>
    <select id="findRunPumpState" resultType="java.lang.Integer">
        select count (1) from wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID  and a.FACTV >0 and b.PID=#{pid}
        and b.name  like '%'+ '机组运行' +'%'
    </select>

    <select id="findRunGateCount" resultType="java.lang.Integer">
        select count (1) from wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID   and a.FACTV >0 and b.name like '%'+ #{name} +'%' and b.name like '%'+ '开度' +'%'
    </select>
    <select id="exchangePmpcdByid" resultType="java.lang.String">
        select pmpcd from jhir_pump_b where pump_code =#{id}
    </select>
    <select id="exchangePrjnmcdByid" resultType="java.lang.String">
        select str_code from jhir_strobe_b where strobe_code =#{id}
    </select>
    <select id="exchangeGpcdByid" resultType="java.lang.String">
        select gpcd from jhir_strobe_b where strobe_code =#{id}
    </select>
    <select id="findWaterLineByData" resultType="java.lang.Double">
        select a.FACTV as data  from wds.wds.REAL a ,pubuser.PUBUSER.WDS_HYDRO_ELEMENTS b
        WHERE  a.SENID=b.ID
        and  b.NAME like  '%' + #{name} + '%'
        and  b.NAME =#{waterName}
    </select>
    <select id="findWaterLineByCode" resultType="com.jhdr.operation.entity.vo.GateRealVo">
        select FACTV as data ,TIME as addTime from wds.wds.REAL
        WHERE  SENID=#{code}
    </select>
    <select id="findRealWasData" resultType="com.jhdr.operation.entity.po.StWasRPo">
        select * from st_was_r where stcd=#{stcd} and tm=#{addTime}
    </select>
    <select id="findRealRiverData" resultType="com.jhdr.operation.entity.po.StRiverRPo">
        select * from st_river_r where stcd=#{stcd} and tm=#{addTime}
    </select>
    <select id="findJhCodeByCode" resultType="java.lang.String">
        select jh_code from jh_code_contrast where nrcode=#{code} and code_type='水文'
    </select>
    <select id="findShareCds" resultType="java.lang.String">
        select jh_code from jh_code_contrast where station_type=#{type} and code_type='水文'
    </select>
    <select id="findUpWaterByCode" resultType="com.jhdr.operation.entity.po.StWasRPo">
        SELECT top 1
        stcd,tm,upz,dwz,tgtq,swchrcd,supwptn,sdwwptn,msqmt
        from ST_WAS_R
        WHERE stcd =#{upcd}
        ORDER BY tm desc
    </select>
    <select id="findDownWaterByCode" resultType="com.jhdr.operation.entity.po.StRiverRPo">
        SELECT top 1
        stcd,tm,z,q,xsa,xsmxv,flwchrcd,wptn,msqmt,msamt,msvmt
        from ST_RIVER_R
        WHERE stcd =#{downCd}
        ORDER BY tm desc
    </select>
    <select id="findPustPid" resultType="com.jhdr.operation.entity.vo.PustCodeVo">
        select str_code,pid_code ,ifnull(des_flow,7.52) as desFlow ,pump_num from jhir_pust_b
        where  source='20' and pid_code !='' and status=1
    </select>
    <select id="findWdwcdByStrCode" resultType="java.lang.String">
        SELECT wdwcd from jhir_pust_b where str_code=#{strCode}
    </select>
    <select id="findWupcdByStrCode" resultType="java.lang.String">
        SELECT wupcd from jhir_pust_b where str_code=#{strCode}
    </select>
    <select id="getWaterLineByName" resultType="java.lang.String">
        SELECT top 1 waterLine FROM(SELECT  waterLine from (select  top 1  b.upz as waterLine from st_stbprp_b a,st_was_r b WHERE a.stcd=b.stcd and a.stnm=#{name}  ORDER BY b.tm desc) UNION all
        SELECT  waterLine from (select  top 1  b.z as waterLine from st_stbprp_b a,st_river_r b WHERE a.stcd=b.stcd and a.stnm=#{name}   ORDER BY b.tm desc))
    </select>
    <select id="getUsers" resultType="com.jhdr.operation.entity.vo.OpoUser">
        select user_id,user_name from public.sys_user where status=0 and del_flag=0
        order by user_name asc
    </select>
    <select id="findGisData" resultType="com.jhdr.operation.entity.vo.VoiceResultVo">
        SELECT top 1 guid,cd,nm,gisdata,gissign,lgtd,lttd from gis_datas_d
        where nm  = #{strName}  and  gissign=#{type}
    </select>
    <select id="findAllPumpCount" resultType="java.lang.Integer">
        SELECT count(1) FROM jh_pump_dtl_r_real WHERE prjnmcd=#{strCode}
    </select>
    <select id="findRunPumpCount" resultType="java.lang.Integer">
        SELECT count(1) FROM jh_pump_dtl_r_real WHERE  om=1 and  prjnmcd=#{strCode}
    </select>
    <select id="findOneDataById" resultType="java.lang.Double">
        select FACTV as data ,a.TIME as addTime from wds.wds.REAL
    </select>
    <select id="findWagaList" resultType="com.jhdr.operation.entity.vo.WagaCodeVo">
        SELECT  a.str_code as strCode,a.calculate as calculate, a.gaor_num as gaorNum,b.nrcode as waterCode
        FROM jhir_waga_b a LEFT JOIN jh_code_contrast b ON a.str_code = b.jh_code
        WHERE a.source='20' and a.status=1  and b.station_type='水位' and b.code_type='涵闸'
    </select>
    <select id="findGateCodesByStr" resultType="com.jhdr.operation.entity.vo.GateCodeVo">
        select gtoph_code,gtaop_code,gtaco_code,gpcd FROM jhir_strobe_b WHERE str_code =#{strCode}
    </select>


</mapper>