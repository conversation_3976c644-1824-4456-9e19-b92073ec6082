<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoVideoStationMapper">





    <select id="selectOpoVideoStationList"  resultType="com.jhdr.operation.entity.vo.OpoVideoStationVo">
        SELECT  cd,nvr,nm,ip,cn,camera_code,status,alarm_time ,alarm_content,discern,
        discern_state,site_type,site_url,construction,is_mon
        FROM jhom_vm_b  where usfl=1

            <if test="nm != null  and nm != ''"> and nm like concat('%', #{nm}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
            <if test="indexCode != null  and indexCode != ''"> and region_index_code = #{indexCode}</if>

    </select>

    <select id="getVideoStateAmount" resultType="java.lang.Integer">
        SELECT   count(1)
        FROM jhom_vm_b where  status =#{status} and usfl=1 and is_mon=1
        <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
    </select>

    <select id="selectOpoVideoStationById"  resultType="com.jhdr.operation.entity.vo.OpoVideoStationVo">
        SELECT  cd,nvr,nm,ip,cn,camera_code,status,alarm_time FROM jhom_vm_b
            where cd = #{cd}
    </select>
<!--    <select id="findAlarmTimeByCd" resultType="java.util.Date">-->
<!--        select add_time from opo_alarm_message where device_code=#{cd}-->
<!--    </select>-->
    <select id="findAllCodes" resultType="java.lang.String">
        select camera_index_code FROM opo_jhom_vm
    </select>
    <select id="findCodeByState" resultType="com.jhdr.operation.entity.vo.OpoVideoStateVo">
        SELECT cd,nm,camera_code as code ,status ,ifnull(update_time,'2024-01-01 00:00:00')as updateTime
        FROM jhom_vm_b WHERE status =#{state} and usfl=1 and is_mon=1
    </select>
    <select id="selectDHVideoChannel" resultType="com.jhdr.operation.entity.vo.OpoDHVideoStateVo">
        select cd,nm,dh_channel from jhom_vm_b where site_type=2 and dh_channel !='' and usfl=1
        and status=#{state}
    </select>
    <select id="selectNmByIndexCode" resultType="java.lang.String">
        select top 1 nm from jhom_vm_b where camera_code =#{indexCode}
    </select>
    <select id="selectHKWSCode" resultType="com.jhdr.operation.entity.vo.OpoHKWSVideoStateVo">
        SELECT cd ,nm,camera_code FROM jhom_vm_b where usfl=1 and site_type=1 and status=#{state}
    </select>


    <insert id="insertOpoVideoStation" parameterType="com.jhdr.operation.entity.param.OpoVideoStationAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_video_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="staName != null">sta_name,</if>
                    <if test="staCode != null">sta_code,</if>
                    <if test="deviceName != null">device_name,</if>
                    <if test="ip != null">ip,</if>
                    <if test="channelId != null">channel_id,</if>
                    <if test="staState != null">sta_state,</if>
                    <if test="project != null">project,</if>
                    <if test="region != null">region,</if>
                    <if test="alarmTime != null">alarm_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="staName != null">#{staName},</if>
                    <if test="staCode != null">#{staCode},</if>
                    <if test="deviceName != null">#{deviceName},</if>
                    <if test="ip != null">#{ip},</if>
                    <if test="channelId != null">#{channelId},</if>
                    <if test="staState != null">#{staState},</if>
                    <if test="project != null">#{project},</if>
                    <if test="region != null">#{region},</if>
                    <if test="alarmTime != null">#{alarmTime},</if>
        </trim>
    </insert>

    <update id="updateOpoVideoStation" parameterType="com.jhdr.operation.entity.param.OpoVideoStationEditParam">
        update opo_video_station
        <trim prefix="SET" suffixOverrides=",">
                    <if test="staName != null">sta_name = #{staName},</if>
                    <if test="staCode != null">sta_code = #{staCode},</if>
                    <if test="deviceName != null">device_name = #{deviceName},</if>
                    <if test="ip != null">ip = #{ip},</if>
                    <if test="channelId != null">channel_id = #{channelId},</if>
                    <if test="staState != null">sta_state = #{staState},</if>
                    <if test="project != null">project = #{project},</if>
                    <if test="region != null">region = #{region},</if>
                    <if test="alarmTime != null">alarm_time = #{alarmTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCodeByNm">
<!--        update jhom_vm_b set camera_code =#{code} where nm like concat('%', #{name}, '%')-->
        update jhom_vm_b set camera_code =#{code} where nm = #{name}
    </update>
    <update id="updateState">
        UPDATE jhom_vm_b
        SET status = #{status}, alarm_time =#{time}
        WHERE camera_code = #{code}
    </update>
    <update id="updateUpdateTime">
        update jhom_vm_b set update_time = now() where camera_code=#{indexCode} and site_type=1
    </update>
    <update id="updateDiscernByChannel">
        update jhom_vm_b set discern =#{picture} where discern_channel=#{nChannelID}
    </update>
    <update id="updateUpdateTimeByCd">
        update jhom_vm_b set update_time = now() where cd=#{cd}
    </update>
    <update id="updateStateByCd">
        UPDATE jhom_vm_b
        SET status = #{state}, alarm_time =#{time},alarm_content=#{content}
        WHERE cd = #{cd}
    </update>
    <update id="isMon">
        update jhom_vm_b set is_mon=#{isMon} where cd=#{cd}
    </update>


    <delete id="deleteOpoVideoStationById" parameterType="Long">
        delete from opo_video_station where id = #{id}
    </delete>

    <delete id="deleteOpoVideoStationByIds" parameterType="String">
        delete from opo_video_station where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
