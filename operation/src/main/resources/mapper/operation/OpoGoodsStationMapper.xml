<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoGoodsStationMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoGoodsStationPo" id="OpoGoodsStationResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="person"    column="person"    />
        <result property="phone"    column="phone"    />
        <result property="admauth"    column="admauth"    />
        <result property="site"    column="site"    />
        <result property="content"    column="content"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectOpoGoodsStationVo">
        select id, name, code, longitude, latitude, person, phone, admauth, site, content, is_delete,path from opo_goods_station
    </sql>

    <select id="selectOpoGoodsStationList"  resultType="com.jhdr.operation.entity.vo.OpoGoodsStationVo">
        <include refid="selectOpoGoodsStationVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code like concat('%', #{code}, '%')</if>
            <if test="person != null  and person != ''"> and person = #{person}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            and is_delete=0
        </where>
    </select>

    <select id="selectOpoGoodsStationById"  resultType="com.jhdr.operation.entity.vo.OpoGoodsStationVo">
            <include refid="selectOpoGoodsStationVo"/>
            where id = #{id}
    </select>
    <select id="getGoodStations" resultType="com.jhdr.operation.entity.vo.OpoGoodsStationVo">
        <include refid="selectOpoGoodsStationVo"/>
        where is_delete=0
        <if test="code != null  and code != ''"> and code = #{code} </if>
    </select>

    <insert id="insertOpoGoodsStation" parameterType="com.jhdr.operation.entity.param.OpoGoodsStationAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_goods_station
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="name != null">name,</if>
                    <if test="code != null">code,</if>
                    <if test="longitude != null">longitude,</if>
                    <if test="latitude != null">latitude,</if>
                    <if test="person != null">person,</if>
                    <if test="phone != null">phone,</if>
                    <if test="admauth != null">admauth,</if>
                    <if test="site != null">site,</if>
                    <if test="content != null">content,</if>
                    <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="name != null">#{name},</if>
                    <if test="code != null">#{code},</if>
                    <if test="longitude != null">#{longitude},</if>
                    <if test="latitude != null">#{latitude},</if>
                    <if test="person != null">#{person},</if>
                    <if test="phone != null">#{phone},</if>
                    <if test="admauth != null">#{admauth},</if>
                    <if test="site != null">#{site},</if>
                    <if test="content != null">#{content},</if>
                    <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateOpoGoodsStation" parameterType="com.jhdr.operation.entity.param.OpoGoodsStationEditParam">
        update opo_goods_station
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">name = #{name},</if>
                    <if test="code != null">code = #{code},</if>
                    <if test="longitude != null">longitude = #{longitude},</if>
                    <if test="latitude != null">latitude = #{latitude},</if>
                    <if test="person != null">person = #{person},</if>
                    <if test="phone != null">phone = #{phone},</if>
                    <if test="admauth != null">admauth = #{admauth},</if>
                    <if test="site != null">site = #{site},</if>
                    <if test="content != null">content = #{content},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpoGoodsStationById" parameterType="Integer">
        update  opo_goods_station  set is_delete=1 where id = #{id}
    </delete>

    <delete id="deleteOpoGoodsStationByIds" parameterType="String">
        update  opo_goods_station  set is_delete=1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
