<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.operation.mapper.OpoDispatchPlanMapper">

    <resultMap type="com.jhdr.operation.entity.po.OpoDispatchPlanPo" id="OpoDispatchPlanResult">
        <result property="id"    column="id"    />
        <result property="planType"    column="plan_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="path"    column="path"    />
        <result property="addTime"    column="add_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectOpoDispatchPlanVo">
        select id, plan_type, file_name, path, add_time, is_delete from opo_dispatch_plan
    </sql>

    <select id="selectOpoDispatchPlanList"  resultType="com.jhdr.operation.entity.vo.OpoDispatchPlanVo">
        <include refid="selectOpoDispatchPlanVo"/>
        <where>
            <if test="planType != null  and planType != ''"> and plan_type = #{planType}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="addTime != null "> and add_time = #{addTime}</if>
            and is_delete=0
        </where>
        order by id desc
    </select>

    <select id="selectOpoDispatchPlanById"  resultType="com.jhdr.operation.entity.vo.OpoDispatchPlanVo">
            <include refid="selectOpoDispatchPlanVo"/>
            where id = #{id}
    </select>

    <insert id="insertOpoDispatchPlan" parameterType="com.jhdr.operation.entity.param.OpoDispatchPlanAddParam" useGeneratedKeys="true" keyProperty="id">
        insert into opo_dispatch_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="planType != null">plan_type,</if>
                    <if test="fileName != null">file_name,</if>
                    <if test="path != null">path,</if>
                    <if test="addTime != null">add_time,</if>
                    <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="planType != null">#{planType},</if>
                    <if test="fileName != null">#{fileName},</if>
                    <if test="path != null">#{path},</if>
                    <if test="addTime != null">#{addTime},</if>
                    <if test="isDelete != null">#{isDelete},</if>
        </trim>
    </insert>

    <update id="updateOpoDispatchPlan" parameterType="com.jhdr.operation.entity.param.OpoDispatchPlanEditParam">
        update opo_dispatch_plan
        <trim prefix="SET" suffixOverrides=",">
                    <if test="planType != null">plan_type = #{planType},</if>
                    <if test="fileName != null">file_name = #{fileName},</if>
                    <if test="path != null">path = #{path},</if>
                    <if test="addTime != null">add_time = #{addTime},</if>
                    <if test="isDelete != null">is_delete = #{isDelete},</if>
        </trim>
        where id = #{id}

    </update>
    <update id="updatePlan">
        update opo_dispatch_plan set is_delete=1 where id = #{id}
    </update>

    <delete id="deleteOpoDispatchPlanById" parameterType="Integer">
        delete from opo_dispatch_plan where id = #{id}
    </delete>

    <delete id="deleteOpoDispatchPlanByIds" parameterType="String">
        delete from opo_dispatch_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
