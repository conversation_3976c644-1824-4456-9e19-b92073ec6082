<?xml version="1.0" encoding="UTF-8" ?>
<dynamic-lib>
    <win64>
        <lib>ACDLL</lib>
        <lib>avnetsdk</lib>
        <lib>dhconfigsdk</lib>
        <lib>dhnetsdk</lib>
        <lib>dhplay</lib>
        <lib>fisheye</lib>
        <lib>HeatMap</lib>
        <lib>ImageAlg</lib>
        <lib>Infra</lib>
        <lib>IvsDrawer</lib>
        <lib>Json</lib>
        <lib>libcurl</lib>
        <lib>libeay32</lib>
        <lib>NetFramework</lib>
        <lib>ssleay32</lib>
        <lib>Stream</lib>
        <lib>StreamConvertor</lib>
        <lib>StreamPackage</lib>
        <lib>StreamParser</lib>
        <lib>StreamSvr</lib>
    </win64>
    <win32>
        <lib>ACDLL</lib>
        <lib>avnetsdk</lib>
        <lib>dhconfigsdk</lib>
        <lib>dhnetsdk</lib>
        <lib>dhplay</lib>
        <lib>fisheye</lib>
        <lib>ImageAlg</lib>
        <lib>IvsDrawer</lib>
        <lib>json</lib>
        <lib>libcurl</lib>
        <lib>libeay32</lib>
        <lib>NetFramework</lib>
        <lib>ssleay32</lib>
        <lib>Stream</lib>
        <lib>StreamConvertor</lib>
        <lib>StreamSvr</lib>
    </win32>
    <linux64>
        <lib>avnetsdk</lib>
        <lib>crypto</lib>
        <lib>curl</lib>
        <lib>dhnetsdk</lib>
        <lib>dhconfigsdk</lib>
        <lib>dhplay</lib>
        <lib>heatmap</lib>
        <lib>ImageAlg</lib>
        <lib>Infra</lib>
        <lib>NetFramework</lib>
        <lib>NetFramework_ssl</lib>
        <lib>Stream</lib>
        <lib>StreamConvertor</lib>
        <lib>StreamPackage</lib>
        <lib>StreamParser</lib>
        <lib>StreamSvr</lib>
        <lib>ssl</lib>
    </linux64>
    <linux32>
        <lib>avnetsdk</lib>
        <lib>curl</lib>
        <lib>dhconfigsdk</lib>
        <lib>dhnetsdk</lib>
        <lib>ImageAlg</lib>
        <lib>Infra</lib>
        <lib>NetFramework</lib>
        <lib>NetFramework_ssl</lib>
        <lib>Stream</lib>
        <lib>StreamConvertor</lib>
        <lib>StreamSvr</lib>
    </linux32>
    <mac64>
        <lib>avnetsdk</lib>
        <lib>dhnetsdk</lib>
        <lib>dhconfigsdk</lib>
        <lib>Infra</lib>
        <lib>json</lib>
        <lib>NetFramework</lib>
        <lib>Stream</lib>
        <lib>StreamSvr</lib>
        <lib>StreamConvertor</lib>
    </mac64>
    <linuxARM>
        <lib>dhnetsdk</lib>
        <lib>dhconfigsdk</lib>
        <lib>ImageAlg</lib>
    </linuxARM>
</dynamic-lib>