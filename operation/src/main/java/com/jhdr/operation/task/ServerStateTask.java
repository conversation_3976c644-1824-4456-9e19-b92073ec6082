package com.jhdr.operation.task;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.operation.entity.po.OpoAlarmRecordPo;
import com.jhdr.operation.entity.po.OpoServerStationPo;
import com.jhdr.operation.entity.vo.OpoAlarmRecordVo;
import com.jhdr.operation.entity.vo.ServerStateVo;
import com.jhdr.operation.service.IOpoServerStationService;
import com.jhdr.operation.service.OpoAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;


/**
 *服务器状态更新
 */
@Component
public class ServerStateTask {
    @Autowired
    private IOpoServerStationService opoServerStationService;

    @Autowired
    private OpoAlarmRecordService opoAlarmRecordService;

    private String runState1="正常";
    private String runState2="异常";
    private Double alarmLine=90.0;

    private String alarmType1="磁盘空间不足";
    private String alarmType2="cpu算力不足";
    private String alarmType3="内存不足";

    /**
     * 正常服务器中查找异常
     */
//    @Scheduled(cron = "0 */10 * * * ?")
    public void ServerAlarmState(){

        int amount=0;
        List<ServerStateVo> list=opoServerStationService.findServer(runState1);
        for (ServerStateVo vo:list) {
            String type="";
            Double cpUse=0.0;
            Double cpuUse=0.0;
            Double ncUse=0.0;

            cpUse = Double.valueOf(vo.getCpUse().replace("%",""));

            cpuUse = Double.valueOf(vo.getCpuUse().replace("%",""));

            ncUse = Double.valueOf(vo.getNcUse().replace("%",""));

            if (cpUse>alarmLine){
                amount=amount+1;
                type=type+"磁盘空间不足;";
            }
            if (cpuUse>alarmLine){
                amount=amount+1;
                type=type+"cpu算力不足;";
            }
            if (ncUse>alarmLine){
                amount=amount+1;
                type=type+"内存不足;";
            }
            if (amount>=1){
                SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date=new Date();
                String time=sdfh.format(date);
            int update=opoServerStationService.updateStateById(vo.getId(),runState2,type,time);
                OpoAlarmRecordPo po=new OpoAlarmRecordPo(null, UUID.randomUUID().toString(),vo.getDeviceName(),String.valueOf(vo.getId()),
                        "服务器",alarmType1,date,"未处理",time+vo.getDeviceName()+"服务器异常:"+type,0,vo.getSite(),null,null,null,null);
                opoAlarmRecordService.save(po);
            }
        }

    }



    /**
     * 异常服务器中恢复正常
     */
//    @Scheduled(cron = "0 */10 * * * ?")
    public void ServerUseState(){
        int amount=0;
        List<ServerStateVo> list=opoServerStationService.findServer(runState2);
        for (ServerStateVo vo:list) {
            String type="";
            Double cpUse = Double.valueOf(vo.getCpUse().substring(0, vo.getCpUse().length() - 1));
            Double cpuUse = Double.valueOf(vo.getCpuUse().substring(0, vo.getCpUse().length() - 1));
            Double ncUse = Double.valueOf(vo.getNcUse().substring(0, vo.getNcUse().length() - 1));
            if (cpUse>alarmLine){
                amount=amount+1;
                type+="磁盘空间不足;";
            }
            if (cpuUse>alarmLine){
                amount=amount+1;
                type+="cpu算力不足;";
            }
            if (ncUse>alarmLine){
                amount=amount+1;
                type+="内存不足;";
            }
            if (amount==0){

                String time="";
                opoServerStationService.updateStateById(vo.getId(),runState1,type,time);
                OpoAlarmRecordPo po=opoAlarmRecordService.getMaxOneByCode(vo.getDeviceName());
                if (!ObjectUtil.isEmpty(po)){
                    po.setRegainTime(new Date());
                    opoAlarmRecordService.updateById(po);
                }
            }else {
                OpoServerStationPo po=opoServerStationService.getOne(new QueryWrapper<OpoServerStationPo>().eq("id",vo.getId()));
                po.setAlarmType(type);

            }
        }
    }
}
