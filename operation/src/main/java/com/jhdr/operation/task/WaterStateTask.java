package com.jhdr.operation.task;

import cn.hutool.core.util.ObjectUtil;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.operation.entity.po.OpoAlarmMessagePo;
import com.jhdr.operation.entity.vo.OpoVideoStateVo;
import com.jhdr.operation.entity.vo.WaterStateVo;
import com.jhdr.operation.service.IOpoAlarmMessageService;
import com.jhdr.operation.service.IOpoSwStationService;
import com.jhdr.operation.service.IOpoVideoStationService;
import com.jhdr.operation.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 *水文站点状态更新
 */
@Component
public class WaterStateTask {
    @Autowired
    private IOpoSwStationService swStationService;

    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;

    private String staType1="水位站";
    private String staType2="流量站";

    /**
     * 正常水位站中查找异常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void WaterAlarmState() throws ParseException {
        String state="正常";
        List<WaterStateVo> stcds=swStationService.findCodeByState(state);

        for (WaterStateVo vo:stcds) {
            System.out.println(vo.getStcd());
            String newTime=swStationService.findNewTimeByCD(vo.getStcd());

            if (!StringUtils.isEmpty(newTime)){
                SimpleDateFormat newDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date newTime1=newDate.parse(newTime);
//            Long tm1=swStationService.findWaterStateByCode(vo.getStcd()).getTime();
                Long tm1=newTime1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2>90000000){
                    SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date=new Date();
                    String time=sdfh.format(date);
                    swStationService.updateWaterState(vo.getStcd(),"异常","数据掉线",time);
                    OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, UUID.randomUUID().toString(),vo.getStnm(),vo.getStcd(),null,null
                            ,null,"水位站",vo.getConstruction(),null,"水位站数据掉线",date,"未处理",null,0,null,null,null,null);
                    opoAlarmMessageService.save(po);
                }
            }
        }

    }

    /**
     * 异常水位站中查找正常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void WaterUseState() throws ParseException {
        String state="异常";
        List<WaterStateVo> stcds=swStationService.findCodeByState(state);
        for (WaterStateVo vo:stcds) {
            String newTime=swStationService.findNewTimeByCD(vo.getStcd());

            if (!StringUtils.isEmpty(newTime)){
                SimpleDateFormat newDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date newTime1=newDate.parse(newTime);
//            Long tm1=swStationService.findWaterStateByCode(vo.getStcd()).getTime();
                Long tm1=newTime1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2<90000000){
                    String time="";
                    int result=swStationService.updateWaterState(vo.getStcd(),"正常","",time);
                    OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCodeType(vo.getStcd(),staType1);
                    if (!ObjectUtil.isEmpty(po)){
                        po.setRegainTime(new Date());
                        po.setStep("已处理");
                        opoAlarmMessageService.updateById(po);
                    }
                }
            }

        }

    }




    /**
     * 正常流量站中查找异常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void FlowAlarmState(){
        String state="正常";
        List<WaterStateVo> stcds=swStationService.findFlowByState(state);

        for (WaterStateVo vo:stcds) {
            Date time1=swStationService.findFlowStateByCode(vo.getStcd());
            if (!ObjectUtil.isEmpty(time1)){
                Long tm1=time1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2>90000000){
                    SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date=new Date();
                    String time=sdfh.format(date);
                    swStationService.updateWaterState(vo.getStcd(),"异常","数据掉线",time);
                    String alarmCode= "GZ"+ DateUtils.dateTimeNow();
                    OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, alarmCode,vo.getStnm(),vo.getStcd(),null,null
                            ,null,"流量站",vo.getConstruction(),null,"流量站数据掉线",date,"未处理",null,0,null,null,null,null);
                    opoAlarmMessageService.save(po);
                }
            }
        }

    }



    /**
     * 异常流量站中查找正常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void FlowUseState(){
        String state="异常";
        List<WaterStateVo> stcds=swStationService.findFlowByState(state);

        for (WaterStateVo vo:stcds) {
            Date time1=swStationService.findFlowStateByCode(vo.getStcd());
            if (!ObjectUtil.isEmpty(time1)){
                Long tm1=time1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2<90000000){
                    String time="";
                    int result=swStationService.updateWaterState(vo.getStcd(),"正常","",time);
                    OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCodeType(vo.getStcd(),staType2);
                    if (!ObjectUtil.isEmpty(po)){
                        po.setRegainTime(new Date());
                        po.setStep("已处理");
                        opoAlarmMessageService.updateById(po);
                    }
                }
            }

        }

    }


}
