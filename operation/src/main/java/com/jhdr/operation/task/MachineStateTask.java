package com.jhdr.operation.task;

import cn.hutool.core.util.ObjectUtil;
import com.jhdr.operation.entity.po.OpoAlarmMessagePo;
import com.jhdr.operation.entity.vo.MachineStateVo;
import com.jhdr.operation.entity.vo.WaterStateVo;
import com.jhdr.operation.service.IOpoAlarmMessageService;
import com.jhdr.operation.service.IOpoGqStationService;
import com.jhdr.operation.service.IOpoSwStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 *工情站点状态更新
 */
@Component
public class MachineStateTask {
    @Autowired
    private IOpoGqStationService gqStationService;

    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;

    /**
     * 正常泵站中查找异常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void PumpAlarmState(){
        String state="正常";
        List<MachineStateVo> codes=gqStationService.findPumpCodeByState(state);
            for (MachineStateVo vo:codes) {
                Date dtlTime=gqStationService.findPumpDtlTime(vo.getStrCode());
                vo.setAddTime(dtlTime);
                    Long tm2=new Date().getTime()-vo.getAddTime().getTime();
                    if (tm2>86400000){
                        SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date=new Date();
                        String time=sdfh.format(date);
                        OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, UUID.randomUUID().toString(),vo.getStrName(),vo.getStrCode(),null,null
                                ,null,"泵站",null,null,"数据掉线",date,"未处理",null,0,null,null,null,null);
                        opoAlarmMessageService.save(po);
                        Integer alarmId=po.getId();
                        int pustState=gqStationService.updatePustState(vo.getStrCode(),"异常","数据掉线",time,alarmId);
                    }
            }

    }




    /**
     * 异常泵站中查找正常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void PumpUseState(){
        String state="异常";
        List<MachineStateVo> codes=gqStationService.findPumpCodeByState(state);
            for (MachineStateVo vo:codes) {
                Date dtlTime=gqStationService.findPumpDtlTime(vo.getStrCode());
                vo.setAddTime(dtlTime);
                    Long tm2=new Date().getTime()-vo.getAddTime().getTime();
                    if (tm2<86400000){
                        String time="";
                        gqStationService.updatePustState(vo.getStrCode(),"正常","",time,vo.getAlarmId());
                        OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCode(vo.getStrCode());
                        if (!ObjectUtil.isEmpty(po)){
                            po.setRegainTime(new Date());
                            po.setStep("已处理");
                            opoAlarmMessageService.updateById(po);
                        }
                    }
            }
    }




    /**
     * 正常涵闸中查找异常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void GateAlarmState(){
        String state="正常";

        List<MachineStateVo> codes=gqStationService.findGateCodeByState(state);

            for (MachineStateVo vo:codes) {
                Date dtlTime=gqStationService.findWagaDtlTime(vo.getStrCode());
                vo.setAddTime(dtlTime);
                    Long tm2=new Date().getTime()-vo.getAddTime().getTime();
                    if (tm2>86400000){
                        SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date=new Date();
                        String time=sdfh.format(date);
                        int pustState=gqStationService.updateWagaState(vo.getStrCode(),"异常","数据掉线",time);
                        OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, UUID.randomUUID().toString(),vo.getStrName(),vo.getStrCode(),null,null
                                ,null,"涵闸",null,null,"数据掉线",date,"未处理",null,0,null,null,null,null);
                        opoAlarmMessageService.save(po);
                    }

            }


    }



    /**
     * 异常涵闸中查找正常
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void GateUseState(){
        String state="异常";
        List<MachineStateVo> codes=gqStationService.findGateCodeByState(state);
            for (MachineStateVo vo:codes) {
                Date dtlTime=gqStationService.findWagaDtlTime(vo.getStrCode());
                vo.setAddTime(dtlTime);
                    Long tm2=new Date().getTime()-vo.getAddTime().getTime();
                    System.out.println(tm2);
                    if (tm2<86400000){
                        String time="";
                        int result=gqStationService.updateWagaState(vo.getStrCode(),"正常","",time);
                        OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCode(vo.getStrCode());
                        if (!ObjectUtil.isEmpty(po)){
                            po.setRegainTime(new Date());
                            po.setStep("已处理");
                            opoAlarmMessageService.updateById(po);
                        }
                    }
            }

    }

}
