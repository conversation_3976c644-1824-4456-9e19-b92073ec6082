package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.common.core.domain.R;
import com.jhdr.operation.entity.param.OpoAlarmRecordEditParam;
import com.jhdr.operation.service.OpoAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.param.OpoAlarmRecordParam;
import com.jhdr.operation.entity.vo.OpoAlarmRecordVo;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 机房告警记录
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Api(tags = "告警记录")
@RestController
@RequestMapping("/alarmRecord")
public class OpoAlarmRecordController extends BaseController
{
    @Autowired
    private OpoAlarmRecordService opoAlarmRecordService;

    /**
     * 查询机房告警记录列表
     */
    @ApiOperation(value = "查询告警记录列表",notes="operation:alarmRecord:list")
    @RequiresPermissions("operation:alarmRecord:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoAlarmRecordVo>> list(OpoAlarmRecordParam opoAlarmRecordParam)
    {
        startPage();
        List<OpoAlarmRecordVo> list = opoAlarmRecordService.selectOpoAlarmRecordList(opoAlarmRecordParam);
        return getDataTable(list);
    }

    /**
     * 导出机房告警记录列表
     */
    @ApiOperation(value = "导出告警记录列表",notes="operation:alarmRecord:export")
    @RequiresPermissions("operation:alarmRecord:export")
    @Log(title = "机房告警记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpoAlarmRecordParam opoAlarmRecord)
    {
        List<OpoAlarmRecordVo> list = opoAlarmRecordService.selectOpoAlarmRecordList(opoAlarmRecord);
        ExcelUtil<OpoAlarmRecordVo> util = new ExcelUtil<OpoAlarmRecordVo>(OpoAlarmRecordVo.class);
        util.exportExcel(response, list, "机房告警记录数据");
    }


    @ApiOperation(value = "告警类型下拉框",notes="operation:alarmRecord:list")
    @RequiresPermissions("operation:alarmRecord:list")
    @GetMapping("/findAlarmType")
    public R<List<String>> list()
    {

        List<String> list = opoAlarmRecordService.findAlarmType();
        return R.ok(list);
    }


    @ApiOperation(value = "设备类型下拉框",notes="operation:alarmRecord:device")
    @RequiresPermissions("operation:alarmRecord:device")
    @GetMapping("/findDeviceType")
    public R<List<String>> findDeviceType()
    {

        List<String> list = opoAlarmRecordService.findDeviceType();
        return R.ok(list);
    }


    /**
     * 获取告警记录详细信息
     */
    @ApiOperation(value = "获取告警记录详细信息",notes="operation:alarmRecord:query")
    @RequiresPermissions("operation:alarmRecord:query")
    @GetMapping(value = "/{id}")
    public R<OpoAlarmRecordVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(opoAlarmRecordService.selectOpoAlarmRecordById(id));
    }
//
//    /**
//     * 新增机房告警记录
//     */
//    @ApiOperation(value = "新增告警记录",notes="operation:alarmRecord:add")
//    @RequiresPermissions("operation:alarmRecord:add")
//    @Log(title = "机房告警记录", businessType = BusinessType.INSERT)
//    @PostMapping
//    public R add(@RequestBody OpoAlarmRecordAddParam opoAlarmRecord)
//    {
//        return toAjaxR(opoAlarmRecordService.insertOpoAlarmRecord(opoAlarmRecord),"新增");
//    }
//
    /**
     * 修改机房告警记录
     */
    @ApiOperation(value = "修改告警记录",notes="operation:alarmRecord:edit")
    @RequiresPermissions("operation:alarmRecord:edit")
    @Log(title = "机房告警记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody OpoAlarmRecordEditParam opoAlarmRecord)
    {
        return toAjaxR(opoAlarmRecordService.updateOpoAlarmRecord(opoAlarmRecord),"修改");
    }

    /**
     * 修改机房告警记录
     */
    @ApiOperation(value = "处理告警记录")
    @PutMapping("/solveAlarm")
    public R solveAlarm(@RequestBody OpoAlarmRecordEditParam opoAlarmRecord)
    {
        return toAjaxR(opoAlarmRecordService.solveAlarm(opoAlarmRecord),"修改");
    }
//
//    /**
//     * 删除机房告警记录
//     */
//    @ApiOperation(value = "删除告警记录",notes="operation:alarmRecord:remove")
//    @RequiresPermissions("operation:alarmRecord:remove")
//    @Log(title = "机房告警记录", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public R remove(@PathVariable Long[] ids)
//    {
//        return toAjaxR(opoAlarmRecordService.deleteOpoAlarmRecordByIds(ids),"删除");
//    }
}
