package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.operation.entity.vo.AlarmCountVo;
import com.jhdr.operation.entity.vo.AlarmRankVo;
import com.jhdr.operation.entity.vo.AlarmTypeCountVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.OpoAlarmMessagePo;
import com.jhdr.operation.entity.param.OpoAlarmMessageParam;
import com.jhdr.operation.entity.param.OpoAlarmMessageAddParam;
import com.jhdr.operation.entity.param.OpoAlarmMessageEditParam;
import com.jhdr.operation.entity.vo.OpoAlarmMessageVo;
import com.jhdr.operation.service.IOpoAlarmMessageService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 报警信息
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Api(tags = "故障记录")
@RestController
@RequestMapping("/alarmMessage")
public class OpoAlarmMessageController extends BaseController
{
    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;

    /**
     * 查询报警信息列表
     */
    @ApiOperation(value = "查询报警信息列表",notes="operation:alarmMessage:list")
    @RequiresPermissions("operation:alarmMessage:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoAlarmMessageVo>> list(OpoAlarmMessageParam opoAlarmMessageParam)
    {
        startPage();
        List<OpoAlarmMessageVo> list = opoAlarmMessageService.selectOpoAlarmMessageList(opoAlarmMessageParam);
        return getDataTable(list);
    }

    /**
     * 导出报警信息列表
     */
    @ApiOperation(value = "导出报警信息列表",notes="operation:alarmMessage:export")
    @RequiresPermissions("operation:alarmMessage:export")
    @Log(title = "报警信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpoAlarmMessageParam opoAlarmMessage)
    {
        List<OpoAlarmMessageVo> list = opoAlarmMessageService.selectOpoAlarmMessageList(opoAlarmMessage);
        ExcelUtil<OpoAlarmMessageVo> util = new ExcelUtil<OpoAlarmMessageVo>(OpoAlarmMessageVo.class);
        util.exportExcel(response, list, "报警信息数据");
    }

    @ApiOperation(value = "站点类型下拉框",notes="operation:alarmMessage:staType")
    @RequiresPermissions("operation:alarmMessage:staType")
    @GetMapping("/findStationType")
    public R<List<String>> findStationType()
    {

        List<String> list = opoAlarmMessageService.findStationType();
        return R.ok(list);
    }

    @ApiOperation(value = "设备类型下拉框",notes="operation:alarmMessage:deviceType")
    @RequiresPermissions("operation:alarmMessage:deviceType")
    @GetMapping("/findDeviceType")
    public R<List<String>> findDeviceType()
    {

        List<String> list = opoAlarmMessageService.findDeviceType();
        return R.ok(list);
    }

    @ApiOperation(value = "故障类型下拉框",notes="operation:alarmMessage:alarmType")
    @RequiresPermissions("operation:alarmMessage:alarmType")
    @GetMapping("/findAlarmType")
    public R<List<String>> findAlarmType() {

        List<String> list = opoAlarmMessageService.findAlarmType();
        return R.ok(list);
    }

    @ApiOperation(value = "承建单位下拉框",notes="operation:alarmMessage:construction")
    @RequiresPermissions("operation:alarmMessage:construction")
    @GetMapping("/findConstruction")
    public R<List<String>> findConstruction()
    {

        List<String> list = opoAlarmMessageService.findConstruction();
        return R.ok(list);
    }


    @ApiOperation(value = "故障数量统计",notes="operation:alarmMessage:alarmCount")
    @RequiresPermissions("operation:alarmMessage:alarmCount")
    @GetMapping("/findAlarmCount")
    public R<List<AlarmCountVo>> findAlarmCount(OpoAlarmMessageParam param) {

        List<AlarmCountVo> list = opoAlarmMessageService.findAlarmCount(param);
        return R.ok(list);
    }

    @ApiOperation(value = "站点故障类型统计",notes="operation:alarmMessage:typePercent")
    @RequiresPermissions("operation:alarmMessage:typePercent")
    @GetMapping("/findTypePercent")
    public R<List<AlarmTypeCountVo>> findTypePercent(OpoAlarmMessageParam param)
    {

        List<AlarmTypeCountVo> list = opoAlarmMessageService.findTypePercent(param);
        return R.ok(list);
    }





//    /**
//     * 获取报警信息详细信息
//     */
//    @ApiOperation(value = "获取报警信息详细信息",notes="operation:alarmMessage:query")
//    @RequiresPermissions("operation:alarmMessage:query")
//    @GetMapping(value = "/{id}")
//    public R<OpoAlarmMessageVo> getInfo(@PathVariable("id") Long id)
//    {
//
//        return R.ok(opoAlarmMessageService.selectOpoAlarmMessageById(id));
//    }
//
//    /**
//     * 新增报警信息
//     */
//    @ApiOperation(value = "新增报警信息",notes="operation:alarmMessage:add")
//    @RequiresPermissions("operation:alarmMessage:add")
//    @Log(title = "报警信息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public R add(@RequestBody OpoAlarmMessageAddParam opoAlarmMessage)
//    {
//        return toAjaxR(opoAlarmMessageService.insertOpoAlarmMessage(opoAlarmMessage),"新增");
//    }
//
    /**
     * 修改报警信息
     */
//    @ApiOperation(value = "修改报警信息",notes="operation:alarmMessage:edit")
//    @RequiresPermissions("operation:alarmMessage:edit")
//    @Log(title = "报警信息", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public R edit(@RequestBody OpoAlarmMessageEditParam opoAlarmMessage)
//    {
//        return toAjaxR(opoAlarmMessageService.updateOpoAlarmMessage(opoAlarmMessage),"修改");
//    }


    @ApiOperation(value = "故障处理功能")
    @PutMapping
    public R solveAlarm(@RequestBody OpoAlarmMessageEditParam opoAlarmMessage) {

        return toAjaxR(opoAlarmMessageService.solveAlarm(opoAlarmMessage),"修改");

    }
//
//    /**
//     * 删除报警信息
//     */
//    @ApiOperation(value = "删除报警信息",notes="operation:alarmMessage:remove")
//    @RequiresPermissions("operation:alarmMessage:remove")
//    @Log(title = "报警信息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public R remove(@PathVariable Long[] ids)
//    {
//        return toAjaxR(opoAlarmMessageService.deleteOpoAlarmMessageByIds(ids),"删除");
//    }


    @ApiOperation(value = "故障排序",notes="operation:alarmMessage:alarmRank")
    @GetMapping("/findAlarmRank")
    public R<List<AlarmRankVo>> findAlarmRank(OpoAlarmMessageParam param) {

        List<AlarmRankVo> list = opoAlarmMessageService.findAlarmRank(param);
        return R.ok(list);
    }


}
