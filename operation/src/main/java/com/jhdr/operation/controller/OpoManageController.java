package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.operation.entity.param.OpoStateChangeParam;
import com.jhdr.operation.entity.vo.OpoManageStateVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.param.OpoManageParam;
import com.jhdr.operation.entity.param.OpoManageAddParam;
import com.jhdr.operation.entity.vo.OpoManageVo;
import com.jhdr.operation.service.IOpoManageService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 业务管理
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Api(tags = "业务管理")
@RestController
@RequestMapping("/manage")
public class OpoManageController extends BaseController
{
    @Autowired
    private IOpoManageService opoManageService;

    /**
     * 查询业务管理列表
     */
    @ApiOperation(value = "查询业务管理列表",notes="operation:manage:list")
    @RequiresPermissions("operation:manage:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoManageVo>> list(OpoManageParam opoManageParam)
    {
        startPage();
        List<OpoManageVo> list = opoManageService.selectOpoManageList(opoManageParam);
        return getDataTable(list);
    }

    /**
     * 新增业务管理
     */
    @ApiOperation(value = "新增业务管理",notes="operation:manage:add")
    @RequiresPermissions("operation:manage:add")
    @Log(title = "业务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(OpoManageAddParam opoManage)
    {
        return toAjaxR(opoManageService.insertOpoManage(opoManage),"新增");
    }


    /**
     * 系统监控图
     */
    @ApiOperation(value = "系统监控图",notes="operation:manage:findAllState")
    @RequiresPermissions("operation:manage:findAllState")
    @GetMapping("/findAllState")
    public R<OpoManageStateVo> findAllState() {

        OpoManageStateVo vo = opoManageService.findAllState();
        return R.ok(vo);
    }


    /**
     * 业务系统状态监测
     */
    @ApiOperation(value = "业务系统状态监测",notes="operation:manage:updateState")
    @RequiresPermissions("operation:manage:updateState")
    @GetMapping("/updateState")
    public R updateState(OpoStateChangeParam param) {
        return toAjaxR(opoManageService.updateState(param),"修改");
    }



    @ApiOperation(value = "业务管理状态修改",notes="operation:irrigate:updateManage")
    @RequiresPermissions("operation:irrigate:updateManage")
    @PostMapping("/updateManage")
    public R updateWeather(String cd) {
        return toAjaxR(opoManageService.updateManage(cd),"修改");
    }





}
