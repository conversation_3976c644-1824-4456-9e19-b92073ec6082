package com.jhdr.operation.controller;



import com.jhdr.common.core.domain.R;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.service.OverviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "运维总览")
@RestController
@RequestMapping("/overview")
public class OverviewController {

    @Autowired
    private OverviewService overviewService;

    @ApiOperation(value = "站点概况",notes="operation:overview:stationCount")
    @RequiresPermissions("operation:overview:stationCount")
    @GetMapping("/findStationCount")
    public R<List<OverStationVo>> findStationCount() {

        List<OverStationVo> list = overviewService.findStationCount();
        return R.ok(list);
    }


    @ApiOperation(value = "机房概况",notes="operation:overview:roomCount")
    @RequiresPermissions("operation:overview:roomCount")
    @GetMapping("/findRoomCount")
    public R<List<OverStationVo>> findRoomCount() {

        List<OverStationVo> list = overviewService.findRoomCount();
        return R.ok(list);
    }




    @ApiOperation(value = "年度站点异常统计",notes="operation:overview:stationAlarmCount")
    @RequiresPermissions("operation:overview:stationAlarmCount")
    @GetMapping("/findStationAlarmCount")
    public R<List<OverStationAlarmVo>> findStationAlarmCount() {

        List<OverStationAlarmVo> list = overviewService.findStationAlarmCount();
        return R.ok(list);
    }





    @ApiOperation(value = "年度设备异常统计",notes="operation:overview:deviceAlarmCount")
    @RequiresPermissions("operation:overview:deviceAlarmCount")
    @GetMapping("/findDeviceAlarmCount")
    public R<List<OverStationAlarmVo>> findDeviceAlarmCount() {

        List<OverStationAlarmVo> list = overviewService.findDeviceAlarmCount();
        return R.ok(list);
    }

    @ApiOperation(value = "年度站点故障类型统计",notes="operation:overview:alarmTypeCount")
    @RequiresPermissions("operation:overview:alarmTypeCount")
    @GetMapping("/findAlarmTypeCount")
    public R<List<OverAlarmTypeVo>> findAlarmTypeCount() {

        List<OverAlarmTypeVo> list = overviewService.findAlarmTypeCount();
        return R.ok(list);
    }



    @ApiOperation(value = "年度设备Top5",notes="operation:overview:deviceAlarmTop")
    @RequiresPermissions("operation:overview:deviceAlarmTop")
    @GetMapping("/findDeviceAlarmTop")
    public R<List<OverStationAlarmVo>> findDeviceAlarmTop() {

        List<OverStationAlarmVo> list = overviewService.findDeviceAlarmTop();
        return R.ok(list);
    }


    @ApiOperation(value = "沿河泵站",notes="operation:overview:pustState")
    @RequiresPermissions("operation:overview:pustState")
    @GetMapping("/findPustState")
    public R<List<OverStationStateVo>> findPustState() {

        List<OverStationStateVo> list = overviewService.findPustState();
        return R.ok(list);
    }


//    @ApiOperation(value = "总览图终端状态",notes="operation:overview:overview")
//    @RequiresPermissions("operation:overview:overview")
//    @GetMapping("/findOverview")
//    public R<OverviewStateVo> findOverview() {
//
//        OverviewStateVo vo = overviewService.findOverview();
//        return R.ok(vo);
//    }

    @ApiOperation(value = "总览图链路状态",notes="operation:overview:wire")
    @RequiresPermissions("operation:overview:wire")
    @GetMapping("/findOverviewWire")
    public R<OverviewWireVo> findOverviewWire() {

        OverviewWireVo vo = overviewService.findOverviewWire();
        return R.ok(vo);
    }

    @ApiOperation(value = "网络拓扑图链路状态",notes="operation:overview:netWire")
    @RequiresPermissions("operation:overview:netWire")
    @GetMapping("/findNetWire")
    public R<OverviewNetWireVo> findNetWire() {
        OverviewNetWireVo vo = overviewService.findNetWire();
        return R.ok(vo);
    }



}
