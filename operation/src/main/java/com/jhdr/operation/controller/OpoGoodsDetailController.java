package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.OpoGoodsDetailPo;
import com.jhdr.operation.entity.param.OpoGoodsDetailParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailAddParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailEditParam;
import com.jhdr.operation.entity.vo.OpoGoodsDetailVo;
import com.jhdr.operation.service.IOpoGoodsDetailService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 物资详情
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Api(tags = "物资详情")
@RestController
@RequestMapping("/goodsDetail")
public class OpoGoodsDetailController extends BaseController
{
    @Autowired
    private IOpoGoodsDetailService opoGoodsDetailService;

    /**
     * 查询物资详情列表
     */
    @ApiOperation(value = "查询物资详情列表",notes="operation:goodsDetail:list")
    @RequiresPermissions("operation:goodsDetail:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoGoodsDetailVo>> list(OpoGoodsDetailParam opoGoodsDetailParam)
    {
        startPage();
        List<OpoGoodsDetailVo> list = opoGoodsDetailService.selectOpoGoodsDetailList(opoGoodsDetailParam);
        return getDataTable(list);
    }

    /**
     * 导出物资详情列表
     */
    @ApiOperation(value = "导出物资详情列表",notes="operation:goodsDetail:export")
    @RequiresPermissions("operation:goodsDetail:export")
    @Log(title = "物资详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpoGoodsDetailParam opoGoodsDetail)
    {
        List<OpoGoodsDetailVo> list = opoGoodsDetailService.selectOpoGoodsDetailList(opoGoodsDetail);
        ExcelUtil<OpoGoodsDetailVo> util = new ExcelUtil<OpoGoodsDetailVo>(OpoGoodsDetailVo.class);
        util.exportExcel(response, list, "物资详情数据");
    }

    /**
     * 获取物资详情详细信息
     */
    @ApiOperation(value = "获取物资详情详细信息",notes="operation:goodsDetail:query")
    @RequiresPermissions("operation:goodsDetail:query")
    @GetMapping(value = "/{id}")
    public R<OpoGoodsDetailVo> getInfo(@PathVariable("id") Integer id)
    {

        return R.ok(opoGoodsDetailService.selectOpoGoodsDetailById(id));
    }

    /**
     * 新增物资详情
     */
    @ApiOperation(value = "新增物资详情",notes="operation:goodsDetail:add")
    @RequiresPermissions("operation:goodsDetail:add")
    @Log(title = "物资详情", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody OpoGoodsDetailAddParam opoGoodsDetail)
    {
        return toAjaxR(opoGoodsDetailService.insertOpoGoodsDetail(opoGoodsDetail),"新增");
    }

    /**
     * 修改物资详情
     */
    @ApiOperation(value = "修改物资详情",notes="operation:goodsDetail:edit")
    @RequiresPermissions("operation:goodsDetail:edit")
    @Log(title = "物资详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody OpoGoodsDetailEditParam opoGoodsDetail)
    {
        return toAjaxR(opoGoodsDetailService.updateOpoGoodsDetail(opoGoodsDetail),"修改");
    }

    /**
     * 删除物资详情
     */
    @ApiOperation(value = "删除物资详情",notes="operation:goodsDetail:remove")
    @RequiresPermissions("operation:goodsDetail:remove")
    @Log(title = "物资详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Integer[] ids)
    {
        return toAjaxR(opoGoodsDetailService.deleteOpoGoodsDetailByIds(ids),"删除");
    }
}
