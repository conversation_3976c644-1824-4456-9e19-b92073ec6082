package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.operation.entity.param.PatrolTaskDetailAddParam;
import com.jhdr.operation.entity.vo.PatrolOneTaskVo;
import com.jhdr.operation.entity.vo.PatrolTaskResultVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.PatrolTaskPo;
import com.jhdr.operation.entity.param.PatrolTaskParam;
import com.jhdr.operation.entity.param.PatrolTaskAddParam;
import com.jhdr.operation.entity.param.PatrolTaskEditParam;
import com.jhdr.operation.entity.vo.PatrolTaskVo;
import com.jhdr.operation.service.IPatrolTaskService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 巡检任务
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Api(tags = "巡检任务")
@RestController
@RequestMapping("/patrolTask")
public class PatrolTaskController extends BaseController {
    @Autowired
    private IPatrolTaskService patrolTaskService;

    /**
     * 查询巡检任务列表
     */
    @ApiOperation(value = "查询巡检任务列表",notes="operation:patrolTask:list")
    @RequiresPermissions("operation:patrolTask:list")
    @GetMapping("/list")
    public TableDataInfo<List<PatrolTaskVo>> list(PatrolTaskParam patrolTaskParam)
    {
        startPage();
        List<PatrolTaskVo> list = patrolTaskService.selectPatrolTaskList(patrolTaskParam);
        return getDataTable(list);
    }

    /**
     * 导出巡检任务列表
     */
    @ApiOperation(value = "导出巡检任务列表",notes="operation:patrolTask:export")
    @RequiresPermissions("operation:patrolTask:export")
    @Log(title = "巡检任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolTaskParam patrolTask)
    {
        List<PatrolTaskVo> list = patrolTaskService.selectPatrolTaskList(patrolTask);
        ExcelUtil<PatrolTaskVo> util = new ExcelUtil<PatrolTaskVo>(PatrolTaskVo.class);
        util.exportExcel(response, list, "巡检任务数据");
    }

    /**
     * 获取巡检任务详细信息
     */
    @ApiOperation(value = "获取巡检任务详细信息",notes="operation:patrolTask:query")
    @RequiresPermissions("operation:patrolTask:query")
    @GetMapping(value = "/{id}")
    public R<PatrolOneTaskVo> getInfo(@PathVariable("id") Integer id)
    {

        return R.ok(patrolTaskService.selectPatrolTaskById(id));
    }

//    /**
//     * 新增巡检任务
//     */
//    @ApiOperation(value = "新增巡检任务",notes="operation:patrolTask:add")
//    @RequiresPermissions("operation:patrolTask:add")
//    @Log(title = "巡检任务", businessType = BusinessType.INSERT)
//    @PostMapping
//    public R add(@RequestBody PatrolTaskAddParam patrolTask)
//    {
//        return toAjaxR(patrolTaskService.insertPatrolTask(patrolTask),"新增");
//    }

//    /**
//     * 修改巡检任务
//     */
//    @ApiOperation(value = "修改巡检任务",notes="operation:patrolTask:edit")
//    @RequiresPermissions("operation:patrolTask:edit")
//    @Log(title = "巡检任务", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public R edit(@RequestBody PatrolTaskEditParam patrolTask)
//    {
//        return toAjaxR(patrolTaskService.updatePatrolTask(patrolTask),"修改");
//    }

//    /**
//     * 删除巡检任务
//     */
//    @ApiOperation(value = "删除巡检任务",notes="operation:patrolTask:remove")
//    @RequiresPermissions("operation:patrolTask:remove")
//    @Log(title = "巡检任务", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public R remove(@PathVariable Long[] ids)
//    {
//        return toAjaxR(patrolTaskService.deletePatrolTaskByIds(ids),"删除");
//    }



    /**
     * 今日自动巡检结果
     */
    @ApiOperation(value = "今日自动巡检结果",notes="operation:patrolTask:taskResult")
    @RequiresPermissions("operation:patrolTask:taskResult")
    @GetMapping(value = "/taskResult")
    public R<PatrolTaskResultVo> getTaskResult()
    {

        return R.ok(patrolTaskService.getTaskResult());
    }


    /**
     * 自定义巡检
     */

    @ApiOperation(value = "自定义巡检",notes="operation:patrolTask:addPatrolTask")
    @RequiresPermissions("operation:patrolTask:addPatrolTask")
    @PostMapping("/addPatrolTask")
    public R addPatrolTask(@RequestBody PatrolTaskDetailAddParam param) throws Exception {
        patrolTaskService.addPatrolTask(param);
        return R.ok();
    }

//    @ApiOperation(value = "自定义巡检后端测试")
//    @PostMapping("/addPatrolTaskHD")
//    public R addPatrolTaskHD(PatrolTaskDetailAddParam param)
//    {
//        return toAjaxR(patrolTaskService.addPatrolTask(param),"新增");
//    }

    /**
     * 巡检进度条
     */
    @ApiOperation(value = "巡检进度条",notes="operation:patrolTask:findProgress")
    @GetMapping(value = "/findProgress")
    public R<String> findProgress()
    {
        return R.ok(patrolTaskService.findProgress());
    }


    /**
     * 巡检类型下拉框
     */
    @ApiOperation(value = "巡检类型下拉框",notes="operation:patrolTask:patrolType")
    @RequiresPermissions("operation:patrolTask:patrolType")
    @GetMapping(value = "/findPatrolType")
    public R<List<String>> findPatrolType()
    {

        return R.ok(patrolTaskService.findPatrolType());
    }

    /**
     * 巡检对象下拉框
     */
    @ApiOperation(value = "巡检对象下拉框",notes="operation:patrolTask:patrolType")
    @RequiresPermissions("operation:patrolTask:patrolType")
    @GetMapping(value = "/findPatrolScope")
    public R<List<String>> findPatrolScope()
    {

        return R.ok(patrolTaskService.findPatrolScope());
    }


}
