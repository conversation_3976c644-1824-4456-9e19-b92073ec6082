package com.jhdr.operation.controller;

import java.util.HashMap;
import java.util.List;
import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.service.IOpoAlarmMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.OpoVideoStationPo;
import com.jhdr.operation.service.IOpoVideoStationService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 运维监控-视频站点
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "运维监控-视频站点")
@RestController
@RequestMapping("/videoStation")
public class OpoVideoStationController extends BaseController {
    @Autowired
    private IOpoVideoStationService opoVideoStationService;

    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;

    /**
     * 查询运维监控-视频站点列表
     */
    @ApiOperation(value = "查询运维监控-视频站点列表", notes = "operation:videoStation:list")
    @RequiresPermissions("operation:videoStation:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoVideoStationVo>> list(OpoVideoStationParam opoVideoStationParam) {
        startPage();
        List<OpoVideoStationVo> list = opoVideoStationService.selectOpoVideoStationList(opoVideoStationParam);
        return getDataTable(list);
    }
    @ApiOperation(value = "获取视频正常异常数量")
    @GetMapping("/getVideoStateAmount")
    public R<StateCountVo> getVideoStateAmount(String addvcd) {

        return R.ok(opoVideoStationService.getVideoStateAmount(addvcd));
    }
    /**
     * 导出运维监控-视频站点列表
     */
    @ApiOperation(value = "导出运维监控-视频站点列表", notes = "operation:videoStation:export")
    @RequiresPermissions("operation:videoStation:export")
    @Log(title = "运维监控-视频站点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpoVideoStationParam opoVideoStation) {
        List<OpoVideoStationVo> list = opoVideoStationService.selectOpoVideoStationList(opoVideoStation);
        ExcelUtil<OpoVideoStationVo> util = new ExcelUtil<OpoVideoStationVo>(OpoVideoStationVo.class);
        util.exportExcel(response, list, "运维监控-视频站点数据");
    }


    /**
     * 获取运维监控-视频站点详细信息
     */
    @ApiOperation(value = "获取运维监控-视频站点详细信息",notes="operation:videoStation:query")
    @RequiresPermissions("operation:videoStation:query")
    @GetMapping(value = "/{cd}")
    public R<OpoVideoStationVo> getInfo(@PathVariable("cd") String cd)
    {

        return R.ok(opoVideoStationService.selectOpoVideoStationById(cd));
    }


    /**
     * 获取状态下拉框
     */
    @ApiOperation(value = "获取状态下拉框", notes = "operation:videoStation:status")
    @RequiresPermissions("operation:videoStation:status")
    @GetMapping(value = "/getStatus")
    public R<List<String>> getStatus() {

        return R.ok(opoVideoStationService.getStatus());
    }


    @ApiOperation(value = "测试编码", notes = "operation:videoStation:shiniuYanhe")
    @GetMapping(value = "/getBianma")
    public R<Object> getBianma() throws Exception {
        return R.ok(opoVideoStationService.getBianma());

    }

    @ApiOperation(value = "测试视频状态", notes = "operation:videoStation:videoState")
    @GetMapping(value = "/testVideoState")
    public R<Object> testVideoState() throws Exception {
        return R.ok(opoVideoStationService.testVideoState());

    }


    @ApiOperation(value = "根据编码获取视频流", notes = "operation:videoStation:getVideoStreamByCode")
    @RequiresPermissions("operation:videoStation:getVideoStreamByCode")
    @GetMapping(value = "/getVideoStreamByCode")
    public R<Object> getVideoStreamByCode(String cameraCode) throws Exception {

        return R.ok(opoVideoStationService.getVideoStreamByCode(cameraCode));
    }

    /**
     * 查询告警信息列表
     */
    @ApiOperation(value = "查询告警信息列表",notes="operation:waterLineStation:getVideoAlarm")
    @RequiresPermissions("operation:alarmMessage:getVideoAlarm")
    @GetMapping("/getVideoAlarm")
    public TableDataInfo<List<StrAlarmMessageVo>> getVideoAlarm(StrAlarmMessageParam param)
    {
        startPage();
        param.setStaType("监控");
        List<StrAlarmMessageVo> list = opoAlarmMessageService.selectStaAlarmMessage(param);
        return getDataTable(list);
    }


    @ApiOperation(value = "获取视频区域层级")
    @GetMapping("/region")
    public R<List<VideoRegionVo>> regionList()
    {
        List<VideoRegionVo> list = opoAlarmMessageService.selectRegionTreeList();
        return R.ok(list);
    }


    @ApiOperation(value = "获取大华视频状态")
    @GetMapping(value = "/getDHState")
    public R<Object> getDHState()  {
        return R.ok(opoVideoStationService.getDHState());

    }


//    @ApiOperation(value = "获取视频区域层级")
//    @GetMapping("/newRegion")
//    public R<List<VideoNewRegionVo>> regionList()
//    {
//        List<VideoNewRegionVo> list = opoAlarmMessageService.selectRegionTreeList();
//        return R.ok(list);
//    }

    /**
     * 拆除按钮
     */
    @ApiOperation(value = "拆除按钮")
    @PutMapping("/isMon")
    public R isMon(@RequestBody IsMonVideoParam param) {
        opoVideoStationService.isMon(param);
        return R.ok();
    }





}