package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.RemoteChatDetailPo;
import com.jhdr.operation.entity.param.RemoteChatDetailParam;
import com.jhdr.operation.entity.param.RemoteChatDetailAddParam;
import com.jhdr.operation.entity.param.RemoteChatDetailEditParam;
import com.jhdr.operation.entity.vo.RemoteChatDetailVo;
import com.jhdr.operation.service.IRemoteChatDetailService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 异地会商详情
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Api(tags = "异地会商详情")
@RestController
@RequestMapping("/remoteChatDetail")
public class RemoteChatDetailController extends BaseController
{
    @Autowired
    private IRemoteChatDetailService remoteChatDetailService;

    /**
     * 查询异地会商详情列表
     */
    @ApiOperation(value = "查询异地会商详情列表",notes="operation:remoteChatDetail:list")
    @RequiresPermissions("operation:remoteChatDetail:list")
    @GetMapping("/list")
    public TableDataInfo<List<RemoteChatDetailVo>> list(RemoteChatDetailParam remoteChatDetailParam)
    {
        startPage();
        List<RemoteChatDetailVo> list = remoteChatDetailService.selectRemoteChatDetailList(remoteChatDetailParam);
        return getDataTable(list);
    }

    /**
     * 导出异地会商详情列表
     */
    @ApiOperation(value = "导出异地会商详情列表",notes="operation:remoteChatDetail:export")
    @RequiresPermissions("operation:remoteChatDetail:export")
    @Log(title = "异地会商详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RemoteChatDetailParam remoteChatDetail)
    {
        List<RemoteChatDetailVo> list = remoteChatDetailService.selectRemoteChatDetailList(remoteChatDetail);
        ExcelUtil<RemoteChatDetailVo> util = new ExcelUtil<RemoteChatDetailVo>(RemoteChatDetailVo.class);
        util.exportExcel(response, list, "异地会商详情数据");
    }

    /**
     * 获取异地会商详情详细信息
     */
    @ApiOperation(value = "获取异地会商详情详细信息",notes="operation:remoteChatDetail:query")
    @RequiresPermissions("operation:remoteChatDetail:query")
    @GetMapping(value = "/{id}")
    public R<RemoteChatDetailVo> getInfo(@PathVariable("id") Integer id)
    {

        return R.ok(remoteChatDetailService.selectRemoteChatDetailById(id));
    }

    /**
     * 新增异地会商详情
     */
    @ApiOperation(value = "新增异地会商详情",notes="operation:remoteChatDetail:add")
    @RequiresPermissions("operation:remoteChatDetail:add")
    @Log(title = "异地会商详情", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody RemoteChatDetailAddParam remoteChatDetail)
    {
        return toAjaxR(remoteChatDetailService.insertRemoteChatDetail(remoteChatDetail),"新增");
    }

    /**
     * 修改异地会商详情
     */
    @ApiOperation(value = "修改异地会商详情",notes="operation:remoteChatDetail:edit")
    @RequiresPermissions("operation:remoteChatDetail:edit")
    @Log(title = "异地会商详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody RemoteChatDetailEditParam remoteChatDetail)
    {
        return toAjaxR(remoteChatDetailService.updateRemoteChatDetail(remoteChatDetail),"修改");
    }

    /**
     * 删除异地会商详情
     */
    @ApiOperation(value = "删除异地会商详情",notes="operation:remoteChatDetail:remove")
    @RequiresPermissions("operation:remoteChatDetail:remove")
    @Log(title = "异地会商详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Integer[] ids)
    {
        return toAjaxR(remoteChatDetailService.deleteRemoteChatDetailByIds(ids),"删除");
    }
}
