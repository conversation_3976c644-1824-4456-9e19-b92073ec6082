package com.jhdr.operation.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.nacos.common.packagescan.resource.ResourceLoader;
import com.baidu.aip.speech.AipSpeech;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.vo.OpoNetsafeStationVo;
import com.jhdr.operation.entity.vo.OverviewNetWireVo;
import com.jhdr.operation.entity.vo.WaterRegionVo;
//import com.jhdr.operation.utils.FfmpegTest;
import com.jhdr.operation.utils.voice.Demo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

@RestController
@Api(tags = "语音模块")
@Slf4j
@RequestMapping("/voice")
public class VoiceController {

//    private static final  String serverURL = "http://vop.baidu.com/server_api";
//    private static String token = "";
//    private static final String testFileName ="D:\\voiceExchange\\test.pcm";
//    public static final String cuid = "KAliwovyTi69hvzPG4lC10Luj5skedp5";
//    public static final String apiKey = "Z8SEeCofD1bVX7C24rb40T15";
//    public static final String secretKey = "hdE6Thkiu2TXvAh5GFztPpxU7Tys7Vz3";

//    /**
//     * 语音文件格式转换
//     */
//    @ApiOperation(value = "语音文件格式转换",notes="operation:voice:exchange")
//    @RequiresPermissions("operation:voice:exchange")
//    @GetMapping(value = "/exchange")
//    public R exchangeVoice(String name) {
//        String sPath = "D:\\voiceExchange\\"+name;
//        String tPath = "D:\\voiceExchange\\test.pcm";
//        try {
//            new FfmpegTest().changeAmrToMp3(sPath, tPath);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return R.ok();
//    }


//    public void changeAmrToMp3(String sourcePath, String targetPath) throws Exception {
//        String webroot = "D:\\ffmpeg\\ffmpeg\\bin";//ffmpeg工具bin文件路径
//        Runtime run = null;
//        try {
//            run = Runtime.getRuntime();
//            long start = System.currentTimeMillis();
//            System.out.println(new File(webroot).getAbsolutePath());
//            //执行ffmpeg.exe,前面是ffmpeg.exe的地址，中间是需要转换的文件地址，后面是转换后的文件地址。-i是转换方式，意思是可编码解码，mp3编码方式采用的是libmp3lame
//            //wav转pcm
//            Process p=run.exec(new File(webroot).getAbsolutePath()+"/ffmpeg -y -i "+sourcePath+" -acodec pcm_s16le -f s16le -ac 1 -ar 16000 "+targetPath);
//
//
//            // 将字节数据编码回Base64
//            //mp3转pcm
////            Process p = run.exec(new File(webroot).getAbsolutePath() + "/ffmpeg -y -i " + sourcePath + " -acodec pcm_s16le -f s16le -ac 1 -ar 16000 " + targetPath);
//            //释放进程
//            p.getOutputStream().close();
//            p.getInputStream().close();
//            p.getErrorStream().close();
//            p.waitFor();
//
//
//            long end = System.currentTimeMillis();
//            System.out.println(sourcePath + " convert success, costs:" + (end - start) + "ms");
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            //run调用lame解码器最后释放内存
//            run.freeMemory();
//        }
//    }

//
//    /**
//     * 语音文件转换成文字
//     */
//    @ApiOperation(value = "语音文件转换成文字",notes="operation:voice:word")
//    @RequiresPermissions("operation:voice:word")
//    @GetMapping(value = "/word")
//    public R findVoiceWord() throws Exception {
//        String getTokenURL = "https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials" +
//                "&client_id=" + apiKey + "&client_secret=" + secretKey;
//        HttpURLConnection conn = (HttpURLConnection) new URL(getTokenURL).openConnection();
//        token = new JSONObject(printResponse(conn)).getStr("access_token");
//
//        // 初始化一个AipSpeech  cuid  apiKey  secretKey
//        AipSpeech client = new AipSpeech(cuid, apiKey, secretKey);
//
//        // 可选：设置网络连接参数
//        client.setConnectionTimeoutInMillis(2000);
//        client.setSocketTimeoutInMillis(60000);
//
//        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
//        //client.setHttpProxy("proxy_host", proxy_port);  // 设置http代理
//        //client.setSocketProxy("proxy_host", proxy_port);  // 设置socket代理
//
//        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
//        // 也可以直接通过jvm启动参数设置此环境变量
//        //System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");
//
//        // 调用接口
//        org.json.JSONObject res = client.asr(testFileName, "pcm", 16000, null);
//        com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(res.toString(2));
//        String result = jsonObject1.getString("result");
//        if(result.length() >= 4) {
//            result = result.substring(2, result.length() - 2);
//            System.out.println(result);
//        } else {
//            System.out.println("字符串长度不足，无法去除前后两个字符");
//        }
//
////        System.out.println(res.toString(2));
//        return R.ok(result);
//    }
//
//    private static String printResponse(HttpURLConnection conn) throws Exception {
//        if (conn.getResponseCode() != 200) {
//            // request error
//            return "";
//        }
//        InputStream is = conn.getInputStream();
//        BufferedReader rd = new BufferedReader(new InputStreamReader(is));
//        String line;
//        StringBuffer response = new StringBuffer();
//        while ((line = rd.readLine()) != null) {
//            response.append(line);
//            response.append('\r');
//        }
//        rd.close();
////        System.out.println(new JSONObject(response.toString()).toString());
//        return response.toString();
//    }





    private BufferedOutputStream bufferedOutputStream = null;
    /**
     * form-data 类型
     * form-data 类型即常用的表单提交
     * 两种处理参数的方式
     * <p>
     * MultipartFile 类接受前台传过来的文件
     * part 接收字节流
     */
    @PostMapping("/uploadFile")
    @ApiOperation(value = "文件上传",notes="operation:voice:word")
    public R<String> uploadFile( @RequestPart("file1") MultipartFile file1) throws IOException, ServletException {


        // 获取项目部署路径
       /* String appPath = request.getServletContext().getRealPath("/");
        // 构建上传文件的目录路径
        String path = appPath + "static/upload/";*/

        //绝对路劲
        String path = "/home/<USER>/uploadPath/voice/";
        //相对路径
//        String path = "src/main/resources/static/";

        //前端传递多个file（只对file2进行处理）
//        for (MultipartFile multipartFile : file2) {
////            使用 MultipartFile 字节流保存文件
//            fileUtil(multipartFile, String.valueOf(path));
//        }
        fileUtil(file1, path);
        String fileName=file1.getOriginalFilename();
        System.out.println(fileName);

        return R.ok(fileName);
    }

//    /**
//     * part 接收字节流
//     */
//    @PostMapping("/uploadFileByRequest")
//    public String uploadFileByRequest(HttpServletRequest request) throws IOException, ServletException {
//        // 获取项目部署路径
//       /* String appPath = request.getServletContext().getRealPath("/");
//        // 构建上传文件的目录路径
//        String path = appPath + "static/upload/";*/
//
//        //绝对路劲
//        String path = "D:\\voiceExchange\\";
//
//
////		使用 Part 接收文件字节流
////        Part file1 = request.getPart("file1");
////        file1.write(path + file1.getSubmittedFileName());
//
//        // request.getParts() 获取的是全部参数（name,age,file1,file2），包括文件参数和非文件参数
//        for (Part part : request.getParts()) {
//            // 获取文件类型
//            String contentType = part.getContentType();
//            // 获取文件大小
//            long size = part.getSize();
//            // 获取文件名
//            String submittedFileName = part.getSubmittedFileName();
//
//            if (part.getContentType() != null) {
//                //如果是文件会进行写入
//                part.write(path + part.getSubmittedFileName());
//            } else {
//                // 如果是参数会获取参数（根据实际需求对参数进行处理）
//                // 获取参数名
//                String name1 = part.getName();
//            }
//        }
//        return "success";
//    }


    public  String  fileUtil(MultipartFile file, String path) {

        if (!file.isEmpty()) {
            try {
                byte[] bytes = file.getBytes();
                bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(
                        new File(path + file.getOriginalFilename())));
                bufferedOutputStream.write(bytes);
                bufferedOutputStream.close();
                return file.getOriginalFilename() + "上传成功";
            } catch (Exception e) {
                return file.getOriginalFilename() + "上传失败，错误信息为：" + e;
            }
        } else {
            return "上传得文件为空";
        }
    }


    @ApiOperation(value = "获取语音文件内容")
    @GetMapping(value = "/getResult")
    public R<String> getResult(String fileName) throws InterruptedException {
        String result = Demo.getResult(fileName);
        return R.ok(result);
    }





}