package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.operation.entity.vo.OpoUser;
import com.jhdr.operation.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.OpoDispatchTaskPo;
import com.jhdr.operation.entity.param.OpoDispatchTaskParam;
import com.jhdr.operation.entity.param.OpoDispatchTaskAddParam;
import com.jhdr.operation.entity.param.OpoDispatchTaskEditParam;
import com.jhdr.operation.entity.vo.OpoDispatchTaskVo;
import com.jhdr.operation.service.IOpoDispatchTaskService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 工程调度
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Api(tags = "工程调度")
@RestController
@RequestMapping("/dispatchTask")
public class OpoDispatchTaskController extends BaseController
{
    @Autowired
    private IOpoDispatchTaskService opoDispatchTaskService;

    /**
     * 查询工程调度列表
     */
    @ApiOperation(value = "查询工程调度列表",notes="operation:dispatchTask:list")
    @RequiresPermissions("operation:dispatchTask:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoDispatchTaskVo>> list(OpoDispatchTaskParam opoDispatchTaskParam)
    {
        startPage();
        List<OpoDispatchTaskVo> list = opoDispatchTaskService.selectOpoDispatchTaskList(opoDispatchTaskParam);
        return getDataTable(list);
    }

//    /**
//     * 导出工程调度列表
//     */
//    @ApiOperation(value = "导出工程调度列表",notes="operation:dispatchTask:export")
//    @RequiresPermissions("operation:dispatchTask:export")
//    @Log(title = "工程调度", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, OpoDispatchTaskParam opoDispatchTask)
//    {
//        List<OpoDispatchTaskVo> list = opoDispatchTaskService.selectOpoDispatchTaskList(opoDispatchTask);
//        ExcelUtil<OpoDispatchTaskVo> util = new ExcelUtil<OpoDispatchTaskVo>(OpoDispatchTaskVo.class);
//        util.exportExcel(response, list, "工程调度数据");
//    }

    /**
     * 获取工程调度详细信息
     */
    @ApiOperation(value = "获取工程调度详细信息",notes="operation:dispatchTask:query")
    @RequiresPermissions("operation:dispatchTask:query")
    @GetMapping(value = "/{id}")
    public R<OpoDispatchTaskVo> getInfo(@PathVariable("id") Integer id)
    {

        return R.ok(opoDispatchTaskService.selectOpoDispatchTaskById(id));
    }

    /**
     * 新增工程调度
     */
    @ApiOperation(value = "新增工程调度",notes="operation:dispatchTask:add")
    @RequiresPermissions("operation:dispatchTask:add")
    @Log(title = "工程调度", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody OpoDispatchTaskAddParam opoDispatchTask)
    {
        return toAjaxR(opoDispatchTaskService.insertOpoDispatchTask(opoDispatchTask),"新增");
    }

    /**
     * 修改工程调度
     */
    @ApiOperation(value = "修改工程调度",notes="operation:dispatchTask:edit")
    @RequiresPermissions("operation:dispatchTask:edit")
    @Log(title = "工程调度", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody OpoDispatchTaskEditParam opoDispatchTask)
    {
        return toAjaxR(opoDispatchTaskService.updateOpoDispatchTask(opoDispatchTask),"修改");
    }

    /**
     * 修改工程调度状态
     */
    @ApiOperation(value = "修改工程调度状态",notes="operation:dispatchTask:updateStatus")
    @RequiresPermissions("operation:dispatchTask:updateStatus")
    @PutMapping("/updateStatus")
    public R updateStatus(@RequestBody OpoDispatchTaskEditParam opoDispatchTask)
    {
        return toAjaxR(opoDispatchTaskService.updateStatus(opoDispatchTask),"修改");
    }

    /**
     * 删除工程调度
     */
    @ApiOperation(value = "删除工程调度",notes="operation:dispatchTask:remove")
    @RequiresPermissions("operation:dispatchTask:remove")
    @Log(title = "工程调度", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public R remove(@PathVariable Integer id)
    {
        return toAjaxR(opoDispatchTaskService.deleteOpoTaskById(id),"删除");
    }


    @Autowired
    private CommonService commonService;
    /**
     * 用户下拉框
     */
    @ApiOperation(value = "用户下拉框",notes="operation:dispatchTask:users")
    @RequiresPermissions("operation:dispatchTask:users")
    @GetMapping(value = "/getUsers")
    public R<List<OpoUser>> getUsers()
    {

        return R.ok(commonService.getUsers());
    }

    /**
     * 获取当前用户信息
     */
    @ApiOperation(value = "获取当前用户信息",notes="operation:dispatchTask:getLoginUsers")
    @RequiresPermissions("operation:dispatchTask:getLoginUsers")
    @GetMapping(value = "/getLoginUsers")
    public R<OpoUser> getLoginUsers()
    {
        return R.ok(commonService.getLoginUsers());
    }
}
