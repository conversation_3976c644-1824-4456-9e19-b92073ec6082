package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.operation.entity.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.OpoMachineRoomPo;
import com.jhdr.operation.entity.param.OpoMachineRoomParam;
import com.jhdr.operation.entity.param.OpoMachineRoomAddParam;
import com.jhdr.operation.entity.param.OpoMachineRoomEditParam;
import com.jhdr.operation.service.IOpoMachineRoomService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 机房
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "机房")
@RestController
@RequestMapping("/machineRoom")
public class OpoMachineRoomController extends BaseController
{
    @Autowired
    private IOpoMachineRoomService opoMachineRoomService;



    /**
     * 设备统计数据
     */
    @ApiOperation(value = "设备统计数据",notes="operation:machineRoom:query")
    @RequiresPermissions("operation:machineRoom:query")
    @GetMapping(value = "/findMachineAnalyse")
    public R<MachineAnalyseVo> findMachineAnalyse()
    {

        return R.ok(opoMachineRoomService.findMachineAnalyse());
    }

    /**
     * 机房数据展示
     */
    @ApiOperation(value = "机房数据展示",notes="operation:machineRoom:query")
    @RequiresPermissions("operation:machineRoom:query")
    @GetMapping(value = "/findMachineDeviceCount")
    public R<MachineDeviceCountVo> findMachineDeviceCount()
    {

        return R.ok(opoMachineRoomService.findMachineDeviceCount());
    }

    /**
     * 机房数据展示
     */
    @ApiOperation(value = "机房设备状态数量",notes="operation:machineRoom:query")
    @RequiresPermissions("operation:machineRoom:query")
    @GetMapping(value = "/findMachineStateCountVo")
    public R<MachineStateCountVo> findMachineStateCountVo()
    {

        return R.ok(opoMachineRoomService.findMachineStateCountVo());
    }

    /**
     * 机房数据展示
     */
    @ApiOperation(value = "机房设备状态数量",notes="operation:machineRoom:query")
    @RequiresPermissions("operation:machineRoom:query")
    @GetMapping(value = "/findMachineAlarm")
    public R<MachineAlarmVo> findMachineAlarm()
    {

        return R.ok(opoMachineRoomService.findMachineAlarm());
    }

    /**
     * 修改机房
     */
    @ApiOperation(value = "修改机房",notes="operation:machineRoom:edit")
    @RequiresPermissions("operation:machineRoom:edit")
    @Log(title = "机房", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody OpoMachineRoomEditParam opoMachineRoom)
    {
        return toAjaxR(opoMachineRoomService.updateOpoMachineRoom(opoMachineRoom),"修改");
    }

    /**
     * 实时监测数据
     */
    @ApiOperation(value = "实时监测数据",notes="operation:machineRoom:value")
    @RequiresPermissions("operation:machineRoom:value")
    @GetMapping(value = "/findMachineValue")
    public R<List<OpoRoomValueVo>> findMachineValue()
    {

        return R.ok(opoMachineRoomService.findMachineValue());
    }


    /**
     * 机柜数据监测
     */
    @ApiOperation(value = "机柜数据监测",notes="operation:machineRoom:cabinetValue")
    @RequiresPermissions("operation:machineRoom:cabinetValue")
    @GetMapping(value = "/findCabinetValue")
    public R<OpoCabinetVo> findCabinetValue(String name)
    {

        return R.ok(opoMachineRoomService.findCabinetValue(name));
    }


    /**
     * 机柜名称
     */
    @ApiOperation(value = "机柜名称",notes="operation:machineRoom:cabinetName")
    @RequiresPermissions("operation:machineRoom:cabinetName")
    @GetMapping(value = "/findCabinetName")
    public R<List<String>> findCabinetName()
    {

        return R.ok(opoMachineRoomService.findCabinetName());
    }

}
