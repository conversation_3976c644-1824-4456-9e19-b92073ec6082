package com.jhdr.operation.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;


import com.jhdr.operation.entity.vo.PatrolOnePlanVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.param.PatrolPlanParam;
import com.jhdr.operation.entity.param.PatrolPlanAddParam;
import com.jhdr.operation.entity.param.PatrolPlanEditParam;
import com.jhdr.operation.entity.vo.PatrolPlanVo;
import com.jhdr.operation.service.IPatrolPlanService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 巡检计划
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Api(tags = "巡检计划")
@RestController
@RequestMapping("/patrolPlan")
public class PatrolPlanController extends BaseController
{
    @Autowired
    private IPatrolPlanService patrolPlanService;

    /**
     * 查询巡检计划列表
     */
    @ApiOperation(value = "查询巡检计划列表",notes="operation:plan:list")
    @RequiresPermissions("operation:plan:list")
    @GetMapping("/list")
    public TableDataInfo<List<PatrolPlanVo>> list(PatrolPlanParam patrolPlanParam)
    {
        startPage();
        List<PatrolPlanVo> list = patrolPlanService.selectPatrolPlanList(patrolPlanParam);
        return getDataTable(list);
    }

    /**
     * 导出巡检计划列表
     */
    @ApiOperation(value = "导出巡检计划列表",notes="operation:plan:export")
    @RequiresPermissions("operation:plan:export")
    @Log(title = "巡检计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PatrolPlanParam patrolPlan)
    {
        List<PatrolPlanVo> list = patrolPlanService.selectPatrolPlanList(patrolPlan);
        ExcelUtil<PatrolPlanVo> util = new ExcelUtil<PatrolPlanVo>(PatrolPlanVo.class);
        util.exportExcel(response, list, "巡检计划数据");
    }

    /**
     * 获取巡检计划详细信息
     */
    @ApiOperation(value = "获取巡检计划详细信息",notes="operation:plan:query")
    @RequiresPermissions("operation:plan:query")
    @GetMapping(value = "/{id}")
    public R<PatrolOnePlanVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(patrolPlanService.selectPatrolPlanById(id));
    }

    /**
     * 新增巡检计划
     */
    @ApiOperation(value = "新增巡检计划",notes="operation:plan:add")
    @RequiresPermissions("operation:plan:add")
    @Log(title = "巡检计划", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody PatrolPlanAddParam patrolPlan)
    {
        return toAjaxR(patrolPlanService.insertPatrolPlan(patrolPlan),"新增");
    }

    /**
     * 修改巡检计划
     */
    @ApiOperation(value = "修改巡检计划",notes="operation:plan:edit")
    @RequiresPermissions("operation:plan:edit")
    @Log(title = "巡检计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody PatrolPlanEditParam patrolPlan)
    {
        return toAjaxR(patrolPlanService.updatePatrolPlan(patrolPlan),"修改");
    }

    /**
     * 删除巡检计划
     */
    @ApiOperation(value = "删除巡检计划",notes="operation:plan:remove")
    @RequiresPermissions("operation:plan:remove")
    @Log(title = "巡检计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(patrolPlanService.deletePatrolPlanByIds(ids),"删除");
    }






}
