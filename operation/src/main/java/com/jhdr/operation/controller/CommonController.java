package com.jhdr.operation.controller;



import com.jhdr.common.core.domain.R;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.vo.OverviewNetWireVo;
import com.jhdr.operation.entity.vo.VoiceResultVo;
import com.jhdr.operation.service.CommonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Api(tags = "公共请求接口数据")
@RestController
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private CommonService commonService;


    @ApiOperation(value = "语音结果拆分",notes="operation:common:voiceResult")
    @RequiresPermissions("operation:overview:voiceResult")
    @GetMapping("/findVoiceResult")
    public R<VoiceResultVo> findVoiceResult(String result) {
        VoiceResultVo vo = commonService.findVoiceResult(result);
        return R.ok(vo);
    }


    @ApiOperation(value = "语音获取水位")
    @GetMapping(value = "/getWaterLine")
    public R<String> getWaterLine(String name)  {
        String waterLine=commonService.getWaterLineByName(name);
        return R.ok(waterLine);
    }




}
