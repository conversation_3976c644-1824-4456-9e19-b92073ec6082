package com.jhdr.operation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.po.OpoNetworkStationPo;
import com.jhdr.operation.entity.vo.OpoAlarmRecordVo;
import com.jhdr.operation.entity.vo.StateCountVo;
import com.jhdr.operation.service.OpoAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.operation.entity.po.OpoNetsafeStationPo;
import com.jhdr.operation.entity.vo.OpoNetsafeStationVo;
import com.jhdr.operation.service.IOpoNetsafeStationService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 运维监控-网络安全设备
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Api(tags = "运维监控-网络安全设备")
@RestController
@RequestMapping("/netsafeStation")
public class OpoNetsafeStationController extends BaseController
{
    @Autowired
    private IOpoNetsafeStationService opoNetsafeStationService;

    @Autowired
    private OpoAlarmRecordService opoAlarmRecordService;

    /**
     * 查询运维监控-网络安全设备列表
     */
    @ApiOperation(value = "查询运维监控-网络安全设备列表",notes="operation:netsafeStation:list")
    @RequiresPermissions("operation:netsafeStation:list")
    @GetMapping("/list")
    public TableDataInfo<List<OpoNetsafeStationVo>> list(OpoNetsafeStationParam opoNetsafeStationParam)
    {
        startPage();
        List<OpoNetsafeStationVo> list = opoNetsafeStationService.selectOpoNetsafeStationList(opoNetsafeStationParam);
        return getDataTable(list);
    }
    @ApiOperation(value = "获取安全设备正常异常数量")
    @GetMapping("/getSafeStateAmount")
    public R<StateCountVo> getSafeStateAmount() {

        return R.ok(opoNetsafeStationService.getSafeStateAmount());
    }
    /**
     * 导出运维监控-网络安全设备列表
     */
    @ApiOperation(value = "导出运维监控-网络安全设备列表",notes="operation:netsafeStation:export")
    @RequiresPermissions("operation:netsafeStation:export")
    @Log(title = "运维监控-网络安全设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpoNetsafeStationParam opoNetsafeStation)
    {
        List<OpoNetsafeStationVo> list = opoNetsafeStationService.selectOpoNetsafeStationList(opoNetsafeStation);
        ExcelUtil<OpoNetsafeStationVo> util = new ExcelUtil<OpoNetsafeStationVo>(OpoNetsafeStationVo.class);
        util.exportExcel(response, list, "运维监控-网络安全设备数据");
    }

    /**
     * 获取运维监控-网络安全设备详细信息
     */
    @ApiOperation(value = "获取运维监控-网络安全设备详细信息",notes="operation:netsafeStation:query")
    @RequiresPermissions("operation:netsafeStation:query")
    @GetMapping(value = "/{id}")
    public R<OpoNetsafeStationVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(opoNetsafeStationService.selectOpoNetsafeStationById(id));
    }

    /**
     * 新增运维监控-网络安全设备
     */
    @ApiOperation(value = "新增运维监控-网络安全设备",notes="operation:netsafeStation:add")
    @RequiresPermissions("operation:netsafeStation:add")
    @Log(title = "运维监控-网络安全设备", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody OpoNetsafeStationAddParam opoNetsafeStation)
    {
        return toAjaxR(opoNetsafeStationService.insertOpoNetsafeStation(opoNetsafeStation),"新增");
    }

    /**
     * 新增运维监控-网络安全设备
     */
    @ApiOperation(value = "新增运维监控-网络安全设备后端添加",notes="operation:netsafeStation:addTest")
    @RequiresPermissions("operation:netsafeStation:addTest")
    @PostMapping("/addTest")
    public R addTest(OpoNetsafeStationAddParam opoNetsafeStation)
    {
        return toAjaxR(opoNetsafeStationService.insertOpoNetsafeStation(opoNetsafeStation),"新增");
    }

    /**
     * 修改运维监控-网络安全设备
     */
    @ApiOperation(value = "修改运维监控-网络安全设备",notes="operation:netsafeStation:edit")
    @RequiresPermissions("operation:netsafeStation:edit")
    @Log(title = "运维监控-网络安全设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody OpoNetsafeStationEditParam opoNetsafeStation)
    {
        return toAjaxR(opoNetsafeStationService.updateOpoNetsafeStation(opoNetsafeStation),"修改");
    }

    /**
     * 删除运维监控-网络安全设备
     */
    @ApiOperation(value = "删除运维监控-网络安全设备",notes="operation:netsafeStation:remove")
    @RequiresPermissions("operation:netsafeStation:remove")
    @Log(title = "运维监控-网络安全设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public R remove(@PathVariable Integer id)
    {
        return toAjaxR(opoNetsafeStationService.removeById(id),"删除");
    }






    /**
     * 查询网络设备告警记录
     */
    @ApiOperation(value = "查询网络安全设备告警记录",notes="operation:alarmRecord:network")
    @RequiresPermissions("operation:alarmRecord:network")
    @GetMapping("/findNetworkAlarm")
    public TableDataInfo<List<OpoAlarmRecordVo>> findNetworkAlarm(StrAlarmRecordParam param)
    {
        startPage();
        param.setDeviceType("网络安全设备");
        List<OpoAlarmRecordVo> list = opoAlarmRecordService.findStrAlarmRecord(param);
        return getDataTable(list);
    }


    /**
     * 拆除按钮
     */
    @ApiOperation(value = "网络安全设备拆除按钮")
    @PutMapping("/isMon")
    public R isMon(@RequestBody IsMonParam param) {
        OpoNetsafeStationPo po=opoNetsafeStationService.getOne(new QueryWrapper<OpoNetsafeStationPo>().eq("id", param.getId()));
        po.setIsMon(param.getIsMon());
        opoNetsafeStationService.update(po, new QueryWrapper<OpoNetsafeStationPo>().eq("id", po.getId()));
        return R.ok();
    }







}

