package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.operation.entity.po.RemoteChatDetailPo;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.mapper.RemoteChatDetailMapper;
import com.jhdr.system.api.model.LoginUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.RemoteChatPo;
import com.jhdr.operation.entity.param.RemoteChatParam;
import com.jhdr.operation.entity.param.RemoteChatAddParam;
import com.jhdr.operation.entity.param.RemoteChatEditParam;
import com.jhdr.operation.mapper.RemoteChatMapper;
import com.jhdr.operation.service.IRemoteChatService;

import javax.xml.crypto.Data;
import java.util.*;

/**
 * 异地会商Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Service
public class RemoteChatServiceImpl extends ServiceImpl<RemoteChatMapper, RemoteChatPo> implements IRemoteChatService {

    @Override
    public List<RemoteChatVo> queryList(RemoteChatPo remoteChat) {
        LambdaQueryWrapper<RemoteChatPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(remoteChat.getTitle())){
            lqw.like(RemoteChatPo::getTitle ,remoteChat.getTitle());
        }
        if (remoteChat.getStatus() != null){
            lqw.eq(RemoteChatPo::getStatus ,remoteChat.getStatus());
        }
        List<RemoteChatVo> remoteChatVos= BeanUtil.copyToList(this.list(lqw), RemoteChatVo.class);
        return remoteChatVos;
    }
    /**
     * 查询异地会商
     *
     * @param id 异地会商主键
     * @return 异地会商
     */
    @Override
    public RemoteChatVo selectRemoteChatById(Integer id)
    {
        RemoteChatVo vo=baseMapper.selectRemoteChatById(id);
        List<RemoteFileVo> files=baseMapper.selectFiles(vo.getId());
        List<RemoteChatDetailVo> joins=baseMapper.selectJoinPersons(vo.getId());
        List<RemoteChatDetailVo> details=baseMapper.selectDetails(vo.getId());
        List<RemoteChatDetailVo> results=baseMapper.selectResults(vo.getId());
        if (!CollectionUtils.isEmpty(files)){
            vo.setFiles(files);
        }
        if (!CollectionUtils.isEmpty(joins)){
            vo.setJoinPerson(joins);
        }
        if (!CollectionUtils.isEmpty(details)){
            vo.setChatDetails(details);
        }
        if (!CollectionUtils.isEmpty(results)){
            vo.setResults(results);
        }

        return vo;
    }

    /**
     * 查询异地会商列表
     *
     * @param remoteChat 异地会商
     * @return 异地会商
     */
    @Override
    public List<RemoteChatVo> selectRemoteChatList(RemoteChatParam remoteChat)
    {
        List<RemoteChatVo> list= baseMapper.selectRemoteChatList(remoteChat);
        for (RemoteChatVo vo:list){
            List<RemoteChatDetailVo> join=baseMapper.selectJoinPersons(vo.getId());
            vo.setJoinPerson(join);

        }
        return list;
    }

    /**
     * 新增异地会商
     *
     * @param param 异地会商
     * @return 结果
     */
    @Autowired
    private RemoteChatDetailMapper remoteChatDetailMapper;

    @Override
    public int insertRemoteChat(RemoteChatAddParam param)
    {

        RemoteChatPo remoteChat=new RemoteChatPo();
        BeanUtil.copyProperties(param,remoteChat);
        Date data=new Date();
        remoteChat.setCreatTime(data);

        Integer userId=  Integer.valueOf(String.valueOf(SecurityUtils.getUserId()));
        String userName=SecurityUtils.getUsername();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String deptName=loginUser.getSysUser().getDept().getDeptName();
        remoteChat.setPerson(userName);
        remoteChat.setRoleName(deptName);
        remoteChat.setPersonId(userId);

        baseMapper.insert(remoteChat);

        Integer id=remoteChat.getId();
        if (!CollectionUtils.isEmpty(param.getFiles())){
            for (RemoteFileVo file: param.getFiles()){
                RemoteChatDetailPo detailPo=new RemoteChatDetailPo();
                BeanUtil.copyProperties(file,detailPo);
                detailPo.setChatId(id);
                detailPo.setStatus(0);
                remoteChatDetailMapper.insert(detailPo);
            }
        }
        if (!CollectionUtils.isEmpty(param.getJoinPersons())){
            for (RemoteJoinPersonVo person: param.getJoinPersons()){
                RemoteChatDetailPo detailPo=new RemoteChatDetailPo();
                BeanUtil.copyProperties(person,detailPo);
                detailPo.setChatId(id);
                detailPo.setStatus(3);
                remoteChatDetailMapper.insert(detailPo);
            }
        }







        return 1;
    }

    /**
     * 修改异地会商
     *
     * @param remoteChatEditParam 异地会商
     * @return 结果
     */
    @Override
    public int updateRemoteChat(RemoteChatEditParam remoteChatEditParam)
    {
        RemoteChatPo remoteChat=new RemoteChatPo();
        BeanUtil.copyProperties(remoteChatEditParam,remoteChat);
        return baseMapper.updateById(remoteChat);
    }

    /**
     * 批量删除异地会商
     *
     * @param ids 需要删除的异地会商主键
     * @return 结果
     */
    @Override
    public int deleteRemoteChatByIds(Integer[] ids)
    {
        return baseMapper.deleteRemoteChatByIds(ids);
    }

    /**
     * 删除异地会商信息
     *
     * @param id 异地会商主键
     * @return 结果
     */
    @Override
    public int deleteRemoteChatById(Integer id)
    {
        return baseMapper.deleteRemoteChatById(id);
    }

    @Override
    public List<RemoteVideoVo> getVideoSite() {
        return baseMapper.getVideoSite();
    }
}
