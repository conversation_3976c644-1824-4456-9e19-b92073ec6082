package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.utils.BigDecimalUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoMachineRoomPo;
import com.jhdr.operation.entity.param.OpoMachineRoomParam;
import com.jhdr.operation.entity.param.OpoMachineRoomAddParam;
import com.jhdr.operation.entity.param.OpoMachineRoomEditParam;
import com.jhdr.operation.mapper.OpoMachineRoomMapper;
import com.jhdr.operation.service.IOpoMachineRoomService;

import java.util.ArrayList;

import java.util.List;

/**
 * 机房Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class OpoMachineRoomServiceImpl extends ServiceImpl<OpoMachineRoomMapper, OpoMachineRoomPo> implements IOpoMachineRoomService {

    @Autowired
    private OpoMachineRoomMapper opoMachineRoomMapper;
    @Override
    public List<OpoMachineRoomVo> queryList(OpoMachineRoomPo opoMachineRoom) {
        LambdaQueryWrapper<OpoMachineRoomPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoMachineRoom.getRoomName())){
            lqw.like(OpoMachineRoomPo::getRoomName ,opoMachineRoom.getRoomName());
        }
        if (StringUtils.isNotBlank(opoMachineRoom.getTemp())){
            lqw.eq(OpoMachineRoomPo::getTemp ,opoMachineRoom.getTemp());
        }
        if (StringUtils.isNotBlank(opoMachineRoom.getHumidity())){
            lqw.eq(OpoMachineRoomPo::getHumidity ,opoMachineRoom.getHumidity());
        }
        if (StringUtils.isNotBlank(opoMachineRoom.getAirState())){
            lqw.eq(OpoMachineRoomPo::getAirState ,opoMachineRoom.getAirState());
        }
        if (StringUtils.isNotBlank(opoMachineRoom.getSmogState())){
            lqw.eq(OpoMachineRoomPo::getSmogState ,opoMachineRoom.getSmogState());
        }
        List<OpoMachineRoomVo> opoMachineRoomVos= BeanUtil.copyToList(this.list(lqw), OpoMachineRoomVo.class);
        return opoMachineRoomVos;
    }
    /**
     * 查询机房
     *
     * @param id 机房主键
     * @return 机房
     */
    @Override
    public OpoMachineRoomVo selectOpoMachineRoomById(Long id)
    {
        return baseMapper.selectOpoMachineRoomById(id);
    }



    /**
     * 查询机房列表
     *
     * @param opoMachineRoom 机房
     * @return 机房
     */
    @Override
    public List<OpoMachineRoomVo> selectOpoMachineRoomList(OpoMachineRoomParam opoMachineRoom)
    {
        return baseMapper.selectOpoMachineRoomList(opoMachineRoom);
    }

    /**
     * 新增机房
     *
     * @param opoMachineRoomAddParam 机房
     * @return 结果
     */
    @Override
    public int insertOpoMachineRoom(OpoMachineRoomAddParam opoMachineRoomAddParam)
    {

        OpoMachineRoomPo opoMachineRoom=new OpoMachineRoomPo();
        BeanUtil.copyProperties(opoMachineRoomAddParam,opoMachineRoom);
        return baseMapper.insert(opoMachineRoom);
    }

    /**
     * 修改机房
     *
     * @param opoMachineRoomEditParam 机房
     * @return 结果
     */
    @Override
    public int updateOpoMachineRoom(OpoMachineRoomEditParam opoMachineRoomEditParam)
    {
        OpoMachineRoomPo opoMachineRoom=new OpoMachineRoomPo();
        BeanUtil.copyProperties(opoMachineRoomEditParam,opoMachineRoom);
        opoMachineRoom.setId(0);
        opoMachineRoom.setRoomName("机房");
        return baseMapper.updateById(opoMachineRoom);
    }

    /**
     * 批量删除机房
     *
     * @param ids 需要删除的机房主键
     * @return 结果
     */
    @Override
    public int deleteOpoMachineRoomByIds(Long[] ids)
    {
        return baseMapper.deleteOpoMachineRoomByIds(ids);
    }

    /**
     * 删除机房信息
     *
     * @param id 机房主键
     * @return 结果
     */
    @Override
    public int deleteOpoMachineRoomById(Long id)
    {
        return baseMapper.deleteOpoMachineRoomById(id);
    }

    @Override
    public MachineAnalyseVo findMachineAnalyse() {
        MachineAnalyseVo vo=new MachineAnalyseVo();
        Integer fwqCount =opoMachineRoomMapper.findFwqCount();
        Integer safeCount =opoMachineRoomMapper.findNetSafeCount();
        Integer netCount  =opoMachineRoomMapper.findNetworkCount();
        Integer all  =fwqCount+safeCount+netCount;

        String fwqPercent = BigDecimalUtils.reserve(Double.valueOf(fwqCount*100)/all);
        String safePercent = BigDecimalUtils.reserve(Double.valueOf(safeCount*100)/all);
        String netPercent = BigDecimalUtils.reserve(Double.valueOf(netCount*100)/all);

        vo.setFwqCount(fwqCount);
        vo.setSafeCount(safeCount);
        vo.setNetCount(netCount);
        vo.setFwqPercent(fwqPercent);
        vo.setSafePercent(safePercent);
        vo.setNetPercent(netPercent);
        return vo;
    }

    @Override
    public MachineDeviceCountVo findMachineDeviceCount() {

        MachineDeviceCountVo vo=new MachineDeviceCountVo();
        Integer netCount  =opoMachineRoomMapper.findNetworkCount();
        vo.setDeviceCount(netCount);
        vo.setJgCount(14);
        vo.setUCount(14*42);
        return vo;
    }

    @Override
    public MachineStateCountVo findMachineStateCountVo() {
        String state1="正常";
        String state2="异常";
        Integer useCount  =opoMachineRoomMapper.findCountStateByState(state1);
        Integer alarmCount  =opoMachineRoomMapper.findCountStateByState(state2);
        MachineStateCountVo vo=new MachineStateCountVo();
        vo.setUseCount(useCount);
        vo.setAlarmCount(alarmCount);
        return vo;
    }

    @Override
    public MachineAlarmVo findMachineAlarm() {
        MachineAlarmVo vo=opoMachineRoomMapper.findDeviceAlarm();

        return vo;
    }

    @Override
    public List<OpoRoomValueVo> findMachineValue() {
        List<OpoRoomValueVo> list=opoMachineRoomMapper.findMachineValue();
        return list;
    }

    @Override
    public OpoCabinetVo findCabinetValue(String name) {
        OpoCabinetVo vo=opoMachineRoomMapper.findBaseCabinet(name);
        String tempData=opoMachineRoomMapper.findMachineByCode(vo.getTemp());
        String humidityData=opoMachineRoomMapper.findMachineByCode(vo.getHumidity());
        vo.setTemp(tempData);
        vo.setHumidityData(humidityData);

        List<OpoCabinetDeviceVo> list=opoMachineRoomMapper.findCabinetDevice(name);
        if (!CollectionUtils.isEmpty(list)){
            vo.setDevices(list);
        }
        vo.setDeviceAmount(list.size());
        return vo;
    }

    @Override
    public List<String> findCabinetName() {
        return opoMachineRoomMapper.findCabinetName();
    }
}
