package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.mapper.*;
import com.jhdr.operation.service.JhPumpRService;
import com.jhdr.operation.service.JhSlcrsRService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 运维测试Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Service
public class JhSlcrsRServiceImpl extends ServiceImpl<JhSlcrsRMapper, JhSlcrsRPo> implements JhSlcrsRService {
    @Autowired
    private JhSlcrsRMapper jhSlcrsRMapper;

    @Autowired
    private JhSlcrsRealMapper jhSlcrsRealMapper;

    @Autowired
    private JhSlcrsDtlRealMapper jhSlcrsDtlRealMapper;


    @Override
    public void insertReal(JhSlcrsRPo slcrsRPo) {
        JhSlcrsRealPo po=jhSlcrsRMapper.selectRealSlcrs(slcrsRPo.getPrjnmcd());

        JhSlcrsRealPo slcrsRPo1=jhSlcrsRMapper.selectSlcrsR(slcrsRPo.getPrjnmcd(),slcrsRPo.getClltm());

        if (ObjectUtil.isEmpty(slcrsRPo1)){
            jhSlcrsRMapper.insert(slcrsRPo);
        }

        if (ObjectUtil.isEmpty(po)){
            JhSlcrsRealPo po1=new JhSlcrsRealPo();
            BeanUtil.copyProperties(slcrsRPo,po1);
            jhSlcrsRealMapper.insert(po1);
        }else {
            BeanUtil.copyProperties(slcrsRPo,po);
            jhSlcrsRealMapper.update(po,new QueryWrapper<JhSlcrsRealPo>().eq("prjnmcd",po.getPrjnmcd()));
        }
    }

    @Override
    public void insertDtlReal(JhSlcrsDtlRPo dtlRPo) {
        JhSlcrsDtlRealPo po=jhSlcrsRMapper.selectDtlRealSlcrs(dtlRPo.getGpcd(),dtlRPo.getPrjnmcd());
        if (ObjectUtil.isEmpty(po)){
            JhSlcrsDtlRealPo po1=new JhSlcrsDtlRealPo();
            BeanUtil.copyProperties(dtlRPo,po1);
            jhSlcrsDtlRealMapper.insert(po1);
        }else {
            BeanUtil.copyProperties(dtlRPo,po);
            jhSlcrsDtlRealMapper.update(po,new QueryWrapper<JhSlcrsDtlRealPo>().eq("gpcd",po.getGpcd())
                    .eq("prjnmcd",po.getPrjnmcd()));
        }
    }
}
