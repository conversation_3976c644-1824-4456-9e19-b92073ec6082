package com.jhdr.operation.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.util.StringUtil;
import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.entity.vo.FindMonthFlowVo;
import com.jhdr.operation.entity.vo.FlowAccumStcdVo;
import com.jhdr.operation.entity.vo.FlowStcdVo;
import com.jhdr.operation.mapper.CommonMapper;
import com.jhdr.operation.mapper.JhFlowycRMapper;
import com.jhdr.operation.mapper.ShareDataMapper;
import com.jhdr.operation.mapper.StStationWaterYearMapper;
import com.jhdr.operation.service.ShareDataService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

@Service
public class ShareDataServiceImpl implements ShareDataService {
    @Autowired
    private ShareDataMapper shareDataMapper;

    @Override
    public List<String> findRainStcds() {
        return shareDataMapper.findRainStcds();
    }

    @Override
    public StPptnRPo findRainDataByStcd(String stcd) {
        return shareDataMapper.findRainDataByStcd(stcd);
    }

    @Override
    public StPptnRPo findRainRealData(String stcd, Date tm) {
        return shareDataMapper.findRainRealData(stcd,tm);
    }

    @Override
    public List<String> findFlowStcds() {
        return shareDataMapper.findFlowStcds();
    }

    @Override
    public StRiverRPo findFlowDataByStcd(String stcd) {
        return shareDataMapper.findFlowDataByStcd(stcd);
    }

    @Override
    public StRiverRPo findFlowRealData(String stcd, Date tm) {
        return shareDataMapper.findFlowRealData(stcd,tm);
    }

    @Override
    public List<String> findWaterLineStcds() {
        return shareDataMapper.findWaterLineStcds();
    }

    @Override
    public List<FlowAccumStcdVo> findFlowAccumStcds() {
        return shareDataMapper.findFlowAccumStcds();
    }

    @Override
    public JhFlowspRPo findFlowAccumByStcd(String nrcode) {
        return shareDataMapper.findFlowAccumByStcd(nrcode);
    }

    @Override
    public JhFlowspRPo findFlowAccumData(String jhCode, Date tm) {
        return shareDataMapper.findFlowAccumData(jhCode,tm);
    }

    @Override
    public List<StPptnRPo> findAllRain(String stcd) {
        return shareDataMapper.findAllRain(stcd);
    }

    @Override
    public List<StPptnRPo> findShareAllRain(String stcd) {
        return shareDataMapper.findShareAllRain(stcd);


    }

    @Override
    public List<StPptnRPo> findRainsByStcd(String stcd) {
        return shareDataMapper.findRainsByStcd(stcd);
    }

    @Autowired
    private JhFlowycRMapper jhFlowycRMapper;
    private static String type1="水量";
    private static String type2="水位";
    @Override
    public void flowInsert() {
        List<FlowStcdVo> codes=shareDataMapper.findAllFlow();
        Date date = new Date();
        for (FlowStcdVo code:codes){
            String jhCode=code.getJhCode();
            Date updateTime= DateUtils.addSeconds(code.getUpdateTime(),1);
            JhFlowycRPo flow=shareDataMapper.selectFlowByCode(code.getNrcode(),updateTime);
            if (!ObjectUtil.isEmpty(flow)){
                String accumCode=shareDataMapper.findCodeByType(jhCode,type1);
                Double flowAccum=0.0;
                if (!StringUtil.isEmpty(accumCode)){
                     flowAccum=shareDataMapper.findDataByCode(accumCode);
                }
                String water=shareDataMapper.findCodeByType(jhCode,type2);
                Double waterLine=0.0;
                if (!StringUtil.isEmpty(water)){
                    waterLine=shareDataMapper.findDataByCode(water);
                }

                String speedCode=shareDataMapper.findCodeByType(jhCode,type2);
                Double speed=0.0;
                if (!StringUtil.isEmpty(speedCode)){
                    speed=shareDataMapper.findDataByCode(speedCode);
                }
                flow.setZ(waterLine);
                flow.setYcbm(jhCode);
                flow.setRecvtm(date);
                flow.setStcd(jhCode);
                flow.setFlowd(flowAccum);
                flow.setSp(speed);
                shareDataMapper.updateContrastTime(jhCode,flow.getDatatm());
                jhFlowycRMapper.insert(flow);

            }
        }

    }

    @Override
    public Double findNowFlowByCd(String nrcode) {
        return shareDataMapper.findNowFlowByCd(nrcode);
    }

    @Autowired
    private StStationWaterYearMapper stStationWaterYearMapper;
    @Override
    public void flowInsertYearMonth() {
        List<FlowStcdVo> codes=shareDataMapper.findNewAllFlow();
        ZonedDateTime currentDateTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        String year = String.valueOf(currentDateTime.getYear());
        int month = currentDateTime.getMonthValue();
        for (FlowStcdVo code:codes){
            String jhCode=code.getJhCode();
            String nrCode=code.getNrcode();
            Double monthFlow=shareDataMapper.findMonthFlowByNrCode(nrCode);
            StStationWaterYearPo po=shareDataMapper.findYearPoByTime(jhCode,year,month);
            if (!ObjectUtil.isEmpty(po)){
                StStationWaterYearPo yearPo=new StStationWaterYearPo(null,code.getStationName(),code.getJhCode(),code.getNewType(),year,
                        monthFlow,0.0,2,month);
                stStationWaterYearMapper.insert(yearPo);
            }else {
                po.setDroughtWaterExtraction(monthFlow);
                stStationWaterYearMapper.updateById(po);
            }

        }
    }

    @Override
    public void testYearMonthInsert() {
        List<FlowStcdVo> codes=shareDataMapper.findNewAllFlow();

        for (FlowStcdVo code:codes){
            String jhCode=code.getJhCode();
            String nrCode=code.getNrcode();

            List<FindMonthFlowVo> list=shareDataMapper.findAllMonthFlow(nrCode);

            for (FindMonthFlowVo vo:list){
                ZonedDateTime zonedDateTime = vo.getTm().toInstant()
                        .atZone(ZoneId.systemDefault());

                String year =String.valueOf(zonedDateTime.getYear()) ;
                int month = zonedDateTime.getMonthValue();
                StStationWaterYearPo po=shareDataMapper.findYearPoByTime(jhCode,year,month);
                Double monthFlow=vo.getFlowAccum();
                if (!ObjectUtil.isEmpty(po)){
                    StStationWaterYearPo yearPo=new StStationWaterYearPo(null,code.getStationName(),code.getJhCode(),code.getNewType(),year,
                            monthFlow,0.0,2,month);
                    stStationWaterYearMapper.insert(yearPo);
                }else {
                    po.setDroughtWaterExtraction(monthFlow);
                    stStationWaterYearMapper.updateById(po);
                }

            }







        }
    }


}
