package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.system.api.model.LoginUser;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.RemoteChatDetailPo;
import com.jhdr.operation.entity.param.RemoteChatDetailParam;
import com.jhdr.operation.entity.param.RemoteChatDetailAddParam;
import com.jhdr.operation.entity.param.RemoteChatDetailEditParam;
import com.jhdr.operation.entity.vo.RemoteChatDetailVo;
import com.jhdr.operation.mapper.RemoteChatDetailMapper;
import com.jhdr.operation.service.IRemoteChatDetailService;

import java.util.ArrayList;

import java.util.Date;
import java.util.List;

/**
 * 异地会商详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
@Service
public class RemoteChatDetailServiceImpl extends ServiceImpl<RemoteChatDetailMapper, RemoteChatDetailPo> implements IRemoteChatDetailService {

    @Override
    public List<RemoteChatDetailVo> queryList(RemoteChatDetailPo remoteChatDetail) {
        LambdaQueryWrapper<RemoteChatDetailPo> lqw = Wrappers.lambdaQuery();
        List<RemoteChatDetailVo> remoteChatDetailVos= BeanUtil.copyToList(this.list(lqw), RemoteChatDetailVo.class);
        return remoteChatDetailVos;
    }
    /**
     * 查询异地会商详情
     *
     * @param id 异地会商详情主键
     * @return 异地会商详情
     */
    @Override
    public RemoteChatDetailVo selectRemoteChatDetailById(Integer id)
    {
        return baseMapper.selectRemoteChatDetailById(id);
    }

    /**
     * 查询异地会商详情列表
     *
     * @param remoteChatDetail 异地会商详情
     * @return 异地会商详情
     */
    @Override
    public List<RemoteChatDetailVo> selectRemoteChatDetailList(RemoteChatDetailParam remoteChatDetail)
    {
        return baseMapper.selectRemoteChatDetailList(remoteChatDetail);
    }

    /**
     * 新增异地会商详情
     *
     * @param remoteChatDetailAddParam 异地会商详情
     * @return 结果
     */
    @Override
    public int insertRemoteChatDetail(RemoteChatDetailAddParam remoteChatDetailAddParam)
    {
        RemoteChatDetailPo detailPo=new RemoteChatDetailPo();

        System.out.println(remoteChatDetailAddParam);
        BeanUtil.copyProperties(remoteChatDetailAddParam,detailPo);
        detailPo.setAddTime(new Date());
        if (detailPo.getStatus().equals(1)){
            baseMapper.updateBaseStatus(detailPo.getChatId());
        }
        Integer userId=  Integer.valueOf(String.valueOf(SecurityUtils.getUserId()));
        String userName= SecurityUtils.getUsername();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        String deptName=loginUser.getSysUser().getDept().getDeptName();
        detailPo.setRoleName(deptName);
        detailPo.setPerson(userName);
        detailPo.setPersonId(userId);

        return baseMapper.insert(detailPo);
    }

    /**
     * 修改异地会商详情
     *
     * @param remoteChatDetailEditParam 异地会商详情
     * @return 结果
     */
    @Override
    public int updateRemoteChatDetail(RemoteChatDetailEditParam remoteChatDetailEditParam)
    {
        RemoteChatDetailPo remoteChatDetail=new RemoteChatDetailPo();
        BeanUtil.copyProperties(remoteChatDetailEditParam,remoteChatDetail);
        return baseMapper.updateById(remoteChatDetail);
    }

    /**
     * 批量删除异地会商详情
     *
     * @param ids 需要删除的异地会商详情主键
     * @return 结果
     */
    @Override
    public int deleteRemoteChatDetailByIds(Integer[] ids)
    {
        return baseMapper.deleteRemoteChatDetailByIds(ids);
    }

    /**
     * 删除异地会商详情信息
     *
     * @param id 异地会商详情主键
     * @return 结果
     */
    @Override
    public int deleteRemoteChatDetailById(Integer id)
    {
        return baseMapper.deleteRemoteChatDetailById(id);
    }
}
