package com.jhdr.operation.service;

import java.util.List;

import com.jhdr.operation.entity.param.StrAlarmMessageParam;
import com.jhdr.operation.entity.po.OpoAlarmMessagePo;
import com.jhdr.operation.entity.param.OpoAlarmMessageParam;
import com.jhdr.operation.entity.param.OpoAlarmMessageAddParam;
import com.jhdr.operation.entity.param.OpoAlarmMessageEditParam;
import com.jhdr.operation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 报警信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface IOpoAlarmMessageService extends IService<OpoAlarmMessagePo>
{

    /**
     * 查询报警信息列表
     *
     * @param opoAlarmMessage 报警信息
     * @return 报警信息集合
     */
    public List<OpoAlarmMessageVo> queryList(OpoAlarmMessagePo opoAlarmMessage);

    List<StrAlarmMessageVo> selectStaAlarmMessage(StrAlarmMessageParam param);

    /**
     * 查询报警信息
     *
     * @param id 报警信息主键
     * @return 报警信息
     */
    public OpoAlarmMessageVo selectOpoAlarmMessageById(Long id);

    /**
     * 查询报警信息列表
     *
     * @param opoAlarmMessage 报警信息
     * @return 报警信息集合
     */
    public List<OpoAlarmMessageVo> selectOpoAlarmMessageList(OpoAlarmMessageParam opoAlarmMessage);

    /**
     * 新增报警信息
     *
     * @param opoAlarmMessage 报警信息
     * @return 结果
     */
    public int insertOpoAlarmMessage(OpoAlarmMessageAddParam opoAlarmMessage);

    /**
     * 修改报警信息
     *
     * @param opoAlarmMessage 报警信息
     * @return 结果
     */
    public int updateOpoAlarmMessage(OpoAlarmMessageEditParam opoAlarmMessage);

    /**
     * 批量删除报警信息
     *
     * @param ids 需要删除的报警信息主键集合
     * @return 结果
     */
    public int deleteOpoAlarmMessageByIds(Long[] ids);

    /**
     * 删除报警信息信息
     *
     * @param id 报警信息主键
     * @return 结果
     */
    public int deleteOpoAlarmMessageById(Long id);

    List<String> findStationType();

    List<String> findDeviceType();

    List<String> findAlarmType();

    List<String> findConstruction();

    List<AlarmCountVo> findAlarmCount(OpoAlarmMessageParam param);

    List<AlarmTypeCountVo> findTypePercent(OpoAlarmMessageParam param);


    List<VideoRegionVo> selectRegionTreeList();


    int solveAlarm(OpoAlarmMessageEditParam opoAlarmMessage);

    OpoAlarmMessagePo getMaxOneByCode(String strCode);

    OpoAlarmMessagePo getMaxOneByCodeType(String strCode, String staType);

    List<AlarmRankVo> findAlarmRank(OpoAlarmMessageParam param);
}
