package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.RemoteChatDetailPo;
import com.jhdr.operation.entity.param.RemoteChatDetailParam;
import com.jhdr.operation.entity.param.RemoteChatDetailAddParam;
import com.jhdr.operation.entity.param.RemoteChatDetailEditParam;
import com.jhdr.operation.entity.vo.RemoteChatDetailVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 异地会商详情Service接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public interface IRemoteChatDetailService extends IService<RemoteChatDetailPo>
{

    /**
     * 查询异地会商详情列表
     *
     * @param remoteChatDetail 异地会商详情
     * @return 异地会商详情集合
     */
    public List<RemoteChatDetailVo> queryList(RemoteChatDetailPo remoteChatDetail);

    /**
     * 查询异地会商详情
     *
     * @param id 异地会商详情主键
     * @return 异地会商详情
     */
    public RemoteChatDetailVo selectRemoteChatDetailById(Integer id);

    /**
     * 查询异地会商详情列表
     *
     * @param remoteChatDetail 异地会商详情
     * @return 异地会商详情集合
     */
    public List<RemoteChatDetailVo> selectRemoteChatDetailList(RemoteChatDetailParam remoteChatDetail);

    /**
     * 新增异地会商详情
     *
     * @param remoteChatDetail 异地会商详情
     * @return 结果
     */
    public int insertRemoteChatDetail(RemoteChatDetailAddParam remoteChatDetail);

    /**
     * 修改异地会商详情
     *
     * @param remoteChatDetail 异地会商详情
     * @return 结果
     */
    public int updateRemoteChatDetail(RemoteChatDetailEditParam remoteChatDetail);

    /**
     * 批量删除异地会商详情
     *
     * @param ids 需要删除的异地会商详情主键集合
     * @return 结果
     */
    public int deleteRemoteChatDetailByIds(Integer[] ids);

    /**
     * 删除异地会商详情信息
     *
     * @param id 异地会商详情主键
     * @return 结果
     */
    public int deleteRemoteChatDetailById(Integer id);

}
