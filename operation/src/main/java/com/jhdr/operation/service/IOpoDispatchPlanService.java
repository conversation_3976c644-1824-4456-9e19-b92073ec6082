package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.OpoDispatchPlanPo;
import com.jhdr.operation.entity.param.OpoDispatchPlanParam;
import com.jhdr.operation.entity.param.OpoDispatchPlanAddParam;
import com.jhdr.operation.entity.param.OpoDispatchPlanEditParam;
import com.jhdr.operation.entity.vo.OpoDispatchPlanVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 调度预案Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IOpoDispatchPlanService extends IService<OpoDispatchPlanPo>
{

    /**
     * 查询调度预案列表
     *
     * @param opoDispatchPlan 调度预案
     * @return 调度预案集合
     */
    public List<OpoDispatchPlanVo> queryList(OpoDispatchPlanPo opoDispatchPlan);

    /**
     * 查询调度预案
     *
     * @param id 调度预案主键
     * @return 调度预案
     */
    public OpoDispatchPlanVo selectOpoDispatchPlanById(Integer id);

    /**
     * 查询调度预案列表
     *
     * @param opoDispatchPlan 调度预案
     * @return 调度预案集合
     */
    public List<OpoDispatchPlanVo> selectOpoDispatchPlanList(OpoDispatchPlanParam opoDispatchPlan);

    /**
     * 新增调度预案
     *
     * @param opoDispatchPlan 调度预案
     * @return 结果
     */
    public int insertOpoDispatchPlan(OpoDispatchPlanAddParam opoDispatchPlan);

    /**
     * 修改调度预案
     *
     * @param opoDispatchPlan 调度预案
     * @return 结果
     */
    public int updateOpoDispatchPlan(OpoDispatchPlanEditParam opoDispatchPlan);

    /**
     * 批量删除调度预案
     *
     * @param ids 需要删除的调度预案主键集合
     * @return 结果
     */
    public int deleteOpoDispatchPlanByIds(Integer[] ids);

    /**
     * 删除调度预案信息
     *
     * @param id 调度预案主键
     * @return 结果
     */
    public int deleteOpoDispatchPlanById(Integer id);

    int deleteOpoDispatchPlan(Integer id);

    List<String> getTypes();
}
