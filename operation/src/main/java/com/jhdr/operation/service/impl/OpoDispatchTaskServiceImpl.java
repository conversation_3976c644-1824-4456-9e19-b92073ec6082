package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.operation.entity.po.OpoDispatchDetailPo;
import com.jhdr.operation.mapper.OpoDispatchDetailMapper;
import net.sf.jsqlparser.expression.LongValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoDispatchTaskPo;
import com.jhdr.operation.entity.param.OpoDispatchTaskParam;
import com.jhdr.operation.entity.param.OpoDispatchTaskAddParam;
import com.jhdr.operation.entity.param.OpoDispatchTaskEditParam;
import com.jhdr.operation.entity.vo.OpoDispatchTaskVo;
import com.jhdr.operation.mapper.OpoDispatchTaskMapper;
import com.jhdr.operation.service.IOpoDispatchTaskService;

import java.util.ArrayList;

import java.util.Date;
import java.util.List;

/**
 * 工程调度Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class OpoDispatchTaskServiceImpl extends ServiceImpl<OpoDispatchTaskMapper, OpoDispatchTaskPo> implements IOpoDispatchTaskService {

    @Override
    public List<OpoDispatchTaskVo> queryList(OpoDispatchTaskPo opoDispatchTask) {
        LambdaQueryWrapper<OpoDispatchTaskPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoDispatchTask.getFileName())){
            lqw.like(OpoDispatchTaskPo::getFileName ,opoDispatchTask.getFileName());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPath())){
            lqw.eq(OpoDispatchTaskPo::getPath ,opoDispatchTask.getPath());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson1Id())){
            lqw.eq(OpoDispatchTaskPo::getPerson1Id ,opoDispatchTask.getPerson1Id());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson1())){
            lqw.eq(OpoDispatchTaskPo::getPerson1 ,opoDispatchTask.getPerson1());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson2Id())){
            lqw.eq(OpoDispatchTaskPo::getPerson2Id ,opoDispatchTask.getPerson2Id());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson2())){
            lqw.eq(OpoDispatchTaskPo::getPerson2 ,opoDispatchTask.getPerson2());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson3Id())){
            lqw.eq(OpoDispatchTaskPo::getPerson3Id ,opoDispatchTask.getPerson3Id());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson3())){
            lqw.eq(OpoDispatchTaskPo::getPerson3 ,opoDispatchTask.getPerson3());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson4Id())){
            lqw.eq(OpoDispatchTaskPo::getPerson4Id ,opoDispatchTask.getPerson4Id());
        }
        if (StringUtils.isNotBlank(opoDispatchTask.getPerson4())){
            lqw.eq(OpoDispatchTaskPo::getPerson4 ,opoDispatchTask.getPerson4());
        }
        if (opoDispatchTask.getAddTime() != null){
            lqw.eq(OpoDispatchTaskPo::getAddTime ,opoDispatchTask.getAddTime());
        }
        if (opoDispatchTask.getIsDelete() != null){
            lqw.eq(OpoDispatchTaskPo::getIsDelete ,opoDispatchTask.getIsDelete());
        }
        List<OpoDispatchTaskVo> opoDispatchTaskVos= BeanUtil.copyToList(this.list(lqw), OpoDispatchTaskVo.class);
        return opoDispatchTaskVos;
    }
    /**
     * 查询工程调度
     *
     * @param id 工程调度主键
     * @return 工程调度
     */
    @Override
    public OpoDispatchTaskVo selectOpoDispatchTaskById(Integer id)
    {
        return baseMapper.selectOpoDispatchTaskById(id);
    }

    /**
     * 查询工程调度列表
     *
     * @param param 工程调度
     * @return 工程调度
     */
    @Override
    public List<OpoDispatchTaskVo> selectOpoDispatchTaskList(OpoDispatchTaskParam param)
    {
        Long userId= SecurityUtils.getUserId();
        param.setPersonId(String.valueOf(userId));

        if (param.getPersonId().equals("1")){

            List<OpoDispatchTaskVo> list=baseMapper.selectAllTaskList(param);
            return list;
        }else {

            List<OpoDispatchTaskVo> list=baseMapper.selectOpoDispatchTaskList(param);
            return list;
        }

    }

    /**
     * 新增工程调度
     *
     * @param opoDispatchTaskAddParam 工程调度
     * @return 结果
     */
    @Override
    public int insertOpoDispatchTask(OpoDispatchTaskAddParam opoDispatchTaskAddParam)
    {
        Long userId= SecurityUtils.getUserId();
        String userName=SecurityUtils.getUsername();

        OpoDispatchTaskPo opoDispatchTask=new OpoDispatchTaskPo();
        Date date=new Date();
        BeanUtil.copyProperties(opoDispatchTaskAddParam,opoDispatchTask);
        opoDispatchTask.setIsDelete(0);
        opoDispatchTask.setPerson1Id(String.valueOf(userId));
        opoDispatchTask.setPerson1(userName);
        opoDispatchTask.setAddTime(date);

        return baseMapper.insert(opoDispatchTask);
    }

    /**
     * 修改工程调度
     *
     * @param opoDispatchTaskEditParam 工程调度
     * @return 结果
     */
    @Override
    public int updateOpoDispatchTask(OpoDispatchTaskEditParam opoDispatchTaskEditParam)
    {
        OpoDispatchTaskPo opoDispatchTask=new OpoDispatchTaskPo();
        BeanUtil.copyProperties(opoDispatchTaskEditParam,opoDispatchTask);
        return baseMapper.updateById(opoDispatchTask);
    }

    /**
     * 批量删除工程调度
     *
     * @param ids 需要删除的工程调度主键
     * @return 结果
     */
    @Override
    public int deleteOpoDispatchTaskByIds(Integer[] ids)
    {
        return baseMapper.deleteOpoDispatchTaskByIds(ids);
    }

    /**
     * 删除工程调度信息
     *
     * @param id 工程调度主键
     * @return 结果
     */
    @Override
    public int deleteOpoDispatchTaskById(Integer id)
    {
        return baseMapper.deleteOpoDispatchTaskById(id);
    }

    @Override
    public int deleteOpoTaskById(Integer id) {
        return baseMapper.updateOpoTaskById(id);
    }

    @Autowired
    private OpoDispatchDetailMapper opoDispatchDetailMapper;

    @Override
    public int updateStatus(OpoDispatchTaskEditParam opoDispatchTask) {


        OpoDispatchTaskPo taskPo=baseMapper.selectOne(new QueryWrapper<OpoDispatchTaskPo>().eq("id", opoDispatchTask.getId()));
        taskPo.setStatus(opoDispatchTask.getStatus());
        Long userId= SecurityUtils.getUserId();
        String userName=SecurityUtils.getUsername();
        Date date=new Date();
        OpoDispatchDetailPo detailPo=new OpoDispatchDetailPo(null,String.valueOf(userId),userName,date,0,opoDispatchTask.getStatus(),taskPo.getId());
        opoDispatchDetailMapper.insert(detailPo);
        return  baseMapper.updateById(taskPo);
    }
}
