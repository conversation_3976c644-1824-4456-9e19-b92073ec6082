package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.vo.*;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoGqStationPo;
import com.jhdr.operation.mapper.OpoGqStationMapper;
import com.jhdr.operation.service.IOpoGqStationService;

import java.util.ArrayList;

import java.util.Date;
import java.util.List;

/**
 * 运维监控-工情站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class OpoGqStationServiceImpl extends ServiceImpl<OpoGqStationMapper, OpoGqStationPo> implements IOpoGqStationService {


    /**
     * 查询运维监控-工情站点
     *
     * @param id 运维监控-工情站点主键
     * @return 运维监控-工情站点
     */
    @Override
    public OpoGqStationVo selectOpoGqStationById(Long id)
    {
        return baseMapper.selectOpoGqStationById(id);
    }

    /**
     * 查询运维监控-工情站点列表
     *
     * @param param 运维监控-工情站点
     * @return 运维监控-工情站点
     */
    @Override
    public List<OpoPumpStationVo> selectOpoGqStationList(OpoGqStationParam param) {
        List<OpoPumpStationVo> list=baseMapper.selectPumpStation(param);

        return list;
    }
    private  static  String status1="正常";
    private  static  String status2="异常";
    @Override
    public StateCountVo getPustStateAmount(String addvcd) {
        Integer useAmount=baseMapper.getPustStateAmount(addvcd,status1);
        Integer alarmAmount=baseMapper.getPustStateAmount(addvcd,status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }

    @Override
    public StateCountVo getWagaStateAmount(String addvcd) {
        Integer useAmount=baseMapper.getWagaStateAmount(addvcd,status1);
        Integer alarmAmount=baseMapper.getWagaStateAmount(addvcd,status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }

    @Override
    public Date findPumpDtlTime(String strCode) {
        return baseMapper.findPumpDtlTime(strCode);
    }

    @Override
    public Date findWagaDtlTime(String strCode) {
        return baseMapper.findWagaDtlTime(strCode);
    }

    @Override
    public void isMon(IsMachineParam param) {
        baseMapper.isMon(param.getStrCode(),param.getOpoMon());
    }

    @Override
    public void isWagaMon(IsMachineWagaParam param) {
        baseMapper.isWagaMon(param.getStrCode(),param.getStatus());
    }

    @Override
    public List<OpoGateStationVo> selectGateStationList(OpoGqStationParam param) {
        List<OpoGateStationVo> list=baseMapper.selectGateStationList(param);
//        for (OpoGateStationVo vo:list) {
//            List<OpoGateStationVo> details=baseMapper.selectGateDetail(vo.getStrCode());
//            for (OpoGateStationVo detail:details) {
//                if (detail.getGtoph()>0.00){
//                    detail.setGtopn("开启");
//                }else {
//                    detail.setGtopn("关闭");
//                }
//            }
//            vo.setChildren(details);
//        }
        return list;
    }

    @Override
    public List<String> getPumpName(String name) {
        return baseMapper.getPumpName(name);
    }

    @Override
    public List<String> getGateName(String name) {
        return baseMapper.getGateName(name);
    }

    @Override
    public List<WaterRegionVo> pustRegion() {
        List<WaterRegionVo> regionTreeList= baseMapper.pustRegion();

        List<WaterRegionVo> menusList = new ArrayList<>();

        for (WaterRegionVo vo : regionTreeList) {
            //设置一级菜单
            if (vo.getAddvcd().equals("0")){
                menusList.add(vo);
            }
        }

        //设置一级菜单
//        StRiverRegionDataVo menus = new StRiverRegionDataVo();
//        menus.setAddvcd("1").setStnm("安徽茨淮新河").setStcd("0");
//        menusList.add(menus);

        //设置一级菜单的子集菜单
        for (WaterRegionVo menusVo : menusList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }
        return menusList;
    }

    @Override
    public List<WaterRegionVo> wagaRegion() {
        List<WaterRegionVo> regionTreeList= baseMapper.wagaRegion();

        List<WaterRegionVo> menusList = new ArrayList<>();

        for (WaterRegionVo vo : regionTreeList) {
            //设置一级菜单
            if (vo.getAddvcd().equals("0")){
                menusList.add(vo);
            }
        }

        //设置一级菜单
//        StRiverRegionDataVo menus = new StRiverRegionDataVo();
//        menus.setAddvcd("1").setStnm("安徽茨淮新河").setStcd("0");
//        menusList.add(menus);

        //设置一级菜单的子集菜单
        for (WaterRegionVo menusVo : menusList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }
        return menusList;
    }



    private List<WaterRegionVo> getChildren(String id, List<WaterRegionVo> regionTreeList) {
        //定义集合
        List<WaterRegionVo> childList = new ArrayList<>();
        //遍历子集菜单
        for (WaterRegionVo menu : regionTreeList) {
            //父级id与子集id比较
            if (menu.getAddvcd().equals(id)){
                childList.add(menu);
            }
        }
        //循环子集菜单
        for (WaterRegionVo menusVo : childList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }

        //递归条件退出
        if (childList.size() ==0){
            return null;
        }
        return childList;
    }

    /**
     * 新增运维监控-工情站点
     *
     * @param opoGqStationAddParam 运维监控-工情站点
     * @return 结果
     */
    @Override
    public int insertOpoGqStation(OpoGqStationAddParam opoGqStationAddParam)
    {

        OpoGqStationPo opoGqStation=new OpoGqStationPo();
        BeanUtil.copyProperties(opoGqStationAddParam,opoGqStation);
        return baseMapper.insert(opoGqStation);
    }

    /**
     * 修改运维监控-工情站点
     *
     * @param opoGqStationEditParam 运维监控-工情站点
     * @return 结果
     */
    @Override
    public int updateOpoGqStation(OpoGqStationEditParam opoGqStationEditParam)
    {
        OpoGqStationPo opoGqStation=new OpoGqStationPo();
        BeanUtil.copyProperties(opoGqStationEditParam,opoGqStation);
        return baseMapper.updateById(opoGqStation);
    }

    /**
     * 批量删除运维监控-工情站点
     *
     * @param ids 需要删除的运维监控-工情站点主键
     * @return 结果
     */
    @Override
    public int deleteOpoGqStationByIds(Long[] ids)
    {
        return baseMapper.deleteOpoGqStationByIds(ids);
    }

    /**
     * 删除运维监控-工情站点信息
     *
     * @param id 运维监控-工情站点主键
     * @return 结果
     */
    @Override
    public int deleteOpoGqStationById(Long id)
    {
        return baseMapper.deleteOpoGqStationById(id);
    }




    @Override
    public List<MachineStateVo> findPumpCodeByState(String state) {
        return baseMapper.findPumpCodeByState(state);
    }

    @Override
    public List<MachineStateVo> findTimeByCode(String code) {
        return baseMapper.findTimeByCode(code);
    }

    @Override
    public int updatePumpState(String strCode, String deviceCode, String state, String alarmType, String time) {
        int result=baseMapper.updatePumpState(strCode,deviceCode,state,alarmType,time);
        return result;
    }

    @Override
    public int updatePustState(String strCode, String state, String alarmType, String time, Integer alarmId) {
        int pust=baseMapper.updatePustState(strCode,state,alarmType,time,alarmId);
        return pust;
    }

    @Override
    public int findPumpUseCount(String code) {
        int count=baseMapper.findPumpUseCount(code);
        return count;
    }

    @Override
    public List<MachineStateVo> findGateCodeByState(String state) {
        return baseMapper.findGateCodeByState(state);
    }

    @Override
    public List<MachineStateVo> findGateTimeByCode(String code) {
        return baseMapper.findGateTimeByCode(code);
    }

    @Override
    public int updateGateState(String strCode, String state, String alarmType, String time) {
        int result=baseMapper.updateGateState(strCode,state,alarmType,time);
        return result;
    }

    @Override
    public int updateWagaState(String strCode, String state, String alarmType, String time) {
        int waga=baseMapper.updateWagaState(strCode,state,alarmType,time);
        return waga;
    }

    @Override
    public int findGateUseCount(String code) {
        int count=baseMapper.findGateUseCount(code);
        return count;
    }



}
