package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.OpoMachineRoomDevicePo;
import com.jhdr.operation.entity.param.OpoMachineRoomDeviceParam;
import com.jhdr.operation.entity.param.OpoMachineRoomDeviceAddParam;
import com.jhdr.operation.entity.param.OpoMachineRoomDeviceEditParam;
import com.jhdr.operation.entity.vo.OpoMachineRoomDeviceVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 运维机房设备信息apiService接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface IOpoMachineRoomDeviceService extends IService<OpoMachineRoomDevicePo>
{

    /**
     * 查询运维机房设备信息api列表
     *
     * @param opoMachineRoomDevice 运维机房设备信息api
     * @return 运维机房设备信息api集合
     */
    public List<OpoMachineRoomDeviceVo> queryList(OpoMachineRoomDevicePo opoMachineRoomDevice);

    /**
     * 查询运维机房设备信息api
     *
     * @param id 运维机房设备信息api主键
     * @return 运维机房设备信息api
     */
    public OpoMachineRoomDeviceVo selectOpoMachineRoomDeviceById(Long id);

    /**
     * 查询运维机房设备信息api列表
     *
     * @param opoMachineRoomDevice 运维机房设备信息api
     * @return 运维机房设备信息api集合
     */
    public List<OpoMachineRoomDeviceVo> selectOpoMachineRoomDeviceList(OpoMachineRoomDeviceParam opoMachineRoomDevice);

    /**
     * 新增运维机房设备信息api
     *
     * @param opoMachineRoomDevice 运维机房设备信息api
     * @return 结果
     */
    public int insertOpoMachineRoomDevice(OpoMachineRoomDeviceAddParam opoMachineRoomDevice);

    /**
     * 修改运维机房设备信息api
     *
     * @param opoMachineRoomDevice 运维机房设备信息api
     * @return 结果
     */
    public int updateOpoMachineRoomDevice(OpoMachineRoomDeviceEditParam opoMachineRoomDevice);

    /**
     * 批量删除运维机房设备信息api
     *
     * @param ids 需要删除的运维机房设备信息api主键集合
     * @return 结果
     */
    public int deleteOpoMachineRoomDeviceByIds(Long[] ids);

    /**
     * 删除运维机房设备信息api信息
     *
     * @param id 运维机房设备信息api主键
     * @return 结果
     */
    public int deleteOpoMachineRoomDeviceById(Long id);

}
