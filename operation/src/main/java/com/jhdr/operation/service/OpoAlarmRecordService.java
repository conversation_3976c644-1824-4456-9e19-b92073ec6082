package com.jhdr.operation.service;

import java.util.List;

import com.jhdr.operation.entity.param.StrAlarmRecordParam;
import com.jhdr.operation.entity.po.OpoAlarmRecordPo;
import com.jhdr.operation.entity.param.OpoAlarmRecordParam;
import com.jhdr.operation.entity.param.OpoAlarmRecordAddParam;
import com.jhdr.operation.entity.param.OpoAlarmRecordEditParam;
import com.jhdr.operation.entity.vo.OpoAlarmRecordVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 机房告警记录Service接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface OpoAlarmRecordService extends IService<OpoAlarmRecordPo>
{

    /**
     * 查询机房告警记录列表
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 机房告警记录集合
     */
    public List<OpoAlarmRecordVo> queryList(OpoAlarmRecordPo opoAlarmRecord);

    /**
     * 查询机房告警记录
     *
     * @param id 机房告警记录主键
     * @return 机房告警记录
     */
    public OpoAlarmRecordVo selectOpoAlarmRecordById(Long id);

    /**
     * 查询机房告警记录列表
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 机房告警记录集合
     */
    public List<OpoAlarmRecordVo> selectOpoAlarmRecordList(OpoAlarmRecordParam opoAlarmRecord);

    /**
     * 新增机房告警记录
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 结果
     */
    public int insertOpoAlarmRecord(OpoAlarmRecordAddParam opoAlarmRecord);

    /**
     * 修改机房告警记录
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 结果
     */
    public int updateOpoAlarmRecord(OpoAlarmRecordEditParam opoAlarmRecord);
    int solveAlarm(OpoAlarmRecordEditParam opoAlarmRecord);
    /**
     * 批量删除机房告警记录
     *
     * @param ids 需要删除的机房告警记录主键集合
     * @return 结果
     */
    public int deleteOpoAlarmRecordByIds(Long[] ids);

    /**
     * 删除机房告警记录信息
     *
     * @param id 机房告警记录主键
     * @return 结果
     */
    public int deleteOpoAlarmRecordById(Long id);

    List<String> findAlarmType();

    List<OpoAlarmRecordVo> findStrAlarmRecord(StrAlarmRecordParam param);

    List<String> findDeviceType();


    OpoAlarmRecordPo getMaxOneByCode(String deviceName);
}
