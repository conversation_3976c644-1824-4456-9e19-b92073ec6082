package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.dahuatech.hutool.http.HttpUtil;
import com.jhdr.operation.entity.param.PatrolTaskDetailAddParam;
import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.mapper.*;
import com.jhdr.operation.service.*;
import com.jhdr.operation.utils.BigDecimalUtils;
import com.jhdr.operation.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.param.PatrolTaskParam;
import com.jhdr.operation.entity.param.PatrolTaskAddParam;
import com.jhdr.operation.entity.param.PatrolTaskEditParam;

import java.io.IOException;
import java.net.InetAddress;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 巡检任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Service
public class PatrolTaskServiceImpl extends ServiceImpl<PatrolTaskMapper, PatrolTaskPo> implements IPatrolTaskService {

    @Autowired
    private PatrolTaskDetailMapper patrolTaskDetailMapper;
    @Override
    public List<PatrolTaskVo> queryList(PatrolTaskPo patrolTask) {
        LambdaQueryWrapper<PatrolTaskPo> lqw = Wrappers.lambdaQuery();

        if (StringUtils.isNotBlank(patrolTask.getPatrolId())){
            lqw.eq(PatrolTaskPo::getPatrolId ,patrolTask.getPatrolId());
        }
        if (StringUtils.isNotBlank(patrolTask.getPatrolName())){
            lqw.like(PatrolTaskPo::getPatrolName ,patrolTask.getPatrolName());
        }
        if (StringUtils.isNotBlank(patrolTask.getContent())){
            lqw.eq(PatrolTaskPo::getContent ,patrolTask.getContent());
        }
        if (StringUtils.isNotBlank(patrolTask.getPatrolType())){
            lqw.eq(PatrolTaskPo::getPatrolType ,patrolTask.getPatrolType());
        }
        if (StringUtils.isNotBlank(patrolTask.getPatrolScope())){
            lqw.eq(PatrolTaskPo::getPatrolScope ,patrolTask.getPatrolScope());
        }
        if (StringUtils.isNotBlank(patrolTask.getCycle())){
            lqw.eq(PatrolTaskPo::getCycle ,patrolTask.getCycle());
        }
        if (patrolTask.getPlanTime() != null){
            lqw.eq(PatrolTaskPo::getPlanTime ,patrolTask.getPlanTime());
        }
        if (patrolTask.getResult() != null){
            lqw.eq(PatrolTaskPo::getResult ,patrolTask.getResult());
        }
        if (patrolTask.getState() != null){
            lqw.eq(PatrolTaskPo::getState ,patrolTask.getState());
        }
        if (patrolTask.getPatrolTime() != null){
            lqw.eq(PatrolTaskPo::getPatrolTime ,patrolTask.getPatrolTime());
        }
        if (StringUtils.isNotBlank(patrolTask.getPerson())){
            lqw.eq(PatrolTaskPo::getPerson ,patrolTask.getPerson());
        }
        if (patrolTask.getIsDelete() != null){
            lqw.eq(PatrolTaskPo::getIsDelete ,patrolTask.getIsDelete());
        }
        List<PatrolTaskVo> patrolTaskVos= BeanUtil.copyToList(this.list(lqw), PatrolTaskVo.class);
        return patrolTaskVos;
    }
    /**
     * 查询巡检任务
     *
     * @param id 巡检任务主键
     * @return 巡检任务
     */
    @Override
    public PatrolOneTaskVo selectPatrolTaskById(Integer id) {
        PatrolOneTaskVo vo=baseMapper.selectPatrolTaskById(id);
        List<PatrolTaskDetailVo> details=baseMapper.selectAlarmDetails(id);
        vo.setDetails(details);
        Integer useAmount=baseMapper.findUseAmount(id);
        Integer alarmAmount=baseMapper.findAlarmAmount(id);
        vo.setAlarmAmount(alarmAmount);
        vo.setUseAmount(useAmount);
        return vo;
    }

    /**
     * 查询巡检任务列表
     *
     * @param patrolTask 巡检任务
     * @return 巡检任务
     */
    @Override
    public List<PatrolTaskVo> selectPatrolTaskList(PatrolTaskParam patrolTask)
    {
        List<PatrolTaskVo> list= baseMapper.selectPatrolTaskList(patrolTask);
        for (PatrolTaskVo vo:list) {
            if (vo.getPatrolType().equals("自动巡检")){
                List<String> notifys=baseMapper.findNotify(vo.getPatrolId());
                String noti="";
                for (String notify:notifys){
                    noti=noti+notify+";";
                }
                vo.setNotify(noti);
            }


            Integer alarmAmount=baseMapper.findAlarmAmount(vo.getId());
            if (alarmAmount.equals(0)){
                vo.setResult("正常");
            }else {
                vo.setResult("异常");
            }
        }
        return list;
    }
    /**
     * 巡检结果
     *
     * @return 巡检结果
     */
    @Override
    public PatrolTaskResultVo getTaskResult() {
        PatrolTaskPo po=baseMapper.findFristTask();
        if (ObjectUtil.isEmpty(po)){
            return new PatrolTaskResultVo();
        }else {
            PatrolTaskResultVo vo=new PatrolTaskResultVo();
            List<PatrolTaskDetailVo> details=baseMapper.selectAlarmDetails(po.getId());
            Integer useAmount=baseMapper.findUseAmount(po.getId());
            Integer alarmAmount=baseMapper.findAlarmAmount(po.getId());
            if (!(useAmount+alarmAmount==0)){
                String usePercent = BigDecimalUtils.reserve(Double.valueOf(useAmount*100)/(useAmount+alarmAmount));
                vo.setUsePercent(usePercent);
            }else {
                vo.setUsePercent("0.00");
            }
            vo.setAlarmAmount(alarmAmount);
            vo.setUseAmount(useAmount);
            vo.setDetails(details);


            return vo;
        }

    }
    @Autowired
    private PatrolProgressMapper patrolProgressMapper;

    @Override
    @Async
    public void addPatrolTask(PatrolTaskDetailAddParam param) throws Exception {
//        baseMapper.finish();
        PatrolProgressPo progressPo=new PatrolProgressPo(null,"0",new Date());
        patrolProgressMapper.insert(progressPo);

        updateMon(param.getPatrolScope());//更新状态



        PatrolTaskPo task=new PatrolTaskPo();
        BeanUtil.copyProperties(param,task);
        String patrolCode= "XJ"+ DateUtils.dateTimeNow();
        task.setPatrolCode(patrolCode);
        task.setPatrolType("自定义巡检");
        task.setIsDelete(0);

//        String patrolScope="";
//        for (String scope:param.getPatrolScope()) {
//            patrolScope=patrolScope+scope+",";
//        }
//        task.setPatrolScope(patrolScope);

        task.setPatrolTime(new Date());
        int success=baseMapper.insert(task);


        Integer id=task.getId();
        for (String scope:param.getPatrolScope()) {

            List<PatrolResultVo> results=baseMapper.selectPatrolResult(scope);
            for (PatrolResultVo vo:results) {
                PatrolTaskDetailPo po=new PatrolTaskDetailPo();
                BeanUtil.copyProperties(vo,po);
                po.setTaskCode(id);
                patrolTaskDetailMapper.insert(po);
            }
        }

    }

    @Override
    public List<String> findPatrolType() {
        List<String> list=new ArrayList<>();
        String name1="自动巡检";
        String name2="自定义巡检";
        list.add(name1);
        list.add(name2);
        return list;
    }

    @Override
    public List<String> findPatrolScope() {
        List<String> list=new ArrayList<>();
        String name2="网络设备";
        String name3="网络安全设备";
        String name4="服务器";
        String name5="泵站";
        String name6="涵闸";
        String name7="水位站";
        String name8="流量站";
        String name9="监控";

        list.add(name2);
        list.add(name3);
        list.add(name4);
        list.add(name5);
        list.add(name6);
        list.add(name7);
        list.add(name8);
        list.add(name9);
        return list;
    }

    @Override
    public String findProgress() {
        String progress=baseMapper.findProgress();
        if (ObjectUtil.isEmpty(progress)){
            progress="100";
        }
        return progress;
    }

    /**
     * 新增巡检任务
     *
     * @param param 巡检任务
     * @return 结果
     */
    @Override
    public int insertPatrolTask(PatrolTaskAddParam param)
    {

        PatrolTaskPo patrolTask=new PatrolTaskPo();
        BeanUtil.copyProperties(param,patrolTask);
        int success=baseMapper.insert(patrolTask);
        Integer id=patrolTask.getId();
        System.out.println(id+"=====");
        return success;
    }

    /**
     * 修改巡检任务
     *
     * @param patrolTaskEditParam 巡检任务
     * @return 结果
     */
    @Override
    public int updatePatrolTask(PatrolTaskEditParam patrolTaskEditParam)
    {
        PatrolTaskPo patrolTask=new PatrolTaskPo();
        BeanUtil.copyProperties(patrolTaskEditParam,patrolTask);
        return baseMapper.updateById(patrolTask);
    }

    /**
     * 批量删除巡检任务
     *
     * @param ids 需要删除的巡检任务主键
     * @return 结果
     */
    @Override
    public int deletePatrolTaskByIds(Long[] ids)
    {
        return baseMapper.deletePatrolTaskByIds(ids);
    }

    /**
     * 删除巡检任务信息
     *
     * @param id 巡检任务主键
     * @return 结果
     */
    @Override
    public int deletePatrolTaskById(Long id)
    {
        return baseMapper.deletePatrolTaskById(id);
    }
    @Autowired
    private OpoVideoStationMapper opoVideoStationMapper;

    @Autowired
    private OpoAlarmMessageMapper opoAlarmMessageMapper;

    @Autowired
    private IOpoVideoStationService opoVideoStationService;

    @Autowired
    private IOpoGqStationService gqStationService;

    @Autowired
    private IOpoAlarmMessageService opoAlarmMessageService;

    private String state1="正常";
    private String state2="异常";
    private String content1="正常";
    private String content2="视频掉线";

    private String name1="网络设备";
    private String name2="网络安全设备";
    private String name3="服务器";
    private String name4="泵站";
    private String name5="涵闸";
    private String name6="水位站";
    private String name7="流量站";
    private String name8="监控";


     void updateMon(List<String> scopes) throws Exception {
        int amount =scopes.size();
        Double percent= NumberUtil.div(1,amount)*100;
        Double add=0.0;
        for (String scope:scopes) {
            switch(scope){
                case "网络设备":
                    updateNet();
                    break;
                case "网络安全设备":
                    updateSafe();
                    break;
                case "服务器":
                    break;
                case "泵站":
                    updatePump();
                    break;
                case "涵闸":
                    updateGate();
                    break;
                case "水位站":
                    updateLine();
                    break;
                case "流量站":
                    updateFlow();
                    break;
                case "监控":
                    updateVideo();
                    break;
            }
            add=add+percent;
            String format = NumberUtil.decimalFormat("0", add);

            PatrolProgressPo progressPo=new PatrolProgressPo(null,format,new Date());
            patrolProgressMapper.insert(progressPo);
        }

    }


     void updateVideo() throws Exception {
        UpdateGbAlarmState();
        UpdateHkAlarmState();
        UpdateGbUseState();
        UpdateHkUseState();
    }

    private void updateNet() throws Exception {
        NetWorkState();
        NetWorkAlarmState();
    }

    private void updateSafe() throws Exception {
        NetSafeState();
        NetSafeAlarmState();
    }

    private void updatePump() throws Exception {
        PumpAlarmState();
        PumpUseState();
    }

    private void updateGate() throws Exception {
        System.out.println("进入涵闸巡检");
        GateUseState();
        GateAlarmState();
    }

    private void updateFlow() throws Exception {
        System.out.println("进入流量巡检");
        FlowAlarmState();
        FlowUseState();
    }

    private void updateLine() throws Exception {
        WaterAlarmState();
        WaterUseState();
    }

    private void UpdateGbUseState() throws Exception{

        List<OpoDHVideoStateVo> codes=opoVideoStationMapper.selectDHVideoChannel(state1);

        for (OpoDHVideoStateVo vo:codes){
            String channel=vo.getDhChannel();
            try{
                String result= HttpUtil.get("http://10.34.188.156:10001/api/play/start/"+channel,10000);
                JSONObject jsonObject = JSONObject.parseObject(result);
                String code = jsonObject.getString("code");
                if (!code.equals("0")){
                    SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date=new Date();
                    String time=sdfh.format(date);
                    String alarmCode= "GZ"+ DateUtils.dateTimeNow();
                    opoVideoStationMapper.updateStateByCd(vo.getCd(),state2,time,content2);
                    OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, alarmCode,vo.getNm(),vo.getCd(),null,null
                            ,null,"监控",null,null,"视频不在线",date,"未处理",null,0,null,null,null,null);
                    opoAlarmMessageMapper.insert(po);
                }
            }catch (Exception e){
                SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date=new Date();
                String time=sdfh.format(date);
                String alarmCode= "GZ"+ DateUtils.dateTimeNow();
                opoVideoStationMapper.updateStateByCd(vo.getCd(),state2,time,content2);
                OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, alarmCode,vo.getNm(),vo.getCd(),null,null
                        ,null,"监控",null,null,"视频不在线",date,"未处理",null,0,null,null,null,null);
                opoAlarmMessageMapper.insert(po);
            }


        }

    }
    private void UpdateGbAlarmState() throws Exception{

        List<OpoDHVideoStateVo> codes=opoVideoStationMapper.selectDHVideoChannel(state2);

        for (OpoDHVideoStateVo vo:codes){
            String channel=vo.getDhChannel();
            try {
                String result= HttpUtil.get("http://10.34.188.156:10001/api/play/start/"+channel,10000);
                JSONObject jsonObject = JSONObject.parseObject(result);
                String code = jsonObject.getString("code");
                if (code.equals("0")){
                    String time="";
                    opoVideoStationMapper.updateStateByCd(vo.getCd(),state1,time,content1);
                }
            }catch (Exception e){

            }

        }

    }

    private void UpdateHkUseState() throws Exception {
        List<OpoHKWSVideoStateVo> codes=opoVideoStationMapper.selectHKWSCode(state1);

        for (OpoHKWSVideoStateVo vo:codes){
            String url=opoVideoStationService.getVideoStreamByCode(vo.getCameraCode());
            try{
                String result=HttpUtil.get(url,10000);

            }catch (Exception e){
                SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date=new Date();
                String time=sdfh.format(date);
                String alarmCode= "GZ"+ DateUtils.dateTimeNow();
                opoVideoStationService.updateStateByCd(vo.getCd(),state2,time,content2);
                OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, alarmCode,vo.getNm(),vo.getCd(),null,null
                        ,null,"监控",null,null,"视频不在线",date,"未处理",null,0,null,null,null,null);
                opoAlarmMessageMapper.insert(po);

            }

        }
    }

    private void UpdateHkAlarmState() throws Exception {
        List<OpoHKWSVideoStateVo> codes=opoVideoStationMapper.selectHKWSCode(state2);

        for (OpoHKWSVideoStateVo vo:codes){
            String url=opoVideoStationService.getVideoStreamByCode(vo.getCameraCode());
            try{
                String result=HttpUtil.get(url,10000);
                String time="";
                opoVideoStationService.updateStateByCd(vo.getCd(),state1,time,content1);

            }catch (Exception e){


            }

        }
    }
    @Autowired
    private IOpoNetworkStationService opoNetworkStationService;

    @Autowired
    private OpoAlarmRecordService opoAlarmRecordService;
    private static final String  alarmType="网络异常";
    @Autowired
    private IOpoNetsafeStationService opoNetsafeStationService;

    private void NetWorkState() throws IOException {
        List<DeviceIpMonVo> list=opoNetworkStationService.findWorkByState(state1);
        Date date=new Date();
        for (DeviceIpMonVo vo:list) {
            InetAddress net = InetAddress.getByName(vo.getIp());
            if (!net.isReachable(5000)) {
                String time= DateUtils.getTime();
                opoNetworkStationService.updateNetWork(vo.getId(),time,state2,alarmType);
                opoNetworkStationService.updateTopologyState(vo.getDeviceName(),state2);
                String alarmCode= UUID.randomUUID().toString();
                OpoAlarmRecordPo recordPo=new OpoAlarmRecordPo(null, alarmCode,vo.getDeviceName(),
                        String.valueOf(vo.getId()),"网络设备","网络不通",date,
                        "未处理",vo.getDeviceName()+"网络不通",0,vo.getSite(),null,null,null,null);
                opoAlarmRecordService.save(recordPo);
            }

        }
    }

    /**
     * 网络设备监测异常恢复
     */

    private void NetWorkAlarmState() throws IOException {
        List<DeviceIpMonVo> list=opoNetworkStationService.findWorkByState(state2);
        for (DeviceIpMonVo vo:list) {
            InetAddress net = InetAddress.getByName(vo.getIp());
            String time="";
            if (net.isReachable(5000)) {
                opoNetworkStationService.updateTopologyState(vo.getDeviceName(),state1);
                opoNetworkStationService.updateNetWork(vo.getId(),time,state1,"");
                OpoAlarmRecordPo po=opoAlarmRecordService.getMaxOneByCode(vo.getDeviceName());
                if (ObjectUtil.isEmpty(po)){
                    po.setRegainTime(new Date());
                    opoAlarmRecordService.updateById(po);
                }
            }
        }
    }

    /**
     * 网络安全设备监测异常
     */

    private void NetSafeState() throws IOException {

        List<DeviceIpMonVo> list=opoNetsafeStationService.findSafeByState(state1);
        Date date=new Date();
        for (DeviceIpMonVo vo:list) {
            InetAddress net = InetAddress.getByName(vo.getIp());
            if (!net.isReachable(5000)) {
                String time= DateUtils.getTime();
                opoNetworkStationService.updateTopologyState(vo.getDeviceName(),state2);
                opoNetsafeStationService.updateNetWork(vo.getId(),time,state2,alarmType);
                String alarmCode= UUID.randomUUID().toString();
                OpoAlarmRecordPo recordPo=new OpoAlarmRecordPo(null, alarmCode,vo.getDeviceName(),
                        String.valueOf(vo.getId()),"网络安全设备","网络不通",date,
                        "未处理",vo.getDeviceName()+"网络不通",0,vo.getSite(),null,null,null,null);
                opoAlarmRecordService.save(recordPo);

            }
        }
    }

    /**
     * 网络安全设备异常恢复
     */
    private void NetSafeAlarmState() throws IOException {
        List<DeviceIpMonVo> list=opoNetsafeStationService.findSafeByState(state2);
        for (DeviceIpMonVo vo:list) {
            InetAddress net = InetAddress.getByName(vo.getIp());
            if (!net.isReachable(5000)) {
                String time="";
                opoNetsafeStationService.updateNetWork(vo.getId(),time,state1,"");
                opoNetworkStationService.updateTopologyState(vo.getDeviceName(),state1);
                OpoAlarmRecordPo po=opoAlarmRecordService.getMaxOneByCode(vo.getDeviceName());
                if (ObjectUtil.isEmpty(po)){
                    po.setRegainTime(new Date());
                    opoAlarmRecordService.updateById(po);
                }
            }
        }
    }


    private void PumpAlarmState(){
        String state="正常";
        String sttp="BZ";

        List<MachineStateVo> codes=gqStationService.findPumpCodeByState(state);
        for (MachineStateVo vo:codes) {
            Date dtlTime=gqStationService.findPumpDtlTime(vo.getStrCode());
            vo.setAddTime(dtlTime);

            Long tm2=new Date().getTime()-vo.getAddTime().getTime();
            if (tm2>86400000){
                SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date=new Date();
                String time=sdfh.format(date);
                OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, UUID.randomUUID().toString(),vo.getStrName(),vo.getStrCode(),null,null
                        ,null,"泵站",null,null,"数据掉线",date,"未处理",null,0,null,null,null,null);
                opoAlarmMessageService.save(po);
                int alarmId=po.getId();
                int pustState=gqStationService.updatePustState(vo.getStrCode(),"异常","数据掉线",time,alarmId);
            }

        }

    }


    /**
     * 异常泵站中查找正常
     */
    private void PumpUseState(){
        String state="异常";
        List<MachineStateVo> codes=gqStationService.findPumpCodeByState(state);

        for (MachineStateVo vo:codes) {
            Date dtlTime=gqStationService.findPumpDtlTime(vo.getStrCode());
            vo.setAddTime(dtlTime);
            Long tm2=new Date().getTime()-vo.getAddTime().getTime();
            if (tm2<86400000){
                String time="";
                OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCode(vo.getStrCode());
                int result=gqStationService.updatePustState(vo.getStrCode(),"正常","",time,po.getId());
                if (!ObjectUtil.isEmpty(po)){
                    po.setRegainTime(new Date());
                    opoAlarmMessageService.updateById(po);
                }
            }

        }

    }


    /**
     * 正常涵闸中查找异常
     */
    private void GateAlarmState(){
        String state="正常";
        String sttp="HZ";

        List<MachineStateVo> codes=gqStationService.findGateCodeByState(state);

//            List<MachineStateVo> stcds=gqStationService.findGateTimeByCode(code);
        for (MachineStateVo vo:codes) {
            Date dtlTime=gqStationService.findWagaDtlTime(vo.getStrCode());
            vo.setAddTime(dtlTime);
            Long tm2=new Date().getTime()-vo.getAddTime().getTime();
            if (tm2>86400000){
                SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date=new Date();
                String time=sdfh.format(date);
//                        int result=gqStationService.updateGateState(vo.getStrCode(),vo.getDeviceCode(),"异常","数据掉线",time);
                int pustState=gqStationService.updateWagaState(vo.getStrCode(),"异常","数据掉线",time);
                OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, UUID.randomUUID().toString(),vo.getStrName(),vo.getStrCode(),null,null
                        ,null,"涵闸",null,null,"数据掉线",date,"未处理",null,0,null,null,null,null);
                opoAlarmMessageService.save(po);
            }

        }
        System.out.println("正常涵闸中查找异常");

    }



    /**
     * 异常涵闸中查找正常
     */
    private void GateUseState(){
        String state="异常";
        List<MachineStateVo> codes=gqStationService.findGateCodeByState(state);
        for (MachineStateVo vo:codes) {
//                if (!ObjectUtil.isEmpty(vo.getAddTime())){
            Date dtlTime=gqStationService.findWagaDtlTime(vo.getStrCode());
            vo.setAddTime(dtlTime);
            Long tm2=new Date().getTime()-vo.getAddTime().getTime();
            System.out.println(tm2);
            if (tm2<86400000){
                String time="";
                int result=gqStationService.updateWagaState(vo.getStrCode(),"正常","",time);
                OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCode(vo.getStrCode());
                if (!ObjectUtil.isEmpty(po)){
                    po.setRegainTime(new Date());
                    opoAlarmMessageService.updateById(po);
                }
            }
//                }
        }
        System.out.println("异常涵闸中查找正常");
    }

    @Autowired
    private IOpoSwStationService swStationService;
    /**
     * 正常水位站中查找异常
     */
    private void WaterAlarmState() throws ParseException {
        String state="正常";
        List<WaterStateVo> stcds=swStationService.findCodeByState(state);

        for (WaterStateVo vo:stcds) {
            System.out.println(vo.getStcd());
            String newTime=swStationService.findNewTimeByCD(vo.getStcd());

            if (!com.jhdr.common.core.utils.StringUtils.isEmpty(newTime)){
                SimpleDateFormat newDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date newTime1=newDate.parse(newTime);
//            Long tm1=swStationService.findWaterStateByCode(vo.getStcd()).getTime();
                Long tm1=newTime1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2>90000000){
                    SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date=new Date();
                    String time=sdfh.format(date);
                    swStationService.updateWaterState(vo.getStcd(),"异常","数据掉线",time);
                    OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, UUID.randomUUID().toString(),vo.getStnm(),vo.getStcd(),null,null
                            ,null,"水位站",vo.getConstruction(),null,"水位站数据掉线",date,"未处理",null,0,null,null,null,null);
                    opoAlarmMessageService.save(po);
                }
            }
        }

    }

    /**
     * 异常水位站中查找正常
     */
    private void WaterUseState() throws ParseException {
        String state="异常";
        List<WaterStateVo> stcds=swStationService.findCodeByState(state);
        for (WaterStateVo vo:stcds) {
            String newTime=swStationService.findNewTimeByCD(vo.getStcd());

            if (!com.jhdr.common.core.utils.StringUtils.isEmpty(newTime)){
                SimpleDateFormat newDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date newTime1=newDate.parse(newTime);
//            Long tm1=swStationService.findWaterStateByCode(vo.getStcd()).getTime();
                Long tm1=newTime1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2<90000000){
                    String time="";
                    int result=swStationService.updateWaterState(vo.getStcd(),"正常","",time);
                    OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCode(vo.getStcd());
                    if (!ObjectUtil.isEmpty(po)){
                        po.setRegainTime(new Date());
                        opoAlarmMessageService.updateById(po);
                    }
                }
            }

        }

    }




    /**
     * 正常流量站中查找异常
     */
    private void FlowAlarmState(){
        String state="正常";
        List<WaterStateVo> stcds=swStationService.findFlowByState(state);

        for (WaterStateVo vo:stcds) {
            Date time1=swStationService.findFlowStateByCode(vo.getStcd());
            if (!ObjectUtil.isEmpty(time1)){
                Long tm1=time1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2>90000000){
                    SimpleDateFormat sdfh = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date date=new Date();
                    String time=sdfh.format(date);
                    swStationService.updateWaterState(vo.getStcd(),"异常","数据掉线",time);
                    String alarmCode= "GZ"+ DateUtils.dateTimeNow();
                    OpoAlarmMessagePo po=new OpoAlarmMessagePo(null, alarmCode,vo.getStnm(),vo.getStcd(),null,null
                            ,null,"流量站",vo.getConstruction(),null,"流量站数据掉线",date,"未处理",null,0,null,null,null,null);
                    opoAlarmMessageService.save(po);
                }
            }
        }

    }



    /**
     * 异常流量站中查找正常
     */
    private void FlowUseState(){
        String state="异常";
        List<WaterStateVo> stcds=swStationService.findFlowByState(state);

        for (WaterStateVo vo:stcds) {
            Date time1=swStationService.findFlowStateByCode(vo.getStcd());
            if (!ObjectUtil.isEmpty(time1)){
                Long tm1=time1.getTime();
                Long tm2=new Date().getTime()-tm1;
                if (tm2<90000000){
                    String time="";
                    int result=swStationService.updateWaterState(vo.getStcd(),"正常","",time);
                    OpoAlarmMessagePo po=opoAlarmMessageService.getMaxOneByCode(vo.getStcd());
                    if (!ObjectUtil.isEmpty(po)){
                        po.setRegainTime(new Date());
                        opoAlarmMessageService.updateById(po);
                    }
                }
            }

        }

    }

}
