package com.jhdr.operation.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.operation.entity.po.OpoMachineRoomAlarmPo;
import com.jhdr.operation.entity.po.OpoMachineRoomDetailPo;
import com.jhdr.operation.entity.po.OpoMachineRoomDevicePo;
import com.jhdr.operation.service.IOpoMachineRoomAlarmService;
import com.jhdr.operation.service.IOpoMachineRoomDetailService;
import com.jhdr.operation.service.IOpoMachineRoomDeviceService;
import lombok.extern.java.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Log
@Component
public class MachineRoomDeviceHttp {

    @Autowired
    private IOpoMachineRoomDeviceService roomDeviceService;

    @Autowired
    private IOpoMachineRoomAlarmService roomAlarmService;

    @Autowired
    private IOpoMachineRoomDetailService roomDetailService;

    public JSONArray processGetResponse(String response) {
        if (StrUtil.isNotBlank(response)) {
            // 解析JSON响应

            // 提取JSON字符串
            String dataJson = response.substring(
                    response.indexOf("<string xmlns=\"http://tempuri.org/\">")
                            +"<string xmlns=\"http://tempuri.org/\">".length(),
                    response.indexOf("</string>"));
            if (JSONUtil.isTypeJSONArray(dataJson)){
                JSONArray jsonResponse = JSONUtil.parseArray(dataJson);

                return jsonResponse;
            }else {
                return null;
            }

       }else {
            return null;
        }

    }

    // 获取机房设备信息
    private void opoMachineRoomDevice(){
        String get = HttpUtil.get("http://************/yms/YMS.asmx/GetSite");
        JSONArray machineRoomDevices = processGetResponse(get);
        if (machineRoomDevices != null) {
            List<OpoMachineRoomDevicePo> roomDevicePos=new ArrayList<>();
            for (Object machineRoomDevice : machineRoomDevices) {
                OpoMachineRoomDevicePo roomDevicePo = new OpoMachineRoomDevicePo();
                JSONObject jsonObject = (JSONObject) machineRoomDevice;
                roomDevicePo.setId(jsonObject.getLong("id")) ;
                roomDevicePo.setParentId(jsonObject.getLong("ParentID"));
                roomDevicePo.setIp(jsonObject.getStr("IP"));
                roomDevicePo.setSubClass(jsonObject.getInt("SubClass"));
                roomDevicePo.setName(jsonObject.getStr("Name"));
                roomDevicePo.setNetStatus(jsonObject.getLong("NetStatus")) ;
                roomDevicePo.setSubtype(jsonObject.getLong("SUBTYPE"));
                roomDevicePos.add(roomDevicePo);
            }
            roomDeviceService.saveOrUpdateBatch(roomDevicePos);
        }
    }

    // 获取机房设备实时报警信息
    private void opoMachineRoomAlarm(){
        String get = HttpUtil.get("http://************/yms/YMS.asmx/GetAlarm");
        JSONArray machineRoomAlarms = processGetResponse(get);
        if (machineRoomAlarms != null) {
            List<OpoMachineRoomAlarmPo> alarmInfos = new ArrayList<>();
            for (Object alarm : machineRoomAlarms) {
                JSONObject jsonObject = (JSONObject) alarm;
                OpoMachineRoomAlarmPo alarmInfo = new OpoMachineRoomAlarmPo();
                alarmInfo.setAlarmid(jsonObject.getStr("alarmid"));
                alarmInfo.setLscid(jsonObject.getLong("lscid"));
                alarmInfo.setWyid(jsonObject.getStr("WYID"));
                alarmInfo.setItemLevels(jsonObject.getLong("Itemlevels"));
                alarmInfo.setSiteName(jsonObject.getStr("siteName"));

                String dateStr = jsonObject.getStr("TS");
                if (dateStr != null){
                    // 指定日期字符串的格式
                    String pattern = "yyyy/M/d H:mm:ss";
                    // 使用Hutool的DateUtil.parse方法解析字符串为Date对象
                    Date date = DateUtil.parse(dateStr, pattern);
                    alarmInfo.setTs(date);
                }
                alarmInfo.setId(jsonObject.getLong("id"));
                alarmInfo.setIsad(jsonObject.getStr("isAD")) ;
                alarmInfo.setType(jsonObject.getStr("type"));
                alarmInfo.setIp(jsonObject.getStr("IP"));
                alarmInfo.setName(jsonObject.getStr("name"));
                alarmInfo.setValue(jsonObject.getStr("value"));
                alarmInfo.setDw(jsonObject.getStr("dw"));
                alarmInfo.setItemLevels(jsonObject.getLong("ItemClass"));
            }
            roomAlarmService.saveOrUpdateBatch(alarmInfos);
        }

    }

    // 获取机房设备回去设备内部详细信息
    private void opoMachineRoomDetails(){

        List<OpoMachineRoomDevicePo> roomDevicePos = roomDeviceService.list(
                new QueryWrapper<OpoMachineRoomDevicePo>().isNotNull("ip"));

        for (OpoMachineRoomDevicePo roomDevicePo : roomDevicePos) {
            String ip = roomDevicePo.getIp();
            String get = HttpUtil.get("http://************/YMS/YMS.asmx/GetDataForIP?ip="+ip);
            JSONArray details = processGetResponse(get);
            if (details != null) {
                List<OpoMachineRoomDetailPo> roomDetailPos = new ArrayList<>();
                for (Object detail : details) {
                    JSONObject jsonObject = (JSONObject) detail;

                    OpoMachineRoomDetailPo roomDetailPo = new OpoMachineRoomDetailPo();

                    String wyid = jsonObject.getStr("WYID");
                    if (StrUtil.isNotBlank(wyid)){
                        String id = wyid.replace("127.0.0.1", "").
                                replace(":", "").replace("_", "");

                        roomDetailPo.setId(Long.valueOf(id));
                    }

                    roomDetailPo.setDeviceId(roomDevicePo.getId());
                    roomDetailPo.setDeviceName(roomDevicePo.getName());
                    roomDetailPo.setIp(jsonObject.getStr("IP"));
                    roomDetailPo.setSubName(jsonObject.getStr("subName"));
                    roomDetailPo.setWyid(wyid);
                    roomDetailPo.setValue(jsonObject.getStr("value")) ;
                    roomDetailPo.setDw(jsonObject.getStr("dw")) ;
                    roomDetailPo.setSubid(jsonObject.getLong("SUBID"));
                    roomDetailPo.setTime(DateUtil.parse(jsonObject.getStr("TIME"), "yyyy-MM-dd HH:mm:ss"));
                    roomDetailPos.add(roomDetailPo);
                }
                roomDetailService.saveOrUpdateBatch(roomDetailPos);
            }
        }
    }

    @Scheduled(fixedDelay = 1000 * 60 * 5)
    public void timedExecution(){
        opoMachineRoomDevice();
        opoMachineRoomAlarm();
        opoMachineRoomDetails();
    }

}
