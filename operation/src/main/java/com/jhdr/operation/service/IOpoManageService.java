package com.jhdr.operation.service;

import java.util.Date;
import java.util.List;

import com.jhdr.operation.entity.param.OpoStateChangeParam;
import com.jhdr.operation.entity.po.OpoManagePo;
import com.jhdr.operation.entity.param.OpoManageParam;
import com.jhdr.operation.entity.param.OpoManageAddParam;
import com.jhdr.operation.entity.param.OpoManageEditParam;
import com.jhdr.operation.entity.vo.MonManageVo;
import com.jhdr.operation.entity.vo.OpoManageStateVo;
import com.jhdr.operation.entity.vo.OpoManageVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 业务管理Service接口
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
public interface IOpoManageService extends IService<OpoManagePo>
{

    /**
     * 查询业务管理列表
     *
     * @param opoManage 业务管理
     * @return 业务管理集合
     */
    public List<OpoManageVo> queryList(OpoManagePo opoManage);



    /**
     * 查询业务管理列表
     *
     * @param opoManage 业务管理
     * @return 业务管理集合
     */
    public List<OpoManageVo> selectOpoManageList(OpoManageParam opoManage);

    /**
     * 新增业务管理
     *
     * @param opoManage 业务管理
     * @return 结果
     */
    public int insertOpoManage(OpoManageAddParam opoManage);




    OpoManageStateVo findAllState();


    int updateState(OpoStateChangeParam param);

    int updateManage(String cd);

    Date findRoomTime();

    Date findWeatherTime();

    List<MonManageVo> findManageTime();

    void updateManageState(String cd, String state);
}
