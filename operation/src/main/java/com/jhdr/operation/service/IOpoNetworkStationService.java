package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.OpoNetworkStationPo;
import com.jhdr.operation.entity.param.OpoNetworkStationParam;
import com.jhdr.operation.entity.param.OpoNetworkStationAddParam;
import com.jhdr.operation.entity.param.OpoNetworkStationEditParam;
import com.jhdr.operation.entity.vo.DeviceIpMonVo;
import com.jhdr.operation.entity.vo.NetStateVo;
import com.jhdr.operation.entity.vo.OpoNetworkStationVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.operation.entity.vo.StateCountVo;

/**
 * 运维监控-网络设备Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOpoNetworkStationService extends IService<OpoNetworkStationPo>
{

    /**
     * 查询运维监控-网络设备列表
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 运维监控-网络设备集合
     */
    public List<OpoNetworkStationVo> queryList(OpoNetworkStationPo opoNetworkStation);

    /**
     * 查询运维监控-网络设备
     *
     * @param id 运维监控-网络设备主键
     * @return 运维监控-网络设备
     */
    public OpoNetworkStationVo selectOpoNetworkStationById(Long id);



    /**
     * 查询运维监控-网络设备列表
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 运维监控-网络设备集合
     */

    public List<OpoNetworkStationVo> selectOpoNetworkStationList(OpoNetworkStationParam opoNetworkStation);


    StateCountVo getWorkStateAmount();
    /**
     * 新增运维监控-网络设备
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 结果
     */
    public int insertOpoNetworkStation(OpoNetworkStationAddParam opoNetworkStation);

    /**
     * 修改运维监控-网络设备
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 结果
     */
    public int updateOpoNetworkStation(OpoNetworkStationEditParam opoNetworkStation);

    /**
     * 批量删除运维监控-网络设备
     *
     * @param ids 需要删除的运维监控-网络设备主键集合
     * @return 结果
     */
    public int deleteOpoNetworkStationByIds(Long[] ids);

    /**
     * 删除运维监控-网络设备信息
     *
     * @param id 运维监控-网络设备主键
     * @return 结果
     */
    public int deleteOpoNetworkStationById(Long id);


    List<DeviceIpMonVo> findWorkByState(String state);



    List<NetStateVo> findTopologyState();

    void updateTopologyState(String deviceName, String status);

    void updateNetWork(Integer id, String time, String state, String alarmType);


}
