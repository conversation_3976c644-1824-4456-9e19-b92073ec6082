package com.jhdr.operation.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jhdr.operation.entity.po.JhPumpDtlRPo;
import com.jhdr.operation.entity.po.JhPumpDtlRealPo;
import com.jhdr.operation.entity.po.JhPumpRealPo;
import com.jhdr.operation.mapper.JhPumpDtlRMapper;
import com.jhdr.operation.mapper.JhPumpDtlRealMapper;
import com.jhdr.operation.mapper.JhPumpRMapper;
import com.jhdr.operation.mapper.JhPumpRealMapper;
import com.jhdr.operation.service.JhPumpDtlRService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * 运维测试Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Service
public class JhPumpDtlRServiceImpl extends ServiceImpl<JhPumpDtlRMapper, JhPumpDtlRPo> implements JhPumpDtlRService {
    @Autowired
    private JhPumpDtlRMapper jhPumpDtlRMapper;

    @Autowired
    private JhPumpDtlRealMapper jhPumpDtlRealMapper;


    @Override
    public void insertReal(JhPumpDtlRPo po1) {
        JhPumpDtlRealPo po=jhPumpDtlRMapper.selectRealPump(po1.getPrjnmcd(),po1.getPmpcd());
        JhPumpDtlRPo delPo=jhPumpDtlRMapper.selectDtlPump(po1.getPrjnmcd(),po1.getPmpcd(),po1.getClltm());

        if (ObjectUtil.isEmpty(delPo)){
            jhPumpDtlRMapper.insert(po1);
        }
        if (ObjectUtil.isEmpty(po)){
            JhPumpDtlRealPo po2=new JhPumpDtlRealPo();
            BeanUtil.copyProperties(po1,po2);

            jhPumpDtlRealMapper.insert(po2);

        }else {
            BeanUtil.copyProperties(po1,po);
            JhPumpDtlRealPo test=jhPumpDtlRMapper.selectRealPumpTest(po1.getPrjnmcd(),po1.getPmpcd(),po1.getClltm());
            if (ObjectUtil.isEmpty(test)){
                jhPumpDtlRealMapper.update(po,new QueryWrapper<JhPumpDtlRealPo>().eq("pmpcd",po.getPmpcd())
                        .eq("prjnmcd",po.getPrjnmcd()));
            }


        }
    }
}
