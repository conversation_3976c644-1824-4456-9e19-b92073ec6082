package com.jhdr.operation.service;

import com.jhdr.operation.entity.vo.*;

import java.util.List;

public interface IrrigateService {



    IrrigateIntroduceVo findIntroduce(String name);

    List<IrrigatePlantVo> findPlantPercent(String name);

    List<IrrigateVideoVo> findVideo(String name);

    IrrigateMessageVo findMessage(String name);

    List<IrrigateMonMessageVo> findMonMessage(String name);

    IrrigateMonDetailVo findMonDetail(String code);

    List<IrrigateFlowAnalyseVo> findFlowAnalyse(String name, String startTime, String endTime);

    List<IrrigateRainfallVo> findRainfallAnalyse(String startTime, String endTime);

    List<IrrigateWaterLineVo> findWaterLineAnalyse();

    List<IrrigateWaterTrendVo> findWaterTrend();


}
