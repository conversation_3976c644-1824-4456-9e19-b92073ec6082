package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.operation.entity.vo.DeviceIpMonVo;
import com.jhdr.operation.entity.vo.NetStateVo;
import com.jhdr.operation.entity.vo.StateCountVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoNetworkStationPo;
import com.jhdr.operation.entity.param.OpoNetworkStationParam;
import com.jhdr.operation.entity.param.OpoNetworkStationAddParam;
import com.jhdr.operation.entity.param.OpoNetworkStationEditParam;
import com.jhdr.operation.entity.vo.OpoNetworkStationVo;
import com.jhdr.operation.mapper.OpoNetworkStationMapper;
import com.jhdr.operation.service.IOpoNetworkStationService;

import java.util.ArrayList;

import java.util.Date;
import java.util.List;

/**
 * 运维监控-网络设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class OpoNetworkStationServiceImpl extends ServiceImpl<OpoNetworkStationMapper, OpoNetworkStationPo> implements IOpoNetworkStationService {

    @Override
    public List<OpoNetworkStationVo> queryList(OpoNetworkStationPo opoNetworkStation) {
        LambdaQueryWrapper<OpoNetworkStationPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoNetworkStation.getDeviceName())){
            lqw.like(OpoNetworkStationPo::getDeviceName ,opoNetworkStation.getDeviceName());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getDeviceModel())){
            lqw.eq(OpoNetworkStationPo::getDeviceModel ,opoNetworkStation.getDeviceModel());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getDeviceType())){
            lqw.eq(OpoNetworkStationPo::getDeviceType ,opoNetworkStation.getDeviceType());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getIp())){
            lqw.eq(OpoNetworkStationPo::getIp ,opoNetworkStation.getIp());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getMac())){
            lqw.eq(OpoNetworkStationPo::getMac ,opoNetworkStation.getMac());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getSite())){
            lqw.eq(OpoNetworkStationPo::getSite ,opoNetworkStation.getSite());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getDeviceState())){
            lqw.eq(OpoNetworkStationPo::getDeviceState ,opoNetworkStation.getDeviceState());
        }
        if (StringUtils.isNotBlank(opoNetworkStation.getNetState())){
            lqw.eq(OpoNetworkStationPo::getNetState ,opoNetworkStation.getNetState());
        }
        if (opoNetworkStation.getAlarmTime() != null){
            lqw.eq(OpoNetworkStationPo::getAlarmTime ,opoNetworkStation.getAlarmTime());
        }
        List<OpoNetworkStationVo> opoNetworkStationVos= BeanUtil.copyToList(this.list(lqw), OpoNetworkStationVo.class);
        return opoNetworkStationVos;
    }
    /**
     * 查询运维监控-网络设备
     *
     * @param id 运维监控-网络设备主键
     * @return 运维监控-网络设备
     */
    @Override
    public OpoNetworkStationVo selectOpoNetworkStationById(Long id)
    {
        return baseMapper.selectOpoNetworkStationById(id);
    }



    /**
     * 查询运维监控-网络设备列表
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 运维监控-网络设备
     */
    @Override
    public List<OpoNetworkStationVo> selectOpoNetworkStationList(OpoNetworkStationParam opoNetworkStation)
    {
        return baseMapper.selectOpoNetworkStationList(opoNetworkStation);
    }


    private  static  String status1="正常";
    private  static  String status2="异常";

    @Override
    public StateCountVo getWorkStateAmount() {
        Integer useAmount=baseMapper.getWorkStateAmount(status1);
        Integer alarmAmount=baseMapper.getWorkStateAmount(status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }

    /**
     * 新增运维监控-网络设备
     *
     * @param opoNetworkStationAddParam 运维监控-网络设备
     * @return 结果
     */
    @Override
    public int insertOpoNetworkStation(OpoNetworkStationAddParam opoNetworkStationAddParam)
    {

        OpoNetworkStationPo opoNetworkStation=new OpoNetworkStationPo();
        BeanUtil.copyProperties(opoNetworkStationAddParam,opoNetworkStation);
        opoNetworkStation.setIsDelete(0);
        opoNetworkStation.setUpdateTime(new Date());
        return baseMapper.insert(opoNetworkStation);
    }

    /**
     * 修改运维监控-网络设备
     *
     * @param opoNetworkStationEditParam 运维监控-网络设备
     * @return 结果
     */
    @Override
    public int updateOpoNetworkStation(OpoNetworkStationEditParam opoNetworkStationEditParam)
    {
        OpoNetworkStationPo opoNetworkStation=new OpoNetworkStationPo();
        BeanUtil.copyProperties(opoNetworkStationEditParam,opoNetworkStation);
        return baseMapper.updateById(opoNetworkStation);
    }

    /**
     * 批量删除运维监控-网络设备
     *
     * @param ids 需要删除的运维监控-网络设备主键
     * @return 结果
     */
    @Override
    public int deleteOpoNetworkStationByIds(Long[] ids)
    {
        return baseMapper.deleteOpoNetworkStationByIds(ids);
    }

    /**
     * 删除运维监控-网络设备信息
     *
     * @param id 运维监控-网络设备主键
     * @return 结果
     */
    @Override
    public int deleteOpoNetworkStationById(Long id)
    {
        return baseMapper.deleteOpoNetworkStationById(id);
    }

    @Override
    public List<DeviceIpMonVo> findWorkByState(String state) {
        return baseMapper.findWorkByState(state);
    }



    @Override
    public List<NetStateVo> findTopologyState() {
        return baseMapper.findTopologyState();
    }

    @Override
    public void updateTopologyState(String deviceName, String status) {
        baseMapper.updateTopologyState(deviceName,status);
    }

    @Override
    public void updateNetWork(Integer id, String time, String state,String alarmType) {
        baseMapper.updateNetWork(id,time,state,alarmType);
    }


}
