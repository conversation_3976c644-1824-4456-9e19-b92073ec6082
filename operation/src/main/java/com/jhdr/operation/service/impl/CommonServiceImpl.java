package com.jhdr.operation.service.impl;




import cn.hutool.core.util.ObjectUtil;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.mapper.CommonMapper;
import com.jhdr.operation.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;


@Service
public class CommonServiceImpl implements CommonService {
    @Autowired
    private CommonMapper commonMapper;





    @Override
    public List<FindWaterLineVo> findAllWaterLine() {
        return commonMapper.findAllWaterLine();
    }

    @Override
    public FindWaterLineVo findLineDataByCodeTime(String code, Date addTime) {
        return commonMapper.findLineDataByCodeTime(code, addTime);
    }

    @Override
    public int insertWaterLine(String code, Double waterLine, Date addTime) {
        int rows = commonMapper.insertWaterLine(code, waterLine, addTime);
        return rows;
    }

    @Override
    public List<FindFlowVo> findAllFlow() {
        return commonMapper.findAllFlow();
    }

    @Override
    public FindFlowVo findFlowDataByCodeTime(String code, Date addTime) {
        return commonMapper.findFlowDataByCodeTime(code, addTime);
    }

    @Override
    public int insertFlow(String code, Double flow, Date addTime) {
        int rows = commonMapper.insertFlow(code, flow, addTime);
        return rows;
    }

    @Override
    public List<PumpRealVo> findAllPumpReal(String pid) {

        return commonMapper.findAllPumpReal(pid);
    }

    @Override
    public JhPumpDtlRPo findNewPumpDtl(String pid, String id, Date addTime) {
        return commonMapper.findNewPumpDtl(pid, id, addTime);
    }

    @Override
    public List<String> findAllPid() {
        return commonMapper.findAllPid();
    }

    @Override
    public GateRealVo findGateById(String pid) {
        return commonMapper.findGateById(pid);
    }

    @Override
    public GateRealVo findGate1ById(String pid) {
        return commonMapper.findGate1ById(pid);
    }

    @Override
    public GateRealVo findGate2ById(String pid) {
        return commonMapper.findGate2ById(pid);
    }

    @Override
    public GateRealVo find1GateById(String id) {
        return commonMapper.find1GateById(id);
    }

    @Override
    public GateRealVo findGateNameType(String name, String type) {
        return commonMapper.findGateNameType(name, type);
    }

    @Override
    public JhSlcrsDtlRPo findSlcrsByPid(String id, String pid, Date addTime) {
        return commonMapper.findSlcrsByPid(id, pid, addTime);
    }

    @Override
    public Integer findAllPumoState(String pid) {
        return commonMapper.findAllPumoState(pid);
    }

    @Override
    public Integer findRunPumpState(String pid) {
        return commonMapper.findRunPumpState(pid);
    }

    @Override
    public Integer findRunGateCount(String name) {
        return commonMapper.findRunGateCount(name);
    }

    @Override
    public String exchangePmpcdByid(String id) {
        return commonMapper.exchangePmpcdByid(id);
    }

    @Override
    public String exchangePrjnmcdByid(String id) {
        return commonMapper.exchangePrjnmcdByid(id);
    }

    @Override
    public String exchangeGpcdByid(String id) {
        return commonMapper.exchangeGpcdByid(id);
    }

    @Override
    public Double findWaterLineByData(String name, String waterName) {
        return commonMapper.findWaterLineByData(name, waterName);
    }

    @Override
    public GateRealVo findWaterLineByCode(String code) {
        return commonMapper.findWaterLineByCode(code);
    }

    @Override
    public StWasRPo findRealWasData(String stcd, Date addTime) {
        return commonMapper.findRealWasData(stcd, addTime);
    }

    @Override
    public StRiverRPo findRealRiverData(String stcd, Date addTime) {
        return commonMapper.findRealRiverData(stcd, addTime);
    }

    @Override
    public String findJhCodeByCode(String code) {
        return commonMapper.findJhCodeByCode(code);
    }

    @Override
    public List<String> findShareCds(String type) {
        return commonMapper.findShareCds(type);
    }

    @Override
    public StWasRPo findUpWaterByCode(String upcd) {
        return commonMapper.findUpWaterByCode(upcd);
    }

    @Override
    public StRiverRPo findDownWaterByCode(String downCd) {
        return commonMapper.findDownWaterByCode(downCd);
    }

    @Override
    public List<PustCodeVo> findPustPid() {
        return commonMapper.findPustPid();
    }

    @Override
    public StWasRPo findShareUpLineByCode(String stcd) {
        return null;
    }

    @Override
    public String findWdwcdByStrCode(String strCode) {
        return commonMapper.findWdwcdByStrCode(strCode);
    }

    @Override
    public String findWupcdByStrCode(String strCode) {
        return commonMapper.findWupcdByStrCode(strCode);
    }


    private static String type1="水位";
    private static String type2="雨量";
    private static String type3="视频";
    private static String type4="流量";
    private static String type5="泵站";
    private static String type6="涵闸";
    private static String type7="枢纽";
    private static String key1="日";
    private static String key2="的";
    private static String key3="到";
    private static String type="";
    private static String run1="查看";
    private static String run2="打开";
    private static String date1="今年";
    private static String date2="本月";
    private static String date3="今天";
    private static String startTime="";
    private static String endTime="";


    @Override
    public VoiceResultVo findVoiceResult(String result)  {
        startTime="";
        endTime="";

        VoiceResultVo vo=new VoiceResultVo();
        if (!StringUtils.isEmpty(result)){
            result = result.replaceAll("[\\\\pP‘’“”。，！!./,]", "");
            System.out.println(result);

            if (result.contains(date1)){
                result=result.replaceAll(date1,"");
                LocalDate today = LocalDate.now();

                // 获取当年的开始时间（1月1日 00:00:00）
                LocalDateTime startOfYear = today.withDayOfYear(1).atStartOfDay();
                // 获取当年的结束时间（12月31日 23:59:59.999）
                LocalDateTime endOfYear = today.withDayOfYear(today.lengthOfYear()).atTime(23, 59, 59, 999_999_999);
                // 格式化输
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                startTime=startOfYear.format(formatter);
                endTime=endOfYear.format(formatter);
            }

            if (result.contains(date2)){
                result=result.replaceAll(date2,"");
                LocalDate today = LocalDate.now();

                // 获取当年的开始时间（1月1日 00:00:00）
                LocalDateTime startOfMonth = today.withDayOfMonth(1).atStartOfDay();
                // 获取当年的结束时间（12月31日 23:59:59.999）
                LocalDateTime endOfMonth = today.withDayOfMonth(today.lengthOfMonth()).atTime(23, 59, 59, 999_999_999);
                // 格式化输
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                startTime=startOfMonth.format(formatter);
                endTime=endOfMonth.format(formatter);
            }

            if (result.contains(date3)){
                result=result.replaceAll(date3,"");
                LocalDate today = LocalDate.now();

                // 获取今天的开始时间（00:00:00）
                LocalDateTime startOfDay = today.atStartOfDay();

                // 获取今天的结束时间（23:59:59.999999999）
                LocalDateTime endOfDay = today.atTime(23, 59, 59, 999_999_999);

                // 格式化输出
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                startTime=startOfDay.format(formatter);
                endTime=endOfDay.format(formatter);
            }

            if (result.contains(run1)){
                result=result.replaceAll(run1,"");
            }
            if (result.contains(run2)){
                result=result.replaceAll(run2,"");
            }



            if (result.contains(type1)){
                int index = result.indexOf(type1);
                String strName = result.substring(0, index);
//                vo.setStrName(strName);
//                vo.setStrType(type1);
                type="SW";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }

                data.setStrName(data.getNm());
                data.setStrType(type1);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;

            }else if (result.contains(type2)){
//                    if (result.contains(key3)){
//                        int index1 = result.indexOf(key3);
//                        String startTime=result.substring(0, index1);
//                        startTime = startTime.replaceAll("年","-");
//                        startTime = startTime.replaceAll("月","-");
//                        startTime = startTime.replaceAll("日","");
//                        vo.setStartTime(startTime);
//                        int index2 = result.indexOf(key2);
//                        String endTime=result.substring(index1+1, index2);
//                        vo.setEndTime(endTime);
//                        if (result.contains(type2)){
//                            int index3 = result.indexOf(type2);
//                            String strName = result.substring(index2+1, index3);
//                            vo.setStrType(type2);
//                            vo.setStrName(strName);
//                        }
//                    }else {
                        int index = result.indexOf(type2);
                        String strName = result.substring(0, index);
//                        vo.setStrName(strName);
//                        vo.setStrType(type2);
                type="YL";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type2);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
//                    }
            }else if (result.contains(type3)){
                int index = result.indexOf(type3);
                String strName = result.substring(0, index);
//                vo.setStrName(strName);
//                vo.setStrType(type3);
                type="SP";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type3);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
            }
            else if (result.contains(type4)){
                int index = result.indexOf(type4);
//                String strName = result.substring(0, index);
                String strName = result;

//                vo.setStrName(strName);
//                vo.setStrType(type4);
                type="LL";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type4);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
            } else if (result.contains(type5)){
                int index = result.indexOf(type5);
                String strName = result.substring(0, index);
//                vo.setStrName(strName);
//                vo.setStrType(type4);
                type="BZ";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type5);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
            }else if (result.contains(type6)){
                int index = result.indexOf(type6);
                String strName = result.substring(0, index);

//                vo.setStrName(strName);
//                vo.setStrType(type4);
                type="HZ";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type6);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
            }else if (result.contains(type7)){
                int index = result.indexOf(type7);
                String strName = result.substring(0, index);
                strName=strName.replaceAll(type7,"");
//                vo.setStrName(strName);
//                vo.setStrType(type4);
                type="SN";
                VoiceResultVo data=commonMapper.findGisData(strName,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type7);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
            }else {
                type="BZ";
                VoiceResultVo data=commonMapper.findGisData(result,type);
                if (ObjectUtil.isEmpty(data)){
                    return new VoiceResultVo();
                }
                data.setStrName(data.getNm());
                data.setStrType(type5);
                data.setStartTime(startTime);
                data.setEndTime(endTime);
                return  data;
            }
        }
        return vo;
    }

    @Override
    public String getWaterLineByName(String name) {
        String waterLine=commonMapper.getWaterLineByName(name);
        String result=waterLine+"m";
        return result;

    }

    @Override
    public List<OpoUser> getUsers() {
        return commonMapper.getUsers();
    }

    @Override
    public OpoUser getLoginUsers() {
        OpoUser user=new OpoUser();
        Long userId= SecurityUtils.getUserId();
        String userName=SecurityUtils.getUsername();
        user.setUserName(userName);
        user.setUserId(String.valueOf(userId));
        return user;

    }

    @Override
    public Integer findAllPumpCount(String strCode) {
        return commonMapper.findAllPumpCount(strCode);
    }

    @Override
    public Integer findRunPumpCount(String strCode) {
        return commonMapper.findRunPumpCount(strCode);
    }

    @Override
    public Double findOneDataById(String code) {
        return commonMapper.findOneDataById(code);
    }

    @Override
    public List<WagaCodeVo> findWagaList() {
        return commonMapper.findWagaList();
    }

    @Override
    public List<GateCodeVo> findGateCodesByStr(String strCode) {
        return commonMapper.findGateCodesByStr(strCode);
    }
}