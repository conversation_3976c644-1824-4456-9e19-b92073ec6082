package com.jhdr.operation.service;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import com.jhdr.operation.entity.po.OpoMessageSendPo;
import com.jhdr.operation.entity.param.OpoMessageSendParam;
import com.jhdr.operation.entity.param.OpoMessageSendAddParam;
import com.jhdr.operation.entity.param.OpoMessageSendEditParam;
import com.jhdr.operation.entity.vo.OpoMessageSendVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.operation.entity.vo.OpoNoSendVo;

/**
 * 短信平台Service接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface IOpoMessageSendService extends IService<OpoMessageSendPo>
{

    /**
     * 查询短信平台列表
     *
     * @param opoMessageSend 短信平台
     * @return 短信平台集合
     */
    public List<OpoMessageSendVo> queryList(OpoMessageSendPo opoMessageSend);

    /**
     * 查询短信平台
     *
     * @param id 短信平台主键
     * @return 短信平台
     */
    public OpoMessageSendVo selectOpoMessageSendById(Integer id);

    /**
     * 查询短信平台列表
     *
     * @param opoMessageSend 短信平台
     * @return 短信平台集合
     */
    public List<OpoMessageSendVo> selectOpoMessageSendList(OpoMessageSendParam opoMessageSend);

    /**
     * 新增短信平台
     *
     * @param opoMessageSend 短信平台
     * @return 结果
     */
    public int insertOpoMessageSend(OpoMessageSendAddParam opoMessageSend) ;

    /**
     * 修改短信平台
     *
     * @param opoMessageSend 短信平台
     * @return 结果
     */
    public int updateOpoMessageSend(OpoMessageSendEditParam opoMessageSend);


    /**
     * 删除短信平台信息
     *
     * @param id 短信平台主键
     * @return 结果
     */
    public int deleteOpoMessageSendById(Integer id);


    List<OpoNoSendVo> getNoSendMessage();
}
