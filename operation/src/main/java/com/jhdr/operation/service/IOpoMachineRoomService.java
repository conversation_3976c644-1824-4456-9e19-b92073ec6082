package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.OpoMachineRoomPo;
import com.jhdr.operation.entity.param.OpoMachineRoomParam;
import com.jhdr.operation.entity.param.OpoMachineRoomAddParam;
import com.jhdr.operation.entity.param.OpoMachineRoomEditParam;
import com.jhdr.operation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 机房Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOpoMachineRoomService extends IService<OpoMachineRoomPo>
{

    /**
     * 查询机房列表
     *
     * @param opoMachineRoom 机房
     * @return 机房集合
     */
    public List<OpoMachineRoomVo> queryList(OpoMachineRoomPo opoMachineRoom);

    /**
     * 查询机房
     *
     * @param id 机房主键
     * @return 机房
     */
    public OpoMachineRoomVo selectOpoMachineRoomById(Long id);



    /**
     * 查询机房列表
     *
     * @param opoMachineRoom 机房
     * @return 机房集合
     */
    public List<OpoMachineRoomVo> selectOpoMachineRoomList(OpoMachineRoomParam opoMachineRoom);

    /**
     * 新增机房
     *
     * @param opoMachineRoom 机房
     * @return 结果
     */
    public int insertOpoMachineRoom(OpoMachineRoomAddParam opoMachineRoom);

    /**
     * 修改机房
     *
     * @param opoMachineRoom 机房
     * @return 结果
     */
    public int updateOpoMachineRoom(OpoMachineRoomEditParam opoMachineRoom);

    /**
     * 批量删除机房
     *
     * @param ids 需要删除的机房主键集合
     * @return 结果
     */
    public int deleteOpoMachineRoomByIds(Long[] ids);

    /**
     * 删除机房信息
     *
     * @param id 机房主键
     * @return 结果
     */
    public int deleteOpoMachineRoomById(Long id);


    MachineAnalyseVo findMachineAnalyse();

    MachineDeviceCountVo findMachineDeviceCount();

    MachineStateCountVo findMachineStateCountVo();

    MachineAlarmVo findMachineAlarm();

    List<OpoRoomValueVo> findMachineValue();

    OpoCabinetVo findCabinetValue(String name);

    List<String> findCabinetName();
}
