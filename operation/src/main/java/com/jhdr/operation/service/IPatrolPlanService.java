package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.PatrolPlanPo;
import com.jhdr.operation.entity.param.PatrolPlanParam;
import com.jhdr.operation.entity.param.PatrolPlanAddParam;
import com.jhdr.operation.entity.param.PatrolPlanEditParam;
import com.jhdr.operation.entity.vo.PatrolOnePlanVo;
import com.jhdr.operation.entity.vo.PatrolPlanVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 巡检计划Service接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface IPatrolPlanService extends IService<PatrolPlanPo>
{

    /**
     * 查询巡检计划列表
     *
     * @param patrolPlan 巡检计划
     * @return 巡检计划集合
     */
    public List<PatrolPlanVo> queryList(PatrolPlanPo patrolPlan);

    /**
     * 查询巡检计划
     *
     * @param id 巡检计划主键
     * @return 巡检计划
     */
    public PatrolOnePlanVo selectPatrolPlanById(Long id);

    /**
     * 查询巡检计划列表
     *
     * @param patrolPlan 巡检计划
     * @return 巡检计划集合
     */
    public List<PatrolPlanVo> selectPatrolPlanList(PatrolPlanParam patrolPlan);

    /**
     * 新增巡检计划
     *
     * @param patrolPlan 巡检计划
     * @return 结果
     */
    public int insertPatrolPlan(PatrolPlanAddParam patrolPlan);

    /**
     * 修改巡检计划
     *
     * @param patrolPlan 巡检计划
     * @return 结果
     */
    public int updatePatrolPlan(PatrolPlanEditParam patrolPlan);

    /**
     * 批量删除巡检计划
     *
     * @param ids 需要删除的巡检计划主键集合
     * @return 结果
     */
    public int deletePatrolPlanByIds(Long[] ids);

    /**
     * 删除巡检计划信息
     *
     * @param id 巡检计划主键
     * @return 结果
     */
    public int deletePatrolPlanById(Long id);

}
