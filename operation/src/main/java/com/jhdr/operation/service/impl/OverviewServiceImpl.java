package com.jhdr.operation.service.impl;

import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.mapper.OverviewMapper;
import com.jhdr.operation.service.OverviewService;
import com.jhdr.operation.utils.BigDecimalUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class OverviewServiceImpl implements OverviewService {
    @Autowired
    private OverviewMapper overviewMapper;


    @Override
    public List<OverStationVo> findStationCount() {
        List<OverStationVo> list=overviewMapper.findStationCount();

        for (OverStationVo vo:list) {
            Integer all=vo.getUseCount()+vo.getAlarmCount();
            vo.setAllCount(all);

        }
        return list;
    }

    @Override
    public List<OverStationAlarmVo> findStationAlarmCount() {
        List<OverStationAlarmVo> list =overviewMapper.findStationAlarmCount();
        return list;
    }

    @Override
    public List<OverStationAlarmVo> findDeviceAlarmCount() {
        List<OverStationAlarmVo> list =overviewMapper.findDeviceAlarmCount();
        return list;
    }



    @Override
    public List<OverStationVo> findRoomCount() {
        List<OverStationVo> list=overviewMapper.findRoomCount();
        return list;
    }


    @Override
    public List<OverAlarmTypeVo> findAlarmTypeCount() {
        List<OverAlarmTypeVo> list=overviewMapper.findAlarmTypeCount();
        Integer count=overviewMapper.findAllCount();
        if (!count.equals(0)){
            for (OverAlarmTypeVo vo:list) {
                String percent = BigDecimalUtils.reserve(Double.valueOf(vo.getCount()*100)/count);
                vo.setPercent(percent);
            }
        }else {
            OverAlarmTypeVo vo=new OverAlarmTypeVo("其他",0,"100");
            list.add(vo);
        }


        return list;
    }

    @Override
    public List<OverStationAlarmVo> findDeviceAlarmTop() {
        List<OverStationAlarmVo> list=overviewMapper.findDeviceAlarmTop();

        return list;
    }

    @Override
    public List<OverStationStateVo> findPustState() {
        List<OverStationStateVo> list=overviewMapper.findPustState();
        return list;
    }

    @Override
    public OverviewStateVo findOverview() {
        List<FindOverStateVo> list=overviewMapper.findOverState();
        OverviewStateVo vo=new OverviewStateVo();
        Optional<FindOverStateVo> pump = list.stream().filter(p -> p.getType().equals("泵站")).findFirst();
        Optional<FindOverStateVo> gate = list.stream().filter(p -> p.getType().equals("涵闸")).findFirst();
        Optional<FindOverStateVo> water = list.stream().filter(p -> p.getType().equals("水位")).findFirst();
        Optional<FindOverStateVo> flow = list.stream().filter(p -> p.getType().equals("流量")).findFirst();
        Optional<FindOverStateVo> video = list.stream().filter(p -> p.getType().equals("监控")).findFirst();
        Optional<FindOverStateVo> server1 = list.stream().filter(p -> p.getType().equals("数据采集前置系统自动化数据采集")).findFirst();//自动化采集
        Optional<FindOverStateVo> server2 = list.stream().filter(p -> p.getType().equals("数据采集前置系统水文数据采集")).findFirst();//水文采集
        Optional<FindOverStateVo> server3 = list.stream().filter(p -> p.getType().equals("数据采集前置系统共享数据采集")).findFirst();//共享采集
        Optional<FindOverStateVo> server4 = list.stream().filter(p -> p.getType().equals("数据库系统")).findFirst();//存储
        Optional<FindOverStateVo> gztz1 = list.stream().filter(p -> p.getType().equals("探针1")).findFirst();//自动化采集
        Optional<FindOverStateVo> gztz2 = list.stream().filter(p -> p.getType().equals("探针2")).findFirst();//自动化采集
        Optional<FindOverStateVo> waf = list.stream().filter(p -> p.getType().equals("WAF")).findFirst();//WAF
        Optional<FindOverStateVo> vpn = list.stream().filter(p -> p.getType().equals("VPN")).findFirst();//VPN
        Optional<FindOverStateVo> geli1 = list.stream().filter(p -> p.getType().equals("隔离网闸1")).findFirst();//隔离网闸1
        Optional<FindOverStateVo> geli2 = list.stream().filter(p -> p.getType().equals("隔离网闸2")).findFirst();//隔离网闸2
        Optional<FindOverStateVo> geli3 = list.stream().filter(p -> p.getType().equals("隔离网闸3")).findFirst();//隔离网闸2
        Optional<FindOverStateVo> change1 = list.stream().filter(p -> p.getType().equals("一期业务区48口交换机1")).findFirst();//一期业务区48口交换机1
        Optional<FindOverStateVo> change2 = list.stream().filter(p -> p.getType().equals("一期业务区48口交换机2")).findFirst();//一期业务区48口交换机2
        Optional<FindOverStateVo> change3 = list.stream().filter(p -> p.getType().equals("二期业务区48口交换机1")).findFirst();//二期业务区48口交换机1
        Optional<FindOverStateVo> change4 = list.stream().filter(p -> p.getType().equals("二期业务区48口交换机2")).findFirst();//二期业务区48口交换机2
        Optional<FindOverStateVo> luyou = list.stream().filter(p -> p.getType().equals("华为AR-3200路由")).findFirst();//华为AR-3200路由
        Optional<FindOverStateVo> fhq1 = list.stream().filter(p -> p.getType().equals("内网防火墙")).findFirst();//内网防火墙
        Optional<FindOverStateVo> fhq2 = list.stream().filter(p -> p.getType().equals("水利专网防火墙")).findFirst();//水利专网防火墙
        Optional<FindOverStateVo> fhq3 = list.stream().filter(p -> p.getType().equals("出口防火墙")).findFirst();//出口防火墙

        vo.setPump(pump.get().getStatus());
        vo.setGate(gate.get().getStatus());
        vo.setWater(water.get().getStatus());
        vo.setFlow(flow.get().getStatus());
        vo.setVideo(video.get().getStatus());
        vo.setGztz1(gztz1.get().getStatus());
        vo.setGztz2(gztz2.get().getStatus());
        vo.setGlwz1(geli1.get().getStatus());
        vo.setGlwz2(geli2.get().getStatus());
        vo.setGlwz3(geli3.get().getStatus());
        vo.setWaf(waf.get().getStatus());
        vo.setVpn(vpn.get().getStatus());
        vo.setServer1(server1.get().getStatus());
        vo.setServer2(server2.get().getStatus());
        vo.setServer3(server3.get().getStatus());
        vo.setServer4(server4.get().getStatus());

        vo.setChange1(change1.get().getStatus());
        vo.setChange2(change2.get().getStatus());
        vo.setChange3(change3.get().getStatus());
        vo.setChange4(change4.get().getStatus());

        vo.setLuyou(luyou.get().getStatus());

        vo.setFhq1(fhq1.get().getStatus());
        vo.setFhq2(fhq2.get().getStatus());
        vo.setFhq3(fhq3.get().getStatus());

        return vo;
    }

    @Override
    public OverviewWireVo findOverviewWire() {
        List<FindOverStateVo> list=overviewMapper.findOverviewWire();
        Optional<FindOverStateVo> state1 = list.stream().filter(p -> p.getType().equals("泵站控制到电信设备1")).findFirst();
        Optional<FindOverStateVo> state2 = list.stream().filter(p -> p.getType().equals("泵站量测到电信设备1")).findFirst();
        Optional<FindOverStateVo> state3 = list.stream().filter(p -> p.getType().equals("枢纽到电信设备1")).findFirst();
        Optional<FindOverStateVo> state4 = list.stream().filter(p -> p.getType().equals("电信设备1到隔离网闸2")).findFirst();
        Optional<FindOverStateVo> state5 = list.stream().filter(p -> p.getType().equals("隔离网闸2到隔离网闸3")).findFirst();
        Optional<FindOverStateVo> state6 = list.stream().filter(p -> p.getType().equals("隔离网闸1到节制闸控制")).findFirst();
        Optional<FindOverStateVo> state7 = list.stream().filter(p -> p.getType().equals("隔离网闸1到控制区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state8 = list.stream().filter(p -> p.getType().equals("闸泵站通讯服务器到控制区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state10 = list.stream().filter(p -> p.getType().equals("隔离网闸3到控制区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state11 = list.stream().filter(p -> p.getType().equals("隔离网闸3到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state14 = list.stream().filter(p -> p.getType().equals("业务区48口交换机1到waf")).findFirst();
        Optional<FindOverStateVo> state15 = list.stream().filter(p -> p.getType().equals("隔离网闸4到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state16 = list.stream().filter(p -> p.getType().equals("原业务区服务器到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state17 = list.stream().filter(p -> p.getType().equals("联想工作站到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state18 = list.stream().filter(p -> p.getType().equals("感知探针1到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state19 = list.stream().filter(p -> p.getType().equals("上桥三楼机房水利专网交换机到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state20 = list.stream().filter(p -> p.getType().equals("摄像头1到16口交换机")).findFirst();
        Optional<FindOverStateVo> state21 = list.stream().filter(p -> p.getType().equals("录像机1到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state22 = list.stream().filter(p -> p.getType().equals("上桥食堂硬盘录像机到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state23 = list.stream().filter(p -> p.getType().equals("对时系统到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state25 = list.stream().filter(p -> p.getType().equals("省水利厅到电信设备2")).findFirst();
        Optional<FindOverStateVo> state26 = list.stream().filter(p -> p.getType().equals("电信设备2到华为AR-3200路由")).findFirst();
        Optional<FindOverStateVo> state27 = list.stream().filter(p -> p.getType().equals("华为AR-3200路由到防火墙")).findFirst();
        Optional<FindOverStateVo> state28 = list.stream().filter(p -> p.getType().equals("上桥食堂监控交换机到上桥食堂硬盘录像机")).findFirst();
        Optional<FindOverStateVo> state29 = list.stream().filter(p -> p.getType().equals("出口防火墙到互联网区48口交换机")).findFirst();


        Optional<FindOverStateVo> state30 = list.stream().filter(p -> p.getType().equals("互联网区48口交换机到互联网前置服务器区")).findFirst();
        Optional<FindOverStateVo> state32 = list.stream().filter(p -> p.getType().equals("感知探针2到互联网区48口交换机")).findFirst();
        Optional<FindOverStateVo> state33 = list.stream().filter(p -> p.getType().equals("互联网区48口交换机到互联网办公终端区")).findFirst();
        Optional<FindOverStateVo> state35 = list.stream().filter(p -> p.getType().equals("vpn到16口楼层交换机1")).findFirst();
        Optional<FindOverStateVo> state37 = list.stream().filter(p -> p.getType().equals("隔离网闸4到互联网区48口交换机")).findFirst();
        Optional<FindOverStateVo> state38 = list.stream().filter(p -> p.getType().equals("泵站采集到云")).findFirst();
        Optional<FindOverStateVo> state39 = list.stream().filter(p -> p.getType().equals("泵站摄像头到云")).findFirst();
        Optional<FindOverStateVo> state40 = list.stream().filter(p -> p.getType().equals("云到电信设备3")).findFirst();
        Optional<FindOverStateVo> state41 = list.stream().filter(p -> p.getType().equals("电信设备3到出口防火墙")).findFirst();
        OverviewWireVo wire=new OverviewWireVo();
        wire.setState1(state1.get().getStatus());
        wire.setState2(state2.get().getStatus());
        wire.setState3(state3.get().getStatus());
        wire.setState4(state4.get().getStatus());
        wire.setState5(state5.get().getStatus());
        wire.setState6(state6.get().getStatus());
        wire.setState7(state7.get().getStatus());
        wire.setState8(state8.get().getStatus());

        wire.setState10(state10.get().getStatus());
        wire.setState11(state11.get().getStatus());


        wire.setState14(state14.get().getStatus());
        wire.setState15(state15.get().getStatus());
        wire.setState16(state16.get().getStatus());

        wire.setState17(state17.get().getStatus());
        wire.setState18(state18.get().getStatus());
        wire.setState19(state19.get().getStatus());
        wire.setState20(state20.get().getStatus());
        wire.setState21(state21.get().getStatus());
        wire.setState22(state22.get().getStatus());
        wire.setState23(state23.get().getStatus());

        wire.setState25(state25.get().getStatus());
        wire.setState26(state26.get().getStatus());
        wire.setState27(state27.get().getStatus());
        wire.setState28(state28.get().getStatus());
        wire.setState29(state29.get().getStatus());
        wire.setState30(state30.get().getStatus());

        wire.setState32(state32.get().getStatus());
        wire.setState33(state33.get().getStatus());

        wire.setState35(state35.get().getStatus());

        wire.setState37(state37.get().getStatus());
        wire.setState38(state38.get().getStatus());
        wire.setState39(state39.get().getStatus());
        wire.setState40(state40.get().getStatus());
        wire.setState41(state41.get().getStatus());


        return wire;
    }

    @Override
    public OverviewNetWireVo findNetWire() {
        List<FindOverStateVo> list=overviewMapper.findOverviewWire();
        Optional<FindOverStateVo> state1a1 = list.stream().filter(p -> p.getType().equals("泵站控制到电信设备1")).findFirst();
        Optional<FindOverStateVo> state1a2 = list.stream().filter(p -> p.getType().equals("泵站量测到电信设备1")).findFirst();
        Optional<FindOverStateVo> state1a3 = list.stream().filter(p -> p.getType().equals("枢纽到电信设备1")).findFirst();
        Optional<FindOverStateVo> state2 = list.stream().filter(p -> p.getType().equals("电信设备1到隔离网闸2")).findFirst();
        Optional<FindOverStateVo> state3 = list.stream().filter(p -> p.getType().equals("隔离网闸2到48口核心交换机")).findFirst();

        Optional<FindOverStateVo> state4 = list.stream().filter(p -> p.getType().equals("隔离网闸1到节制闸控制")).findFirst();
        Optional<FindOverStateVo> state5 = list.stream().filter(p -> p.getType().equals("隔离网闸1到控制区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state6a1 = list.stream().filter(p -> p.getType().equals("闸泵站通讯服务器到控制区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state6a2 = list.stream().filter(p -> p.getType().equals("视频服务平台到控制区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state6a3 = list.stream().filter(p -> p.getType().equals("通讯前置机到控制区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state7= list.stream().filter(p -> p.getType().equals("控制区48口交换机1到48口核心交换机")).findFirst();
        Optional<FindOverStateVo> state8 = list.stream().filter(p -> p.getType().equals("48口核心交换机到隔离网闸3")).findFirst();

        Optional<FindOverStateVo> state9 = list.stream().filter(p -> p.getType().equals("隔离网闸3到控制区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state10a1 = list.stream().filter(p -> p.getType().equals("摄像头1到16口交换机")).findFirst();
        Optional<FindOverStateVo> state10a2 = list.stream().filter(p -> p.getType().equals("16口交换机到上桥抽水站水利专用交换机")).findFirst();

        Optional<FindOverStateVo> state11a1 = list.stream().filter(p -> p.getType().equals("省水利厅到电信设备2")).findFirst();
        Optional<FindOverStateVo> state11a2 = list.stream().filter(p -> p.getType().equals("电信设备2到华为AR-3200路由")).findFirst();
        Optional<FindOverStateVo> state11a3 = list.stream().filter(p -> p.getType().equals("华为AR-3200路由到防火墙")).findFirst();
        Optional<FindOverStateVo> state11a4 = list.stream().filter(p -> p.getType().equals("防火墙到入侵防御")).findFirst();
        Optional<FindOverStateVo> state11a5 = list.stream().filter(p -> p.getType().equals("入侵防御到防毒墙")).findFirst();
        Optional<FindOverStateVo> state11a6 = list.stream().filter(p -> p.getType().equals("防毒墙到上桥三楼机房水利专网交换机")).findFirst();
        Optional<FindOverStateVo> state11a7 = list.stream().filter(p -> p.getType().equals("上桥三楼机房水利专网交换机到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state12a1 = list.stream().filter(p -> p.getType().equals("感知探针1到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state12a2 = list.stream().filter(p -> p.getType().equals("业务区48口交换机1到waf")).findFirst();

        Optional<FindOverStateVo> state13a3 = list.stream().filter(p -> p.getType().equals("原业务区服务器到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state13a4 = list.stream().filter(p -> p.getType().equals("上桥节制闸水情服务器到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state14a3 = list.stream().filter(p -> p.getType().equals("堡垒机到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state15a3 = list.stream().filter(p -> p.getType().equals("新增存储区服务器到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state16a1 = list.stream().filter(p -> p.getType().equals("新建6路监控到门卫室16口交换机")).findFirst();
        Optional<FindOverStateVo> state16a2 = list.stream().filter(p -> p.getType().equals("门卫室原有硬盘录像机到门卫室16口交换机")).findFirst();
        Optional<FindOverStateVo> state16a3 = list.stream().filter(p -> p.getType().equals("门卫室原有硬盘录像机到门卫室16口交换机")).findFirst();

        Optional<FindOverStateVo> state17a1 = list.stream().filter(p -> p.getType().equals("上桥食堂监控交换机到上桥食堂硬盘录像机")).findFirst();
        Optional<FindOverStateVo> state17a2 = list.stream().filter(p -> p.getType().equals("上桥食堂硬盘录像机到业务区48口交换机1")).findFirst();


        Optional<FindOverStateVo> state18a1 = list.stream().filter(p -> p.getType().equals("联想工作站到业务区48口交换机1")).findFirst();
        Optional<FindOverStateVo> state18a3 = list.stream().filter(p -> p.getType().equals("隔离网闸4到业务区48口交换机1")).findFirst();

        Optional<FindOverStateVo> state19 = list.stream().filter(p -> p.getType().equals("隔离网闸4到互联网区48口交换机")).findFirst();




        Optional<FindOverStateVo> state20a1 = list.stream().filter(p -> p.getType().equals("泵站采集到云")).findFirst();
        Optional<FindOverStateVo> state20a2 = list.stream().filter(p -> p.getType().equals("泵站摄像头到云")).findFirst();
        Optional<FindOverStateVo> state20a3 = list.stream().filter(p -> p.getType().equals("云到电信设备3")).findFirst();
        Optional<FindOverStateVo> state20a4 = list.stream().filter(p -> p.getType().equals("电信设备3到出口防火墙")).findFirst();
        Optional<FindOverStateVo> state20a5 = list.stream().filter(p -> p.getType().equals("出口防火墙到互联网区48口交换机")).findFirst();

        Optional<FindOverStateVo> state21 = list.stream().filter(p -> p.getType().equals("感知探针2到互联网区48口交换机")).findFirst();

        Optional<FindOverStateVo> state22a1 = list.stream().filter(p -> p.getType().equals("无线接入区到上网行为管理")).findFirst();
        Optional<FindOverStateVo> state22a2 = list.stream().filter(p -> p.getType().equals("上网行为管理到互联网区48口交换机")).findFirst();

        Optional<FindOverStateVo> state23 = list.stream().filter(p -> p.getType().equals("互联网区48口交换机到互联网办公终端区")).findFirst();

        Optional<FindOverStateVo> state24= list.stream().filter(p -> p.getType().equals("上桥食堂互联网到互联网区48口交换机")).findFirst();

        Optional<FindOverStateVo> state25a1= list.stream().filter(p -> p.getType().equals("16口楼层交换机1到互联网区48口交换机")).findFirst();
        Optional<FindOverStateVo> state25a3= list.stream().filter(p -> p.getType().equals("楼层互联网电脑到16口楼层交换机1")).findFirst();
        Optional<FindOverStateVo> state25a4= list.stream().filter(p -> p.getType().equals("调度中心互联网电脑到16口楼层交换机1")).findFirst();
        Optional<FindOverStateVo> state25a5= list.stream().filter(p -> p.getType().equals("vpn到16口楼层交换机1")).findFirst();



        OverviewNetWireVo wire=new OverviewNetWireVo();
        wire.setState1a1(state1a1.get().getStatus());
        wire.setState1a2(state1a2.get().getStatus());
        wire.setState1a3(state1a3.get().getStatus());
        wire.setState2(state2.get().getStatus());
        wire.setState3(state3.get().getStatus());
        wire.setState4(state4.get().getStatus());
        wire.setState5(state5.get().getStatus());
        wire.setState6a1(state6a1.get().getStatus());
        wire.setState6a2(state6a2.get().getStatus());
        wire.setState6a3(state6a3.get().getStatus());


        wire.setState7(state7.get().getStatus());
        wire.setState8(state8.get().getStatus());
        wire.setState9(state9.get().getStatus());

        wire.setState10a1(state10a1.get().getStatus());
        wire.setState10a2(state10a2.get().getStatus());

        wire.setState11a1(state11a1.get().getStatus());
        wire.setState11a2(state11a2.get().getStatus());
        wire.setState11a3(state11a3.get().getStatus());
        wire.setState11a4(state11a4.get().getStatus());
        wire.setState11a5(state11a5.get().getStatus());
        wire.setState11a6(state11a6.get().getStatus());
        wire.setState11a7(state11a7.get().getStatus());

        wire.setState12a1(state12a1.get().getStatus());
        wire.setState12a2(state12a2.get().getStatus());



        wire.setState13a3(state13a3.get().getStatus());
        wire.setState13a4(state13a4.get().getStatus());


        wire.setState14a3(state14a3.get().getStatus());



        wire.setState15a3(state15a3.get().getStatus());

        wire.setState16a1(state16a1.get().getStatus());
        wire.setState16a2(state16a2.get().getStatus());
        wire.setState16a3(state16a3.get().getStatus());

        wire.setState17a1(state17a1.get().getStatus());
        wire.setState17a2(state17a2.get().getStatus());


        wire.setState18a1(state18a1.get().getStatus());

        wire.setState18a3(state18a3.get().getStatus());


        wire.setState19(state19.get().getStatus());



        wire.setState20a1(state20a1.get().getStatus());
        wire.setState20a2(state20a2.get().getStatus());
        wire.setState20a3(state20a3.get().getStatus());
        wire.setState20a4(state20a4.get().getStatus());
        wire.setState20a5(state20a5.get().getStatus());

        wire.setState21(state21.get().getStatus());

        wire.setState22a1(state22a1.get().getStatus());
        wire.setState22a2(state22a2.get().getStatus());

        wire.setState23(state23.get().getStatus());

        wire.setState24(state24.get().getStatus());

        wire.setState25a1(state25a1.get().getStatus());

        wire.setState25a3(state25a3.get().getStatus());
        wire.setState25a4(state25a4.get().getStatus());
        wire.setState25a5(state25a5.get().getStatus());


        return wire;
    }

}
