package com.jhdr.operation.service;




import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.entity.vo.*;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

public interface CommonService {

    List<FindWaterLineVo> findAllWaterLine();

    FindWaterLineVo findLineDataByCodeTime(String code, Date addTime);

    int insertWaterLine(String code, Double waterLine, Date addTime);

    List<FindFlowVo> findAllFlow();

    FindFlowVo findFlowDataByCodeTime(String code, Date addTime);

    int insertFlow(String code, Double flow, Date addTime);

    List<PumpRealVo> findAllPumpReal(String pid);

    JhPumpDtlRPo findNewPumpDtl(String pid, String id, Date addTime);

    List<String> findAllPid();


    GateRealVo findGateById(String pid);

    GateRealVo findGate1ById(String pid);
    GateRealVo findGate2ById(String pid);

    GateRealVo find1GateById(String id);


    GateRealVo findGateNameType(String name, String type);

    JhSlcrsDtlRPo findSlcrsByPid(String id,String pid, Date addTime);


    Integer findAllPumoState(String pid);

    Integer findRunPumpState(String pid);

    Integer findRunGateCount(String name);

    String exchangePmpcdByid(String id);

    String exchangePrjnmcdByid(String id);

    String exchangeGpcdByid(String id);

    Double findWaterLineByData(String name, String waterName);

    GateRealVo findWaterLineByCode(String code);

    StWasRPo findRealWasData(String stcd, Date addTime);

    StRiverRPo findRealRiverData(String stcd, Date addTime);

    String findJhCodeByCode(String code);

    List<String> findShareCds(String type);

    StWasRPo findUpWaterByCode(String upcd);

    StRiverRPo findDownWaterByCode(String downCd);

    List<PustCodeVo> findPustPid();


    StWasRPo findShareUpLineByCode(String stcd);

    String findWdwcdByStrCode(String strCode);

    String findWupcdByStrCode(String strCode);

    VoiceResultVo findVoiceResult(String result) ;

    String getWaterLineByName(String name);

    List<OpoUser> getUsers();

    OpoUser getLoginUsers();

    Integer findAllPumpCount(String strCode);

    Integer findRunPumpCount(String strCode);

    Double findOneDataById(String code);


    List<WagaCodeVo> findWagaList();

    List<GateCodeVo> findGateCodesByStr(String strCode);
}
