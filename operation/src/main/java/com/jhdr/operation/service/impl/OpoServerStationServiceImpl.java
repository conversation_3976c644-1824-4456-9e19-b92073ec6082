package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.operation.entity.vo.DeviceIpMonVo;
import com.jhdr.operation.entity.vo.ServerStateVo;
import com.jhdr.operation.entity.vo.StateCountVo;
import com.jhdr.operation.utils.BigDecimalUtils;
import io.swagger.models.auth.In;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoServerStationPo;
import com.jhdr.operation.entity.param.OpoServerStationParam;
import com.jhdr.operation.entity.param.OpoServerStationAddParam;
import com.jhdr.operation.entity.param.OpoServerStationEditParam;
import com.jhdr.operation.entity.vo.OpoServerStationVo;
import com.jhdr.operation.mapper.OpoServerStationMapper;
import com.jhdr.operation.service.IOpoServerStationService;

import java.util.ArrayList;

import java.util.Date;
import java.util.List;

/**
 * 运维监控-服务器管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class OpoServerStationServiceImpl extends ServiceImpl<OpoServerStationMapper, OpoServerStationPo> implements IOpoServerStationService {

    @Override
    public List<OpoServerStationVo> queryList(OpoServerStationPo opoServerStation) {
        LambdaQueryWrapper<OpoServerStationPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoServerStation.getDeviceName())){
            lqw.like(OpoServerStationPo::getDeviceName ,opoServerStation.getDeviceName());
        }
        if (StringUtils.isNotBlank(opoServerStation.getDeviceType())){
            lqw.eq(OpoServerStationPo::getDeviceType ,opoServerStation.getDeviceType());
        }
        if (StringUtils.isNotBlank(opoServerStation.getRunState())){
            lqw.eq(OpoServerStationPo::getRunState ,opoServerStation.getRunState());
        }
        List<OpoServerStationVo> opoServerStationVos= BeanUtil.copyToList(this.list(lqw), OpoServerStationVo.class);
        return opoServerStationVos;
    }
    /**
     * 查询运维监控-服务器管理
     *
     * @param id 运维监控-服务器管理主键
     * @return 运维监控-服务器管理
     */
    @Override
    public OpoServerStationVo selectOpoServerStationById(Integer id)
    {
        return baseMapper.selectOpoServerStationById(id);
    }

    /**
     * 查询运维监控-服务器管理列表
     *
     * @param opoServerStation 运维监控-服务器管理
     * @return 运维监控-服务器管理
     */
    @Override
    public List<OpoServerStationVo> selectOpoServerStationList(OpoServerStationParam opoServerStation) {
        List<OpoServerStationVo> list=baseMapper.selectOpoServerStationList(opoServerStation);
        for (OpoServerStationVo vo:list){
            String ncUse= BigDecimalUtils.reserve(Double.valueOf(vo.getNcUse().substring(0, vo.getNcUse().length() - 1)))+"%";
            String jhqUse= BigDecimalUtils.reserve(Double.valueOf(vo.getJhqUse().substring(0, vo.getJhqUse().length() - 1)))+"%";
            String cpUse= BigDecimalUtils.reserve(Double.valueOf(vo.getCpUse().substring(0, vo.getCpUse().length() - 1)))+"%";
            String cpuUse= BigDecimalUtils.reserve(Double.valueOf(vo.getCpuUse().substring(0, vo.getCpuUse().length() - 1)))+"%";
            vo.setNcUse(ncUse);
            vo.setJhqUse(jhqUse);
            vo.setCpUse(cpUse);
            vo.setCpuUse(cpuUse);
        }
        return list;
    }
    private  static  String status1="正常";
    private  static  String status2="异常";
    @Override
    public StateCountVo getServerStateAmount() {
        Integer useAmount=baseMapper.getServerStateAmount(status1);
        Integer alarmAmount=baseMapper.getServerStateAmount(status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }

    /**
     * 新增运维监控-服务器管理
     *
     * @param opoServerStationAddParam 运维监控-服务器管理
     * @return 结果
     */
    @Override
    public int insertOpoServerStation(OpoServerStationAddParam opoServerStationAddParam)
    {

        OpoServerStationPo opoServerStation=new OpoServerStationPo();
        BeanUtil.copyProperties(opoServerStationAddParam,opoServerStation);
        opoServerStation.setUpdateTime(new Date());
        opoServerStation.setIsDelete(0);
        return baseMapper.insert(opoServerStation);
    }

    /**
     * 修改运维监控-服务器管理
     *
     * @param opoServerStationEditParam 运维监控-服务器管理
     * @return 结果
     */
    @Override
    public int updateOpoServerStation(OpoServerStationEditParam opoServerStationEditParam)
    {
        OpoServerStationPo opoServerStation=new OpoServerStationPo();
        BeanUtil.copyProperties(opoServerStationEditParam,opoServerStation);
        return baseMapper.updateById(opoServerStation);
    }

    /**
     * 批量删除运维监控-服务器管理
     *
     * @param ids 需要删除的运维监控-服务器管理主键
     * @return 结果
     */
    @Override
    public int deleteOpoServerStationByIds(Long[] ids)
    {
        return baseMapper.deleteOpoServerStationByIds(ids);
    }

    /**
     * 删除运维监控-服务器管理信息
     *
     * @param id 运维监控-服务器管理主键
     * @return 结果
     */
    @Override
    public int deleteOpoServerStationById(Long id)
    {
        return baseMapper.deleteOpoServerStationById(id);
    }

    @Override
    public List<ServerStateVo> findServer(String runState) {
        List<ServerStateVo> list= baseMapper.findServer(runState);
        return list;
    }

    @Override
    public int updateStateById(Integer id, String runState, String type,String time) {
        return baseMapper.updateStateById(id,runState,type,time);
    }

    @Override
    public List<DeviceIpMonVo> findWorkByState(String state) {
        return baseMapper.findWorkByState(state);
    }

    @Override
    public void updateNetWork(Integer id, String time, String state, String alarmType) {
        baseMapper.updateNetWork(id,time,state,alarmType);
    }
}
