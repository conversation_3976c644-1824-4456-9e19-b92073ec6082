package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.OpoGoodsDetailPo;
import com.jhdr.operation.entity.param.OpoGoodsDetailParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailAddParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailEditParam;
import com.jhdr.operation.entity.vo.OpoGoodsDetailVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 物资详情Service接口
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
public interface IOpoGoodsDetailService extends IService<OpoGoodsDetailPo>
{

    /**
     * 查询物资详情列表
     *
     * @param opoGoodsDetail 物资详情
     * @return 物资详情集合
     */
    public List<OpoGoodsDetailVo> queryList(OpoGoodsDetailPo opoGoodsDetail);

    /**
     * 查询物资详情
     *
     * @param id 物资详情主键
     * @return 物资详情
     */
    public OpoGoodsDetailVo selectOpoGoodsDetailById(Integer id);

    /**
     * 查询物资详情列表
     *
     * @param opoGoodsDetail 物资详情
     * @return 物资详情集合
     */
    public List<OpoGoodsDetailVo> selectOpoGoodsDetailList(OpoGoodsDetailParam opoGoodsDetail);

    /**
     * 新增物资详情
     *
     * @param opoGoodsDetail 物资详情
     * @return 结果
     */
    public int insertOpoGoodsDetail(OpoGoodsDetailAddParam opoGoodsDetail);

    /**
     * 修改物资详情
     *
     * @param opoGoodsDetail 物资详情
     * @return 结果
     */
    public int updateOpoGoodsDetail(OpoGoodsDetailEditParam opoGoodsDetail);

    /**
     * 批量删除物资详情
     *
     * @param ids 需要删除的物资详情主键集合
     * @return 结果
     */
    public int deleteOpoGoodsDetailByIds(Integer[] ids);

    /**
     * 删除物资详情信息
     *
     * @param id 物资详情主键
     * @return 结果
     */
    public int deleteOpoGoodsDetailById(Integer id);

}
