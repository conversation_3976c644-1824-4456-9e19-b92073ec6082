package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dahuatech.hutool.http.HttpUtil;
import com.dahuatech.hutool.http.Method;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import com.dahuatech.icc.oauth.model.v202010.OauthConfigUserPwdInfo;
import com.dahuatech.icc.oauth.utils.HttpUtils;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.po.OpoJhomVmPo;
import com.jhdr.operation.entity.vo.*;
import com.jhdr.operation.mapper.OpoJhomVmMapper;
import lombok.extern.java.Log;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoVideoStationPo;
import com.jhdr.operation.mapper.OpoVideoStationMapper;
import com.jhdr.operation.service.IOpoVideoStationService;

import java.io.IOException;
import java.util.*;

/**
 * 运维监控-视频站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
@Log
public class OpoVideoStationServiceImpl extends ServiceImpl<OpoVideoStationMapper, OpoVideoStationPo> implements IOpoVideoStationService {

    @Autowired
    private OpoJhomVmMapper opoJhomVmMapper;



    @Override
    public List<OpoVideoStationVo> queryList(OpoVideoStationPo opoVideoStation) {
        LambdaQueryWrapper<OpoVideoStationPo> lqw = Wrappers.lambdaQuery();
        List<OpoVideoStationVo> opoVideoStationVos= BeanUtil.copyToList(this.list(lqw), OpoVideoStationVo.class);
        return opoVideoStationVos;
    }



    /**
     * 查询运维监控-视频站点
     *
     * @param cd 运维监控-视频站点主键
     * @return 运维监控-视频站点
     */
    @Override
    public OpoVideoStationVo selectOpoVideoStationById(String cd)
    {
        return baseMapper.selectOpoVideoStationById(cd);
    }

    /**
     * 查询运维监控-视频站点列表
     *
     * @param opoVideoStation 运维监控-视频站点
     * @return 运维监控-视频站点
     */
    @Override
    public List<OpoVideoStationVo> selectOpoVideoStationList(OpoVideoStationParam opoVideoStation)
    {
        List<OpoVideoStationVo> list= baseMapper.selectOpoVideoStationList(opoVideoStation);
        return list;
    }
    private  static  String status1="正常";
    private  static  String status2="异常";
    @Override
    public StateCountVo getVideoStateAmount(String addvcd) {
        Integer useAmount=baseMapper.getVideoStateAmount(addvcd,status1);
        Integer alarmAmount=baseMapper.getVideoStateAmount(addvcd,status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }
    /**
     * 新增运维监控-视频站点
     *
     * @param opoVideoStationAddParam 运维监控-视频站点
     * @return 结果
     */
    @Override
    public int insertOpoVideoStation(OpoVideoStationAddParam opoVideoStationAddParam)
    {

        OpoVideoStationPo opoVideoStation=new OpoVideoStationPo();
        BeanUtil.copyProperties(opoVideoStationAddParam,opoVideoStation);
        return baseMapper.insert(opoVideoStation);
    }

    /**
     * 修改运维监控-视频站点
     *
     * @param opoVideoStationEditParam 运维监控-视频站点
     * @return 结果
     */
    @Override
    public int updateOpoVideoStation(OpoVideoStationEditParam opoVideoStationEditParam)
    {
        OpoVideoStationPo opoVideoStation=new OpoVideoStationPo();
        BeanUtil.copyProperties(opoVideoStationEditParam,opoVideoStation);
        return baseMapper.updateById(opoVideoStation);
    }

    /**
     * 批量删除运维监控-视频站点
     *
     * @param ids 需要删除的运维监控-视频站点主键
     * @return 结果
     */
    @Override
    public int deleteOpoVideoStationByIds(Long[] ids)
    {
        return baseMapper.deleteOpoVideoStationByIds(ids);
    }

    /**
     * 删除运维监控-视频站点信息
     *
     * @param id 运维监控-视频站点主键
     * @return 结果
     */
    @Override
    public int deleteOpoVideoStationById(Long id)
    {
        return baseMapper.deleteOpoVideoStationById(id);
    }






    @Override
    public List<String> getStatus() {
        List<String> list=new ArrayList<>();
        list.add("正常");
        list.add("异常");
        return list;
    }

    @Override
    public String testVideoState() {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("29702023");  // 秘钥appkey
        config.setAppSecret("YzjDOjGl4h0MeWQcO3ll");// 秘钥appSecret
        final String getCamsApi = "/artemis" + "/api/nms/v1/online/camera/get";
        Map<String, Object> paramMap = new HashMap<String, Object>();// post请求Form表单参数
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "1000");
        paramMap.put("regionId", "root000000");
        paramMap.put("includeSubNode", "1");
        List<String> indexCodes=baseMapper.findAllCodes();
        paramMap.put("indexCodes", indexCodes);
        paramMap.put("status", "1");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };

        String CamerasResult = null;


        try {
            CamerasResult = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
            JSONObject jsonObject = JSONObject.parseObject(CamerasResult);
            String data = jsonObject.getString("data");
            JSONObject jsonObject1 = JSONObject.parseObject(data);
            String list = jsonObject1.getString("list");
            JSONArray json = JSONArray.parseArray(list);
            if (json.size() > 0) {
                for (int i = 0; i < json.size(); i++) {
                    JSONObject job = (JSONObject) json.get(i);
                    String indexCode = job.getString("indexCode");
                    String online = job.getString("online");
                    String cn = job.getString("cn");
                    if (online.equals("1")){
                        baseMapper.updateUpdateTime(indexCode);
//                        String name=baseMapper.selectNmByIndexCode(indexCode);
//                        log.info(name+":"+indexCode);
                    }
//                    else {
//                        String name=baseMapper.selectNmByIndexCode(indexCode);
//                        log.info("不在线"+name+":"+indexCode);
//                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return CamerasResult.toString();
    }



    @Override
    public List<OpoVideoStateVo> findCodeByState(String state) {
        return baseMapper.findCodeByState(state);
    }


    @Override
    public int updateState(String code, String status,String time) {


        return baseMapper.updateState(code,status,time);
    }

    @Override
    public void updateDiscernByChannel(int nChannelID, String picture) {
        baseMapper.updateDiscernByChannel(nChannelID,picture);
    }

    private String state1="正常";
    private String state2="异常";
    @Override
    public Object getDHState()  {
        List<OpoDHVideoStateVo> codes=baseMapper.selectDHVideoChannel(state1);

        for (OpoDHVideoStateVo vo:codes){
            String channel=vo.getDhChannel();
            String result= HttpUtil.get("http://10.34.188.156:10001/api/play/start/"+channel);
//            log.info("result："+result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            String code = jsonObject.getString("code");
//            log.info("code:"+code);
            if (code.equals("0")){
                baseMapper.updateUpdateTimeByCd(vo.getCd());
            }
        }
        return null;
    }




    @Override
    public List<OpoDHVideoStateVo> selectDHChannel(String state) {
        return  baseMapper.selectDHVideoChannel(state);
    }

    @Override
    public void updateStateByCd(String cd, String state, String time, String content) {
        baseMapper.updateStateByCd(cd,state,time,content);
    }

    @Override
    public List<OpoHKWSVideoStateVo> selectHkCodes(String state) {
        return  baseMapper.selectHKWSCode(state);
    }

    @Override
    public void isMon(IsMonVideoParam param) {
     baseMapper.isMon(param.getCd(),param.getIsMon());
    }


    @Override
    public int updateByNm(String name, String code) {
        return baseMapper.updateCodeByNm(name,code);
    }

    @Override
    public String getBianma() {

        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("29702023");  // 秘钥appkey
        config.setAppSecret("YzjDOjGl4h0MeWQcO3ll");// 秘钥appSecret
        final String getCamsApi = "/artemis" + "/api/resource/v1/cameras";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "1000");
        paramMap.put("regionIndexCode", "root000000");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };

        String CamerasResult = null;
        try {
            CamerasResult = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.parseObject(CamerasResult);
        String data = jsonObject.getString("data");
        JSONObject jsonObject1 = JSONObject.parseObject(data);
        String list = jsonObject1.getString("list");

        JSONArray json = JSONArray.parseArray(list);
//		JSONObject jsonObject2 = JSON.parseObject(list);
        if (json.size() > 0) {
            for (int i = 0; i < json.size(); i++) {
                JSONObject job = (JSONObject) json.get(i);

                String name = job.getString("cameraName");
                String code = job.getString("cameraIndexCode");
                String cameraType=job.getString("cameraType");
                String channelNo=job.getString("channelNo");
                String channelType=job.getString("channelType");
                String regionIndexCode = job.getString("regionIndexCode");
                String encodeDevIndexCode = job.getString("encodeDevIndexCode");
                String longitude = job.getString("longitude");
                String latitude = job.getString("latitude");
                OpoJhomVmPo po=new OpoJhomVmPo(null,code,name,cameraType,channelNo,channelType,regionIndexCode,encodeDevIndexCode,longitude,latitude);
//                opoJhomVmMapper.insert(po);

//                int rows = baseMapper.updateCodeByNm(name, code);

                log.info(name + ":" + code);

            }

        }

        return json.toString();
    }

    @Override
    public String getVideoStreamByCode(String cameraCode) throws Exception {

        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("29702023");  // 秘钥appkey
        config.setAppSecret("YzjDOjGl4h0MeWQcO3ll");// 秘钥appSecret
        final String getCamsApi = "/artemis" + "/api/video/v2/cameras/previewURLs";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", cameraCode);
        paramMap.put("streamType", "0");
        paramMap.put("protocol", "hls");
        paramMap.put("transmode", "1");
        paramMap.put("expand", "transcode=0");
        paramMap.put("streamform", "ps");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        String json= ArtemisHttpUtil.doPostStringArtemis(config,path, body, null, null, "application/json");

        JSONObject jsonObject = JSONObject.parseObject(json);
        String data = jsonObject.getString("data");

        JSONObject jsonObject1 = JSONObject.parseObject(data);
        String url = "http://*************"+jsonObject1.getString("url").substring(16);

        return url;
    }



    private HttpGet httpGet;
    private CloseableHttpClient httpClient;
    public static final String CONTENT_TYPE = "Content-Type";
    public String sendGet(String url) throws IOException {
        httpClient = HttpClients.createDefault();
        httpGet = new HttpGet(url);
        CloseableHttpResponse response = httpClient.execute(httpGet);
        String resp;
        try {
            HttpEntity entity = response.getEntity();
            resp = EntityUtils.toString(entity, "utf-8");
            EntityUtils.consume(entity);
        } finally {
            response.close();
        }
        LoggerFactory.getLogger(getClass()).info(" resp:{}", resp);
        return resp;
    }






}
