package com.jhdr.operation.service;

import java.util.List;

import com.jhdr.operation.entity.param.PatrolTaskDetailAddParam;
import com.jhdr.operation.entity.po.PatrolTaskPo;
import com.jhdr.operation.entity.param.PatrolTaskParam;
import com.jhdr.operation.entity.param.PatrolTaskAddParam;
import com.jhdr.operation.entity.param.PatrolTaskEditParam;
import com.jhdr.operation.entity.vo.PatrolOneTaskVo;
import com.jhdr.operation.entity.vo.PatrolTaskResultVo;
import com.jhdr.operation.entity.vo.PatrolTaskVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 巡检任务Service接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface IPatrolTaskService extends IService<PatrolTaskPo>
{

    /**
     * 查询巡检任务列表
     *
     * @param patrolTask 巡检任务
     * @return 巡检任务集合
     */
    public List<PatrolTaskVo> queryList(PatrolTaskPo patrolTask);

    /**
     * 查询巡检任务
     *
     * @param id 巡检任务主键
     * @return 巡检任务
     */
    public PatrolOneTaskVo selectPatrolTaskById(Integer id);

    /**
     * 查询巡检任务列表
     *
     * @param patrolTask 巡检任务
     * @return 巡检任务集合
     */
    public List<PatrolTaskVo> selectPatrolTaskList(PatrolTaskParam patrolTask);

    /**
     * 新增巡检任务
     *
     * @param patrolTask 巡检任务
     * @return 结果
     */
    public int insertPatrolTask(PatrolTaskAddParam patrolTask);

    /**
     * 修改巡检任务
     *
     * @param patrolTask 巡检任务
     * @return 结果
     */
    public int updatePatrolTask(PatrolTaskEditParam patrolTask);

    /**
     * 批量删除巡检任务
     *
     * @param ids 需要删除的巡检任务主键集合
     * @return 结果
     */
    public int deletePatrolTaskByIds(Long[] ids);

    /**
     * 删除巡检任务信息
     *
     * @param id 巡检任务主键
     * @return 结果
     */
    public int deletePatrolTaskById(Long id);

    PatrolTaskResultVo getTaskResult();

    void addPatrolTask(PatrolTaskDetailAddParam param) throws Exception;

    List<String> findPatrolType();

    List<String> findPatrolScope();

    String findProgress();
}
