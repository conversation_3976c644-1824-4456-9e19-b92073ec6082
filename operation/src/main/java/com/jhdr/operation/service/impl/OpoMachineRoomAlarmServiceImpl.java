package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoMachineRoomAlarmPo;
import com.jhdr.operation.entity.param.OpoMachineRoomAlarmParam;
import com.jhdr.operation.entity.param.OpoMachineRoomAlarmAddParam;
import com.jhdr.operation.entity.param.OpoMachineRoomAlarmEditParam;
import com.jhdr.operation.entity.vo.OpoMachineRoomAlarmVo;
import com.jhdr.operation.mapper.OpoMachineRoomAlarmMapper;
import com.jhdr.operation.service.IOpoMachineRoomAlarmService;

import java.util.ArrayList;

import java.util.List;

/**
 * 运维机房设备报警apiService业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
public class OpoMachineRoomAlarmServiceImpl extends ServiceImpl<OpoMachineRoomAlarmMapper, OpoMachineRoomAlarmPo> implements IOpoMachineRoomAlarmService {

    @Override
    public List<OpoMachineRoomAlarmVo> queryList(OpoMachineRoomAlarmPo opoMachineRoomAlarm) {
        LambdaQueryWrapper<OpoMachineRoomAlarmPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getAlarmid())){
            lqw.eq(OpoMachineRoomAlarmPo::getAlarmid ,opoMachineRoomAlarm.getAlarmid());
        }
        if (opoMachineRoomAlarm.getLscid() != null){
            lqw.eq(OpoMachineRoomAlarmPo::getLscid ,opoMachineRoomAlarm.getLscid());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getWyid())){
            lqw.eq(OpoMachineRoomAlarmPo::getWyid ,opoMachineRoomAlarm.getWyid());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getSiteName())){
            lqw.like(OpoMachineRoomAlarmPo::getSiteName ,opoMachineRoomAlarm.getSiteName());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getName())){
            lqw.like(OpoMachineRoomAlarmPo::getName ,opoMachineRoomAlarm.getName());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getIsad())){
            lqw.eq(OpoMachineRoomAlarmPo::getIsad ,opoMachineRoomAlarm.getIsad());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getType())){
            lqw.eq(OpoMachineRoomAlarmPo::getType ,opoMachineRoomAlarm.getType());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getIp())){
            lqw.eq(OpoMachineRoomAlarmPo::getIp ,opoMachineRoomAlarm.getIp());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getValue())){
            lqw.eq(OpoMachineRoomAlarmPo::getValue ,opoMachineRoomAlarm.getValue());
        }
        if (StringUtils.isNotBlank(opoMachineRoomAlarm.getDw())){
            lqw.eq(OpoMachineRoomAlarmPo::getDw ,opoMachineRoomAlarm.getDw());
        }
        if (opoMachineRoomAlarm.getItemLevels() != null){
            lqw.eq(OpoMachineRoomAlarmPo::getItemLevels ,opoMachineRoomAlarm.getItemLevels());
        }
        if (opoMachineRoomAlarm.getTs() != null){
            lqw.eq(OpoMachineRoomAlarmPo::getTs ,opoMachineRoomAlarm.getTs());
        }
        List<OpoMachineRoomAlarmVo> opoMachineRoomAlarmVos= BeanUtil.copyToList(this.list(lqw), OpoMachineRoomAlarmVo.class);
        return opoMachineRoomAlarmVos;
    }
    /**
     * 查询运维机房设备报警api
     *
     * @param id 运维机房设备报警api主键
     * @return 运维机房设备报警api
     */
    @Override
    public OpoMachineRoomAlarmVo selectOpoMachineRoomAlarmById(Long id)
    {
        return baseMapper.selectOpoMachineRoomAlarmById(id);
    }

    /**
     * 查询运维机房设备报警api列表
     *
     * @param opoMachineRoomAlarm 运维机房设备报警api
     * @return 运维机房设备报警api
     */
    @Override
    public List<OpoMachineRoomAlarmVo> selectOpoMachineRoomAlarmList(OpoMachineRoomAlarmParam opoMachineRoomAlarm)
    {
        return baseMapper.selectOpoMachineRoomAlarmList(opoMachineRoomAlarm);
    }

    /**
     * 新增运维机房设备报警api
     *
     * @param opoMachineRoomAlarmAddParam 运维机房设备报警api
     * @return 结果
     */
    @Override
    public int insertOpoMachineRoomAlarm(OpoMachineRoomAlarmAddParam opoMachineRoomAlarmAddParam)
    {

        OpoMachineRoomAlarmPo opoMachineRoomAlarm=new OpoMachineRoomAlarmPo();
        BeanUtil.copyProperties(opoMachineRoomAlarmAddParam,opoMachineRoomAlarm);
        return baseMapper.insert(opoMachineRoomAlarm);
    }

    /**
     * 修改运维机房设备报警api
     *
     * @param opoMachineRoomAlarmEditParam 运维机房设备报警api
     * @return 结果
     */
    @Override
    public int updateOpoMachineRoomAlarm(OpoMachineRoomAlarmEditParam opoMachineRoomAlarmEditParam)
    {
        OpoMachineRoomAlarmPo opoMachineRoomAlarm=new OpoMachineRoomAlarmPo();
        BeanUtil.copyProperties(opoMachineRoomAlarmEditParam,opoMachineRoomAlarm);
        return baseMapper.updateById(opoMachineRoomAlarm);
    }

    /**
     * 批量删除运维机房设备报警api
     *
     * @param ids 需要删除的运维机房设备报警api主键
     * @return 结果
     */
    @Override
    public int deleteOpoMachineRoomAlarmByIds(Long[] ids)
    {
        return baseMapper.deleteOpoMachineRoomAlarmByIds(ids);
    }

    /**
     * 删除运维机房设备报警api信息
     *
     * @param id 运维机房设备报警api主键
     * @return 结果
     */
    @Override
    public int deleteOpoMachineRoomAlarmById(Long id)
    {
        return baseMapper.deleteOpoMachineRoomAlarmById(id);
    }
}
