package com.jhdr.operation.service;

import java.util.Date;
import java.util.List;

import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.po.OpoSwStationPo;
import com.jhdr.operation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 运维监控-水文站点Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOpoSwStationService extends IService<OpoSwStationPo>
{

    /**
     * 查询运维监控-水文站点列表
     *
     * @param opoSwStation 运维监控-水文站点
     * @return 运维监控-水文站点集合
     */
    public List<OpoSwStationVo> queryList(OpoSwStationPo opoSwStation);

    /**
     * 查询运维监控-水文站点
     *
     * @param id 运维监控-水文站点主键
     * @return 运维监控-水文站点
     */
    public OpoSwStationVo selectOpoSwStationById(Long id);

    /**
     * 查询运维监控-水文站点列表
     *
     * @param opoSwStation 运维监控-水文站点
     * @return 运维监控-水文站点集合
     */
    public List<OpoSwStationVo> selectOpoSwStationList(OpoSwStationParam opoSwStation);

    List<OpoSwStationVo> selectOpoFlowStationList(OpoSwStationParam opoSwStationParam);

    /**
     * 新增运维监控-水文站点
     *
     * @param opoSwStation 运维监控-水文站点
     * @return 结果
     */
    public int insertOpoSwStation(OpoSwStationAddParam opoSwStation);

    /**
     * 修改运维监控-水文站点
     *
     * @param opoSwStation 运维监控-水文站点
     * @return 结果
     */
    public int updateOpoSwStation(OpoSwStationEditParam opoSwStation);

    /**
     * 批量删除运维监控-水文站点
     *
     * @param ids 需要删除的运维监控-水文站点主键集合
     * @return 结果
     */
    public int deleteOpoSwStationByIds(Long[] ids);

    /**
     * 删除运维监控-水文站点信息
     *
     * @param id 运维监控-水文站点主键
     * @return 结果
     */
    public int deleteOpoSwStationById(Long id);




    List<WaterRegionVo> selectRegionTreeList(String sttp);

    List<WaterStateVo> findCodeByState(String state);


    Date findWaterStateByCode(String stcd);
    String findNewTimeByCD(String stcd);

    int updateWaterState(String stcd, String state, String alarmType, String time);

    Date findFlowStateByCode(String stcd);

    List<CountyVo> findCounty();


    List<WaterStateVo> findFlowByState(String state);

    StateCountVo getStateAmount(String addvcd);

    StateCountVo getFlowStateAmount(String addvcd);


    void isMon(IsMonSWParam param);
}
