package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoGoodsStationPo;
import com.jhdr.operation.entity.param.OpoGoodsStationParam;
import com.jhdr.operation.entity.param.OpoGoodsStationAddParam;
import com.jhdr.operation.entity.param.OpoGoodsStationEditParam;
import com.jhdr.operation.entity.vo.OpoGoodsStationVo;
import com.jhdr.operation.mapper.OpoGoodsStationMapper;
import com.jhdr.operation.service.IOpoGoodsStationService;

import java.util.ArrayList;

import java.util.List;

/**
 * 物资点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Service
public class OpoGoodsStationServiceImpl extends ServiceImpl<OpoGoodsStationMapper, OpoGoodsStationPo> implements IOpoGoodsStationService {

    @Override
    public List<OpoGoodsStationVo> queryList(OpoGoodsStationPo opoGoodsStation) {
        LambdaQueryWrapper<OpoGoodsStationPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoGoodsStation.getName())){
            lqw.like(OpoGoodsStationPo::getName ,opoGoodsStation.getName());
        }
        if (StringUtils.isNotBlank(opoGoodsStation.getCode())){
            lqw.like(OpoGoodsStationPo::getCode ,opoGoodsStation.getCode());
        }
        if (StringUtils.isNotBlank(opoGoodsStation.getPerson())){
            lqw.eq(OpoGoodsStationPo::getPerson ,opoGoodsStation.getPerson());
        }
        if (StringUtils.isNotBlank(opoGoodsStation.getPhone())){
            lqw.eq(OpoGoodsStationPo::getPhone ,opoGoodsStation.getPhone());
        }
        List<OpoGoodsStationVo> opoGoodsStationVos= BeanUtil.copyToList(this.list(lqw), OpoGoodsStationVo.class);
        return opoGoodsStationVos;
    }
    /**
     * 查询物资点
     *
     * @param id 物资点主键
     * @return 物资点
     */
    @Override
    public OpoGoodsStationVo selectOpoGoodsStationById(Integer id)
    {
        return baseMapper.selectOpoGoodsStationById(id);
    }

    /**
     * 查询物资点列表
     *
     * @param opoGoodsStation 物资点
     * @return 物资点
     */
    @Override
    public List<OpoGoodsStationVo> selectOpoGoodsStationList(OpoGoodsStationParam opoGoodsStation)
    {
        return baseMapper.selectOpoGoodsStationList(opoGoodsStation);
    }

    /**
     * 新增物资点
     *
     * @param opoGoodsStationAddParam 物资点
     * @return 结果
     */
    @Override
    public int insertOpoGoodsStation(OpoGoodsStationAddParam opoGoodsStationAddParam)
    {

        OpoGoodsStationPo po=new OpoGoodsStationPo();
        BeanUtil.copyProperties(opoGoodsStationAddParam,po);
        po.setIsDelete(0);
        return baseMapper.insert(po);
    }

    /**
     * 修改物资点
     *
     * @param opoGoodsStationEditParam 物资点
     * @return 结果
     */
    @Override
    public int updateOpoGoodsStation(OpoGoodsStationEditParam opoGoodsStationEditParam)
    {
        OpoGoodsStationPo opoGoodsStation=new OpoGoodsStationPo();
        BeanUtil.copyProperties(opoGoodsStationEditParam,opoGoodsStation);
        return baseMapper.updateById(opoGoodsStation);
    }

    /**
     * 批量删除物资点
     *
     * @param ids 需要删除的物资点主键
     * @return 结果
     */
    @Override
    public int deleteOpoGoodsStationByIds(Integer[] ids)
    {
        return baseMapper.deleteOpoGoodsStationByIds(ids);
    }

    /**
     * 删除物资点信息
     *
     * @param id 物资点主键
     * @return 结果
     */
    @Override
    public int deleteOpoGoodsStationById(Integer id)
    {
        return baseMapper.deleteOpoGoodsStationById(id);
    }

    @Override
    public List<OpoGoodsStationVo> getGoodStations(String code) {
        return baseMapper.getGoodStations(code);
    }
}
