package com.jhdr.operation.service;

import java.util.Date;
import java.util.List;

import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.po.OpoGqStationPo;
import com.jhdr.operation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 运维监控-工情站点Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOpoGqStationService extends IService<OpoGqStationPo>
{
    /**
     * 查询运维监控-工情站点
     *
     * @param id 运维监控-工情站点主键
     * @return 运维监控-工情站点
     */
    public OpoGqStationVo selectOpoGqStationById(Long id);

    /**
     * 查询运维监控-工情站点列表
     *
     * @param opoGqStation 运维监控-工情站点
     * @return 运维监控-工情站点集合
     */
    public List<OpoPumpStationVo> selectOpoGqStationList(OpoGqStationParam opoGqStation);

    /**
     * 新增运维监控-工情站点
     *
     * @param opoGqStation 运维监控-工情站点
     * @return 结果
     */
    public int insertOpoGqStation(OpoGqStationAddParam opoGqStation);

    /**
     * 修改运维监控-工情站点
     *
     * @param opoGqStation 运维监控-工情站点
     * @return 结果
     */
    public int updateOpoGqStation(OpoGqStationEditParam opoGqStation);

    /**
     * 批量删除运维监控-工情站点
     *
     * @param ids 需要删除的运维监控-工情站点主键集合
     * @return 结果
     */
    public int deleteOpoGqStationByIds(Long[] ids);

    /**
     * 删除运维监控-工情站点信息
     *
     * @param id 运维监控-工情站点主键
     * @return 结果
     */
    public int deleteOpoGqStationById(Long id);

    List<OpoGateStationVo> selectGateStationList(OpoGqStationParam opoGqStationParam);

    List<String> getPumpName(String name);

    List<String> getGateName(String name);



    List<WaterRegionVo> pustRegion();

    List<WaterRegionVo> wagaRegion();

    List<MachineStateVo> findPumpCodeByState(String state);

    List<MachineStateVo> findTimeByCode(String code);

    int updatePumpState(String strCode, String deviceCode, String state, String alarmType, String time);


    int updatePustState(String strCode, String state, String alarmType, String time,Integer alarmId);

    int findPumpUseCount(String code);

    List<MachineStateVo> findGateCodeByState(String state);

    List<MachineStateVo> findGateTimeByCode(String code);

    int updateGateState(String strCode, String state, String alarmType, String time);

    int updateWagaState(String strCode, String state, String alarmType, String time);

    int findGateUseCount(String code);

    StateCountVo getPustStateAmount(String addvcd);

    StateCountVo getWagaStateAmount(String addvcd);

    Date findPumpDtlTime(String strCode);

    Date findWagaDtlTime(String strCode);

    void isMon(IsMachineParam param);

    void isWagaMon(IsMachineWagaParam param);
}
