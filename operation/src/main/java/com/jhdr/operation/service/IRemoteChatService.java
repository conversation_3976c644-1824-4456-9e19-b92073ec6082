package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.RemoteChatPo;
import com.jhdr.operation.entity.param.RemoteChatParam;
import com.jhdr.operation.entity.param.RemoteChatAddParam;
import com.jhdr.operation.entity.param.RemoteChatEditParam;
import com.jhdr.operation.entity.vo.RemoteChatVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.operation.entity.vo.RemoteVideoVo;

/**
 * 异地会商Service接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public interface IRemoteChatService extends IService<RemoteChatPo>
{

    /**
     * 查询异地会商列表
     *
     * @param remoteChat 异地会商
     * @return 异地会商集合
     */
    public List<RemoteChatVo> queryList(RemoteChatPo remoteChat);

    /**
     * 查询异地会商
     *
     * @param id 异地会商主键
     * @return 异地会商
     */
    public RemoteChatVo selectRemoteChatById(Integer id);

    /**
     * 查询异地会商列表
     *
     * @param remoteChat 异地会商
     * @return 异地会商集合
     */
    public List<RemoteChatVo> selectRemoteChatList(RemoteChatParam remoteChat);

    /**
     * 新增异地会商
     *
     * @param remoteChat 异地会商
     * @return 结果
     */
    public int insertRemoteChat(RemoteChatAddParam remoteChat);

    /**
     * 修改异地会商
     *
     * @param remoteChat 异地会商
     * @return 结果
     */
    public int updateRemoteChat(RemoteChatEditParam remoteChat);

    /**
     * 批量删除异地会商
     *
     * @param ids 需要删除的异地会商主键集合
     * @return 结果
     */
    public int deleteRemoteChatByIds(Integer[] ids);

    /**
     * 删除异地会商信息
     *
     * @param id 异地会商主键
     * @return 结果
     */
    public int deleteRemoteChatById(Integer id);

    List<RemoteVideoVo> getVideoSite();
}
