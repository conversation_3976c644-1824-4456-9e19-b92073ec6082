package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoGoodsDetailPo;
import com.jhdr.operation.entity.param.OpoGoodsDetailParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailAddParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailEditParam;
import com.jhdr.operation.entity.vo.OpoGoodsDetailVo;
import com.jhdr.operation.mapper.OpoGoodsDetailMapper;
import com.jhdr.operation.service.IOpoGoodsDetailService;

import java.util.ArrayList;

import java.util.List;

/**
 * 物资详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Service
public class OpoGoodsDetailServiceImpl extends ServiceImpl<OpoGoodsDetailMapper, OpoGoodsDetailPo> implements IOpoGoodsDetailService {

    @Override
    public List<OpoGoodsDetailVo> queryList(OpoGoodsDetailPo opoGoodsDetail) {
        LambdaQueryWrapper<OpoGoodsDetailPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoGoodsDetail.getName())){
            lqw.like(OpoGoodsDetailPo::getName ,opoGoodsDetail.getName());
        }
        List<OpoGoodsDetailVo> opoGoodsDetailVos= BeanUtil.copyToList(this.list(lqw), OpoGoodsDetailVo.class);
        return opoGoodsDetailVos;
    }
    /**
     * 查询物资详情
     *
     * @param id 物资详情主键
     * @return 物资详情
     */
    @Override
    public OpoGoodsDetailVo selectOpoGoodsDetailById(Integer id)
    {
        return baseMapper.selectOpoGoodsDetailById(id);
    }

    /**
     * 查询物资详情列表
     *
     * @param opoGoodsDetail 物资详情
     * @return 物资详情
     */
    @Override
    public List<OpoGoodsDetailVo> selectOpoGoodsDetailList(OpoGoodsDetailParam opoGoodsDetail)
    {
        return baseMapper.selectOpoGoodsDetailList(opoGoodsDetail);
    }

    /**
     * 新增物资详情
     *
     * @param opoGoodsDetailAddParam 物资详情
     * @return 结果
     */
    @Override
    public int insertOpoGoodsDetail(OpoGoodsDetailAddParam opoGoodsDetailAddParam)
    {

        OpoGoodsDetailPo opoGoodsDetail=new OpoGoodsDetailPo();
        BeanUtil.copyProperties(opoGoodsDetailAddParam,opoGoodsDetail);
        return baseMapper.insert(opoGoodsDetail);
    }

    /**
     * 修改物资详情
     *
     * @param opoGoodsDetailEditParam 物资详情
     * @return 结果
     */
    @Override
    public int updateOpoGoodsDetail(OpoGoodsDetailEditParam opoGoodsDetailEditParam)
    {
        OpoGoodsDetailPo opoGoodsDetail=new OpoGoodsDetailPo();
        BeanUtil.copyProperties(opoGoodsDetailEditParam,opoGoodsDetail);
        return baseMapper.updateById(opoGoodsDetail);
    }

    /**
     * 批量删除物资详情
     *
     * @param ids 需要删除的物资详情主键
     * @return 结果
     */
    @Override
    public int deleteOpoGoodsDetailByIds(Integer[] ids)
    {
        return baseMapper.deleteOpoGoodsDetailByIds(ids);
    }

    /**
     * 删除物资详情信息
     *
     * @param id 物资详情主键
     * @return 结果
     */
    @Override
    public int deleteOpoGoodsDetailById(Integer id)
    {
        return baseMapper.deleteOpoGoodsDetailById(id);
    }
}
