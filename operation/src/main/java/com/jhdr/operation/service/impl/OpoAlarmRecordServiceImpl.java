package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.operation.entity.param.StrAlarmRecordParam;
import com.jhdr.operation.service.OpoAlarmRecordService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoAlarmRecordPo;
import com.jhdr.operation.entity.param.OpoAlarmRecordParam;
import com.jhdr.operation.entity.param.OpoAlarmRecordAddParam;
import com.jhdr.operation.entity.param.OpoAlarmRecordEditParam;
import com.jhdr.operation.entity.vo.OpoAlarmRecordVo;
import com.jhdr.operation.mapper.OpoAlarmRecordMapper;


import java.util.Date;
import java.util.List;

/**
 * 机房告警记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@Service
public class OpoAlarmRecordServiceImpl extends ServiceImpl<OpoAlarmRecordMapper, OpoAlarmRecordPo> implements OpoAlarmRecordService {

    @Override
    public List<OpoAlarmRecordVo> queryList(OpoAlarmRecordPo opoAlarmRecord) {
        LambdaQueryWrapper<OpoAlarmRecordPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoAlarmRecord.getAlarmCode())) {
            lqw.eq(OpoAlarmRecordPo::getAlarmCode, opoAlarmRecord.getAlarmCode());
        }
        if (StringUtils.isNotBlank(opoAlarmRecord.getDeviceName())) {
            lqw.like(OpoAlarmRecordPo::getDeviceName, opoAlarmRecord.getDeviceName());
        }
        if (StringUtils.isNotBlank(opoAlarmRecord.getDeviceCode())) {
            lqw.eq(OpoAlarmRecordPo::getDeviceCode, opoAlarmRecord.getDeviceCode());
        }
        if (StringUtils.isNotBlank(opoAlarmRecord.getAlarmType())) {
            lqw.eq(OpoAlarmRecordPo::getAlarmType, opoAlarmRecord.getAlarmType());
        }
        if (opoAlarmRecord.getAddTime() != null) {
            lqw.eq(OpoAlarmRecordPo::getAddTime, opoAlarmRecord.getAddTime());
        }
        if (StringUtils.isNotBlank(opoAlarmRecord.getAlarmState())) {
            lqw.eq(OpoAlarmRecordPo::getAlarmState, opoAlarmRecord.getAlarmState());
        }
        if (StringUtils.isNotBlank(opoAlarmRecord.getContent())) {
            lqw.eq(OpoAlarmRecordPo::getContent, opoAlarmRecord.getContent());
        }
        if (opoAlarmRecord.getIsDelete() != null) {
            lqw.eq(OpoAlarmRecordPo::getIsDelete, opoAlarmRecord.getIsDelete());
        }
        List<OpoAlarmRecordVo> opoAlarmRecordVos = BeanUtil.copyToList(this.list(lqw), OpoAlarmRecordVo.class);
        return opoAlarmRecordVos;
    }

    /**
     * 查询机房告警记录
     *
     * @param id 机房告警记录主键
     * @return 机房告警记录
     */
    @Override
    public OpoAlarmRecordVo selectOpoAlarmRecordById(Long id) {
        return baseMapper.selectOpoAlarmRecordById(id);
    }

    /**
     * 查询机房告警记录列表
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 机房告警记录
     */
    @Override
    public List<OpoAlarmRecordVo> selectOpoAlarmRecordList(OpoAlarmRecordParam opoAlarmRecord) {
        List<OpoAlarmRecordVo> list = baseMapper.selectOpoAlarmRecordList(opoAlarmRecord);

        for (OpoAlarmRecordVo vo : list) {
            Long alarmCount=baseMapper.selectCount(new QueryWrapper<OpoAlarmRecordPo>().
                    eq("device_code", vo.getDeviceCode()).eq("device_type", vo.getDeviceType())
                    .eq("is_delete", 0));
            vo.setAlarmCount(alarmCount.intValue());
        }

        return list;
    }

    /**
     * 新增机房告警记录
     *
     * @param opoAlarmRecordAddParam 机房告警记录
     * @return 结果
     */
    @Override
    public int insertOpoAlarmRecord(OpoAlarmRecordAddParam opoAlarmRecordAddParam) {

        OpoAlarmRecordPo opoAlarmRecord = new OpoAlarmRecordPo();
        BeanUtil.copyProperties(opoAlarmRecordAddParam, opoAlarmRecord);
        return baseMapper.insert(opoAlarmRecord);
    }

    /**
     * 修改机房告警记录
     *
     * @param opoAlarmRecordEditParam 机房告警记录
     * @return 结果
     */
    @Override
    public int updateOpoAlarmRecord(OpoAlarmRecordEditParam opoAlarmRecordEditParam) {
        OpoAlarmRecordPo opoAlarmRecord = new OpoAlarmRecordPo();
        BeanUtil.copyProperties(opoAlarmRecordEditParam, opoAlarmRecord);
        return baseMapper.updateById(opoAlarmRecord);
    }

    @Override
    public int solveAlarm(OpoAlarmRecordEditParam opoAlarmRecord) {
        OpoAlarmRecordPo po = new OpoAlarmRecordPo();
        BeanUtil.copyProperties(opoAlarmRecord, po);
        String userName= SecurityUtils.getUsername();
        po.setPerson(userName);
        po.setAlarmState("已处理");
        po.setUpdateTime(new Date());
        return baseMapper.updateById(po);
    }

    /**
     * 批量删除机房告警记录
     *
     * @param ids 需要删除的机房告警记录主键
     * @return 结果
     */
    @Override
    public int deleteOpoAlarmRecordByIds(Long[] ids) {
        return baseMapper.deleteOpoAlarmRecordByIds(ids);
    }

    /**
     * 删除机房告警记录信息
     *
     * @param id 机房告警记录主键
     * @return 结果
     */
    @Override
    public int deleteOpoAlarmRecordById(Long id) {
        return baseMapper.deleteOpoAlarmRecordById(id);
    }

    @Override
    public List<String> findAlarmType() {
        return baseMapper.findAlarmType();
    }

    @Override
    public List<OpoAlarmRecordVo> findStrAlarmRecord(StrAlarmRecordParam param) {
        return baseMapper.findStrAlarmRecord(param.getDeviceName(), param.getDeviceType(), param.getStartTime(), param.getEndTime());
    }

    @Override
    public List<String> findDeviceType() {
        return baseMapper.findDeviceType();
    }

    @Override
    public OpoAlarmRecordPo getMaxOneByCode(String deviceName) {
        return baseMapper.getMaxOneByCode(deviceName);
    }


}