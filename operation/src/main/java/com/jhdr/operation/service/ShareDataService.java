package com.jhdr.operation.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jhdr.operation.entity.po.JhFlowspRPo;
import com.jhdr.operation.entity.po.StPptnRPo;
import com.jhdr.operation.entity.po.StRiverRPo;
import com.jhdr.operation.entity.vo.FlowAccumStcdVo;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


public interface ShareDataService {
    List<String> findRainStcds();

    StPptnRPo findRainDataByStcd(String stcd);

    StPptnRPo findRainRealData(String stcd, Date tm);

    List<String> findFlowStcds();

    StRiverRPo findFlowDataByStcd(String stcd);

    StRiverRPo findFlowRealData(String stcd, Date tm);

    List<String> findWaterLineStcds();

    List<FlowAccumStcdVo> findFlowAccumStcds();


    JhFlowspRPo findFlowAccumByStcd(String nrcode);

    JhFlowspRPo findFlowAccumData(String jhCode, Date tm);


    List<StPptnRPo> findAllRain(String stcd);

    List<StPptnRPo> findShareAllRain(String stcd);

    List<StPptnRPo> findRainsByStcd(String stcd);

    void flowInsert();

    Double findNowFlowByCd(String nrcode);

    void flowInsertYearMonth();

    void testYearMonthInsert();
}
