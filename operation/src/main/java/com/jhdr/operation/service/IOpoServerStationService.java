package com.jhdr.operation.service;

import java.util.List;
import com.jhdr.operation.entity.po.OpoServerStationPo;
import com.jhdr.operation.entity.param.OpoServerStationParam;
import com.jhdr.operation.entity.param.OpoServerStationAddParam;
import com.jhdr.operation.entity.param.OpoServerStationEditParam;
import com.jhdr.operation.entity.vo.DeviceIpMonVo;
import com.jhdr.operation.entity.vo.OpoServerStationVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.operation.entity.vo.ServerStateVo;
import com.jhdr.operation.entity.vo.StateCountVo;

/**
 * 运维监控-服务器管理Service接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface IOpoServerStationService extends IService<OpoServerStationPo>
{

    /**
     * 查询运维监控-服务器管理列表
     *
     * @param opoServerStation 运维监控-服务器管理
     * @return 运维监控-服务器管理集合
     */
    public List<OpoServerStationVo> queryList(OpoServerStationPo opoServerStation);

    /**
     * 查询运维监控-服务器管理
     *
     * @param id 运维监控-服务器管理主键
     * @return 运维监控-服务器管理
     */
    public OpoServerStationVo selectOpoServerStationById(Integer id);

    /**
     * 查询运维监控-服务器管理列表
     *
     * @param opoServerStation 运维监控-服务器管理
     * @return 运维监控-服务器管理集合
     */
    public List<OpoServerStationVo> selectOpoServerStationList(OpoServerStationParam opoServerStation);

    StateCountVo getServerStateAmount();
    /**
     * 新增运维监控-服务器管理
     *
     * @param opoServerStation 运维监控-服务器管理
     * @return 结果
     */
    public int insertOpoServerStation(OpoServerStationAddParam opoServerStation);

    /**
     * 修改运维监控-服务器管理
     *
     * @param opoServerStation 运维监控-服务器管理
     * @return 结果
     */
    public int updateOpoServerStation(OpoServerStationEditParam opoServerStation);

    /**
     * 批量删除运维监控-服务器管理
     *
     * @param ids 需要删除的运维监控-服务器管理主键集合
     * @return 结果
     */
    public int deleteOpoServerStationByIds(Long[] ids);

    /**
     * 删除运维监控-服务器管理信息
     *
     * @param id 运维监控-服务器管理主键
     * @return 结果
     */
    public int deleteOpoServerStationById(Long id);

    List<ServerStateVo> findServer(String runState);

    int updateStateById(Integer id, String runState, String type,String time);

    List<DeviceIpMonVo> findWorkByState(String state);

    void updateNetWork(Integer id, String time, String state, String alarmType);


}
