package com.jhdr.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.operation.entity.param.*;
import com.jhdr.operation.entity.vo.*;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.operation.entity.po.OpoSwStationPo;
import com.jhdr.operation.mapper.OpoSwStationMapper;
import com.jhdr.operation.service.IOpoSwStationService;

import java.util.ArrayList;

import java.util.Date;
import java.util.List;

/**
 * 运维监控-水文站点Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
@Service
public class OpoSwStationServiceImpl extends ServiceImpl<OpoSwStationMapper, OpoSwStationPo> implements IOpoSwStationService {

    @Override
    public List<OpoSwStationVo> queryList(OpoSwStationPo opoSwStation) {
        LambdaQueryWrapper<OpoSwStationPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(opoSwStation.getStaName())){
            lqw.like(OpoSwStationPo::getStaName ,opoSwStation.getStaName());
        }
        if (StringUtils.isNotBlank(opoSwStation.getStaCode())){
            lqw.eq(OpoSwStationPo::getStaCode ,opoSwStation.getStaCode());
        }
        if (StringUtils.isNotBlank(opoSwStation.getStaType())){
            lqw.eq(OpoSwStationPo::getStaType ,opoSwStation.getStaType());
        }
        if (StringUtils.isNotBlank(opoSwStation.getManageUnit())){
            lqw.eq(OpoSwStationPo::getManageUnit ,opoSwStation.getManageUnit());
        }
        if (StringUtils.isNotBlank(opoSwStation.getPhone())){
            lqw.eq(OpoSwStationPo::getPhone ,opoSwStation.getPhone());
        }
        if (StringUtils.isNotBlank(opoSwStation.getStaSite())){
            lqw.eq(OpoSwStationPo::getStaSite ,opoSwStation.getStaSite());
        }
        if (StringUtils.isNotBlank(opoSwStation.getStaState())){
            lqw.eq(OpoSwStationPo::getStaState ,opoSwStation.getStaState());
        }
        if (StringUtils.isNotBlank(opoSwStation.getAlarmType())){
            lqw.eq(OpoSwStationPo::getAlarmType ,opoSwStation.getAlarmType());
        }
        if (opoSwStation.getAlarmTime() != null){
            lqw.eq(OpoSwStationPo::getAlarmTime ,opoSwStation.getAlarmTime());
        }
        List<OpoSwStationVo> opoSwStationVos= BeanUtil.copyToList(this.list(lqw), OpoSwStationVo.class);
        return opoSwStationVos;
    }
    /**
     * 查询运维监控-水文站点
     *
     * @param id 运维监控-水文站点主键
     * @return 运维监控-水文站点
     */
    @Override
    public OpoSwStationVo selectOpoSwStationById(Long id)
    {
        return baseMapper.selectOpoSwStationById(id);
    }

    /**
     * 查询运维监控-水位站点列表
     *
     * @param opoSwStation 运维监控-水文站点
     * @return 运维监控-水文站点
     */
    @Override
    public List<OpoSwStationVo> selectOpoSwStationList(OpoSwStationParam opoSwStation)
    {
        List<OpoSwStationVo> list=baseMapper.selectOpoSwStationList(opoSwStation);

        return list;
    }

    @Override
    public List<OpoSwStationVo> selectOpoFlowStationList(OpoSwStationParam opoSwStation)
    {
        List<OpoSwStationVo> list=baseMapper.selectOpoFlowStationList(opoSwStation);
        return list;
    }

    /**
     * 新增运维监控-水文站点
     *
     * @param opoSwStationAddParam 运维监控-水文站点
     * @return 结果
     */
    @Override
    public int insertOpoSwStation(OpoSwStationAddParam opoSwStationAddParam)
    {

        OpoSwStationPo opoSwStation=new OpoSwStationPo();
        BeanUtil.copyProperties(opoSwStationAddParam,opoSwStation);
        return baseMapper.insert(opoSwStation);
    }

    /**
     * 修改运维监控-水文站点
     *
     * @param opoSwStationEditParam 运维监控-水文站点
     * @return 结果
     */
    @Override
    public int updateOpoSwStation(OpoSwStationEditParam opoSwStationEditParam)
    {
        OpoSwStationPo opoSwStation=new OpoSwStationPo();
        BeanUtil.copyProperties(opoSwStationEditParam,opoSwStation);
        return baseMapper.updateById(opoSwStation);
    }

    /**
     * 批量删除运维监控-水文站点
     *
     * @param ids 需要删除的运维监控-水文站点主键
     * @return 结果
     */
    @Override
    public int deleteOpoSwStationByIds(Long[] ids)
    {
        return baseMapper.deleteOpoSwStationByIds(ids);
    }

    /**
     * 删除运维监控-水文站点信息
     *
     * @param id 运维监控-水文站点主键
     * @return 结果
     */
    @Override
    public int deleteOpoSwStationById(Long id)
    {
        return baseMapper.deleteOpoSwStationById(id);
    }



    @Override
    public List<WaterRegionVo> selectRegionTreeList(String sttp) {
        List<WaterRegionVo> regionTreeList= baseMapper.selectRegionTreeList(sttp);

        List<WaterRegionVo> menusList = new ArrayList<>();

        for (WaterRegionVo vo : regionTreeList) {
            //设置一级菜单
            if (vo.getAddvcd().equals("0")){
                menusList.add(vo);
            }
        }

        //设置一级菜单
//        StRiverRegionDataVo menus = new StRiverRegionDataVo();
//        menus.setAddvcd("1").setStnm("安徽茨淮新河").setStcd("0");
//        menusList.add(menus);

        //设置一级菜单的子集菜单
        for (WaterRegionVo menusVo : menusList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }
        return menusList;
    }



    private List<WaterRegionVo> getChildren(String id, List<WaterRegionVo> regionTreeList) {
        //定义集合
        List<WaterRegionVo> childList = new ArrayList<>();
        //遍历子集菜单
        for (WaterRegionVo menu : regionTreeList) {
            //父级id与子集id比较
            if (menu.getAddvcd().equals(id)){
                childList.add(menu);
            }
        }
        //循环子集菜单
        for (WaterRegionVo menusVo : childList) {
            menusVo.setChildren(getChildren(menusVo.getStcd(),regionTreeList));
        }

        //递归条件退出
        if (childList.size() ==0){
            return null;
        }
        return childList;
    }




    @Override
    public List<WaterStateVo> findCodeByState(String state) {
        return baseMapper.findCodeByState(state);
    }

    @Override
    public Date findWaterStateByCode(String stcd) {
        return baseMapper.findWaterStateByCode(stcd);
    }

    @Override
    public String findNewTimeByCD(String stcd) {
        return baseMapper.findNewTimeByCD(stcd);
    }

    @Override
    public int updateWaterState(String stcd, String state, String alarmType, String time) {
        return baseMapper.updateWaterState(stcd,state,alarmType,time);
    }

    @Override
    public Date findFlowStateByCode(String stcd) {
        return baseMapper.findFlowStateByCode(stcd);
    }

    @Override
    public List<CountyVo> findCounty() {
        return baseMapper.findCounty();
    }

    @Override
    public List<WaterStateVo> findFlowByState(String state) {
        return baseMapper.findFlowByState(state);
    }

    private  static  String status1="正常";
    private  static  String status2="异常";
    @Override
    public StateCountVo getStateAmount(String addvcd) {
        Integer useAmount=baseMapper.getStateAmountByStatus(addvcd,status1);
        Integer alarmAmount=baseMapper.getStateAmountByStatus(addvcd,status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }

    public StateCountVo getFlowStateAmount(String addvcd) {
        Integer useAmount=baseMapper.getFlowStateAmountByStatus(addvcd,status1);
        Integer alarmAmount=baseMapper.getFlowStateAmountByStatus(addvcd,status2);
        StateCountVo vo=new StateCountVo(useAmount,alarmAmount);
        return vo;
    }

    @Override
    public void isMon(IsMonSWParam param) {
        baseMapper.isMon(param.getStcd(),param.getIsMon());
    }

}
