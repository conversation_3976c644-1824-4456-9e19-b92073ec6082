package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoDispatchTaskPo;
import com.jhdr.operation.entity.param.OpoDispatchTaskParam;
import com.jhdr.operation.entity.param.OpoDispatchTaskAddParam;
import com.jhdr.operation.entity.param.OpoDispatchTaskEditParam;
import com.jhdr.operation.entity.vo.OpoDispatchTaskVo;
import org.apache.ibatis.annotations.Param;


/**
 * 工程调度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface OpoDispatchTaskMapper extends BaseMapper<OpoDispatchTaskPo>
{
    /**
     * 查询工程调度
     *
     * @param id 工程调度主键
     * @return 工程调度
     */
    public OpoDispatchTaskVo selectOpoDispatchTaskById(Integer id);

    /**
     * 查询工程调度列表
     *
     * @param opoDispatchTask 工程调度
     * @return 工程调度集合
     */
    public List<OpoDispatchTaskVo> selectOpoDispatchTaskList(OpoDispatchTaskParam opoDispatchTask);

    List<OpoDispatchTaskVo> selectAllTaskList(OpoDispatchTaskParam param);

    /**
     * 新增工程调度
     *
     * @param opoDispatchTask 工程调度
     * @return 结果
     */
    public int insertOpoDispatchTask(OpoDispatchTaskAddParam opoDispatchTask);

    /**
     * 修改工程调度
     *
     * @param opoDispatchTask 工程调度
     * @return 结果
     */
    public int updateOpoDispatchTask(OpoDispatchTaskEditParam opoDispatchTask);

    /**
     * 删除工程调度
     *
     * @param id 工程调度主键
     * @return 结果
     */
    public int deleteOpoDispatchTaskById(Integer id);

    /**
     * 批量删除工程调度
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoDispatchTaskByIds(Integer[] ids);

    int updateOpoTaskById(@Param("id") Integer id);


}
