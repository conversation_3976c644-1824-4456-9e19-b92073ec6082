package com.jhdr.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.JhPumpDtlRPo;
import com.jhdr.operation.entity.po.JhPumpRPo;
import com.jhdr.operation.entity.po.JhPumpRealPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface JhPumpRMapper extends BaseMapper<JhPumpRPo> {


    JhPumpRealPo selectRealPump(@Param("prjnmcd") String prjnmcd);

    JhPumpRPo selectPumpR(@Param("prjnmcd")String prjnmcd, @Param("clltm")Date clltm);

    Double findWaterUpLineByCd(@Param("code")String code);

    Double findWaterDownLineByCd(@Param("code")String code);
}
