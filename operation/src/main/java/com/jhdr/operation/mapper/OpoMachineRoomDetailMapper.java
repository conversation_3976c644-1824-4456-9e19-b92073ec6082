package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoMachineRoomDetailPo;
import com.jhdr.operation.entity.param.OpoMachineRoomDetailParam;
import com.jhdr.operation.entity.param.OpoMachineRoomDetailAddParam;
import com.jhdr.operation.entity.param.OpoMachineRoomDetailEditParam;
import com.jhdr.operation.entity.vo.OpoMachineRoomDetailVo;


/**
 * 运维机房设备详细信息apiMapper接口
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface OpoMachineRoomDetailMapper extends BaseMapper<OpoMachineRoomDetailPo>
{
    /**
     * 查询运维机房设备详细信息api
     *
     * @param id 运维机房设备详细信息api主键
     * @return 运维机房设备详细信息api
     */
    public OpoMachineRoomDetailVo selectOpoMachineRoomDetailById(Long id);

    /**
     * 查询运维机房设备详细信息api列表
     *
     * @param opoMachineRoomDetail 运维机房设备详细信息api
     * @return 运维机房设备详细信息api集合
     */
    public List<OpoMachineRoomDetailVo> selectOpoMachineRoomDetailList(OpoMachineRoomDetailParam opoMachineRoomDetail);

    /**
     * 新增运维机房设备详细信息api
     *
     * @param opoMachineRoomDetail 运维机房设备详细信息api
     * @return 结果
     */
    public int insertOpoMachineRoomDetail(OpoMachineRoomDetailAddParam opoMachineRoomDetail);

    /**
     * 修改运维机房设备详细信息api
     *
     * @param opoMachineRoomDetail 运维机房设备详细信息api
     * @return 结果
     */
    public int updateOpoMachineRoomDetail(OpoMachineRoomDetailEditParam opoMachineRoomDetail);

    /**
     * 删除运维机房设备详细信息api
     *
     * @param id 运维机房设备详细信息api主键
     * @return 结果
     */
    public int deleteOpoMachineRoomDetailById(Long id);

    /**
     * 批量删除运维机房设备详细信息api
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoMachineRoomDetailByIds(Long[] ids);
}
