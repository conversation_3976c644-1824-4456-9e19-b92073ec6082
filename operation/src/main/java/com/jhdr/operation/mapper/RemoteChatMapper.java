package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.RemoteChatPo;
import com.jhdr.operation.entity.param.RemoteChatParam;
import com.jhdr.operation.entity.param.RemoteChatAddParam;
import com.jhdr.operation.entity.param.RemoteChatEditParam;
import com.jhdr.operation.entity.vo.RemoteChatDetailVo;
import com.jhdr.operation.entity.vo.RemoteChatVo;
import com.jhdr.operation.entity.vo.RemoteFileVo;
import com.jhdr.operation.entity.vo.RemoteVideoVo;
import org.apache.ibatis.annotations.Param;


/**
 * 异地会商Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public interface RemoteChatMapper extends BaseMapper<RemoteChatPo>
{
    /**
     * 查询异地会商
     *
     * @param id 异地会商主键
     * @return 异地会商
     */
    public RemoteChatVo selectRemoteChatById(Integer id);

    /**
     * 查询异地会商列表
     *
     * @param remoteChat 异地会商
     * @return 异地会商集合
     */
    public List<RemoteChatVo> selectRemoteChatList(RemoteChatParam remoteChat);

    /**
     * 新增异地会商
     *
     * @param remoteChat 异地会商
     * @return 结果
     */
    public int insertRemoteChat(RemoteChatAddParam remoteChat);

    /**
     * 修改异地会商
     *
     * @param remoteChat 异地会商
     * @return 结果
     */
    public int updateRemoteChat(RemoteChatEditParam remoteChat);

    /**
     * 删除异地会商
     *
     * @param id 异地会商主键
     * @return 结果
     */
    public int deleteRemoteChatById(Integer id);

    /**
     * 批量删除异地会商
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRemoteChatByIds(Integer[] ids);

    List<RemoteVideoVo> getVideoSite();

    List<RemoteChatDetailVo> selectJoinPersons(@Param("id") Integer id);

    List<RemoteFileVo> selectFiles(@Param("id")Integer id);

    List<RemoteChatDetailVo> selectDetails(@Param("id")Integer id);

    List<RemoteChatDetailVo> selectResults(@Param("id")Integer id);
}
