package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.RemoteChatDetailPo;
import com.jhdr.operation.entity.param.RemoteChatDetailParam;
import com.jhdr.operation.entity.param.RemoteChatDetailAddParam;
import com.jhdr.operation.entity.param.RemoteChatDetailEditParam;
import com.jhdr.operation.entity.vo.RemoteChatDetailVo;
import org.apache.ibatis.annotations.Param;


/**
 * 异地会商详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-14
 */
public interface RemoteChatDetailMapper extends BaseMapper<RemoteChatDetailPo>
{
    /**
     * 查询异地会商详情
     *
     * @param id 异地会商详情主键
     * @return 异地会商详情
     */
    public RemoteChatDetailVo selectRemoteChatDetailById(Integer id);

    /**
     * 查询异地会商详情列表
     *
     * @param remoteChatDetail 异地会商详情
     * @return 异地会商详情集合
     */
    public List<RemoteChatDetailVo> selectRemoteChatDetailList(RemoteChatDetailParam remoteChatDetail);

    /**
     * 新增异地会商详情
     *
     * @param remoteChatDetail 异地会商详情
     * @return 结果
     */
    public int insertRemoteChatDetail(RemoteChatDetailAddParam remoteChatDetail);

    /**
     * 修改异地会商详情
     *
     * @param remoteChatDetail 异地会商详情
     * @return 结果
     */
    public int updateRemoteChatDetail(RemoteChatDetailEditParam remoteChatDetail);

    /**
     * 删除异地会商详情
     *
     * @param id 异地会商详情主键
     * @return 结果
     */
    public int deleteRemoteChatDetailById(Integer id);

    /**
     * 批量删除异地会商详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRemoteChatDetailByIds(Integer[] ids);


    void updateBaseStatus(@Param("chatId") Integer chatId);
}
