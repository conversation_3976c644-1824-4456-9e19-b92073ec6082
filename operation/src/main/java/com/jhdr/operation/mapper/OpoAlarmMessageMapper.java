package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.param.StrAlarmMessageParam;
import com.jhdr.operation.entity.po.OpoAlarmMessagePo;
import com.jhdr.operation.entity.param.OpoAlarmMessageParam;
import com.jhdr.operation.entity.param.OpoAlarmMessageAddParam;
import com.jhdr.operation.entity.param.OpoAlarmMessageEditParam;
import com.jhdr.operation.entity.vo.*;
import org.apache.ibatis.annotations.Param;


/**
 * 报警信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface OpoAlarmMessageMapper extends BaseMapper<OpoAlarmMessagePo>
{
    /**
     * 查询报警信息
     *
     * @param id 报警信息主键
     * @return 报警信息
     */
    public OpoAlarmMessageVo selectOpoAlarmMessageById(Long id);

    /**
     * 查询报警信息列表
     *
     * @param opoAlarmMessage 报警信息
     * @return 报警信息集合
     */
    public List<OpoAlarmMessageVo> selectOpoAlarmMessageList(OpoAlarmMessageParam opoAlarmMessage);

    /**
     * 新增报警信息
     *
     * @param opoAlarmMessage 报警信息
     * @return 结果
     */
    public int insertOpoAlarmMessage(OpoAlarmMessageAddParam opoAlarmMessage);

    /**
     * 修改报警信息
     *
     * @param opoAlarmMessage 报警信息
     * @return 结果
     */
    public int updateOpoAlarmMessage(OpoAlarmMessageEditParam opoAlarmMessage);

    /**
     * 删除报警信息
     *
     * @param id 报警信息主键
     * @return 结果
     */
    public int deleteOpoAlarmMessageById(Long id);

    /**
     * 批量删除报警信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoAlarmMessageByIds(Long[] ids);

    List<String> findStationType();

    List<String> findDeviceType();

    List<String> findAlarmType();

    List<String> findConstruction();

    List<AlarmCountVo> findAlarmCount(OpoAlarmMessageParam param);

    Integer findAllCount(OpoAlarmMessageParam param);

    List<AlarmTypeCountVo> findTypePercent(OpoAlarmMessageParam param);



    List<StrAlarmMessageVo> selectStaAlarmMessage(@Param("staType")String staType, @Param("staName")String staName, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<VideoRegionVo> selectRegionTreeList();

    OpoAlarmMessagePo getMaxOneByCode(@Param("strCode")String strCode);

    OpoAlarmMessagePo getMaxOneByCodeType(@Param("strCode")String strCode, @Param("staType")String staType);

    Integer selectYearCountByCodeType(@Param("strCode")String staCode, @Param("staType")String staType);

    List<AlarmRankVo> findAlarmRank(OpoAlarmMessageParam param);
}
