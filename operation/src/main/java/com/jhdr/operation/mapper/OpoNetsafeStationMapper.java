package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoNetsafeStationPo;
import com.jhdr.operation.entity.param.OpoNetsafeStationParam;
import com.jhdr.operation.entity.param.OpoNetsafeStationAddParam;
import com.jhdr.operation.entity.param.OpoNetsafeStationEditParam;
import com.jhdr.operation.entity.vo.DeviceIpMonVo;
import com.jhdr.operation.entity.vo.OpoNetsafeStationVo;
import org.apache.ibatis.annotations.Param;


/**
 * 运维监控-网络安全设备Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface OpoNetsafeStationMapper extends BaseMapper<OpoNetsafeStationPo>
{
    /**
     * 查询运维监控-网络安全设备
     *
     * @param id 运维监控-网络安全设备主键
     * @return 运维监控-网络安全设备
     */
    public OpoNetsafeStationVo selectOpoNetsafeStationById(Long id);

    /**
     * 查询运维监控-网络安全设备列表
     *
     * @param opoNetsafeStation 运维监控-网络安全设备
     * @return 运维监控-网络安全设备集合
     */
    public List<OpoNetsafeStationVo> selectOpoNetsafeStationList(OpoNetsafeStationParam opoNetsafeStation);

    /**
     * 新增运维监控-网络安全设备
     *
     * @param opoNetsafeStation 运维监控-网络安全设备
     * @return 结果
     */
    public int insertOpoNetsafeStation(OpoNetsafeStationAddParam opoNetsafeStation);

    /**
     * 修改运维监控-网络安全设备
     *
     * @param opoNetsafeStation 运维监控-网络安全设备
     * @return 结果
     */
    public int updateOpoNetsafeStation(OpoNetsafeStationEditParam opoNetsafeStation);

    /**
     * 删除运维监控-网络安全设备
     *
     * @param id 运维监控-网络安全设备主键
     * @return 结果
     */
    public int deleteOpoNetsafeStationById(Long id);

    /**
     * 批量删除运维监控-网络安全设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoNetsafeStationByIds(Long[] ids);

    List<DeviceIpMonVo> findSafeByState(@Param("state") String state);

    void updateNetWork(@Param("id")Integer id, @Param("time")String time, @Param("state")String state, @Param("alarmType")String alarmType);

    Integer getSafeStateAmount(@Param("status")String status);
}
