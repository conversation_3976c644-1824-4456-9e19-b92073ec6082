package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoDispatchDetailPo;
import com.jhdr.operation.entity.param.OpoDispatchDetailParam;
import com.jhdr.operation.entity.param.OpoDispatchDetailAddParam;
import com.jhdr.operation.entity.param.OpoDispatchDetailEditParam;
import com.jhdr.operation.entity.vo.OpoDispatchDetailVo;


/**
 * 工程调度审批流程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface OpoDispatchDetailMapper extends BaseMapper<OpoDispatchDetailPo>
{
    /**
     * 查询工程调度审批流程
     *
     * @param id 工程调度审批流程主键
     * @return 工程调度审批流程
     */
    public OpoDispatchDetailVo selectOpoDispatchDetailById(Integer id);

    /**
     * 查询工程调度审批流程列表
     *
     * @param opoDispatchDetail 工程调度审批流程
     * @return 工程调度审批流程集合
     */
    public List<OpoDispatchDetailVo> selectOpoDispatchDetailList(OpoDispatchDetailParam opoDispatchDetail);

    /**
     * 新增工程调度审批流程
     *
     * @param opoDispatchDetail 工程调度审批流程
     * @return 结果
     */
    public int insertOpoDispatchDetail(OpoDispatchDetailAddParam opoDispatchDetail);

    /**
     * 修改工程调度审批流程
     *
     * @param opoDispatchDetail 工程调度审批流程
     * @return 结果
     */
    public int updateOpoDispatchDetail(OpoDispatchDetailEditParam opoDispatchDetail);

    /**
     * 删除工程调度审批流程
     *
     * @param id 工程调度审批流程主键
     * @return 结果
     */
    public int deleteOpoDispatchDetailById(Integer id);

    /**
     * 批量删除工程调度审批流程
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoDispatchDetailByIds(Integer[] ids);
}
