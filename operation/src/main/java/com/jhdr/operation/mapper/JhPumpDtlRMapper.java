package com.jhdr.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.JhPumpDtlRPo;


import com.jhdr.operation.entity.po.JhPumpDtlRealPo;
import com.jhdr.operation.entity.po.JhPumpRealPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface JhPumpDtlRMapper extends BaseMapper<JhPumpDtlRPo> {


    JhPumpDtlRealPo selectRealPump(@Param("prjnmcd") String prjnmcd,@Param("pmpcd") String pmpcd);


    JhPumpDtlRPo selectDtlPump(@Param("prjnmcd") String prjnmcd,@Param("pmpcd")String pmpcd, @Param("clltm")Date clltm);

    JhPumpDtlRealPo selectRealPumpTest(@Param("prjnmcd") String prjnmcd,@Param("pmpcd")String pmpcd, @Param("clltm")Date clltm);
}
