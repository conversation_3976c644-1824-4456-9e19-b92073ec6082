package com.jhdr.operation.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.entity.vo.FindMonthFlowVo;
import com.jhdr.operation.entity.vo.FlowAccumStcdVo;
import com.jhdr.operation.entity.vo.FlowStcdVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ShareDataMapper {

    List<String> findRainStcds();

    List<String> findFlowStcds();

    @DS("slave2")
    StPptnRPo findRainDataByStcd(@Param("stcd") String stcd);

    StPptnRPo findRainRealData(@Param("stcd")String stcd, @Param("tm")Date tm);




    @DS("slave2")
    StRiverRPo findFlowDataByStcd(@Param("stcd")String stcd);

    StRiverRPo findFlowRealData(@Param("stcd")String stcd, @Param("tm")Date tm);

    List<String> findWaterLineStcds();

    List<FlowAccumStcdVo> findFlowAccumStcds();

    @DS("slave1")
    JhFlowspRPo findFlowAccumByStcd(@Param("nrcode")String nrcode);

    JhFlowspRPo findFlowAccumData(@Param("jhCode")String jhCode,@Param("tm") Date tm);


    List<StPptnRPo> findAllRain(@Param("stcd")String stcd);
    @DS("slave2")
    List<StPptnRPo> findShareAllRain(@Param("stcd")String stcd);
    @DS("slave2")
    List<StPptnRPo> findRainsByStcd(@Param("stcd")String stcd);

    List<FlowStcdVo> findAllFlow();
    @DS("slave1")
    JhFlowycRPo selectFlowByCode(@Param("nrcode")String nrcode, @Param("updateTime")Date updateTime);

    String findCodeByType(@Param("jhCode")String jhCode, @Param("type")String type);
    @DS("slave1")
    Double findDataByCode(@Param("nrcode")String nrcode);

    void updateContrastTime(@Param("jhCode")String jhCode, @Param("datatm")Date datatm);

    @DS("slave1")
    Double findNowFlowByCd(@Param("nrcode")String nrcode);
    @DS("slave1")
    Double findMonthFlowByNrCode(@Param("nrcode")String nrCode);

    List<FlowStcdVo> findNewAllFlow();

    StStationWaterYearPo findYearPoByTime(@Param("jhCode")String jhCode, @Param("year")String year, @Param("month")int month);

    @DS("slave1")
    List<FindMonthFlowVo> findAllMonthFlow(@Param("nrCode")String nrCode);
}
