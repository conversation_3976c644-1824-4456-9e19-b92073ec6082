package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoMessageSendPo;
import com.jhdr.operation.entity.param.OpoMessageSendParam;
import com.jhdr.operation.entity.param.OpoMessageSendAddParam;
import com.jhdr.operation.entity.param.OpoMessageSendEditParam;
import com.jhdr.operation.entity.vo.OpoMessageSendVo;
import com.jhdr.operation.entity.vo.OpoNoSendVo;


/**
 * 短信平台Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-30
 */
public interface OpoMessageSendMapper extends BaseMapper<OpoMessageSendPo>
{
    /**
     * 查询短信平台
     *
     * @param id 短信平台主键
     * @return 短信平台
     */
    public OpoMessageSendVo selectOpoMessageSendById(Integer id);

    /**
     * 查询短信平台列表
     *
     * @param opoMessageSend 短信平台
     * @return 短信平台集合
     */
    public List<OpoMessageSendVo> selectOpoMessageSendList(OpoMessageSendParam opoMessageSend);

    /**
     * 新增短信平台
     *
     * @param opoMessageSend 短信平台
     * @return 结果
     */
    public int insertOpoMessageSend(OpoMessageSendAddParam opoMessageSend);

    /**
     * 修改短信平台
     *
     * @param opoMessageSend 短信平台
     * @return 结果
     */
    public int updateOpoMessageSend(OpoMessageSendEditParam opoMessageSend);

    /**
     * 删除短信平台
     *
     * @param id 短信平台主键
     * @return 结果
     */
    public int deleteOpoMessageSendById(Integer id);

    List<OpoNoSendVo> getNoSendMessage();

    /**
     * 批量删除短信平台
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */

}
