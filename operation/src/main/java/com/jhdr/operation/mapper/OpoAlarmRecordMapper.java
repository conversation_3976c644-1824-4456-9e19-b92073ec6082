package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoAlarmRecordPo;
import com.jhdr.operation.entity.param.OpoAlarmRecordParam;
import com.jhdr.operation.entity.param.OpoAlarmRecordAddParam;
import com.jhdr.operation.entity.param.OpoAlarmRecordEditParam;
import com.jhdr.operation.entity.vo.OpoAlarmRecordVo;
import org.apache.ibatis.annotations.Param;


/**
 * 机房告警记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface OpoAlarmRecordMapper extends BaseMapper<OpoAlarmRecordPo>
{
    /**
     * 查询机房告警记录
     *
     * @param id 机房告警记录主键
     * @return 机房告警记录
     */
    public OpoAlarmRecordVo selectOpoAlarmRecordById(Long id);

    /**
     * 查询机房告警记录列表
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 机房告警记录集合
     */
    public List<OpoAlarmRecordVo> selectOpoAlarmRecordList(OpoAlarmRecordParam opoAlarmRecord);

    /**
     * 新增机房告警记录
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 结果
     */
    public int insertOpoAlarmRecord(OpoAlarmRecordAddParam opoAlarmRecord);

    /**
     * 修改机房告警记录
     *
     * @param opoAlarmRecord 机房告警记录
     * @return 结果
     */
    public int updateOpoAlarmRecord(OpoAlarmRecordEditParam opoAlarmRecord);

    /**
     * 删除机房告警记录
     *
     * @param id 机房告警记录主键
     * @return 结果
     */
    public int deleteOpoAlarmRecordById(Long id);

    /**
     * 批量删除机房告警记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoAlarmRecordByIds(Long[] ids);

    List<String> findAlarmType();

    List<OpoAlarmRecordVo> findStrAlarmRecord(@Param("deviceName") String deviceName, @Param("deviceType")String deviceType, @Param("startTime")String startTime, @Param("endTime")String endTime);

    List<String> findDeviceType();

    OpoAlarmRecordPo getMaxOneByCode(@Param("deviceName")String deviceName);
}
