package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoGoodsDetailPo;
import com.jhdr.operation.entity.param.OpoGoodsDetailParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailAddParam;
import com.jhdr.operation.entity.param.OpoGoodsDetailEditParam;
import com.jhdr.operation.entity.vo.OpoGoodsDetailVo;


/**
 * 物资详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
public interface OpoGoodsDetailMapper extends BaseMapper<OpoGoodsDetailPo>
{
    /**
     * 查询物资详情
     *
     * @param id 物资详情主键
     * @return 物资详情
     */
    public OpoGoodsDetailVo selectOpoGoodsDetailById(Integer id);

    /**
     * 查询物资详情列表
     *
     * @param opoGoodsDetail 物资详情
     * @return 物资详情集合
     */
    public List<OpoGoodsDetailVo> selectOpoGoodsDetailList(OpoGoodsDetailParam opoGoodsDetail);

    /**
     * 新增物资详情
     *
     * @param opoGoodsDetail 物资详情
     * @return 结果
     */
    public int insertOpoGoodsDetail(OpoGoodsDetailAddParam opoGoodsDetail);

    /**
     * 修改物资详情
     *
     * @param opoGoodsDetail 物资详情
     * @return 结果
     */
    public int updateOpoGoodsDetail(OpoGoodsDetailEditParam opoGoodsDetail);

    /**
     * 删除物资详情
     *
     * @param id 物资详情主键
     * @return 结果
     */
    public int deleteOpoGoodsDetailById(Integer id);

    /**
     * 批量删除物资详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoGoodsDetailByIds(Integer[] ids);
}
