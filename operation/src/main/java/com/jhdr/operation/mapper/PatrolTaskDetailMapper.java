package com.jhdr.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.param.PatrolTaskAddParam;
import com.jhdr.operation.entity.param.PatrolTaskEditParam;
import com.jhdr.operation.entity.param.PatrolTaskParam;
import com.jhdr.operation.entity.po.PatrolTaskDetailPo;
import com.jhdr.operation.entity.po.PatrolTaskPo;
import com.jhdr.operation.entity.vo.PatrolOneTaskVo;
import com.jhdr.operation.entity.vo.PatrolTaskDetailVo;
import com.jhdr.operation.entity.vo.PatrolTaskVo;

import java.util.List;


/**
 * 巡检任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface PatrolTaskDetailMapper extends BaseMapper<PatrolTaskDetailPo>
{

}
