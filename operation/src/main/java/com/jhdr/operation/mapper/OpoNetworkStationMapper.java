package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoNetworkStationPo;
import com.jhdr.operation.entity.param.OpoNetworkStationParam;
import com.jhdr.operation.entity.param.OpoNetworkStationAddParam;
import com.jhdr.operation.entity.param.OpoNetworkStationEditParam;
import com.jhdr.operation.entity.vo.DeviceIpMonVo;
import com.jhdr.operation.entity.vo.NetStateVo;
import com.jhdr.operation.entity.vo.OpoNetworkStationVo;
import org.apache.ibatis.annotations.Param;


/**
 * 运维监控-网络设备Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface OpoNetworkStationMapper extends BaseMapper<OpoNetworkStationPo>
{
    /**
     * 查询运维监控-网络设备
     *
     * @param id 运维监控-网络设备主键
     * @return 运维监控-网络设备
     */
    public OpoNetworkStationVo selectOpoNetworkStationById(Long id);



    /**
     * 查询运维监控-网络设备列表
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 运维监控-网络设备集合
     */
    public List<OpoNetworkStationVo> selectOpoNetworkStationList(OpoNetworkStationParam opoNetworkStation);


    Integer getWorkStateAmount(@Param("status")String status);
    /**
     * 新增运维监控-网络设备
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 结果
     */
    public int insertOpoNetworkStation(OpoNetworkStationAddParam opoNetworkStation);

    /**
     * 修改运维监控-网络设备
     *
     * @param opoNetworkStation 运维监控-网络设备
     * @return 结果
     */
    public int updateOpoNetworkStation(OpoNetworkStationEditParam opoNetworkStation);

    /**
     * 删除运维监控-网络设备
     *
     * @param id 运维监控-网络设备主键
     * @return 结果
     */
    public int deleteOpoNetworkStationById(Long id);

    /**
     * 批量删除运维监控-网络设备
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoNetworkStationByIds(Long[] ids);


    List<DeviceIpMonVo> findWorkByState(@Param("state") String state);



    List<NetStateVo> findTopologyState();

    void updateTopologyState(@Param("deviceName") String deviceName,@Param("status") String status);

    void updateNetWork(@Param("id")Integer id, @Param("time")String time, @Param("state")String state, @Param("alarmType")String alarmType);


}
