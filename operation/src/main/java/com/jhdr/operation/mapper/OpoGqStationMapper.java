package com.jhdr.operation.mapper;

import java.util.Date;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoGqStationPo;
import com.jhdr.operation.entity.param.OpoGqStationParam;
import com.jhdr.operation.entity.param.OpoGqStationAddParam;
import com.jhdr.operation.entity.param.OpoGqStationEditParam;
import com.jhdr.operation.entity.vo.*;
import org.apache.ibatis.annotations.Param;


/**
 * 运维监控-工情站点Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface OpoGqStationMapper extends BaseMapper<OpoGqStationPo>
{
    /**
     * 查询运维监控-工情站点
     *
     * @param id 运维监控-工情站点主键
     * @return 运维监控-工情站点
     */
    public OpoGqStationVo selectOpoGqStationById(Long id);

    /**
     * 查询运维监控-工情站点列表
     *
     * @param opoGqStation 运维监控-工情站点
     * @return 运维监控-工情站点集合
     */
    public List<OpoGqStationVo> selectOpoGqStationList(OpoGqStationParam opoGqStation);

    List<OpoPumpStationVo> selectPumpStation(OpoGqStationParam param);

    List<OpoGateStationVo> selectGateStationList(OpoGqStationParam param);

    /**
     * 新增运维监控-工情站点
     *
     * @param opoGqStation 运维监控-工情站点
     * @return 结果
     */
    public int insertOpoGqStation(OpoGqStationAddParam opoGqStation);

    /**
     * 修改运维监控-工情站点
     *
     * @param opoGqStation 运维监控-工情站点
     * @return 结果
     */
    public int updateOpoGqStation(OpoGqStationEditParam opoGqStation);

    /**
     * 删除运维监控-工情站点
     *
     * @param id 运维监控-工情站点主键
     * @return 结果
     */
    public int deleteOpoGqStationById(Long id);

    /**
     * 批量删除运维监控-工情站点
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoGqStationByIds(Long[] ids);


    List<OpoGateStationVo> selectGateDetail(String strCode);

    List<OpoPumpStationVo> selectPumpDetail(String strCode);

    List<String> getPumpName(@Param("name") String name);

    List<String> getGateName(@Param("name")String name);



    List<WaterRegionVo> pustRegion();

    List<WaterRegionVo> wagaRegion();

    List<MachineStateVo> findPumpCodeByState(@Param("state")String state);

    List<MachineStateVo> findTimeByCode(@Param("code")String code);

    int updatePumpState(@Param("strCode")String strCode, @Param("deviceCode")String deviceCode, @Param("state")String state, @Param("alarmType")String alarmType, @Param("time")String time);

    int updatePustState(@Param("strCode")String strCode, @Param("state")String state, @Param("alarmType")String alarmType, @Param("time")String time,@Param("alarmId")Integer alarmId);

    int findPumpUseCount(@Param("code")String code);

    List<MachineStateVo> findGateCodeByState(@Param("state")String state);

    List<MachineStateVo> findGateTimeByCode(@Param("code")String code);

    int updateGateState(@Param("strCode")String strCode, @Param("state")String state, @Param("alarmType")String alarmType, @Param("time")String time);

    int updateWagaState(@Param("strCode")String strCode, @Param("state")String state, @Param("alarmType")String alarmType, @Param("time")String time);

    int findGateUseCount(@Param("strCode")String code);

    Integer getPustStateAmount(@Param("addvcd")String addvcd, @Param("status")String status);

    Integer getWagaStateAmount(@Param("addvcd")String addvcd, @Param("status")String status);

    Date findPumpDtlTime(@Param("strCode")String strCode);

    Date findWagaDtlTime(@Param("strCode")String strCode);

    void isMon(@Param("strCode")String strCode, @Param("opoMon")Integer opoMon);

    void isWagaMon(@Param("strCode")String strCode, @Param("status")Integer status);
}
