package com.jhdr.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.JhPumpRealPo;
import com.jhdr.operation.entity.po.JhSlcrsDtlRealPo;
import com.jhdr.operation.entity.po.JhSlcrsRPo;
import com.jhdr.operation.entity.po.JhSlcrsRealPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface JhSlcrsRMapper extends BaseMapper<JhSlcrsRPo> {


    JhSlcrsRealPo selectRealSlcrs(@Param("prjnmcd") String prjnmcd);

    JhSlcrsDtlRealPo selectDtlRealSlcrs(@Param("gpcd")String gpcd,@Param("prjnmcd")String prjnmcd);

    JhSlcrsRealPo selectSlcrsR(@Param("prjnmcd")String prjnmcd, @Param("clltm")Date clltm);
}
