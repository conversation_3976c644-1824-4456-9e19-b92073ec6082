package com.jhdr.operation.mapper;



import com.baomidou.dynamic.datasource.annotation.DS;
import com.jhdr.operation.entity.po.JhPumpDtlRPo;
import com.jhdr.operation.entity.po.JhSlcrsDtlRPo;
import com.jhdr.operation.entity.po.*;
import com.jhdr.operation.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface CommonMapper {



    @DS("slave1")
    List<FindWaterLineVo> findAllWaterLine();

    FindWaterLineVo findLineDataByCodeTime(@Param("code") String code, @Param("addTime") Date addTime);

    int insertWaterLine(@Param("code") String code, @Param("waterLine") Double waterLine, @Param("addTime") Date addTime);

    @DS("slave1")
    List<FindFlowVo> findAllFlow();

    FindFlowVo findFlowDataByCodeTime(@Param("code") String code, @Param("addTime") Date addTime);

    int insertFlow(@Param("code") String code, @Param("flow") Double flow, @Param("addTime") Date addTime);

    @DS("slave1")
    List<PumpRealVo> findAllPumpReal(@Param("pid")String pid);

    JhPumpDtlRPo findNewPumpDtl(@Param("pid")String pid, @Param("id")String id, @Param("addTime")Date addTime);

    List<String> findAllPid();

    @DS("slave1")
    GateRealVo findGateById(@Param("pid") String pid);
    @DS("slave1")
    GateRealVo findGate1ById(@Param("pid")String pid);
    @DS("slave1")
    GateRealVo findGate2ById(@Param("pid")String pid);

    @DS("slave1")
    GateRealVo find1GateById(@Param("id")String id);
    @DS("slave1")
    GateRealVo findGateNameType(@Param("name")String name, @Param("type")String type);

    JhSlcrsDtlRPo findSlcrsByPid(@Param("id")String id,@Param("pid")String pid, @Param("addTime") Date addTime );

    @DS("slave1")
    Integer findAllPumoState(@Param("pid")String pid);
    @DS("slave1")
    Integer findRunPumpState(@Param("pid")String pid);
    @DS("slave1")
    Integer findRunGateCount(@Param("name")String name);

    String exchangePmpcdByid(@Param("id")String id);

    String exchangePrjnmcdByid(@Param("id")String id);

    String exchangeGpcdByid(@Param("id")String id);

    @DS("slave1")
    Double findWaterLineByData(@Param("name") String name, @Param("waterName")String waterName);
    @DS("slave1")
    GateRealVo findWaterLineByCode(@Param("code")String code);

    StWasRPo findRealWasData(@Param("stcd")String stcd, @Param("addTime")Date addTime);

    StRiverRPo findRealRiverData(@Param("stcd")String stcd, @Param("addTime")Date addTime);

    String findJhCodeByCode(@Param("code")String code);

    List<String> findShareCds(@Param("type")String type);

    @DS("slave2")
    StWasRPo findUpWaterByCode(@Param("upcd")String upcd);
    @DS("slave2")
    StRiverRPo findDownWaterByCode(@Param("downCd")String downCd);

    List<PustCodeVo> findPustPid();

    String findWdwcdByStrCode(@Param("strCode")String strCode);

    String findWupcdByStrCode(@Param("strCode")String strCode);

    String getWaterLineByName(@Param("name")String name);
    @DS("slave4")
    List<OpoUser> getUsers();

    VoiceResultVo findGisData(@Param("strName")String strName, @Param("type")String type);

    Integer findAllPumpCount(@Param("strCode")String strCode);

    Integer findRunPumpCount(@Param("strCode")String strCode);

    Double findOneDataById(@Param("code")String code);

    List<WagaCodeVo> findWagaList();

    List<GateCodeVo> findGateCodesByStr(@Param("strCode")String strCode);
}
