package com.jhdr.operation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.param.PatrolPlanAddParam;
import com.jhdr.operation.entity.param.PatrolPlanEditParam;
import com.jhdr.operation.entity.param.PatrolPlanParam;
import com.jhdr.operation.entity.po.PatrolPlanPo;
import com.jhdr.operation.entity.po.PatrolProgressPo;
import com.jhdr.operation.entity.vo.PatrolOnePlanVo;
import com.jhdr.operation.entity.vo.PatrolPlanDetailVo;
import com.jhdr.operation.entity.vo.PatrolPlanVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 巡检计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface PatrolProgressMapper extends BaseMapper<PatrolProgressPo>
{

}
