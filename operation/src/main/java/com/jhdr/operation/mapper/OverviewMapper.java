package com.jhdr.operation.mapper;



import com.jhdr.operation.entity.vo.*;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OverviewMapper {


    List<OverStationVo> findStationCount();

    List<OverStationVo> findRoomCount();

    List<OverStationAlarmVo> findStationAlarmCount();

    List<OverStationAlarmVo> findDeviceAlarmCount();

    List<OverAlarmTypeVo> findAlarmTypeCount();

    Integer findAllCount();

    List<OverStationAlarmVo> findDeviceAlarmTop();

    List<OverStationStateVo> findPustState();


    List<FindOverStateVo> findOverState();

    List<FindOverStateVo> findOverviewWire();
}
