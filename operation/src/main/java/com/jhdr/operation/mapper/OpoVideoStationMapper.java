package com.jhdr.operation.mapper;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.OpoVideoStationPo;
import com.jhdr.operation.entity.param.OpoVideoStationParam;
import com.jhdr.operation.entity.param.OpoVideoStationAddParam;
import com.jhdr.operation.entity.param.OpoVideoStationEditParam;
import com.jhdr.operation.entity.vo.OpoDHVideoStateVo;
import com.jhdr.operation.entity.vo.OpoHKWSVideoStateVo;
import com.jhdr.operation.entity.vo.OpoVideoStateVo;
import com.jhdr.operation.entity.vo.OpoVideoStationVo;
import org.apache.ibatis.annotations.Param;


/**
 * 运维监控-视频站点Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-24
 */
public interface OpoVideoStationMapper extends BaseMapper<OpoVideoStationPo>
{
    /**
     * 查询运维监控-视频站点
     *
     * @param cd 运维监控-视频站点主键
     * @return 运维监控-视频站点
     */
    public OpoVideoStationVo selectOpoVideoStationById(@Param("cd") String cd);

    /**
     * 查询运维监控-视频站点列表
     *
     * @param opoVideoStation 运维监控-视频站点
     * @return 运维监控-视频站点集合
     */
    public List<OpoVideoStationVo> selectOpoVideoStationList(OpoVideoStationParam opoVideoStation);

    /**
     * 新增运维监控-视频站点
     *
     * @param opoVideoStation 运维监控-视频站点
     * @return 结果
     */
    public int insertOpoVideoStation(OpoVideoStationAddParam opoVideoStation);

    /**
     * 修改运维监控-视频站点
     *
     * @param opoVideoStation 运维监控-视频站点
     * @return 结果
     */
    public int updateOpoVideoStation(OpoVideoStationEditParam opoVideoStation);

    /**
     * 删除运维监控-视频站点
     *
     * @param id 运维监控-视频站点主键
     * @return 结果
     */
    public int deleteOpoVideoStationById(Long id);

    /**
     * 批量删除运维监控-视频站点
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpoVideoStationByIds(Long[] ids);

//    Date findAlarmTimeByCd(@Param("cd") String cd);

    int updateCodeByNm(@Param("name")String name, @Param("code")String code);


    List<String> findAllCodes();


    List<OpoVideoStateVo> findCodeByState(@Param("state")String state);



    int updateState(@Param("code")String code, @Param("status")String status, @Param("time")String time);

    void updateUpdateTime(@Param("indexCode")String indexCode);


     void updateDiscernByChannel(@Param("nChannelID") int nChannelID, @Param("picture") String picture);



    Integer getVideoStateAmount(@Param("addvcd")String addvcd, @Param("status")String status);


    List<OpoDHVideoStateVo> selectDHVideoChannel(@Param("state")String state);

    void updateUpdateTimeByCd(@Param("cd")String cd);

    String selectNmByIndexCode(@Param("indexCode")String indexCode);

    List<OpoHKWSVideoStateVo> selectHKWSCode( @Param("state")String state);

    void updateStateByCd(@Param("cd")String cd, @Param("state")String state, @Param("time")String time, @Param("content")String content);

    void isMon(@Param("cd")String cd, @Param("isMon")Integer isMon);
}
