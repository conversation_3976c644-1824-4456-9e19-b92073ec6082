package com.jhdr.operation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.operation.entity.po.PatrolTaskPo;
import com.jhdr.operation.entity.param.PatrolTaskParam;
import com.jhdr.operation.entity.param.PatrolTaskAddParam;
import com.jhdr.operation.entity.param.PatrolTaskEditParam;
import com.jhdr.operation.entity.vo.PatrolOneTaskVo;
import com.jhdr.operation.entity.vo.PatrolResultVo;
import com.jhdr.operation.entity.vo.PatrolTaskDetailVo;
import com.jhdr.operation.entity.vo.PatrolTaskVo;
import org.apache.ibatis.annotations.Param;


/**
 * 巡检任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
public interface PatrolTaskMapper extends BaseMapper<PatrolTaskPo> {
    /**
     * 查询巡检任务
     *
     * @param id 巡检任务主键
     * @return 巡检任务
     */
    public PatrolOneTaskVo selectPatrolTaskById(Integer id);

    /**
     * 查询巡检任务列表
     *
     * @param patrolTask 巡检任务
     * @return 巡检任务集合
     */
    public List<PatrolTaskVo> selectPatrolTaskList(PatrolTaskParam patrolTask);

    /**
     * 新增巡检任务
     *
     * @param patrolTask 巡检任务
     * @return 结果
     */
    public int insertPatrolTask(PatrolTaskAddParam patrolTask);

    /**
     * 修改巡检任务
     *
     * @param patrolTask 巡检任务
     * @return 结果
     */
    public int updatePatrolTask(PatrolTaskEditParam patrolTask);

    /**
     * 删除巡检任务
     *
     * @param id 巡检任务主键
     * @return 结果
     */
    public int deletePatrolTaskById(Long id);

    /**
     * 批量删除巡检任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePatrolTaskByIds(Long[] ids);

    List<PatrolTaskDetailVo> selectAlarmDetails(Integer id);

    Integer findUseAmount(Integer id);

    Integer findAlarmAmount(Integer id);

    PatrolTaskPo findFristTask();




    List<PatrolResultVo> selectPatrolResult(@Param("scope") String scope);

    List<PatrolResultVo> selectAllResult();

    List<String> findNotify(@Param("patrolId")Integer patrolId);

    List<PatrolResultVo> selectResultByScope(@Param("scope")String scope);

    Integer selectAmountByScope(@Param("scope")String scope4);
    
    void finish();

    String findProgress();
}
