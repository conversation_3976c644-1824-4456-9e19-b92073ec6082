package com.jhdr.operation;

import com.jhdr.common.security.annotation.EnableCustomConfig;
import com.jhdr.common.security.annotation.EnableRyFeignClients;
import com.jhdr.common.security.aspect.PreAuthorizeAspect;
import com.jhdr.common.swagger.annotation.EnableCustomSwagger2;
//import com.jhdr.operation.utils.videoSb.IVSSIntelligentEventDemo;
//import com.jhdr.operation.utils.videoSb.VideoSbDemo;
//import com.jhdr.operation.utils.videoSb.WaterColorDemo;
import com.jhdr.operation.utils.videoSb.VideoSbDemo;
import lombok.extern.java.Log;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.multipart.MultipartFile;

//import static com.jhdr.operation.utils.videoSb.Initialization.InitTest;
//import static com.jhdr.operation.utils.videoSb.Initialization.LoginOut;

@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@EnableScheduling
//@SpringBootApplication
@Log
//本地调试时候切换这个，取消权限校验 todo
@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, PreAuthorizeAspect.class})
public class OperationApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(OperationApplication.class, args);
        log.info("(♥◠‿◠)ﾉﾞ  运维监控一体化模块启动成功  ");
        VideoSbDemo.init();
        log.info("视频识别成功+++++++++++++");
    }


}

