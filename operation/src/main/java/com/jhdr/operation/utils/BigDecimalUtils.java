package com.jhdr.operation.utils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;

/**
 * 版权：金海迪尔公司.大数据研发中心
 *
 * @author: 江涛
 * @date: 2024/06/25
 * @description: 计算工具类
 */
public class BigDecimalUtils {


    /*
     * 版权：金海迪尔公司.大数据研发中心
     * @author: 江涛
     * @date: 2021/12/24
     * @description: 加法运算
     * @param null
     * @return
     */
    public static BigDecimal add(double val1,double val2){
        BigDecimal a = new BigDecimal(String.valueOf(val1));
        BigDecimal b = new BigDecimal(String.valueOf(val2));
        return a.add(b);
    }


    /*
     * 版权：金海迪尔公司.大数据研发中心
     * @author: 江涛
     * @date: 2021/12/24
     * @description: 相减
     * @param null
     * @return
     */
    public static BigDecimal subtract(double val1,double val2){
        BigDecimal a = new BigDecimal(String.valueOf(val1));
        BigDecimal b = new BigDecimal(String.valueOf(val2));
        return a.subtract(b);
    }



    /*
     * 版权：金海迪尔公司.大数据研发中心
     * @author: 江涛
     * @date: 2021/12/24
     * @description: 乘法
     * @param null
     * @return
     */
    public static BigDecimal multiply(double val1,double val2){
        BigDecimal a = new BigDecimal(String.valueOf(val1));
        BigDecimal b = new BigDecimal(String.valueOf(val2));
        return a.multiply(b);
    }


    /*
     * 版权：金海迪尔公司.大数据研发中心
     * @author: 江涛
     * @date: 2021/12/24
     * @description: 除法
     * @param null
     * @return
     */
    public static BigDecimal divide(double val1,double val2){
        BigDecimal a = new BigDecimal(String.valueOf(val1));
        BigDecimal b = new BigDecimal(String.valueOf(val2));
        return a.divide(b, 2, BigDecimal.ROUND_HALF_UP);
    }



    /*
     * 版权：金海迪尔公司.大数据研发中心
     * @author: 江涛
     * @date: 2021/12/24
     * @description: 集合累加
     * @param null
     * @return
     */
    public static BigDecimal addList(List<?> list){
        BigDecimal sum = new BigDecimal(String.valueOf(0.00));
        for (Object val : list) {
            sum = sum.add(new BigDecimal(String.valueOf(val)));
        }
        return sum;
    }


    /*
     * 版权：金海迪尔公司.大数据研发中心
     * @author: 江涛
     * @date: 2021/12/24
     * @description: 保留两位小数
     * @param null
     * @return
     */
    public static String reserve(Double val) {
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(val);
    }

    public static String reserve2(Double val) {
        DecimalFormat df = new DecimalFormat("0.0000");
        return df.format(val);
    }
}
