package com.jhdr.operation.utils.videoSb;

import com.jhdr.common.core.utils.SpringUtils;
import com.jhdr.operation.mapper.OpoVideoStationMapper;

import com.sun.jna.Pointer;
import lombok.AllArgsConstructor;
import lombok.extern.java.Log;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version 1.0
 * @description ERR220210123
 * @date 2022/2/15 16:11
 */
@Component
@Log
public class IVSSIntelligentEventDemo extends Initialization {



    private  static  OpoVideoStationMapper opoVideoStationMapper;



//      static {
//        // 初始化静态引用
//        opoVideoStationMapper = ApplicationContextProvider.getApplicationContext().getBean(OpoVideoStationMapper.class);
//    }
    int channel=-1;
    NetSDKLib.LLong    attachHandle=new NetSDKLib.LLong(0);
    /**
     * 订阅智能任务
     */

    public NetSDKLib.LLong AttachEventRealLoadPic() {
        // 先退订，设备不会对重复订阅作校验，重复订阅后会有重复的事件返回
        if(attachHandle.longValue()!=0){
            this.DetachEventRealLoadPic();
        }

        // 需要图片
        int bNeedPicture = 1;
        attachHandle = netSdk.CLIENT_RealLoadPictureEx(loginHandle, channel, NetSDKLib.EVENT_IVS_ALL, bNeedPicture,
                AnalyzerDataCB.getInstance(), null, null);
        if (attachHandle.longValue() != 0) {
            System.out.printf("Chn[%d] CLIENT_RealLoadPictureEx Success\n", channel);
        } else {
            System.out.printf("Chn[%d] CLIENT_RealLoadPictureEx Failed!LastError = %s\n", channel,
                    ToolKits.getErrorCode());
        }

        return attachHandle;
    }


    /**
     * 报警事件（智能）回调
     */
    private static class AnalyzerDataCB implements NetSDKLib.fAnalyzerDataCallBack {
        private final File picturePath;
        private static AnalyzerDataCB instance;

        private AnalyzerDataCB() {
            picturePath = new File("/home/<USER>/uploadPath/videosb");
            if (!picturePath.exists()) {
                picturePath.mkdirs();
            }
        }

        public static AnalyzerDataCB getInstance() {
            if (instance == null) {
                synchronized (AnalyzerDataCB.class) {
                    if (instance == null) {
                        instance = new AnalyzerDataCB();
                    }
                }
            }
            return instance;
        }




        @Override
        public int invoke(NetSDKLib.LLong lAnalyzerHandle, int dwAlarmType, Pointer pAlarmInfo, Pointer pBuffer, int dwBufSize,
                          Pointer dwUser, int nSequence, Pointer reserved) {
            if (lAnalyzerHandle == null || lAnalyzerHandle.longValue() == 0) {
                return -1;
            }

            switch (dwAlarmType) {
                case NetSDKLib.EVENT_IVS_FISHING_DETECTION:  {//钓鱼检测事件(对应 DEV_EVENT_FISHING_DETECTION_INFO )
//                    log.info("钓鱼检测事件");

                    DEV_EVENT_FISHING_DETECTION_INFO msg=new DEV_EVENT_FISHING_DETECTION_INFO();

                    ToolKits.GetPointerData(pAlarmInfo, msg);

                    byte[] szName = msg.szName;
                    try {
                        System.out.println("szName UTF-8:"+new String(szName,"UTF-8"));
                        System.out.println("szName GBK:"+new String(szName,"GBK"));
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }

                    //检测目标物体的个数
                    int nObjectCount = msg.nObjectCount;
                    long sysTime=System.currentTimeMillis();
                    NetSDKLib.NET_MSG_OBJECT_EX2[] stuObjects
                            = msg.stuObjects;
                        for(int i=0;i<nObjectCount;i++){

                            NetSDKLib.NET_MSG_OBJECT_EX2 stuObject
                                    = stuObjects[i];
                            NetSDKLib.NET_PIC_INFO stPicInfo
                                    = stuObject.stPicInfo;

//                            if (stPicInfo != null && stPicInfo.dwFileLenth > 0) {
//                                String picture = picturePath + "/" + sysTime + "related.jpg";
//                                ToolKits.savePicture(pBuffer, stPicInfo.dwOffSet, stPicInfo.dwFileLenth, picture);
//                                String filePath="http://10.34.188.148:9300/statics/videosb/"+ sysTime + "related.jpg";
//                                opoVideoStationService.updateDiscernByChannel(msg.nChannelID,filePath);
//                            }



                            //图片
//                            if (stPicInfo != null && stPicInfo.dwFileLenth > 0) {
//                                String picture = picturePath + "/" + System.currentTimeMillis() + "related.jpg";
//                                ToolKits.savePicture(pBuffer, stPicInfo.dwOffSet, stPicInfo.dwFileLenth, picture);
//                            }
                        }
                        NetSDKLib.SCENE_IMAGE_INFO_EX stuSceneImage=msg.stuSceneImage;
                        if (stuSceneImage !=null && stuSceneImage.nLength>0){
                            String picture=picturePath +"/" +sysTime+"scene.jpg";
                            ToolKits.savePicture(pBuffer,stuSceneImage.nOffSet,stuSceneImage.nLength,picture);
//                            log.info("视频站点的编号是："+msg.nChannelID);
                            String filePath="http://10.34.188.148:9300/statics/videosb/"+ sysTime + "scene.jpg";
//                            log.info("视频站点的路径是："+filePath);

                            getOpoVideoStationMapper().updateDiscernByChannel(msg.nChannelID,filePath);


                        }

                    break;
                }
                default:

                    break;
            }
            return 0;
        }
    }


    /**
     * 停止侦听智能事件
     */
    public void DetachEventRealLoadPic() {
        if (this.attachHandle.longValue() != 0) {
            netSdk.CLIENT_StopLoadPic(this.attachHandle);
        }
    }
    public void RunTest()
    {
        System.out.println("Run Test");
        CaseMenu menu = new CaseMenu();;
        menu.addItem((new CaseMenu.Item(this , "AttachEventRealLoadPic" , "AttachEventRealLoadPic")));
        menu.addItem((new CaseMenu.Item(this , "AttachEventRealLoadPic" , "AttachEventRealLoadPic")));

        menu.run();
    }

//    public static void main(String[] args) {
//        IVSSIntelligentEventDemo ivssIntelligentEventDemo=new IVSSIntelligentEventDemo();
//        WaterColorDemo waterColorDemo=new WaterColorDemo();
//        //20.2.35.57
////        InitTest("10.34.188.221",37777,"admin","jh123456");
//        InitTest("192.168.1.108",37777,"admin","jhdr@2024");
//        ivssIntelligentEventDemo.RunTest();
//        waterColorDemo.RunTest();
//
//        LoginOut();
//    }


    public static OpoVideoStationMapper getOpoVideoStationMapper() {
        if (opoVideoStationMapper == null){
            opoVideoStationMapper = SpringUtils.getBean(OpoVideoStationMapper.class);
        }
        return opoVideoStationMapper;
    }
//    @Autowired
//    public void setOpoVideoStationMapper(OpoVideoStationMapper opoVideoStationMapper) {
//        IVSSIntelligentEventDemo.opoVideoStationMapper = opoVideoStationMapper;
//    }
}
