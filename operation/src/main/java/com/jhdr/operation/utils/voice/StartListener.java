package com.jhdr.operation.utils.voice;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

//@Component
public class StartListener implements ApplicationListener<ApplicationStartedEvent> {

    private Logger logger = LoggerFactory.getLogger(StartListener.class);

    @Resource
    private AstConfig astConfig;
    @Resource
    private PerformanceConfig performanceConfig;
    @Resource
    private ParamConfig paramConfig;
    @Resource
    private OtherConfig otherConfig;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent applicationStartedEvent) {
        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(performanceConfig.getThread());
        CountDownLatch countDownLatch = new CountDownLatch(performanceConfig.getCount());
        Semaphore semaphore = new Semaphore(performanceConfig.getThread());
        int startTime = performanceConfig.getStartTime();
        int sigTime = (startTime * 1000) / performanceConfig.getThread();
        logger.info("约{}ms,启动一个连接", sigTime);

        try {
            for (int i = 0; i < performanceConfig.getCount(); i++) {
                final int finalI = i + 1;
                semaphore.acquireUninterruptibly();
                fixedThreadPool.submit(() -> {
                    WebsocketClient websocketClient = new WebsocketClient(countDownLatch, semaphore, "客户端" + finalI, astConfig, paramConfig, otherConfig);
                    websocketClient.start();
                });
                Thread.sleep(sigTime);
            }
            countDownLatch.await();
            fixedThreadPool.shutdown();
            WebsocketClient.stop();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
