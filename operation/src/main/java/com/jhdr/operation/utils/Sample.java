package com.jhdr.operation.utils;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import com.baidu.aip.speech.AipSpeech;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class Sample {
    private static final String serverURL = "http://vop.baidu.com/server_api";
    private static String token = "";
    private static final String testFileName ="D:\\voiceExchange\\test.pcm";
    public static final String cuid = "KAliwovyTi69hvzPG4lC10Luj5skedp5";
    public static final String apiKey = "Z8SEeCofD1bVX7C24rb40T15";
    public static final String secretKey = "hdE6Thkiu2TXvAh5GFztPpxU7Tys7Vz3";
    public static void main(String[] args) throws Exception {
//        getToken();
//       /* method1();
//        method2();*/
//        method3();
        method8();
    }


    public static void method8() throws IOException {
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("text", "测试语音12313232");
        paramMap.put("role", "lingxioaqi");
        paramMap.put("speed", "5");
        System.out.println("+++++");

//        String result2 = HttpRequest.post("http://10.34.188.196:8512/tts")
        String result2 = HttpRequest.post("http://10.34.0.38:8512/tts")
                .header(Header.USER_AGENT, "Hutool http")//头信息，多个头信息多次调用此方法即可
                .form(paramMap.toString())
                .timeout(20000)//超时，毫秒
                .execute().body();
        System.out.println(result2);

    }

    private static void getToken() throws Exception {
        String getTokenURL = "https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials" +
                "&client_id=" + apiKey + "&client_secret=" + secretKey;
        HttpURLConnection conn = (HttpURLConnection) new URL(getTokenURL).openConnection();
        token = new JSONObject(printResponse(conn)).getStr("access_token");
    }


    private static void method3(){
        // 初始化一个AipSpeech  cuid  apiKey  secretKey
        AipSpeech client = new AipSpeech(cuid, apiKey, secretKey);

        // 可选：设置网络连接参数
        client.setConnectionTimeoutInMillis(2000);
        client.setSocketTimeoutInMillis(60000);

        // 可选：设置代理服务器地址, http和socket二选一，或者均不设置
        //client.setHttpProxy("proxy_host", proxy_port);  // 设置http代理
        //client.setSocketProxy("proxy_host", proxy_port);  // 设置socket代理

        // 可选：设置log4j日志输出格式，若不设置，则使用默认配置
        // 也可以直接通过jvm启动参数设置此环境变量
        //System.setProperty("aip.log4j.conf", "path/to/your/log4j.properties");

        // 调用接口
        org.json.JSONObject res = client.asr(testFileName, "pcm", 16000, null);
        com.alibaba.fastjson.JSONObject jsonObject1 = com.alibaba.fastjson.JSONObject.parseObject(res.toString(2));
        String result = jsonObject1.getString("result");
        if(result.length() >= 4) {
            String rs1 = result.substring(2, result.length() - 2);
            System.out.println(rs1);
        } else {
            System.out.println("字符串长度不足，无法去除前后两个字符");
        }


//        System.out.println(res.toString(2));

    }


    private static String printResponse(HttpURLConnection conn) throws Exception {
        if (conn.getResponseCode() != 200) {
            // request error
            return "";
        }
        InputStream is = conn.getInputStream();
        BufferedReader rd = new BufferedReader(new InputStreamReader(is));
        String line;
        StringBuffer response = new StringBuffer();
        while ((line = rd.readLine()) != null) {
            response.append(line);
            response.append('\r');
        }
        rd.close();
//        System.out.println(new JSONObject(response.toString()).toString());
        return response.toString();
    }

    private static byte[] loadFile(File file) throws IOException {
        InputStream is = new FileInputStream(file);

        long length = file.length();
        byte[] bytes = new byte[(int) length];

        int offset = 0;
        int numRead = 0;
        while (offset < bytes.length
                && (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
            offset += numRead;
        }

        if (offset < bytes.length) {
            is.close();
            throw new IOException("Could not completely read file " + file.getName());
        }

        is.close();
        return bytes;
    }

    public static int method4() throws IOException {
        String lasturl = "http://10.34.0.52:7001/sltCas/login";
        String data = "";
        URL url = new URL(lasturl);
        HttpURLConnection urlConn = (HttpURLConnection) url.openConnection();

        //请求头
        urlConn.setRequestProperty("Accept-Charset", "utf-8");
        urlConn.setRequestProperty("Content-Type", "application/json; charset=utf-8");

        urlConn.setDoOutput(true);
        urlConn.setDoInput(true);
        urlConn.setRequestMethod("GET");//GET和POST必须全大写
        urlConn.connect();

        int code = urlConn.getResponseCode();//获得响应码
        if(code == 200) {//响应成功，获得响应的数据
            System.out.println("成功");

        }else {
            System.out.println("失败");
        }

        urlConn.disconnect();   //断开连接
        return code;

    }



    private static com.alibaba.fastjson.JSONObject method6() throws IOException {
//        URL url = new URL("http://10.34.188.196:8512/tts");          //发送请求路径
        URL url = new URL("http://10.34.0.38:8512/tts");          //发送请求路径
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
        connection.setRequestProperty("Accept", "application/json");
        connection.setDoOutput(true);

        // 打印请求信息
//        System.out.println("Request URL: " + requestUrl);
//        System.out.println("Request Body: " + jsonInputString);
        String jsonInputString="{\n" +
                "       \"text\":\"测试语音12313232\",\n" +
                "       \"role\":\"lingxioaqi\",\n" +
                "       \"speed\":\"5\"\n" +
                "    }";

        // 发送请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // 读取响应
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(response.toString());
        // 打印返回结果
        System.out.println("Response: " + response.toString());
        System.out.println("Response: " + response.toString());
        System.out.println(jsonObject);
        return jsonObject;
    }

}
