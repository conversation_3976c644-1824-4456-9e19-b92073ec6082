package com.jhdr.operation.utils.voice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.http.DefaultHttpHeaders;
import io.netty.handler.codec.http.FullHttpResponse;

import io.netty.handler.codec.http.HttpClientCodec;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.websocketx.*;
import io.netty.handler.codec.http.websocketx.extensions.compression.WebSocketClientCompressionHandler;
import io.netty.util.ReferenceCountUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

public class WebsocketClient {




    private Logger logger = LoggerFactory.getLogger(WebsocketClient.class);


    private Channel channel;

    private static final NioEventLoopGroup WORK_GROUP = new NioEventLoopGroup();
    private CountDownLatch countDownLatch;

    private Semaphore semaphore;
    private String clientName;
    private AstConfig astConfig;
    private ParamConfig paramConfig;
    private OtherConfig otherConfig;
    private String traceid;
    private static double avgOffsetTime;

    public WebsocketClient(CountDownLatch countDownLatch, Semaphore semaphore, String clientName, AstConfig astConfig, ParamConfig paramConfig, OtherConfig otherConfig) {
        this.countDownLatch = countDownLatch;
        this.semaphore = semaphore;
        this.clientName = clientName;
        this.astConfig = astConfig;
        this.paramConfig = paramConfig;
        this.otherConfig = otherConfig;
    }

    public void start() {
        traceid = UUID.randomUUID().toString().replace("-", "");
        MDC.put("traceid", traceid);
        logger.info("启动{},当前发送延时偏移{}ms", clientName, avgOffsetTime);
        ChannelPromise handshakeFuture;
        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.option(ChannelOption.TCP_NODELAY, true);
            bootstrap.option(ChannelOption.SO_KEEPALIVE, true);
            bootstrap.option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 2000);
            URI uri = getUri();
            logger.info("{},请求地址：{}", clientName, uri);
            WebsocketClientHandshakerHandler handshakerHandler = new WebsocketClientHandshakerHandler(uri);
            String filePath = paramConfig.getFilePath();
            WebsocketMessageHandler websocketMessageHandler = new WebsocketMessageHandler(new File(filePath));
            bootstrap.group(WORK_GROUP).channel(NioSocketChannel.class).handler(new ChannelInitializer<SocketChannel>() {
                @Override
                protected void initChannel(SocketChannel ch) {
                    ChannelPipeline pipeline = ch.pipeline();
                    pipeline.addLast(new HttpClientCodec());
                    pipeline.addLast(new HttpObjectAggregator(65535));
                    pipeline.addLast(WebSocketClientCompressionHandler.INSTANCE);
                    pipeline.addLast(handshakerHandler);
                    pipeline.addLast(websocketMessageHandler);
                }
            });
            channel = bootstrap.connect(uri.getHost(), -1 == uri.getPort() ? 80 : uri.getPort()).syncUninterruptibly().channel();
            channel.closeFuture().addListener(future -> {
                countDownLatch.countDown();
                semaphore.release();
            });
            handshakeFuture = handshakerHandler.getHandshakeFuture();
            handshakeFuture.syncUninterruptibly();
            logger.info("{},握手结果：{}", clientName, handshakeFuture.isSuccess());
            if (!handshakeFuture.isSuccess()) {
                logger.error("{},握手失败", clientName, handshakeFuture.cause());
                close();
            }
        } catch (Exception e) {
            logger.error("{},连接失败", clientName, e);
            close();
        }
    }

    public URI getUri() throws Exception {
        List<String> base = new ArrayList<>();
        base.add("v1.0");
        base.add(astConfig.getAppId());
        base.add(astConfig.getAccessKeyId());
        base.add(getDataStr());
        base.add(UUID.randomUUID().toString());
        String baseString = URLEncoder.encode(String.join(",", base), "UTF-8");
        String sign = createSign(baseString, astConfig.getAccessKeySecret());
        base.add(sign);
        String authString = String.join(",", base);
        StringBuilder url = new StringBuilder(astConfig.getAddress() +
                "?lang=" + Objects.toString(paramConfig.getLang(), "") +
                "&codec=" + Objects.toString(paramConfig.getCodec(), "") +
                "&samplerate=" + Objects.toString(paramConfig.getSamplerate(), "") +
                "&hotWordId=" + Objects.toString(paramConfig.getHotWordId(), "") +
                "&sourceInfo=" + Objects.toString(paramConfig.getSourceInfo(), "") +
                "&audioEncode=" + Objects.toString(paramConfig.getAudioEncode(), "") +
                "&authString=" + Objects.toString(URLEncoder.encode(authString, "UTF-8"), "") +
                "&trackId=" + traceid);
        return URI.create(url.toString());
    }

    private void close() {
        if (channel != null) {
            channel.close();
        } else {
            countDownLatch.countDown();
            semaphore.release();
        }
    }

    private String createSign(String baseString, String accessKeySecret) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(accessKeySecret.getBytes(StandardCharsets.UTF_8), "UTF-8");
        mac.init(keySpec);
        byte[] bytes = mac.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
        return Base64Utils.encodeToString(bytes);
    }

    private String getDataStr() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZZ");
        return format.format(new Date());
    }

    private String getDataStr(long time, String format) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(time);
    }

    private static class WebsocketClientHandshakerHandler extends ChannelInboundHandlerAdapter {
        private WebSocketClientHandshaker handshaker;
        private ChannelPromise handshakeFuture;

        WebsocketClientHandshakerHandler(URI websocketUri) {
            DefaultHttpHeaders header = new DefaultHttpHeaders();
            this.handshaker = WebSocketClientHandshakerFactory.newHandshaker(websocketUri, WebSocketVersion.V13, null, true, header);
        }

        @Override
        public void channelRead(ChannelHandlerContext ctx, Object msg) {
            if (!(msg instanceof FullHttpResponse)) {
                ctx.fireChannelRead(msg);
                return;
            }
            try {
                Channel channel = ctx.channel();
                if (!handshaker.isHandshakeComplete()) {
                    try {
                        handshaker.finishHandshake(channel, (FullHttpResponse) msg);
                        ctx.pipeline().remove(this);
                        handshakeFuture.setSuccess();
                    } catch (Exception e) {
                        handshakeFuture.setFailure(e);
                    }
                }
            } finally {
                ReferenceCountUtil.release(msg);
            }
        }

        @Override
        public void handlerAdded(ChannelHandlerContext ctx) {
            this.handshakeFuture = ctx.newPromise();
        }

        @Override
        public void channelActive(ChannelHandlerContext ctx) throws Exception {
            handshaker.handshake(ctx.channel());
        }

        ChannelPromise getHandshakeFuture() {
            return handshakeFuture;
        }
    }

    private class WebsocketMessageHandler extends SimpleChannelInboundHandler<WebSocketFrame> {

        private Logger logger = LoggerFactory.getLogger(WebsocketMessageHandler.class);
        private File file;
        private long lastSendTime;
        private long lastReadTime;
        private File txtResultFile;

        public WebsocketMessageHandler(File file) {
            this.file = file;
            this.txtResultFile = new File(String.format("%s/%s.txt", paramConfig.getResultFilePath(), file.getName()));
        }

        @Override
        @SuppressWarnings("all")
        protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame msg) throws Exception {
            MDC.put("traceid", traceid);
            if (msg instanceof TextWebSocketFrame) {
                lastReadTime = System.currentTimeMillis();
                String text = ((TextWebSocketFrame) msg).text();
                JSONObject parse = (JSONObject) JSON.parse(text);
                String action = parse.getString("action");
                String code = parse.getString("code");
                if ("started".equals(action)) {
                    logger.info("{},收到开始消息{}", clientName, text);
                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            MDC.put("traceid", traceid);
                            try (FileInputStream inputStream = new FileInputStream(file)) {
                                byte[] buff = new byte[otherConfig.getBuffSize()];
                                String codec = StringUtils.isEmpty(paramConfig.getAudioEncode()) ? paramConfig.getCodec() : paramConfig.getAudioEncode();
                                if (codec.startsWith("speex-")) {
                                    //去除头4个字节
                                    inputStream.read(new byte[4]);
                                }
                                OggOpusEncodeProxy ins = new OggOpusEncodeProxy();
                                String instanceId = "";
                                if (codec.startsWith("opus-ogg")) {
                                    ins.loadLib();
                                    int ret = ins.init(1, 16000, 16, 32000, 1280, 5005, 0, 100, 10);
                                    instanceId = ins.getInstanceId();
                                    System.out.println("instanceId = " + instanceId);
                                    OpusEncodeResult.concurrentEncodeMap.put(instanceId, new ConcurrentLinkedQueue<>());
                                }

                                int length;
                                int i = 0;
                                int sendSleepMs = otherConfig.getSendSleepMs();
                                long startTime = System.currentTimeMillis();
                                lastSendTime = startTime;

                                final boolean[] opusEnd = {false};
                                FutureTask<String> task = null;
                                if (codec.startsWith("opus-ogg")) {
                                    byte[] opusBuff = new byte[otherConfig.getBuffSize()];
                                    task = new FutureTask<String>(
                                            new Callable<String>() {
                                                @Override
                                                public String call() throws Exception {
                                                    int opusLength;
                                                    while ((opusLength = inputStream.read(opusBuff)) != -1 && channel.isActive()) {
                                                        ins.encode(opusBuff, opusLength);
                                                    }
                                                    ins.encodeDrain();
                                                    ins.unInit();
                                                    opusEnd[0] = true;
                                                    return "ok";
                                                }

                                            });
                                    new Thread(task, "有返回值的线程").start();
                                }
                                if (codec.startsWith("opus-ogg")) {
                                    do {
                                        if (opusEnd[0] && OpusEncodeResult.getQueueByInstanceId(instanceId).isEmpty()) {
                                            logger.info("opus编码结束");
                                            break;
                                        }
                                        buff = OpusEncodeResult.getQueueByInstanceId(instanceId).poll();
                                        if (null == buff) {
                                            continue;
                                        }
                                        length = buff.length;
                                        ByteBuf byteBuf = Unpooled.copiedBuffer(buff, 0, length);
                                        long sleepTime = sendSleepMs * i + (startTime - lastSendTime);
                                        if (sleepTime > 0) {
                                            Thread.sleep(sleepTime);
                                            computeTime((int) (sendSleepMs - sleepTime));
                                        }
                                        channel.writeAndFlush(new BinaryWebSocketFrame(byteBuf));
                                        lastSendTime = System.currentTimeMillis();
                                        i++;
                                    } while (true);
                                }
                                if (codec.startsWith("opus-wb")) {
                                    byte[] combineFrame = new byte[0];
                                    int j = otherConfig.getSendFrameCount();
                                    //先读2字节计算编码的每帧数据大小
                                    while ((length = inputStream.read(buff)) != -1 && channel.isActive()) {
                                        int encodedataLength = byte2short(buff, 0, false);
                                        // logger.debug("opus解码读取头获取长度：{}", encodedataLength);
                                        byte[] bufferNew = new byte[encodedataLength];
                                        if ((length = inputStream.read(bufferNew)) != -1) {
                                            byte[] mergeData = ArrayUtils.addAll(buff, bufferNew);
                                            // logger.debug("opus解码后发送帧长度：{} -- {}", mergeData.length, (length + 2));
                                            combineFrame = ArrayUtils.addAll(combineFrame, mergeData);
                                            if (j == 1) {
                                                ByteBuf byteBuf = Unpooled.copiedBuffer(combineFrame, 0, combineFrame.length);
                                                long sleepTime = sendSleepMs * i + (startTime - lastSendTime);
                                                if (sleepTime > 0) {
                                                    Thread.sleep(sleepTime);
                                                    computeTime((int) (sendSleepMs - sleepTime));
                                                }
                                                channel.writeAndFlush(new BinaryWebSocketFrame(byteBuf));
                                                lastSendTime = System.currentTimeMillis();
                                                i++;

                                                combineFrame = new byte[0];
                                                j = otherConfig.getSendFrameCount();
                                            } else {
                                                j--;
                                            }
                                        }
                                    }
                                } else {
                                    // pcm、speex发送音频帧
                                    while ((length = inputStream.read(buff)) != -1 && channel.isActive()) {
                                        ByteBuf byteBuf = Unpooled.copiedBuffer(buff, 0, length);
                                        long sleepTime = sendSleepMs * i + (startTime - lastSendTime);
                                        if (sleepTime > 0) {
                                            Thread.sleep(sleepTime);
                                            computeTime((int) (sendSleepMs - sleepTime));
                                        }
                                        channel.writeAndFlush(new BinaryWebSocketFrame(byteBuf));
                                        lastSendTime = System.currentTimeMillis();
                                        i++;
                                    }
                                }
                            } catch (Exception e) {
                                logger.error("{},文件读取失败", clientName, e);
                                close();
                            }
                            if (channel.isActive()) {
                                String msg = "{\"end\":true}";
                                logger.info("{},发送结束标记:{}", clientName, msg);
                                channel.writeAndFlush(new TextWebSocketFrame(msg));
                            }
                        }
                    }).start();

                } else if ("end".equals(action)) {
                    logger.info("{},收到结束消息{}", clientName, text);
                } else if (!StringUtils.isEmpty(code)) {
                    logger.info("{},收到错误消息{}", clientName, text);
                } else {
                    logger.debug("{},收到消息：{}", clientName, text);
                    handleResultAndSave(text);
                }
            } else if (msg instanceof PongWebSocketFrame) {
                logger.info("WebSocket Client received pong");
            } else if (msg instanceof CloseWebSocketFrame) {
                logger.info("WebSocket Client received closing");
                ctx.close();
            }

        }

        private void handleResultAndSave(String text) {

            String wordStr = "";
            JSONObject result = JSONObject.parseObject(text);
            JSONObject cn = result.getJSONObject("cn");
            JSONObject st = cn.getJSONObject("st");
            Object typeObj = st.get("type");
            if (null == typeObj) {
                return;
            }
            if (Objects.equals(typeObj.toString(), "0")) {
                // type 等于 0 表示最终结果。统计时间也是通过最终结果来统计的
                JSONArray rt = st.getJSONArray("rt");
                for (int i = 0; i < rt.size(); i++) {
                    JSONObject rtObj = rt.getJSONObject(i);
                    JSONArray ws = rtObj.getJSONArray("ws");
                    for (int j = 0; j < ws.size(); j++) {
                        JSONObject wsObj = ws.getJSONObject(j);
                        Object wbObj = wsObj.get("wb");
                        if (null == wbObj) {
                            return;
                        }
                        Object weObj = wsObj.get("we");
                        if (null == weObj) {
                            return;
                        }
                        JSONArray cw = wsObj.getJSONArray("cw");
                        for (int k = 0; k < cw.size(); k++) {
                            JSONObject cwObj = cw.getJSONObject(k);
                            Object wordObj = cwObj.get("w");
                            if (null == wordObj) {
                                return;
                            }
                            Object wpObj = cwObj.get("wp");
                            if (null == wpObj) {
                                return;
                            }
                            wordStr += wordObj.toString();

                             if (Objects.equals(wpObj.toString(), "g")) {
                                  wordStr += "\r\n";
                             }
                        }

                    }

                }
                writeResultToFile(wordStr);
            }

        }

        // 追加写入文件
        private void writeResultToFile(String wordStr) {
            FileOutputStream fos = null;
            OutputStreamWriter osw = null;
            boolean isFirstWrite = false;
            try {
                if (!this.txtResultFile.exists()) {
                    boolean hasFile = this.txtResultFile.createNewFile();
                    if(hasFile){
                        System.out.println("file not exists, create new file");
                    }
                    fos = new FileOutputStream(this.txtResultFile);
                    isFirstWrite = true;
                } else {
                    fos = new FileOutputStream(this.txtResultFile, true);
                    isFirstWrite = false;
                }
                osw = new OutputStreamWriter(fos, "utf-8");
                if (isFirstWrite) {
                    osw.write(String.format("%s\t", file.getName()));
                }
                osw.write(wordStr); //写入内容
            } catch (Exception e) {
                e.printStackTrace();
            }finally {   //关闭流
                try {
                    if (osw != null) {
                        osw.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                try {
                    if (fos != null) {
                        fos.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
            MDC.put("traceid", traceid);
            logger.error("{},出现错误", clientName, cause);
            close();
        }

        @Override
        public void channelInactive(ChannelHandlerContext ctx) throws Exception {
            MDC.put("traceid", traceid);
            logger.info("{},服务断连,最后发送数据时间{}，最后接收数据时间{}", clientName, getDataStr(lastSendTime, "yyyy-MM-dd HH:mm:ss.SSS"), getDataStr(lastReadTime, "yyyy-MM-dd HH:mm:ss.SSS"));
            close();

            // 将文件追加写入总的合并文件realtime_result.txt中
            try(FileInputStream in = new FileInputStream(txtResultFile);){
                File destFile = new File(String.format("%s/trealtime_result.txt", paramConfig.getResultFilePath()));
                if (!destFile.exists()){
                    destFile.createNewFile();
                }
                try(FileOutputStream out = new FileOutputStream(destFile, true);){
                    out.write("\r\n".getBytes());
                    IOUtils.copyLarge(in, out);
                }
            }

        }

        /**
         * @param buff
         * @param pos
         * @param isLittle
         * @return
         * <AUTHOR>
         */
        public int byte2short(byte[] buff, int pos, boolean isLittle) {
            int value = 0;
            if (isLittle) {
                value = ((buff[pos + 1] << 8) & 0xFF00) | (buff[pos + 0] & 0xFF);
            } else {
                value = ((buff[pos + 0] << 8) & 0xFF00) | (buff[pos + 1] & 0xFF);
            }
            return value;
        }

    }

    public static void stop() {
        WORK_GROUP.shutdownGracefully().addListener(future -> System.exit(0));
    }

    private void computeTime(int time) {
        avgOffsetTime = (avgOffsetTime + time) / 2;
    }
}
