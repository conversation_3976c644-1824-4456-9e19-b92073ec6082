package com.jhdr.operation.utils.voice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jhdr.common.core.utils.StringUtils;
import hisee.sdk.HiseeClient;
import hisee.sdk.HiseeListener;
import hisee.sdk.HiseeRequest;
import hisee.sdk.core.common.HiseeEvent;
import hisee.sdk.core.common.HiseeHostEnv;
import hisee.sdk.entity.result.LatticeItem;
import hisee.sdk.entity.result.ResultItem;
import hisee.sdk.entity.result.ResultWordItem;
import hisee.sdk.utils.AudioInputUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 实时语音转写Demo：基于9月重构后的SDK编写的可多实例运行的
 * 
 * <AUTHOR>
 * 
 * @date 2021-9-24
 */
public class Demo {
    private static final Logger logger = LoggerFactory.getLogger(Demo.class);
    private static final int MAX_REPEAT = 3; // 获取路由重试3次
    private static final String LANG_CN = "cn";
    private static final String ENG_AST = "ast";
    private static final int FRAME_TIME_MS = 1; // 每帧延迟时间 (如果是导入音频场景设置为1，模拟录音场景设置为40)
    private  static String result="";
    
    private static final long REPEAT_DELAY = 500; // 重试间隔
    private volatile AtomicInteger successCount = new AtomicInteger(0);
    private volatile AtomicInteger failedCount = new AtomicInteger(0);

    /**
     * 程序入口参数：1、路由服务器地址，2、音频文件名，3、并发线程数
     */
//    public static void main(String[] args) throws NumberFormatException, InterruptedException {
//
//        Demo demo = new Demo();
//        List<String> pathList = new ArrayList<>();
//        pathList.add("D:/test/record1725354296698.wav");
////        String host="ws://*************:8700/websocket/";
//
//        demo.testWebsocket("", pathList, 1);
////        System.exit(1);
//    }


    public static  String getResult(String fileName) throws NumberFormatException, InterruptedException {
        result ="";
        Demo demo = new Demo();
        List<String> pathList = new ArrayList<>();
//        String host="ws://*************:8700/websocket/";
//        String path="D://test/"+fileName;
        String path="/home/<USER>/uploadPath/voice/"+fileName;
        pathList.add(path);
        demo.testWebsocket("", pathList, 1);
//        System.exit(1);
        return result;
    }

    /**
     * 找目录下的所有WAV文件，仅支持16K采样16bit单声道音频流
     */
    private static List<String> getWavList(String wavFolder) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isEmpty(wavFolder)) {
            return list;
        }
        File file = new File(wavFolder);
        if (file.exists()) {
            if (file.isDirectory()) {
                for (File f : file.listFiles()) {
                    if (f.getAbsolutePath().endsWith("wav")) {
                        list.add(f.getAbsolutePath());
                    } else if (f.getAbsolutePath().endsWith("pcm")) {
                        list.add(f.getAbsolutePath());
                    }
                }
            } else {
                list.add(file.getAbsolutePath());
            }
        }
        return list;
    }

    /**
     * 按照线程来创建并处理任务
     */
    private void testWebsocket(final String host, List<String> wavFilePaths, int threads)
            throws InterruptedException {
        CountDownLatch countDownLatch = new CountDownLatch(threads);
        HiseeHostEnv env = new HiseeHostEnv();

//         env.setUrl("ws://*************:8700/websocket/");
         env.setUrl("ws://**********:8700/websocket/");


        if (host.startsWith("ws:")) {
            env.setDirect(true);
        }

        for (int i = 0; i < threads; i++) {
            Thread thread = new Thread() {
                @Override
                public void run() {
                    for (String filePath : wavFilePaths) {
                        try {
                            processFile(filePath, env, this.getId());
                            Thread.sleep(REPEAT_DELAY);
                        } catch (Exception e) {
                            failedCount.getAndIncrement();
                            logger.error("", e);
                        }
                    }
                    countDownLatch.countDown();
                }
            };
            thread.start();
        }
        countDownLatch.await();

    }

    /**
     * 处理单个文件（目录中有多个文件，顺序处理时会有1秒左右间隔）
     */
    private void processFile(String file, HiseeHostEnv env, long threadId) throws Exception {
        logger.info("thread:{},processFile:{} ", threadId, file);
        // 1、创建会话
        HiseeRequest request = new HiseeRequest();
        // 设置语种，cn为中文，en为英文，"cn;en"为混合语种（需要特定引擎才能支持）
        request.setLang(LANG_CN);


        // ast为实时引擎（按录音速度发送音频），ist为非实时引擎（快速发音频，然后等结果）
        request.setEngine(ENG_AST);

        // 设置会话参数,常用参数ack=1(用于缓冲控制）,pgs=off(是否关闭中间结果)
        request.setParameters("appid=test001,uid=user001,ack=1,pgs=off");


        HiseeClient client = new HiseeClient(env);
        CountDownLatch sessionLatch = new CountDownLatch(1);
        AtomicBoolean isAviable = new AtomicBoolean(false);
        AtomicBoolean sendDone = new AtomicBoolean(false);
        HiseeListener listener = buildCallback(sessionLatch, isAviable, sendDone);
        boolean connectFlag = false;
        for (int i = 0; i < MAX_REPEAT; i++) {
            connectFlag = client.createSession(request, listener);
            if (connectFlag) {
                break;
            }
            Thread.sleep(REPEAT_DELAY);
        }
        if (!connectFlag) {
            // 此次任务创建会话失败
            failedCount.getAndIncrement();
            logger.info("thread:{},createSession failed", threadId);
            return;
        }

        AudioInputUtil input = new AudioInputUtil(file, FRAME_TIME_MS, "thread_" + threadId);
        isAviable.set(true);
        sendDone.set(false);
        while (isAviable.get()) {
            if (!isAviable.get()) {
                logger.info("isAviable.get() false");
                return;
            }
            byte[] data = input.read();
            if (null == data) {
                break;
            }
            // 此处发送数据不需要加数据头，在SDK已经增加
            client.sendData(data, 0, data.length);

        }
        sendDone.set(true);
        client.endData();
        sessionLatch.await();
        successCount.getAndIncrement();
    }

    /**
     * 结果或异常事件回调
     */
    private HiseeListener buildCallback(final CountDownLatch sessionLatch, final AtomicBoolean isAviable,
                                        final AtomicBoolean sendDone) {
        return new HiseeListener() {
            @Override
            public void onSessionMessage(HiseeEvent event) {
                LatticeItem item = JSON.parseObject(event.getContent(), LatticeItem.class);
                String logTxt = parseWords(item);

            }

            @Override
            public void onSessionClose(HiseeEvent event) {
                logger.info("onSessionClose:{}", JSON.toJSONString(event));
                if (!sendDone.get()) {
                    // 数据未发送完毕就被终止
                    logger.info("send un complete");
                    isAviable.set(false);
                }
                sessionLatch.countDown();
            }

            @Override
            public void onSessionError(HiseeEvent event) {
                logger.info("onSessionError : {}", JSONObject.toJSONString(event));
                if (!sendDone.get()) {
                    // 数据未发送完毕就被终止
                    logger.info("send un complete");
                    isAviable.set(false);
                }
                sessionLatch.countDown();
            }
        };
    }

    /**
     * 解析结果中文本示例
     */
    protected String parseWords(LatticeItem item) {
        StringBuilder buffer = new StringBuilder();
        ResultItem[] resultItem = item.getWs();
        if (null == resultItem || 0 == resultItem.length) {
            return buffer.toString();
        }
        for (ResultItem subItem : resultItem) {
            if (null != subItem) {
                ResultWordItem[] wordItem = subItem.getCw();
                for (ResultWordItem word : wordItem) {
                    // 过滤语气词
                    if (word.getWp().equals("s")) {
                        continue;
                    }
                    buffer.append(word.getW());
                }
            }

        }
        result+= buffer.toString();
        String ret = "begin:" + item.getBg() + ",end:" + item.getEd() + ",txt:" + buffer.toString();
        return ret;
    }
}
