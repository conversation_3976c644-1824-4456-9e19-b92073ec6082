//package com.jhdr.operation.utils.sendMessage;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.jhdr.operation.utils.MD5Example;
//
//
//import java.io.*;
//import java.net.URL;
//import java.net.URLConnection;
//import java.net.URLEncoder;
//import java.security.NoSuchAlgorithmException;
//
//public class SendCode {
//
//    /**
//     * 向指定 URL 发送POST方法的请求
//     *
//     * @param url 发送请求的 URL
//     * @return 所代表远程资源的响应结果
//     */
//    public static String sendPost(String url, String param) {
//        PrintWriter out = null;
//        BufferedReader in = null;
//        String result = "";
//        try {
//            URL realUrl = new URL(url);
//            // 打开和URL之间的连接
//            URLConnection conn = realUrl.openConnection();
//            // 设置通用的请求属性
//            conn.setRequestProperty("accept", "*/*");
//            conn.setRequestProperty("connection", "Keep-Alive");
//            conn.setRequestProperty("user-agent",
//                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
//            // 发送POST请求必须设置如下两行
//            conn.setDoOutput(true);
//            conn.setDoInput(true);
//            // 获取URLConnection对象对应的输出流
//            out = new PrintWriter(conn.getOutputStream());
//            // 发送请求参数
//            out.print(param);
//            // flush输出流的缓冲
//            out.flush();
//            // 定义BufferedReader输入流来读取URL的响应
//            in = new BufferedReader(
//                    new InputStreamReader(conn.getInputStream()));
//            String line;
//            while ((line = in.readLine()) != null) {
//                result += line;
//            }
//        } catch (Exception e) {
//            System.out.println("发送 POST 请求出现异常！" + e);
//            e.printStackTrace();
//        }
//        //使用finally块来关闭输出流、输入流
//        finally {
//            try {
//                if (out != null) {
//                    out.close();
//                }
//                if (in != null) {
//                    in.close();
//                }
//            } catch (IOException ex) {
//                ex.printStackTrace();
//            }
//        }
//        return result;
//    }
//
//    public static String sendMessage(String people,String phone,String content) throws NoSuchAlgorithmException, UnsupportedEncodingException {
//        String gwid="c24ef5c4";
//        String  password= MD5Example.getMd5Password("18056090244");
//        if (ObjectUtil.isEmpty(people)){
//            people="茨淮新河管理局";
//        }
//        content = URLEncoder.encode("【"+people+"】"+content, "UTF-8");
//        String postData = "type=send&username=JHDR&password="+password+"&gwid="+gwid+"&mobile=" + phone + "&weather.message=" + content + "";
//        String url = "http://jk.smstcby.com/smsUTF8.aspx";
//        String result = SendCode.sendPost(url, postData);
//        return result;
//    }
//
//
//    //调用方式
//    public static void main(String[] args) throws UnsupportedEncodingException, NoSuchAlgorithmException {
//
//        //手机号  可以多个手机号
//        String phones = "15256932177";
//
//        //短信内容
//        String content = URLEncoder.encode("【茨淮新河】"+"测试数据", "UTF-8");
//        String gwid="c24ef5c4";
//        String  password= MD5Example.getMd5Password("18056090244");
//        //拼接参数
//        String postData = "type=send&username=JHDR&password="+password+"&gwid="+gwid+"&mobile=" + phones + "&weather.message=" + content + "";
//
//        String url = "http://jk.smstcby.com/smsUTF8.aspx";
//
//        //发送并把结果赋给result,返回一个XML信息,解析xml 信息判断
//        String result = SendCode.sendPost(url, postData);
//        System.out.println(result);
//
//    }
//}