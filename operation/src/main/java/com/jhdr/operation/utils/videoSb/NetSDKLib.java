package com.jhdr.operation.utils.videoSb;


import com.sun.jna.*;
import com.sun.jna.ptr.IntByReference;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * NetSDK JNA接口封装
 */
public interface NetSDKLib extends Library {
    NetSDKLib NETSDK_INSTANCE = Native.load(LibraryLoad.getLoadLibrary("dhnetsdk"), NetSDKLib.class);
    NetSDKLib CONFIG_INSTANCE = Native.load(LibraryLoad.getLoadLibrary("dhconfigsdk"), NetSDKLib.class);
    //NetSDKLib CONFIG_JNI = (NetSDKLib)Native.loadLibrary(util.getLoadLibrary("JNI1.dll"), INetSDK.class);
    public static final int MAX_PATH                            = 260;
    public static final int NET_FILE_TYPE_LEN                   = 64;           // 文件类型长度
    public static final int MAX_COMMON_STRING_64                = 64;           // 通用字符串长度64
    public static final int MAX_COMMON_STRING_128               = 128;          // 通用字符串长度128
    public static final int MAX_COMMON_STRING_512               = 512;          // 通用字符串长度512
    public static final int MAX_COMMON_STRING_32                = 32;           // 通用字符串长度32
    public static final int MAX_WINDOWS_COUNT                   = 16;           // 诱导屏最大窗口个数
    public static final int MAX_PROGRAMMES_COUNT                = 32;           // 最多支持的节目个数
    public static final int MAX_PLAYDATES_COUNT                 = 32;           // 最大日期个数
    public static final int MAX_LOG_PATH_LEN                    = 260;          // 日志路径名最大长度
    public static final int NET_SERIALNO_LEN                    = 48;           // 设备序列号字符长度
    public static final int EVENT_IVS_FLOATINGOBJECT_DETECTION  = 0x00000257;   // 漂浮物检测事件 (对应 DEV_EVENT_FLOATINGOBJECT_DETECTION_INFO)
    public static final int NET_MAX_DETECT_REGION_NUM           = 20;           // 规则检测区域最大顶点数
    public static final int HDBJ_MAX_OBJECTS_NUM                = 200;          // 检测到目标的最大个数
    public static final int POINTERSIZE                         = new PointerSize().size(); // JNA指针长度
    public static final int EVENT_ALARM_LOCALALARM              = 0x0000011D;   // 外部报警事件(对应 DEV_EVENT_ALARM_INFO)
    public static final int COMMON_SEAT_MAX_NUMBER              = 8;            // 默认检测最大座驾个数
    // 事件对应文件信息
    public static final int EVENT_IVS_CROSSLINEDETECTION        = 0x00000002;   // 警戒线事件(对应 DEV_EVENT_CROSSLINE_INFO)
    public static final int NET_MAX_DETECT_LINE_NUM             = 20;           // 规则检测线最大顶点数
    public static final int NET_MAX_TRACK_LINE_NUM              = 20;           // 物体运动轨迹最大顶点数
    public static final int MAX_EVENT_ID_LEN                    = 52;           // 国标事件ID最大长度
    public static final int NET_ALARM_ALARM_EX2                 = 0x2175;       // 本地报警事件(对应结构体ALARM_ALARM_INFO_EX2,对NET_ALARM_ALARM_EX升级)
    public static final int NET_QUERY_IVS_REMOTE_DEVICE_INFO    = 0x1b;         // 查询IVS的前端设备所关联的远程设备信息, pInBuf = NET_IN_IVS_REMOTE_DEV_INFO*, pOutBuf = NET_OUT_IVS_REMOTE_DEV_INFO*
    public static final int AV_CFG_Device_ID_Len                = 64;           // 设备ID长度
    public static final int AV_CFG_Channel_Name_Len             = 64;           // 通道名称长度
    public static final int AV_CFG_Monitor_Name_Len             = 64;           // 电视墙名称长度
    public static final int AV_CFG_Max_TV_In_Block              = 128;          // 区块中TV的最大数量
    public static final int AV_CFG_Max_Block_In_Wall            = 128;          // 电视墙中区块的最大数量
    public static final int AV_CFG_IP_Address_Len               = 32;           // IP 长度
    public static final int AV_CFG_Protocol_Len                 = 32;           // 协议名长度
    public static final int AV_CFG_User_Name_Len                = 64;           // 用户名长度
    public static final int AV_CFG_Password_Len                 = 64;           // 密码长度
    public static final int AV_CFG_Serial_Len                   = 32;           // 序列号长度
    public static final int AV_CFG_Device_Class_Len             = 16;           // 设备类型长度
    public static final int AV_CFG_Device_Type_Len              = 32;           // 设备具体型号长度
    public static final int AV_CFG_Device_Name_Len              = 128;          // 机器名称
    public static final int AV_CFG_Address_Len                  = 128;          // 机器部署地点
    public static final int AV_CFG_Max_Path                     = 260;          // 路径长度
    public static final int AV_CFG_Group_Name_Len               = 64;           // 分组名称长度
    public static final int AV_CFG_DeviceNo_Len                 = 32;           // 设备编号长度
    public static final int AV_CFG_Group_Memo_Len               = 128;          // 分组说明长度
    public static final int AV_CFG_Max_Channel_Num              = 1024;         // 最大通道数量
    public static final String CFG_CMD_REMOTEDEVICE             = "RemoteDevice"; // 远程设备信息(对应  AV_CFG_RemoteDevice 数组, 通道无关)

    public static final int MAX_NAME_LEN                        = 128;          // 通用名字字符串长度
    public static final int NET_COMMON_STRING_32                = 32;           // 通用字符串长度32


    // 视频分析源文件信息
    public static class CFG_SOURCE_FILE_INFO extends SdkStructure {
        public byte[]           szFilePath = new byte[MAX_PATH];      // 文件路径
        public int              emFileType;                           // 文件类型，详见 CFG_SOURCE_FILE_TYPE
    }
    public static final String CFG_CMD_ANALYSESOURCE            = "VideoAnalyseSource"; // 视频分析资源配置(对应 CFG_ANALYSESOURCE_INFO)
    public void CLIENT_SetDVRMessCallBack(Callback cbMessage,Pointer dwUser);
    public boolean CLIENT_StartListenEx(LLong lLoginID);
    public boolean CLIENT_StopListen(LLong lLoginID);
    // 本地报警事件(对NET_ALARM_ALARM_EX升级)
    public static class ALARM_ALARM_INFO_EX2 extends SdkStructure
    {
        public int              dwSize;
        public int              nChannelID;                           //通道号
        public int              nAction;                              //0:开始, 1:停止
        public NET_TIME         stuTime;                              //报警事件发生的时间
        public int              emSenseType;                          //传感器类型, 取值范围为  NET_SENSE_METHOD 中的值
//        public NET_MSG_HANDLE_EX stuEventHandler;                     //联动信息
        public int              emDefenceAreaType;                    //防区类型, 取值类型为EM_NET_DEFENCE_AREA_TYPE中的值
        public int              nEventID;                             //事件ID
        public byte[]           szName = new byte[NET_COMMON_STRING_32]; // 通道名称
        public int              nCount;                               // 事件发生次数
        public NET_GPS_STATUS_INFO stuGPS;                            // GPS信息
        /**
         * 本地报警时登陆的用户ID
         */
        public byte[]           szUserID = new byte[32];
        /**
         * 本地报警时登陆的用户名
         */
        public byte[]           szUserName = new byte[128];
        /**
         * 设备序列号
         */
        public byte[]           szSN = new byte[32];
        /**
         * 外部输入报警
         */
        public int              bExAlarmIn;
        /**
         * 报警通道所属区域的个数
         */
        public int              nAreaNums;
        /**
         * 报警通道所属的区域
         */
        public int[]            nAreas = new int[64];
        /**
         * 事件公共扩展字段结构体
         */
        public NET_EVENT_INFO_EXTEND stuEventInfoEx = new NET_EVENT_INFO_EXTEND();
        /**
         * 保留字节
         */
        public byte[]           byReserved = new byte[568];

        public ALARM_ALARM_INFO_EX2() {
            this.dwSize = this.size();
        }
    }
    // 每个视频输入通道对应的视频分析资源配置信息
    public static class CFG_ANALYSESOURCE_INFO extends SdkStructure {
        public byte             bEnable;                              // 视频分析使能   1-使能， 0-禁用
        public byte[]           bReserved = new byte[3];              // 保留对齐
        public int              nChannelID;                           // 智能分析的前端视频通道号
        public int              nStreamType;                          // 智能分析的前端视频码流类型，0:抓图码流; 1:主码流; 2:子码流1; 3:子码流2; 4:子码流3; 5:物体流
        public byte[]           szRemoteDevice = new byte[MAX_NAME_LEN]; // 设备名
        public int              abDeviceInfo;                         // 设备信息是否有效 ; 1-有效，0-无效
        public AV_CFG_RemoteDevice stuDeviceInfo;                     // 设备信息
        public int              emSourceType;                         // 视频分析源类型，详见  CFG_VIDEO_SOURCE_TYPE
        public CFG_SOURCE_FILE_INFO stuSourceFile;                    // 当视频分析源类型为 CFG_VIDEO_SOURCE_FILESTREAM 时，有效
    }
    // 查询 IVS 前端设备入参
    public static class NET_IN_IVS_REMOTE_DEV_INFO extends SdkStructure
    {
        public int              dwSize;                               // 该结构体大小
        public int              nChannel;                             // 通道号

        public NET_IN_IVS_REMOTE_DEV_INFO() {
            this.dwSize = this.size();
        }
    }
    // 远程设备
    public static class AV_CFG_RemoteDevice extends SdkStructure
    {
        public int              nStructSize;
        public int              bEnable;                              // 使能
        public byte[]           szID = new byte[AV_CFG_Device_ID_Len]; // 设备ID
        public byte[]           szIP = new byte[AV_CFG_IP_Address_Len]; // 设备IP
        public int              nPort;                                // 端口
        public byte[]           szProtocol = new byte[AV_CFG_Protocol_Len]; // 协议类型
        public byte[]           szUser = new byte[AV_CFG_User_Name_Len]; // 用户名
        public byte[]           szPassword = new byte[AV_CFG_Password_Len]; // 密码
        public byte[]           szSerial = new byte[AV_CFG_Serial_Len]; // 设备序列号
        public byte[]           szDevClass = new byte[AV_CFG_Device_Class_Len]; // 设备类型
        public byte[]           szDevType = new byte[AV_CFG_Device_Type_Len]; // 设备型号
        public byte[]           szName = new byte[AV_CFG_Device_Name_Len]; // 机器名称
        public byte[]           szAddress = new byte[AV_CFG_Address_Len]; // 机器部署地点
        public byte[]           szGroup = new byte[AV_CFG_Group_Name_Len]; // 机器分组
        public int              nDefinition;                          // 清晰度, 0-标清, 1-高清
        public int              nVideoChannel;                        // 视频输入通道数
        public int              nAudioChannel;                        // 音频输入通道数
        public int              nRtspPort;                            // Rtsp端口号
        public byte[]           szVendor = new byte[MAX_PATH];        // 设备接入类型
        public Pointer          pVideoInput;                          // 视频输入通道，用户申请nMaxVideoInputs个CFG_RemoteDeviceVideoInput空间
        public int              nMaxVideoInputs;
        public int              nRetVideoInputs;
        public int              nHttpPort;                            // http端口号
        /* 以下3项为国际接入方式相关  */
        public int              bGB28181;                             // 是否有国际接入方式
        public int              nDevLocalPort;                        // 设备本地端口
        public byte[]           szDeviceNo = new byte[AV_CFG_DeviceNo_Len]; // 设备编号
        public int              nLoginType;                           // 登录方式 0 : TCP直连(默认方式)  6 : 主动注册  7 : P2P方式，此方式时通过SerialNo与设备通过P2P连接
        public byte[]           szVersion = new byte[32];             // 设备软件版本
        public boolean          bPoE;                                 // 是否由PoE端口连接, 该选项为只读, 只能由设备修改
        public int              nPoEPort;                             // PoE物理端口号, 该选项为只读, 只能由设备修改
        public byte[]           szMac = new byte[18];                 // 设备mac地址，冒号+大写

        public AV_CFG_RemoteDevice() {
            this.nStructSize = this.size();
        }
    }
    public boolean CLIENT_QueryDevInfo(LLong lLoginID,int nQueryType,Pointer pInBuf,Pointer pOutBuf,Pointer pReservedL,int nWaitTime);

    // 查询 IVS 前端设备出参
    public static class NET_OUT_IVS_REMOTE_DEV_INFO extends SdkStructure
    {
        public int              dwSize;                               // 该结构体大小
        public int              nPort;                                // 端口
        public byte[]           szIP = new byte[64];                  // 设备IP
        public byte[]           szUser = new byte[64];                // 用户名
        public byte[]           szPassword = new byte[64];            // 密码
        public byte[]           szAddress = new byte[128];            // 机器部署地点

        public NET_OUT_IVS_REMOTE_DEV_INFO() {
            this.dwSize = this.size();
        }
    }
    // 事件扩展信息
    public static class NET_EXTENSION_INFO extends SdkStructure
    {
        public byte[]           szEventID = new byte[MAX_EVENT_ID_LEN]; // 国标事件ID
        public byte[]           byReserved = new byte[80];            // 保留字节
    }

    public static class DEV_EVENT_CROSSLINE_INFO extends SdkStructure {
        public int              nChannelID;                           // 通道号
        public byte[]           szName = new byte[128];               // 事件名称
        public byte[]           bReserved1 = new byte[4];             // 字节对齐
        public double           PTS;                                  // 时间戳(单位是毫秒)
        public NET_TIME_EX      UTC;                                  // 事件发生的时间
        public int              nEventID;                             // 事件ID
        public NET_MSG_OBJECT   stuObject;                            // 检测到的物体
        public NET_EVENT_FILE_INFO stuFileInfo;                       // 事件对应文件信息
        public NET_POINT[]      DetectLine = (NET_POINT[])new NET_POINT().toArray(NET_MAX_DETECT_LINE_NUM); // 规则检测线
        public int              nDetectLineNum;                       // 规则检测线顶点数
        public NET_POINT[]      TrackLine = (NET_POINT[])new NET_POINT().toArray(NET_MAX_TRACK_LINE_NUM); // 物体运动轨迹
        public int              nTrackLineNum;                        // 物体运动轨迹顶点数
        public byte             bEventAction;                         // 事件动作,0表示脉冲事件,1表示持续性事件开始,2表示持续性事件结束;
        public byte             bDirection;                           // 表示入侵方向, 0-由左至右, 1-由右至左
        public byte[]           byReserved = new byte[1];
        public byte             byImageIndex;                         // 图片的序号, 同一时间内(精确到秒)可能有多张图片, 从0开始
        public int              dwSnapFlagMask;                       // 抓图标志(按位),具体见  NET_RESERVED_COMMON, 0位:"*",1位:"Timing",2位:"Manual",3位:"Marked",4位:"Event",5位:"Mosaic",6位:"Cutout"
        public int              nSourceIndex;                         // 事件源设备上的index,-1表示数据无效,-1表示数据无效
        public byte[]           szSourceDevice = new byte[MAX_PATH];  // 事件源设备唯一标识,字段不存在或者为空表示本地设备
        public int              nOccurrenceCount;                     // 事件触发累计次数, 类型为unsigned int
        public EVENT_INTELLI_COMM_INFO stuIntelliCommInfo;            // 智能事件公共信息
        public NET_EXTENSION_INFO stuExtensionInfo;                   // 扩展信息
        public SCENE_IMAGE_INFO_EX stuSceneImage;                     // 全景广角图
        public int              nObjetcHumansNum;                     // 检测到人的数量
        public NET_VAOBJECT_NUMMAN []stuObjetcHumans = (NET_VAOBJECT_NUMMAN [])new NET_VAOBJECT_NUMMAN().toArray(100); // 检测的到人
        public int              nRuleID;                              // 规则编号,用于标示哪个规则触发的事件，缺省时默认为0
        public int              emEventType;                          // 事件级别,参考EM_EVENT_LEVEL
        public NET_PRESET_POSITION stPosition;                        // 预置点的坐标和放大倍数
        public int              nVisibleHFOV;                         // 可见光横向视场角,单位度 实际角度乘以100
        public int              nVisibleVFOV;                         // 可见光纵向视场角,单位度 实际角度乘以100
        public int              nCurChannelHFOV;                      // 当前报警通道的横向视场角，单位度，实际角度乘以100
        public int              nCurChannelVFOV;                      // 当前报警通道的纵向视场角，单位度，实际角度乘以100
        public int              nImageNum;                            // 图片信息个数
        public Pointer          pImageArray;                          // 图片信息数组，结构体NET_IMAGE_INFO_EX2数组指针
        public int              nCarMirrorStatus;                     // 车的后视镜状态，-1: 未知, 0: 正常, 1: 不正常(如数量缺失等)
        public int              nCarLightStatus;                      // 车的车灯状态,-1: 未知, 0: 正常, 1:不正常(如灯未亮等)
        public int              nObjectBoatsNum;                      // 船只物体个数
        public NET_BOAT_OBJECT[] stuBoatObjects = new NET_BOAT_OBJECT[100]; // 船只物品信息
        public int              nUpDownGoing;                         // 车道/航道方向, 0:未知, 1:上行, 2:下行
        public NET_EVENT_INFO_EXTEND stuEventInfoEx = new NET_EVENT_INFO_EXTEND(); //事件公共扩展字段结构体
        public byte[]           byReserved1 = new byte[452];          // 预留字节
    }
    public interface fMessCallBack extends SDKCallback {
        public boolean invoke(int lCommand,LLong lLoginID,Pointer pStuEvent,int dwBufLen,String strDeviceIP,NativeLong nDevicePort,Pointer dwUser);
    }

    public static class NET_EVENT_FILE_INFO extends SdkStructure
    {
        public byte             bCount;                               // 当前文件所在文件组中的文件总数
        public byte             bIndex;                               // 当前文件在文件组中的文件编号(编号1开始)
        public byte             bFileTag;                             // 文件标签,具体说明见枚举类型 EM_EVENT_FILETAG
        public byte             bFileType;                            // 文件类型,0-普通1-合成2-抠图
        public NET_TIME_EX      stuFileTime;                          // 文件时间
        public int              nGroupId;                             // 同一组抓拍文件的唯一标识

        @Override
        public String toString() {
            return "事件对应文件信息,NET_EVENT_FILE_INFO{" +
                    "bCount=" + bCount +
                    ", bIndex=" + bIndex +
                    ", bFileTag=" + bFileTag +
                    ", bFileType=" + bFileType +
                    ", stuFileTime=" + stuFileTime.toStringTime() +
                    ", nGroupId=" + nGroupId +
                    '}';
        }
    }
    public static class NET_FLOATINGOBJECT_MASK_INFO extends SdkStructure
    {
        public int              nColNum;                              // 点阵列数
        public int              nOffset;                              // 偏移
        public int              nLength;                              // 长度
        public byte[]           byReserved = new byte[508];           // 预留字段
    }

    // 智能报警事件公共信息
    public static class EVENT_INTELLI_COMM_INFO extends SdkStructure
    {
        public int              emClassType;                          // 智能事件所属大类， 取值为  EM_CLASS_TYPE 中的值
        public int              nPresetID;                            // 该事件触发的预置点，对应该设置规则的预置点
        public byte[]           bReserved = new byte[124];            // 保留字节,留待扩展.
    }
    //视频分析物体信息结构体
    public static class NET_MSG_OBJECT extends SdkStructure
    {
        public int              nObjectID;                            //物体ID,每个ID表示一个唯一的物体
        public byte[]           szObjectType = new byte[128];         //物体类型
        public int              nConfidence;                          //置信度(0~255),值越大表示置信度越高
        public int              nAction;                              //物体动作:1:Appear2:Move3:Stay
        public DH_RECT          BoundingBox = new DH_RECT();          //包围盒
        public NET_POINT        Center = new NET_POINT();             //物体型心
        public int              nPolygonNum;                          //多边形顶点个数
        public NET_POINT[]      Contour = new NET_POINT[NET_MAX_POLYGON_NUM]; //较精确的轮廓多边形
        public int              rgbaMainColor;                        //表示车牌、车身等物体主要颜色；按字节表示,分别为红、绿、蓝和透明度,例如:RGB值为(0,255,0),透明度为0时,其值为0x00ff0000.
        public byte[]           szText = new byte[128];               // 物体上相关的带0结束符文本,比如车牌,集装箱号等等
        // "ObjectType"为"Vehicle"或者"Logo"时（尽量使用Logo。Vehicle是为了兼容老产品）表示车标,支持：
        // "Unknown"未知
        // "Audi" 奥迪
        // "Honda" 本田
        // "Buick" 别克
        // "Volkswagen" 大众
        // "Toyota" 丰田
        // "BMW" 宝马
        // "Peugeot" 标致
        // "Ford" 福特
        // "Mazda" 马自达
        // "Nissan" 尼桑
        // "Hyundai" 现代
        // "Suzuki" 铃木
        // "Citroen" 雪铁龙
        // "Benz" 奔驰
        // "BYD" 比亚迪
        // "Geely" 吉利
        // "Lexus" 雷克萨斯
        // "Chevrolet" 雪佛兰
        // "Chery" 奇瑞
        // "Kia" 起亚
        // "Charade" 夏利
        // "DF" 东风
        // "Naveco" 依维柯
        // "SGMW" 五菱
        // "Jinbei" 金杯
        // "JAC" 江淮
        // "Emgrand" 帝豪
        // "ChangAn" 长安
        // "Great Wall" 长城
        // "Skoda" 斯柯达
        // "BaoJun" 宝骏
        // "Subaru" 斯巴鲁
        // "LandWind" 陆风
        // "Luxgen" 纳智捷
        // "Renault" 雷诺
        // "Mitsubishi" 三菱
        // "Roewe" 荣威
        // "Cadillac" 凯迪拉克
        // "MG" 名爵
        // "Zotye" 众泰
        // "ZhongHua" 中华
        // "Foton" 福田
        // "SongHuaJiang" 松花江
        // "Opel" 欧宝
        // "HongQi" 一汽红旗
        // "Fiat" 菲亚特
        // "Jaguar" 捷豹
        // "Volvo" 沃尔沃
        // "Acura" 讴歌
        // "Porsche" 保时捷
        // "Jeep" 吉普
        // "Bentley" 宾利
        // "Bugatti" 布加迪
        // "ChuanQi" 传祺
        // "Daewoo" 大宇
        // "DongNan" 东南
        // "Ferrari" 法拉利
        // "Fudi" 福迪
        // "Huapu" 华普
        // "HawTai" 华泰
        // "JMC" 江铃
        // "JingLong" 金龙客车
        // "JoyLong" 九龙
        // "Karry" 开瑞
        // "Chrysler" 克莱斯勒
        // "Lamborghini" 兰博基尼
        // "RollsRoyce" 劳斯莱斯
        // "Linian" 理念
        // "LiFan" 力帆
        // "LieBao" 猎豹
        // "Lincoln" 林肯
        // "LandRover" 路虎
        // "Lotus" 路特斯
        // "Maserati" 玛莎拉蒂
        // "Maybach" 迈巴赫
        // "Mclaren" 迈凯轮
        // "Youngman" 青年客车
        // "Tesla" 特斯拉
        // "Rely" 威麟
        // "Lsuzu" 五十铃
        // "Yiqi" 一汽
        // "Infiniti" 英菲尼迪
        // "YuTong" 宇通客车
        // "AnKai" 安凯客车
        // "Canghe" 昌河
        // "HaiMa" 海马
        // "Crown" 丰田皇冠
        // "HuangHai" 黄海
        // "JinLv" 金旅客车
        // "JinNing" 精灵
        // "KuBo" 酷博
        // "Europestar" 莲花
        // "MINI" 迷你
        // "Gleagle" 全球鹰
        // "ShiDai" 时代
        // "ShuangHuan" 双环
        // "TianYe" 田野
        // "WeiZi" 威姿
        // "Englon" 英伦
        // "ZhongTong" 中通客车
        // "Changan" 长安轿车
        // "Yuejin" 跃进
        // "Taurus" 金牛星
        // "Alto" 奥拓
        // "Weiwang" 威旺
        // "Chenglong" 乘龙
        // "Haige" 海格
        // "Shaolin" 少林客车
        // "Beifang" 北方客车
        // "Beijing" 北京汽车
        // "Hafu" 哈弗
        public byte[]           szObjectSubType = new byte[62];       // 物体子类别,根据不同的物体类型,可以取以下子类型：
        // Vehicle Category:"Unknown"  未知,"Motor" 机动车,"Non-Motor":非机动车,"Bus": 公交车,"Bicycle" 自行车,"Motorcycle":摩托车,"PassengerCar":客车,
        // "LargeTruck":大货车,    "MidTruck":中货车,"SaloonCar":轿车,"Microbus":面包车,"MicroTruck":小货车,"Tricycle":三轮车,    "Passerby":行人
        // Plate Category："Unknown" 未知,"Normal" 蓝牌黑牌,"Yellow" 黄牌,"DoubleYellow" 双层黄尾牌,"Police" 警牌,
        // "SAR" 港澳特区号牌,"Trainning" 教练车号牌
        // "Personal" 个性号牌,"Agri" 农用牌,"Embassy" 使馆号牌,"Moto" 摩托车号牌,"Tractor" 拖拉机号牌,"Other" 其他号牌
        // HumanFace Category:"Normal" 普通人脸,"HideEye" 眼部遮挡,"HideNose" 鼻子遮挡,"HideMouth" 嘴部遮挡
        public short            wColorLogoIndex;                      // 车标索引
        public short            wSubBrand;                            // 车辆子品牌 需要通过映射表得到真正的子品牌 映射表详见开发手册
        public byte             byReserved1;
        public byte             bPicEnble;                            //是否有物体对应图片文件信息, 类型小bool, 取值0或者1
        public NET_PIC_INFO     stPicInfo = new NET_PIC_INFO();       //物体对应图片信息
        public byte             bShotFrame;                           //是否是抓拍张的识别结果,  类型小bool, 取值0或者1
        public byte             bColor;                               //物体颜色(rgbaMainColor)是否可用, 类型小bool, 取值0或者1
        public byte             byReserved2;
        public byte             byTimeType;                           //时间表示类型,详见 EM_TIME_TYPE 说明
        public NET_TIME_EX      stuCurrentTime = new NET_TIME_EX();   //针对视频浓缩,当前时间戳（物体抓拍或识别时,会将此识别智能帧附在一个视频帧或jpeg图片中,此帧所在原始视频中的出现时间）
        public NET_TIME_EX      stuStartTime = new NET_TIME_EX();     //开始时间戳（物体开始出现时）
        public NET_TIME_EX      stuEndTime = new NET_TIME_EX();       //结束时间戳（物体最后出现时）
        public DH_RECT          stuOriginalBoundingBox = new DH_RECT(); //包围盒(绝对坐标)
        public DH_RECT          stuSignBoundingBox = new DH_RECT();   //车标坐标包围盒
        public int              dwCurrentSequence;                    //当前帧序号（抓下这个物体时的帧）
        public int              dwBeginSequence;                      //开始帧序号（物体开始出现时的帧序号）
        public int              dwEndSequence;                        //结束帧序号（物体消逝时的帧序号）
        public long             nBeginFileOffse;                      //开始时文件偏移,单位:字（物体开始出现时,视频帧在原始视频文件中相对于文件起始处的偏移）
        public long             nEndFileOffset;                       //结束时文件偏移,单位:字节（物体消逝时,视频帧在原始视频文件中相对于文件起始处的偏移）
        public byte[]           byColorSimilar = new byte[EM_COLOR_TYPE.NET_COLOR_TYPE_MAX]; //物体颜色相似度,取值范围：0-100,数组下标值代表某种颜色,详见 EM_COLOR_TYPE
        public byte[]           byUpperBodyColorSimilar = new byte[EM_COLOR_TYPE.NET_COLOR_TYPE_MAX]; //上半身物体颜色相似度(物体类型为人时有效)
        public byte[]           byLowerBodyColorSimilar = new byte[EM_COLOR_TYPE.NET_COLOR_TYPE_MAX]; //下半身物体颜色相似度(物体类型为人时有效)
        public int              nRelativeID;                          //相关物体ID
        public byte[]           szSubText = new byte[20];             //"ObjectType"为"Vehicle"或者"Logo"时,表示车标下的某一车系,比如奥迪A6L,由于车系较多,SDK实现时透传此字段,设备如实填写。
        public short            wBrandYear;                           // 车辆品牌年款 需要通过映射表得到真正的年款 映射表详见开发手册

        protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
            int alignment = super.getNativeAlignment(type, value, isFirstElement);
            return Math.min(4, alignment);
        }

        public NET_MSG_OBJECT(){

            for(int i=0;i<Contour.length;i++){
                Contour[i]=new NET_POINT();
            }
        }
    }


    // 事件类型 EVENT_IVS_FLOATINGOBJECT_DETECTION (漂浮物检测事件) 对应的数据块描述信息
    public static class DEV_EVENT_FLOATINGOBJECT_DETECTION_INFO extends SdkStructure
    {
        public int              nChannelID;                           // 通道号
        public int              nAction;                              // 事件动作,0表示脉冲事件,1表示持续性事件开始,2表示持续性事件结束;
        public byte[]           szName = new byte[128];               // 事件名称
        public double           PTS;                                  // 时间戳(单位是毫秒)
        public NET_TIME_EX      UTC;                                  // 事件发生的时间
        public int              nEventID;                             // 事件ID
        public NET_EVENT_FILE_INFO stuFileInfo;                       // 事件对应文件信息
        public NET_POINT[]      stuDetectRegion = (NET_POINT[])new NET_POINT().toArray(NET_MAX_DETECT_REGION_NUM); // 规则检测区域
        public int              nDetectRegionNum;                     // 规则检测区域顶点数
        public int              nImageIndex;                          // 图片的序号, 同一时间内(精确到秒)可能有多张图片, 从0开始
        public int              dwSnapFlagMask;                       // 抓图标志(按位),具体见NET_RESERVED_COMMON
        public int              nSourceIndex;                         // 事件源设备上的index,-1表示数据无效
        public byte[]           szSourceDevice = new byte[MAX_PATH];  // 事件源设备唯一标识,字段不存在或者为空表示本地设备
        public int              nOccurrenceCount;                     // 事件触发累计次数
        public int              nObjectNum;                           // 检测到的物体个数
        public NET_MSG_OBJECT[] stuObjects = (NET_MSG_OBJECT[]) new NET_MSG_OBJECT().toArray(HDBJ_MAX_OBJECTS_NUM); // 检测到的物体
        public EVENT_INTELLI_COMM_INFO stuIntelliCommInfo;            // 智能事件公共信息
        public byte[]           szPresetName = new byte[64];          // 事件触发的预置名称
        public int              bExistFloatingObject;                 // 是否存在漂浮物
        public int              emEventType;                          // 事件数据类型,详见NET_EM_EVENT_DATA_TYPE
        public float            fCurrentRatio;                        // 漂浮物当前占比（相对于检测区域）单位:%, 取值范围[0, 100]
        public float            fAlarmThreshold;                      // 报警阈值。漂浮物相对于检测区域的占比, 取值范围[0, 100]
        public NET_INTELLIGENCE_IMAGE_INFO stuOriginalImage;          // 原始图
        public NET_INTELLIGENCE_IMAGE_INFO stuSceneImage;             // 球机变到最小倍下的抓图
        public NET_FLOATINGOBJECT_MASK_INFO stuObjectMaskInfo;        // 堆积物点阵图信息
        /**
         用来区分是普通漂浮物场景还是泡沫检测场景 {@link EM_FLOATINGOBJECT_DETECTION_SENCE_TYPE}
         */
        public	int              emDetectSenceType;
        public	int              nImageInfoNum;                        // 图片信息个数
        public	Pointer          pstuImageInfo;                        //图片信息数组, refer to {@link NET_IMAGE_INFO_EX3}
        public	byte[]           byReserved = new byte[3428 - POINTERSIZE]; //保留字节,留待扩展.
    }

    // 智能事件抓图信息
    public static class NET_INTELLIGENCE_IMAGE_INFO extends SdkStructure
    {
        public int              nOffSet;                              // 在二进制数据块中的偏移
        public int              nLength;                              // 图片大小,单位字节
        public int              nWidth;                               // 图片宽度(像素)
        public int              nHeight;                              // 图片高度(像素)
        public int              nIndexInData;                         //在上传图片数据中的图片序号
        public byte[]           byReserved = new byte[44];            // 预留字节
    }

    //二维空间点
    public static class DH_POINT extends SdkStructure
    {
        public short            nx;
        public short            ny;

        public DH_POINT(){}

        public DH_POINT(short x, short y){
            this.nx = x;
            this.ny = y;
        }
    }


    public void CLIENT_Cleanup();
    public LLong CLIENT_LoginWithHighLevelSecurity(NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY pstInParam,NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY pstOutParam);

    public boolean CLIENT_Logout(LLong lLoginID);


    // CLIENT_LoginWithHighLevelSecurity 输入参数
    public static class NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY extends SdkStructure
    {
        public int              dwSize;                               // 结构体大小
        public byte[]           szIP = new byte[64];                  // IP
        public int              nPort;                                // 端口
        public byte[]           szUserName = new byte[64];            // 用户名
        public byte[]           szPassword = new byte[64];            // 密码
        public int              emSpecCap;                            // 登录模式
        public byte[]           byReserved = new byte[4];             // 字节对齐
        public Pointer          pCapParam;                            // 见 CLIENT_LoginEx 接口 pCapParam 与 nSpecCap 关系
        public int              emTLSCap;                             //登录的TLS模式，参考EM_LOGIN_TLS_TYPE，目前仅支持EM_LOGIN_SPEC_CAP_TCP，EM_LOGIN_SPEC_CAP_SERVER_CONN 模式下的 tls登陆

        public NET_IN_LOGIN_WITH_HIGHLEVEL_SECURITY()
        {
            this.dwSize = this.size();
        }// 此结构体大小
    }


    // 报警事件类型 EVENT_ALARM_LOCALALARM(外部报警),EVENT_ALARM_MOTIONALARM(动检报警)报警)
    public static class DEV_EVENT_ALARM_INFO extends SdkStructure
    {
        public int              nChannelID;                           // 通道号
        public byte[]           szName = new byte[128];               // 事件名称
        public byte[]           Reserved = new byte[4];               // 保留字节对齐
        public double           PTS;                                  // 时间戳(单位是毫秒)
        public NET_TIME_EX      UTC;                                  // 事件发生的时间
        public int              nEventID;                             // 事件ID
        public NET_EVENT_FILE_INFO stuFileInfo;                       // 事件对应文件信息
        public EVENT_COMM_INFO  stCommInfo;                           // 公共信息
        public byte             byEventAction;                        // 事件动作,0表示脉冲事件,1表示持续性事件开始,2表示持续性事件结束;
        public byte[]           byReserved = new byte[3];             // 保留字节
        public int              emSenseType;                          // 传感器类型,参考NET_SENSE_METHOD
        public int              emDefenceAreaType;                    // 防区类型 ,参考EM_NET_DEFENCE_AREA_TYPE
        public NET_GPS_STATUS_INFO stuGPS;                            // GPS信息
        public byte[]           szUserID = new byte[32];              // 本地报警时登陆的用户ID
        public byte[]           szUserName = new byte[128];           // 本地报警时登陆的用户名
        public byte[]           szSN = new byte[32];                  // 设备序列号
        public int

                bExAlarmIn;                           // 外部输入报警
        public NET_FILE_PROCESS_INFO stuFileProcessInfo;              // 图片与智能事件信息
        public NET_EVENT_INFO_EXTEND stuEventInfoEx = new NET_EVENT_INFO_EXTEND(); //事件公共扩展字段结构体
        public byte[]           byReservedEx = new byte[512];         // 保留字节
    }


    // GPS状态信息
    public static class NET_GPS_STATUS_INFO extends SdkStructure
    {
        public NET_TIME         revTime = new NET_TIME();             // 定位时间
        public byte[]           DvrSerial = new byte[50];             // 设备序列号
        public byte[]           byRserved1 = new byte[6];             // 对齐字节
        public double           longitude;                            // 经度(单位是百万分之度,范围0-360度)
        public double           latidude;                             // 纬度(单位是百万分之度,范围0-180度)
        public double           height;                               // 高度(米)
        public double           angle;                                // 方向角(正北方向为原点,顺时针为正)
        public double           speed;                                // 速度(单位km/H)
        public short            starCount;                            // 定位星数, emDateSource为 EM_DATE_SOURCE_GPS时有效
        public byte[]           byRserved2 = new byte[2];             // 对齐字节
        public int              antennaState;                         // 天线状态, 参考  NET_THREE_STATUS_BOOL, emDateSource为 EM_DATE_SOURCE_GPS时有效
        public int              orientationState;                     // 定位状态, 参考  NET_THREE_STATUS_BOOL
        public int              workStae;                             // 工作状态(0=未定位,1=非差分定位,2=差分定位,3=无效PPS,6=正在估算
        // emDateSource为 EM_DATE_SOURCE_GPS时有效
        public int              nAlarmCount;                          // 发生的报警位置个数
        public int[]            nAlarmState = new int[128];           // 发生的报警位置,值可能多个, emDateSource为 EM_DATE_SOURCE_GPS时有效
        public byte             bOffline;                             // 0-实时 1-补传
        public byte             bSNR;                                 // GPS信噪比,表示GPS信号强度,值越大,信号越强 范围：0~100,0表示不可用
        public byte[]           byRserved3 = new byte[2];             // 对齐字节
        public int              emDateSource;                         // 数据来源, 参考 EM_DATE_SOURCE
        public int              nSignalStrength;                      // 在当前工作模式下（GPS或北斗等系统）的信号强度
        public float            fHdop;                                // 水平精度因子惯性导航时无效
        public float            fPdop;                                // 位置精度因子,惯性导航时无效
        public int              nMileage;                             //总里程， 单位 米
        public byte[]           byRserved = new byte[96];             // 保留字节
    }
    // 驾驶位违规信息
    public static class EVENT_COMM_SEAT extends SdkStructure
    {
        public int              bEnable;                              //是否检测到座驾信息, 类型BOOL, 取值0或者1
        public int              emSeatType;                           //座驾类型,0:未识别;1:主驾驶; 取值为EM_COMMON_SEAT_TYPE中的值
        public EVENT_COMM_STATUS stStatus;                            //违规状态
        public int              emSafeBeltStatus;                     //安全带状态, 取值为NET_SAFEBELT_STATE中的值
        public int              emSunShadeStatus;                     //遮阳板状态, 取值为NET_SUNSHADE_STATE中的值
        public int              emCallAction;                         //打电话动作,参见枚举定义 {@link com.netsdk.lib.enumeration.EM_CALL_ACTION_TYPE}
        public int              nSafeBeltConf;                        //安全带确信度
        public int              nPhoneConf;                           //打电话置信度
        public int              nSmokeConf;                           //抽烟置信度
        public byte[]           szReserved = new byte[8];             //预留字节
    }
    // 违规状态
    public static class EVENT_COMM_STATUS extends SdkStructure
    {
        public byte             bySmoking;                            //是否抽烟
        public byte             byCalling;                            //是否打电话
        public byte[]           szReserved = new byte[14];            //预留字段
    }

    public static final int NET_MAX_ATTACHMENT_NUM              = 8;            // 最大车辆物件数量
    public static final int NET_MAX_ANNUUALINSPECTION_NUM       = 8;            // 最大年检标识位置
    public static final int NET_MAX_EVENT_PIC_NUM               = 6;            // 最大原始图片张数
    public static class EVENT_COMM_INFO extends SdkStructure
    {
        public int              emNTPStatus;                          // NTP校时状态, 取值为EM_NTP_STATUS中的值
        public int              nDriversNum;                          // 驾驶员信息数
        public Pointer          pstDriversInfo;                       // 驾驶员信息数据，类型为 NET_MSG_OBJECT_EX*
        public Pointer          pszFilePath;                          // 本地硬盘或者sd卡成功写入路径,为NULL时,路径不存在， 类型为char *
        public Pointer          pszFTPPath;                           // 设备成功写到ftp服务器的路径， 类型为char *
        public Pointer          pszVideoPath;                         // 当前接入需要获取当前违章的关联视频的FTP上传路径， 类型为char *
        public EVENT_COMM_SEAT[] stCommSeat = new EVENT_COMM_SEAT[COMMON_SEAT_MAX_NUMBER]; // 驾驶位信息
        public int              nAttachmentNum;                       // 车辆物件个数
        public EVENT_COMM_ATTACHMENT[] stuAttachment = new EVENT_COMM_ATTACHMENT[NET_MAX_ATTACHMENT_NUM]; //车辆物件信息
        public int              nAnnualInspectionNum;                 // 年检标志个数
        public NET_RECT[]       stuAnnualInspection = new NET_RECT[NET_MAX_ANNUUALINSPECTION_NUM]; //年检标志
        public float            fHCRatio;                             // HC所占比例，单位：%
        public float            fNORatio;                             // NO所占比例，单位：%
        public float            fCOPercent;                           // CO所占百分比，单位：% 取值0~100
        public float            fCO2Percent;                          // CO2所占百分比，单位：% 取值0~100
        public float            fLightObscuration;                    // 不透光度，单位：% 取值0~100
        public int              nPictureNum;                          // 原始图片张数
        public EVENT_PIC_INFO[] stuPicInfos = new EVENT_PIC_INFO[NET_MAX_EVENT_PIC_NUM]; // 原始图片信息
        public float            fTemperature;                         // 温度值,单位摄氏度
        public int              nHumidity;                            // 相对湿度百分比值
        public float            fPressure;                            // 气压值,单位Kpa
        public float            fWindForce;                           // 风力值,单位m/s
        public int              nWindDirection;                       // 风向,单位度,范围:[0,360]
        public float            fRoadGradient;                        // 道路坡度值,单位度
        public float            fAcceleration;                        // 加速度值,单位:m/s2
        public NET_RFIDELETAG_INFO stuRFIDEleTagInfo = new NET_RFIDELETAG_INFO(); // RFID 电子车牌标签信息
        public EVENT_PIC_INFO   stuBinarizedPlateInfo = new EVENT_PIC_INFO(); // 二值化车牌抠图
        public EVENT_PIC_INFO   stuVehicleBodyInfo = new EVENT_PIC_INFO(); // 车身特写抠图
        public int              emVehicleTypeInTollStation;           // 收费站车型分类,详见EM_VEHICLE_TYPE
        public int              emSnapCategory;                       // 抓拍的类型，默认为机动车,详见EM_SNAPCATEGORY
        public int              nRegionCode;                          // 车牌所属地区代码,默认-1表示未识别
        public int              emVehicleTypeByFunc;                  // 按功能划分的车辆类型，详见EM_VEHICLE_TYPE_BY_FUNC
        public int              emStandardVehicleType;                // 标准车辆类型，详见EM_STANDARD_VEHICLE_TYPE
        public int              nExtraPlateCount;                     // 额外车牌数量
        public byte[]           szExtraPlateNumer = new byte[3*32];   // 额外车牌信息

        public			int            emOverseaVehicleCategory;
        /**
         车牌所属国家的省、州等地区名
         */
        public			byte[]         szProvince = new byte[64];
        /**
         物体在雷达坐标系中的信息,单位：米，设备视角：右手方向为X轴正向，正前方为Y轴正向
         */
        public			NET_EVENT_RADAR_INFO stuRadarInfo = new NET_EVENT_RADAR_INFO();
        /**
         触发事件时物体的GPS信息
         */
        public			NET_EVENT_GPS_INFO stuGPSInfo = new NET_EVENT_GPS_INFO();
        /**
         辅车牌信息，某些国家或地区一车多牌，比如港澳三地车，一车会有3个车牌，其中一个主车牌，一般是内地发给香港或澳门的能以此在内地行驶的"港澳牌"；另外两个分别是香港牌或澳门牌，是得以在香港或澳门行驶的牌照，而这两个则称为辅牌，有辅牌的车的车牌相关信息则填在此字段，目前最多2个辅车牌
         */
        public			NET_EXTRA_PLATES[] stuExtraPlates = new NET_EXTRA_PLATES[2];
        /**
         辅车牌有效个数
         */
        public			int            nExtraPlatesCount;
        /**
         车牌识别置信度
         */
        public			int            nPlateRecogniseConf;
        /**
         车辆姿态置信度
         */
        public			int            nVecPostureConf;
        /**
         车身颜色置信度
         */
        public			int            nVecColorConf;
        /**
         特殊车辆识别结果置信度
         */
        public			int            nSpecialVehConf;
        /**
         机动车是否为大角度
         */
        public			int            nIsLargeAngle;
        /**
         当前机动车车身是否曾经关联车牌
         */
        public			int            nIsRelatedPlate;
        /**
         机动车检测置信度
         */
        public			int            nDetectConf;
        /**
         机动车清晰度分值
         */
        public			int            nClarity;
        /**
         机动车完整度评分
         */
        public			int            nCompleteScore;
        /**
         机动车优选分数
         */
        public			int            nQeScore;
        public float            fSpeedFloat;                          //浮点型速度值，单位km/h
        public double           dbHeadingAngle;                       //航向角, 以正北方向为基准输出车辆运动方向同正北方向的角度; 范围 0~360，顺时针正,单位为度；
        public int              nDriverNum;                           //车辆前排驾驶室人员数量
        /**
         预留字节
         */
        public			byte[]         bReserved = new byte[112];
        public byte[]           szCountry = new byte[20];             // 国家

        public			EVENT_COMM_INFO(){
            for(int i=0;i<stCommSeat.length;i++){
                stCommSeat[i]=new EVENT_COMM_SEAT();
            }
            for(int i=0;i<stuAttachment.length;i++){
                stuAttachment[i]=new EVENT_COMM_ATTACHMENT();
            }
            for(int i=0;i<stuAnnualInspection.length;i++){
                stuAnnualInspection[i]=new NET_RECT();
            }
            for(int i=0;i<stuPicInfos.length;i++){
                stuPicInfos[i]=new EVENT_PIC_INFO();
            }
            for(int i=0;i<stuExtraPlates.length;i++){
                stuExtraPlates[i]=new NET_EXTRA_PLATES();
            }
        }
    }
    public static final int MAX_RFIDELETAG_CARDID_LEN           = 16;           // RFID 电子车牌标签信息中卡号最大长度
    public static final int NET_MAX_PLATE_NUMBER_LEN            = 32;           // 车牌字符长度
    public static final int MAX_RFIDELETAG_DATE_LEN             = 16;           // RFID 电子车牌标签信息中时间最大长度

    // RFID 电子车牌标签信息
    public static class NET_RFIDELETAG_INFO extends SdkStructure
    {
        public byte[]           szCardID = new byte[MAX_RFIDELETAG_CARDID_LEN]; // 卡号
        public int              nCardType;                            // 卡号类型, 0:交通管理机关发行卡, 1:新车出厂预装卡
        public int              emCardPrivince;                       // 卡号省份, 对应   EM_CARD_PROVINCE
        public byte[]           szPlateNumber = new byte[NET_MAX_PLATE_NUMBER_LEN]; // 车牌号码
        public byte[]           szProductionDate = new byte[MAX_RFIDELETAG_DATE_LEN]; // 出厂日期
        public int              emCarType;                            // 车辆类型, 对应  EM_CAR_TYPE
        public int              nPower;                               // 功率,单位：千瓦时，功率值范围0~254；255表示该车功率大于可存储的最大功率值
        public int              nDisplacement;                        // 排量,单位：百毫升，排量值范围0~254；255表示该车排量大于可存储的最大排量值
        public int              nAntennaID;                           // 天线ID，取值范围:1~4
        public int              emPlateType;                          // 号牌种类, 对应  EM_PLATE_TYPE
        public byte[]           szInspectionValidity = new byte[MAX_RFIDELETAG_DATE_LEN]; // 检验有效期，年-月
        public int              nInspectionFlag;                      // 逾期未年检标志, 0:已年检, 1:逾期未年检
        public int              nMandatoryRetirement;                 // 强制报废期，从检验有效期开始，距离强制报废期的年数
        public int              emCarColor;                           // 车身颜色, 对应  EM_CAR_COLOR_TYPE
        public int              nApprovedCapacity;                    // 核定载客量，该值<0时：无效；此值表示核定载客，单位为人
        public int              nApprovedTotalQuality;                // 此值表示总质量，单位为百千克；该值<0时：无效；该值的有效范围为0~0x3FF，0x3FF（1023）表示数据值超过了可存储的最大值
        public NET_TIME_EX      stuThroughTime;                       // 过车时间
        public int              emUseProperty;                        // 使用性质, 对应  EM_USE_PROPERTY_TYPE
        public byte[]           szPlateCode = new byte[MAX_COMMON_STRING_8]; // 发牌代号，UTF-8编码
        public byte[]           szPlateSN = new byte[MAX_COMMON_STRING_16]; // 号牌号码序号，UTF-8编码
        public byte[]           szTID = new byte[MAX_COMMON_STRING_64]; // 标签(唯一标识), UTF-8编码
        public byte[]           bReserved = new byte[40];             // 保留字节,留待扩展.
    }
    public static final int MAX_COMMON_STRING_8                 = 8;            // 通用字符串长度8

    public static final int MAX_COMMON_STRING_16                = 16;           // 通用字符串长度16

    // 交通抓图图片信息
    public static class EVENT_PIC_INFO extends SdkStructure
    {
        public int              nOffset;                              // 原始图片偏移，单位字节
        public int              nLength;                              // 原始图片长度，单位字节
        public int              nIndexInData;                         // 在上传图片数据中的图片序号
    }

    // 车辆物件
    public static class EVENT_COMM_ATTACHMENT extends SdkStructure
    {
        public int              emAttachmentType;                     //物件类型, 取值为EM_COMM_ATTACHMENT_TYPE中的值
        public NET_RECT         stuRect;                              //坐标
        public int              nConf;                                //置信度
        public byte[]           bReserved = new byte[16];             //预留字节
    }
    // CLIENT_LoginWithHighLevelSecurity 输出参数
    public static class NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY extends SdkStructure
    {
        public int              dwSize;                               // 结构体大小
        public NET_DEVICEINFO_Ex stuDeviceInfo;                       // 设备信息
        public int              nError;                               // 错误码，见 CLIENT_Login 接口错误码
        public byte[]           byReserved = new byte[132];           // 预留字段

        public NET_OUT_LOGIN_WITH_HIGHLEVEL_SECURITY()
        {
            this.dwSize = this.size();
        }// 此结构体大小
    }
    /***********************************************************************
     ** 回调
     ***********************************************************************/
    //JNA Callback方法定义,断线回调
    public interface fDisConnect extends Callback {
        public void invoke(LLong lLoginID,String pchDVRIP,int nDVRPort,Pointer dwUser);
    }

    // 网络连接恢复回调函数原形
    public interface fHaveReConnect extends Callback {
        public void invoke(LLong lLoginID,String pchDVRIP,int nDVRPort,Pointer dwUser);
    }

    // 设备信息扩展///////////////////////////////////////////////////
    public static class NET_DEVICEINFO_Ex extends SdkStructure {
        public byte[]           sSerialNumber = new byte[NET_SERIALNO_LEN]; // 序列号
        public int              byAlarmInPortNum;                     // DVR报警输入个数
        public int              byAlarmOutPortNum;                    // DVR报警输出个数
        public int              byDiskNum;                            // DVR硬盘个数
        public int              byDVRType;                            // DVR类型,见枚举NET_DEVICE_TYPE
        public int              byChanNum;                            // DVR通道个数
        public byte             byLimitLoginTime;                     // 在线超时时间,为0表示不限制登陆,非0表示限制的分钟数
        public byte             byLeftLogTimes;                       // 当登陆失败原因为密码错误时,通过此参数通知用户,剩余登陆次数,为0时表示此参数无效
        public byte[]           bReserved = new byte[2];              // 保留字节,字节对齐
        public int              byLockLeftTime;                       // 当登陆失败,用户解锁剩余时间（秒数）, -1表示设备未设置该参数
        public byte[]           Reserved = new byte[24];              // 保留
    }


    // 打开日志功能
    // pstLogPrintInfo指向LOG_SET_PRINT_INFO的指针
    public boolean CLIENT_LogOpen(LOG_SET_PRINT_INFO pstLogPrintInfo);

    //  JNA直接调用方法定义，设置断线重连成功回调函数，设置后SDK内部断线自动重连, fHaveReConnect 回调
    public void CLIENT_SetAutoReconnect(Callback cbAutoConnect,Pointer dwUser);

    // SDK全局日志打印信息
    public static class LOG_SET_PRINT_INFO extends SdkStructure
    {
        public int              dwSize;
        public int              bSetFilePath;                         //是否重设日志路径, BOOL类型，取值0或1
        public byte[]           szLogFilePath = new byte[MAX_LOG_PATH_LEN]; //日志路径(默认"./sdk_log/sdk_log.log")
        public int              bSetFileSize;                         //是否重设日志文件大小, BOOL类型，取值0或1
        public int              nFileSize;                            //每个日志文件的大小(默认大小10240),单位:比特, 类型为unsigned int
        public int              bSetFileNum;                          //是否重设日志文件个数, BOOL类型，取值0或1
        public int              nFileNum;                             //绕接日志文件个数(默认大小10), 类型为unsigned int
        public int              bSetPrintStrategy;                    //是否重设日志打印输出策略, BOOL类型，取值0或1
        public int              nPrintStrategy;                       //日志输出策略,0:输出到文件(默认); 1:输出到窗口, 类型为unsigned int
        public byte[]           byReserved = new byte[4];             // 字节对齐
        public Pointer          cbSDKLogCallBack;                     // 日志回调，需要将sdk日志回调出来时设置，默认为NULL
        public Pointer          dwUser;                               // 用户数据

        public LOG_SET_PRINT_INFO()
        {
            this.dwSize = this.size();
        }
    }
    public boolean CLIENT_Init(Callback cbDisConnect,Pointer dwUser);

    public boolean CLIENT_GetAllProgrammePlans(LLong lLoginID,NET_IN_GET_ALL_PROGRAMMEPLANS pInParam,NET_OUT_GET_ALL_PROGRAMMEPLANS pOutParam,int nWaitTime);


    // 获取所有节目计划输入参数
    public static class NET_IN_GET_ALL_PROGRAMMEPLANS extends SdkStructure
    {
        public int              dwSize;

        public NET_IN_GET_ALL_PROGRAMMEPLANS() {
            this.dwSize = this.size();
        }
    }

    // 即时节目计划配置信息
    public static class NET_IMMEDIATELY_PLAN_INFO extends SdkStructure
    {
        public byte[]           szPlanName = new byte[MAX_COMMON_STRING_64]; // 节目计划名称
        public byte[]           szPlanID = new byte[MAX_COMMON_STRING_64]; // 节目计划ID ，添加时无效，用于修改、删除
        public byte[]           szSplitScreenID = new byte[MAX_COMMON_STRING_64]; // 分屏ID
        public int              bEnable;                              // 计划是否启用 , BOOL类型
        public int              nPlayTime;                            // 播放时长, 单位 : 分钟
        public byte[]           szProgrammeName = new byte[MAX_COMMON_STRING_64]; // 即时发布的节目名称
        public byte[]           szProgrammeID = new byte[MAX_COMMON_STRING_64]; // 即时发布的节目ID
        public byte[]           byReserved = new byte[512];           // 保留字节
    }

    // 定时节目计划配置信息
    public static class NET_TIMER_PLAN_INFO extends SdkStructure
    {
        public byte[]           szPlanName = new byte[MAX_COMMON_STRING_64]; // 节目计划名称
        public byte[]           szPlanID = new byte[MAX_COMMON_STRING_64]; // 节目计划ID，添加时无效，用于修改、删除
        public byte[]           szSplitScreenID = new byte[MAX_COMMON_STRING_64]; // 分屏ID
        public int              emDataType;                           // 节目计划日期类型, 对应 EM_TIMERPLAN_DATE_TYPE
        public int              nDataCount;                           // 节目计划日期个数
        public int[]            nPlayDates = new int[MAX_PLAYDATES_COUNT]; // 节目播放日期列表
        public NET_PROGRAMME_DATA stuSatrtDate = new NET_PROGRAMME_DATA(); // 节目开始日期
        public NET_PROGRAMME_DATA stuEndDate = new NET_PROGRAMME_DATA(); // 节目结束日期
        public int              emReviewState;                        // 审核状态, 对应  EM_REVIES_STATE
        public byte[]           szReviewOpinion = new byte[MAX_COMMON_STRING_64]; // 审核意见
        public int              bOverdue;                             // 计划是否过期, BOOL类型
        public int              nProgrammes;                          // 节目个数
        public NET_PROGRAMME_OF_PLAN[] stuProgrammes = (NET_PROGRAMME_OF_PLAN[])new NET_PROGRAMME_OF_PLAN().toArray(MAX_PROGRAMMES_COUNT); // 节目组信息
        public byte[]           byReserved = new byte[512];           // 保留字节
    }

    // 节目时间信息
    public static class NET_PROGRAMME_TIME_INFO extends SdkStructure
    {
        public int              dwHour;                               // 时
        public int              dwMinute;                             // 分
        public int              dwSecond;                             // 秒

        public String toString() {
            return dwHour + ":" + dwMinute + "：" + dwSecond;
        }
    }
    // 获取所有节目计划输出参数
    public static class NET_OUT_GET_ALL_PROGRAMMEPLANS extends SdkStructure
    {
        public int              dwSize;
        public int              nMaxPlanCnt;                          // 即时节目和定时节目计划最大个数, 由用户指定
        public int              nRetImmCnt;                           // 实际返回的即时节目计划个数
        public Pointer          pstImmePlan;                          // 即时节目计划信息, 对应  NET_IMMEDIATELY_PLAN_INFO[]，
        // 大小 nMaxPlanCnt 个 NET_IMMEDIATELY_PLAN_INFO
        public int              nRetTimerCnt;                         // 实际返回的定时节目计划个数
        public Pointer          pstTimerPlan;                         // 定时节目计划信息,对应  NET_TIMER_PLAN_INFO[]，
        // 大小 nMaxPlanCnt 个 NET_TIMER_PLAN_INFO

        public NET_OUT_GET_ALL_PROGRAMMEPLANS() {
            this.dwSize = this.size();
        }
    }


    // 节目计划中的节目信息
    public static class NET_PROGRAMME_OF_PLAN extends SdkStructure
    {
        public byte[]           szProgrammeName = new byte[MAX_COMMON_STRING_64]; // 节目名称
        public byte[]           szProgrammeID = new byte[MAX_COMMON_STRING_64]; // 节目ID
        public int              bIsBgProgramme;                       // 是否背景节目, BOOL类型
        public NET_PROGRAMME_TIME_INFO stuSatrtTime;                  // 节目开始时间
        public NET_PROGRAMME_TIME_INFO stuEndTime;                    // 节目结束时间
        public byte[]           byReserved = new byte[128];           // 保留字节
    }

    // 节目日期格式
    public static class NET_PROGRAMME_DATA extends SdkStructure
    {
        public int              dwYear;                               // 年
        public int              dwMonth;                              // 月
        public int              dwDay;                                // 日

        public String toString() {
            return dwYear + "-" + dwMonth + "-" + dwDay;
        }
    }


    // 即时计划与定时计划信息数组
    public static class NET_PROGRAMME_PLANS_INFO extends SdkStructure {
        public NET_IMMEDIATELY_PLAN_INFO[] szImmePlan;                // 即时节目计划信息数组
        public NET_TIMER_PLAN_INFO[] szTimerPlan;                     // 定时节目计划信息数组

        public NET_PROGRAMME_PLANS_INFO() {}

        public NET_PROGRAMME_PLANS_INFO(int maxPlanCount) {
            szImmePlan = new NET_IMMEDIATELY_PLAN_INFO[maxPlanCount];
            szTimerPlan = new NET_TIMER_PLAN_INFO[maxPlanCount];
            for(int i = 0; i < maxPlanCount; i++) {
                szImmePlan[i] = new NET_IMMEDIATELY_PLAN_INFO();
                szTimerPlan[i] = new NET_TIMER_PLAN_INFO();
            }
        }
    }




    public boolean CLIENT_DelMultiProgrammePlans(LLong lLoginID,NET_IN_DEL_PROGRAMMEPLANS pInParam,NET_OUT_DEL_PROGRAMMEPLANS pOutParam,int nWaitTime);

    public boolean CLIENT_DelMultiProgrammesById(LLong lLoginID,NET_IN_DEL_PROGRAMMES pInParam,NET_OUT_DEL_PROGRAMMES pOutParam,int nWaitTime);

    // 删除多个节目计划输出参数
    public static class NET_OUT_DEL_PROGRAMMEPLANS extends SdkStructure
    {
        public int              dwSize;

        public NET_OUT_DEL_PROGRAMMEPLANS() {
            this.dwSize = this.size();
        }
    }
    // 节目计划ID
    public static class PLAN_ID extends SdkStructure
    {
        public byte[]           szPlanID = new byte[MAX_COMMON_STRING_64]; // 节目计划ID
    }
    // 删除多个节目计划输入参数
    public static class NET_IN_DEL_PROGRAMMEPLANS extends SdkStructure
    {
        public int              dwSize;
        public int              nPlanID;                              // 节目计划ID个数
        public PLAN_ID[]        szPlanIDArr = (PLAN_ID[])new PLAN_ID().toArray(MAX_PROGRAMMES_COUNT); // 节目计划ID

        public NET_IN_DEL_PROGRAMMEPLANS() {
            this.dwSize = this.size();
        }
    }

    // 批量删除节目信息接口输出参数
    public static class NET_OUT_DEL_PROGRAMMES extends SdkStructure
    {
        public int              dwSize;

        public NET_OUT_DEL_PROGRAMMES() {
            this.dwSize = this.size();
        }
    }





    // 需要删除的节目ID
    public static class PRO_GRAMME_ID extends SdkStructure
    {
        public byte[]           szProGrammeIdList = new byte[MAX_COMMON_STRING_64]; // 需要删除的节目ID
    }


    // 批量删除节目信息接口输入参数
    public static class NET_IN_DEL_PROGRAMMES extends SdkStructure
    {
        public int              dwSize;
        public int              nProgrammeID;                         // 节目ID个数
        public PRO_GRAMME_ID[]  szProGrammeIdListArr = (PRO_GRAMME_ID[])new PRO_GRAMME_ID().toArray(MAX_PROGRAMMES_COUNT); // 需要删除的节目ID列表

        public NET_IN_DEL_PROGRAMMES() {
            this.dwSize = this.size();
        }
    }


    public boolean CLIENT_GetAllProgramOnPlayBox(LLong lLoginID,NET_IN_GET_ALL_PLAYBOX_PROGRAM pInParam,NET_OUT_GET_ALL_PLAYBOX_PROGRAM pOutParam,int nWaitTime);

    //获取播放盒上全部节目信息接口输入参数
    public static class NET_IN_GET_ALL_PLAYBOX_PROGRAM extends SdkStructure
    {
        public int              dwSize;

        public NET_IN_GET_ALL_PLAYBOX_PROGRAM() {
            this.dwSize = this.size();
        }
    }


    //获取播放盒上全部节目信息接口输出参数
    public static class NET_OUT_GET_ALL_PLAYBOX_PROGRAM extends SdkStructure
    {
        public int              dwSize;
        public int              nMaxProgramCount;                     // 节目信息最大个数，由用户指定
        public int              nRetProgramCount;                     // 实际返回的节目信息个数
        public Pointer          pstProgramInfo;                       // 播放盒上的节目信息, 内存资源由用户维护,对应 NET_PROGRAM_ON_PLAYBOX[]

        public NET_OUT_GET_ALL_PLAYBOX_PROGRAM() {
            this.dwSize = this.size();
        }
    }


    // LOGO节目信息
    public static class NET_PROGRAM_LOGO_INFO extends SdkStructure
    {
        public byte[]           szLogoPath = new byte[MAX_COMMON_STRING_128]; // Logo路径
        public NET_RECT         stuBackgroundRect;                    // Logo位置
        public int              nDiaphaneity;                         // 透明度, 0-100
        public byte[]           byReserved = new byte[128];           // 保留字节
    }
    // 广告条节目信息
    public static class NET_PROGRAM_BAR_INFO extends SdkStructure
    {
        public byte[]           szContent = new byte[MAX_COMMON_STRING_512]; // 广告内容
        public NET_COLOR_RGBA   stuFontColor;                         // 字体颜色
        public int              nFontSize;                            // 字体大小
        public byte[]           szFontStyle = new byte[MAX_COMMON_STRING_32]; // 字体类型
        public int              nPlaySpeed;                           // 播放速度
        public NET_RECT         stuBackgroundRect;                    // 广告条位置
        public NET_COLOR_RGBA   stuBackColor;                         // 广告条背景颜色
        public int              nDiaphaneity;                         // 透明度, 0-100
        public byte[]           byReserved = new byte[128];           // 保留字节
    }

    // 普通广告节目信息
    public static class NET_PROGRAM_ORDINARY_INFO extends SdkStructure
    {
        public int              bTempletState;                        // 节目是否保存为模板
        public byte[]           szDescription = new byte[MAX_COMMON_STRING_128]; // 节目描述信息
        public int              nWidth;                               // 画布宽度
        public int              nHeight;                              // 画布高度
        public int              nWinCount;                            // 窗口数量
        public NET_PLAYBOX_WINDOWS_INFO[] stuWindowsInfo = (NET_PLAYBOX_WINDOWS_INFO[])new NET_PLAYBOX_WINDOWS_INFO().toArray(MAX_WINDOWS_COUNT); // 窗口信息
        public byte[]           byReserved = new  byte[128];          // 保留字节
    }
    // 颜色RGBA
    public static class NET_COLOR_RGBA extends SdkStructure
    {
        public int              nRed;                                 //红
        public int              nGreen;                               //绿
        public int              nBlue;                                //蓝
        public int              nAlpha;                               //透明

        public String toString() {
            return "[" + nRed + " " + nGreen + " " + nBlue + " " + nAlpha + "]";
        }
    }
    // 播放盒上窗口信息
    public static class NET_PLAYBOX_WINDOWS_INFO extends SdkStructure
    {
        public NET_RECT         stuRect;                              // 窗口位置
        public int              nZorder;                              // 窗口Z轴序
        public int              nVolume;                              // 窗口音量，相对整屏音量的百分比
        public NET_COLOR_RGBA   stuBackColor;                         // 窗口背景颜色
        public int              nDiaphaneity;                         // 窗口背景透明度0-100
        public int              emTourPeriodType;                     // 窗口轮训类型 EM_TOURPERIOD_TYPE
        public int              nTourPeriodTime;                      // 自定义轮训时间，单位秒, 轮训类型为自定义轮训时有效
        public int              bAutoPlay;                            // 预览自动播放,Video元素有效
        public int              bLoopPlay;                            // 预览循环播放,Video元素有效
        public int              nElementsCount;                       // 诱导屏窗口元素个数
        public Pointer          pstElementsBuf;                       // 播放盒窗口元素信息缓存区, 根据类型对应不同的结构体
        // 填充多个元素信息, 每个元素信息内容为 NET_ELEMENT_COMMON_INFO + 元素类型对应的结构体
        public int              nBufLen;                              // 诱导屏窗口元素信息缓存区大小
        public byte[]           byReserved = new byte[128];           // 保留字节
    }

    // 播放盒节目信息
    public static class NET_PROGRAM_ON_PLAYBOX extends SdkStructure
    {
        public byte[]           szProgrammeName = new byte[MAX_COMMON_STRING_64]; // 节目名称
        public byte[]           szProgrammeID = new byte[MAX_COMMON_STRING_64]; // 节目ID, 添加节目时不需要指定
        public int              bEnable;                              // 节目是否启用
        public int              emProgramType;                        // 节目类型, 参考  EM_PLAYBOXPROGRAM_TYPE
        public NET_PROGRAM_LOGO_INFO stuLogoInfo;                     // LOGO节目信息, emProgramType为EM_PROGRAM_ON_PLAYBOX_LOGO时有效
        public NET_PROGRAM_BAR_INFO stuBarInfo;                       // 广告条节目信息, emProgramType为EM_PROGRAM_ON_PLAYBOX_BAR时有效
        public NET_PROGRAM_ORDINARY_INFO stuOrdinaryInfo = new NET_PROGRAM_ORDINARY_INFO(); // 普通节目信息, emProgramType为EM_PROGRAM_ON_PLAYBOX_ORDINARY时有效, 此参数需要在库里new对象
        public byte[]           byReserved = new byte[512];           // 保留字节
    }

    // CLIENT_ListRemoteFile 接口输出参数
    public static class NET_OUT_LIST_REMOTE_FILE extends SdkStructure
    {
        public int              dwSize;
        public Pointer          pstuFiles;                            // 文件信息数组, 用户分配内存, 对应 NET_REMOTE_FILE_INFO[],大小为sizeof(NET_REMOTE_FILE_INFO)*nMaxFileCount
        public int              nMaxFileCount;                        // 文件信息数组大小, 用户填写
        public int              nRetFileCount;                        // 返回的文件数量

        public NET_OUT_LIST_REMOTE_FILE() {
            this.dwSize = this.size();
        }
    }

    // CLIENT_ListRemoteFile 接口输入参数
    public static class NET_IN_LIST_REMOTE_FILE extends SdkStructure
    {
        public int              dwSize;
        public String           pszPath;                              // 路径
        public int              bFileNameOnly;                        // 只获取文件名称, 不返回文件夹信息, 文件信息中只有文件名有效, BOOL类型
        public int              emCondition;                          // 指定获取文件的条件, 对应  NET_REMOTE_FILE_COND

        public NET_IN_LIST_REMOTE_FILE() {
            this.dwSize = this.size();
        }
    }


    // 获取文件的条件
    public static class NET_REMOTE_FILE_COND extends SdkStructure
    {
        public static final int   NET_REMOTE_FILE_COND_NONE = 0;        // 无条件
        public static final int   NET_REMOTE_FILE_COND_VOICE = 1;       // 语音联动的文件,*无法*按路径获取,*只能*获取获取文件名称
    }


    public boolean CLIENT_ListRemoteFile(LLong lLoginID,NET_IN_LIST_REMOTE_FILE pInParam,NET_OUT_LIST_REMOTE_FILE pOutParam,int nWaitTime);


    public static class FILE_PATH extends SdkStructure {
        public String           pszPath;
    }
    public boolean CLIENT_RemoveRemoteFiles(LLong lLoginID,NET_IN_REMOVE_REMOTE_FILES pInParam,NET_OUT_REMOVE_REMOTE_FILES pOutParam,int nWaitTime);


    // CLIENT_RemoveRemoteFiles 接口输出参数
    public static class NET_OUT_REMOVE_REMOTE_FILES extends SdkStructure
    {
        public int              dwSize;

        public NET_OUT_REMOVE_REMOTE_FILES() {
            this.dwSize = this.size();
        }
    }

    // CLIENT_RemoveRemoteFiles 接口输入参数
    public static class NET_IN_REMOVE_REMOTE_FILES extends SdkStructure
    {
        public int              dwSize;
        public Pointer          pszPathPointer;                       // 文件路径数组指针,对应 FILE_PATH[]
        public int              nFileCount;                           // 文件路径数量

        public NET_IN_REMOVE_REMOTE_FILES() {
            this.dwSize = this.size();
        }
    }

    public static class NET_PARAM extends SdkStructure
    {
        public int              nWaittime;                            // 等待超时时间(毫秒为单位)，为0默认5000ms
        public int              nConnectTime;                         // 连接超时时间(毫秒为单位)，为0默认1500ms
        public int              nConnectTryNum;                       // 连接尝试次数，为0默认1次
        public int              nSubConnectSpaceTime;                 // 子连接之间的等待时间(毫秒为单位)，为0默认10ms
        public int              nGetDevInfoTime;                      // 获取设备信息超时时间，为0默认1000ms
        public int              nConnectBufSize;                      // 每个连接接收数据缓冲大小(字节为单位)，为0默认250*1024
        public int              nGetConnInfoTime;                     // 获取子连接信息超时时间(毫秒为单位)，为0默认1000ms
        public int              nSearchRecordTime;                    // 按时间查询录像文件的超时时间(毫秒为单位),为0默认为3000ms
        public int              nsubDisconnetTime;                    // 检测子链接断线等待时间(毫秒为单位)，为0默认为60000ms
        public byte             byNetType;                            // 网络类型, 0-LAN, 1-WAN
        public byte             byPlaybackBufSize;                    // 回放数据接收缓冲大小（M为单位），为0默认为4M
        public byte             bDetectDisconnTime;                   // 心跳检测断线时间(单位为秒),为0默认为60s,最小时间为2s
        public byte             bKeepLifeInterval;                    // 心跳包发送间隔(单位为秒),为0默认为10s,最小间隔为2s
        public int              nPicBufSize;                          // 实时图片接收缓冲大小（字节为单位），为0默认为2*1024*1024
        public byte[]           bReserved = new byte[4];              // 保留字段字段
    }
    public LLong CLIENT_LoginEx2(String pchDVRIP,int wDVRPort,String pchUserName,String pchPassword,int nSpecCap,Pointer pCapParam,NET_DEVICEINFO_Ex lpDeviceInfo,IntByReference error/*= 0*/);
    // 设备类型
    public static class NET_DEVICE_TYPE extends SdkStructure
    {
        public static final int   NET_PRODUCT_NONE = 0;
        public static final int   NET_DVR_NONREALTIME_MACE = 1;         // 非实时MACE
        public static final int   NET_DVR_NONREALTIME = 2;              // 非实时
        public static final int   NET_NVS_MPEG1 = 3;                    // 网络视频服务器
        public static final int   NET_DVR_MPEG1_2 = 4;                  // MPEG1二路录像机
        public static final int   NET_DVR_MPEG1_8 = 5;                  // MPEG1八路录像机
        public static final int   NET_DVR_MPEG4_8 = 6;                  // MPEG4八路录像机
        public static final int   NET_DVR_MPEG4_16 = 7;                 // MPEG4十六路录像机
        public static final int   NET_DVR_MPEG4_SX2 = 8;                // LB系列录像机
        public static final int   NET_DVR_MEPG4_ST2 = 9;                // GB系列录像机
        public static final int   NET_DVR_MEPG4_SH2 = 10;               // HB系列录像机               10
        public static final int   NET_DVR_MPEG4_GBE = 11;               // GBE系列录像机
        public static final int   NET_DVR_MPEG4_NVSII = 12;             // II代网络视频服务器
        public static final int   NET_DVR_STD_NEW = 13;                 // 新标准配置协议
        public static final int   NET_DVR_DDNS = 14;                    // DDNS服务器
        public static final int   NET_DVR_ATM = 15;                     // ATM机
        public static final int   NET_NB_SERIAL = 16;                   // 二代非实时NB系列机器
        public static final int   NET_LN_SERIAL = 17;                   // LN系列产品
        public static final int   NET_BAV_SERIAL = 18;                  // BAV系列产品
        public static final int   NET_SDIP_SERIAL = 19;                 // SDIP系列产品
        public static final int   NET_IPC_SERIAL = 20;                  // IPC系列产品                20
        public static final int   NET_NVS_B = 21;                       // NVS B系列
        public static final int   NET_NVS_C = 22;                       // NVS H系列
        public static final int   NET_NVS_S = 23;                       // NVS S系列
        public static final int   NET_NVS_E = 24;                       // NVS E系列
        public static final int   NET_DVR_NEW_PROTOCOL = 25;            // 从QueryDevState中查询设备类型,以字符串格式
        public static final int   NET_NVD_SERIAL = 26;                  // 解码器
        public static final int   NET_DVR_N5 = 27;                      // N5
        public static final int   NET_DVR_MIX_DVR = 28;                 // 混合DVR
        public static final int   NET_SVR_SERIAL = 29;                  // SVR系列
        public static final int   NET_SVR_BS = 30;                      // SVR-BS                     30
        public static final int   NET_NVR_SERIAL = 31;                  // NVR系列
        public static final int   NET_DVR_N51 = 32;                     // N51
        public static final int   NET_ITSE_SERIAL = 33;                 // ITSE 智能分析盒
        public static final int   NET_ITC_SERIAL = 34;                  // 智能交通像机设备
        public static final int   NET_HWS_SERIAL = 35;                  // 雷达测速仪HWS
        public static final int   NET_PVR_SERIAL = 36;                  // 便携式音视频录像机
        public static final int   NET_IVS_SERIAL = 37;                  // IVS（智能视频服务器系列）
        public static final int   NET_IVS_B = 38;                       // 通用智能视频侦测服务器
        public static final int   NET_IVS_F = 39;                       // 目标识别服务器
        public static final int   NET_IVS_V = 40;                       // 视频质量诊断服务器         40
        public static final int   NET_MATRIX_SERIAL = 41;               // 矩阵
        public static final int   NET_DVR_N52 = 42;                     // N52
        public static final int   NET_DVR_N56 = 43;                     // N56
        public static final int   NET_ESS_SERIAL = 44;                  // ESS
        public static final int   NET_IVS_PC = 45;                      // 人数统计服务器
        public static final int   NET_PC_NVR = 46;                      // pc-nvr
        public static final int   NET_DSCON = 47;                       // 大屏控制器
        public static final int   NET_EVS = 48;                         // 网络视频存储服务器
        public static final int   NET_EIVS = 49;                        // 嵌入式智能分析视频系统
        public static final int   NET_DVR_N6 = 50;                      // DVR-N6       50
        public static final int   NET_UDS = 51;                         // 万能解码器
        public static final int   NET_AF6016 = 52;                      // 银行报警主机
        public static final int   NET_AS5008 = 53;                      // 视频网络报警主机
        public static final int   NET_AH2008 = 54;                      // 网络报警主机
        public static final int   NET_A_SERIAL = 55;                    // 报警主机系列
        public static final int   NET_BSC_SERIAL = 56;                  // 门禁系列产品
        public static final int   NET_NVS_SERIAL = 57;                  // NVS系列产品
        public static final int   NET_VTO_SERIAL = 58;                  // VTO系列产品
        public static final int   NET_VTNC_SERIAL = 59;                 // VTNC系列产品
        public static final int   NET_TPC_SERIAL = 60;                  // TPC系列产品, 即热成像设备  60
        public static final int   NET_ASM_SERIAL = 61;                  // 无线中继设备
        public static final int   NET_VTS_SERIAL = 62;                  // 管理机
    }

    public void CLIENT_SetNetworkParam(NET_PARAM pNetParam);
    public void CLIENT_SetConnectTime(int nWaitTime,int nTryTimes);
    class LLong extends IntegerType {
        private static final long serialVersionUID = 1L;

        /** Size of a native long, in bytes. */
        public static int size;
        static {
            size = Native.LONG_SIZE;
            if (Utils.getOsPrefix().equalsIgnoreCase("linux-amd64")
                    || Utils.getOsPrefix().equalsIgnoreCase("win32-amd64")
                    || Utils.getOsPrefix().equalsIgnoreCase("mac-64")) {
                size = 8;
            } else if (Utils.getOsPrefix().equalsIgnoreCase("linux-i386")
                    || Utils.getOsPrefix().equalsIgnoreCase("win32-x86")) {
                size = 4;
            }
        }

        /** Create a zero-valued LLong. */
        public LLong() {
            this(0);
        }

        /** Create a LLong with the given value. */
        public LLong(long value) {
            super(size, value);
        }
    }
    public static class SdkStructure extends Structure {
        @Override
        protected  List<String> getFieldOrder(){
            List<String> fieldOrderList = new ArrayList<String>();
            for (Class<?> cls = getClass();
                 !cls.equals(SdkStructure.class);
                 cls = cls.getSuperclass()) {
                Field[] fields = cls.getDeclaredFields();
                int modifiers;
                for (Field field : fields) {
                    modifiers = field.getModifiers();
                    if (Modifier.isStatic(modifiers) || !Modifier.isPublic(modifiers)) {
                        continue;
                    }
                    fieldOrderList.add(field.getName());
                }
            }
            //            System.out.println(fieldOrderList);

            return fieldOrderList;
        }

        @Override
        public int fieldOffset(String name){
            return super.fieldOffset(name);
        }
    }
    public static final int EVENT_IVS_ALL                       = 0x00000001;   // 订阅所有事件

    public static final int NET_MAX_POLYGON_NUM                 = 16;           // 多边形最大顶点个数

    public LLong CLIENT_RealLoadPictureEx(LLong lLoginID,int nChannelID,int dwAlarmType,int bNeedPicFile,Callback cbAnalyzerData,Pointer dwUser,Pointer Reserved);


    // 智能分析数据回调;nSequence表示上传的相同图片情况，为0时表示是第一次出现，为2表示最后一次出现或仅出现一次，为1表示此次之后还有
    // int nState = *(int*) reserved 表示当前回调数据的状态, 为0表示当前数据为实时数据，为1表示当前回调数据是离线数据，为2时表示离线数据传送结束
    // pAlarmInfo 对应智能事件信息, pBuffer 对应智能图片信息, dwBufSize 智能图片信息大小
    public interface fAnalyzerDataCallBack extends Callback {
        public int invoke(LLong lAnalyzerHandle,int dwAlarmType,Pointer pAlarmInfo,Pointer pBuffer,int dwBufSize,Pointer dwUser,int nSequence,Pointer reserved) throws UnsupportedEncodingException;
    }


    public static final int EVENT_IVS_FISHING_DETECTION         = 0x00000390;   // 钓鱼检测事件(对应 DEV_EVENT_FISHING_DETECTION_INFO )


    public static class NET_MSG_OBJECT_EX2 extends SdkStructure
    {
        public int              dwSize;
        public int              nObjectID;                            //物体ID,每个ID表示一个唯一的物体
        public byte[]           szObjectType = new byte[128];         //物体类型
        public int              nConfidence;                          //置信度(0~255),值越大表示置信度越高
        public int              nAction;                              //物体动作:1:Appear2:Move3:Stay 4:Remove 5:Disappear 6:Split 7:Merge 8:Rename
        public DH_RECT          BoundingBox;                          //包围盒
        public NET_POINT        Center;                               //物体型心
        public int              nPolygonNum;                          //多边形顶点个数
        public NET_POINT[]      Contour = (NET_POINT[])new NET_POINT().toArray(NET_MAX_POLYGON_NUM); //较精确的轮廓多边形
        public int              rgbaMainColor;                        //表示车牌、车身等物体主要颜色；按字节表示,分别为红、绿、蓝和透明度,例如:RGB值为(0,255,0),透明度为0时,其值为0x00ff0000.
        public byte[]           szText = new byte[128];               //同NET_MSG_OBJECT相应字段
        public byte[]           szObjectSubType = new byte[64];       //物体子类别,根据不同的物体类型,可以取以下子类型：
        // 同NET_MSG_OBJECT相应字段
        public byte[]           byReserved1 = new byte[3];
        public byte             bPicEnble;                            //是否有物体对应图片文件信息, 类型为bool, 取值0或者1
        public NET_PIC_INFO     stPicInfo;                            //物体对应图片信息
        public byte             bShotFrame;                           //是否是抓拍张的识别结果, 类型为bool, 取值0或者1
        public byte             bColor;                               //物体颜色(rgbaMainColor)是否可用, 类型为bool, 取值0或者1
        public byte             bLowerBodyColor;                      //下半身颜色(rgbaLowerBodyColor)是否可用
        public byte             byTimeType;                           //时间表示类型,详见EM_TIME_TYPE说明
        public NET_TIME_EX      stuCurrentTime;                       //针对视频浓缩,当前时间戳（物体抓拍或识别时,会将此识别智能帧附在一个视频帧或jpeg图片中,此帧所在原始视频中的出现时间）
        public NET_TIME_EX      stuStartTime;                         //开始时间戳（物体开始出现时）
        public NET_TIME_EX      stuEndTime;                           //结束时间戳（物体最后出现时）
        public DH_RECT          stuOriginalBoundingBox;               //包围盒(绝对坐标)
        public DH_RECT          stuSignBoundingBox;                   //车标坐标包围盒
        public int              dwCurrentSequence;                    //当前帧序号（抓下这个物体时的帧）
        public int              dwBeginSequence;                      //开始帧序号（物体开始出现时的帧序号）
        public int              dwEndSequence;                        //结束帧序号（物体消逝时的帧序号）
        public long             nBeginFileOffset;                     //开始时文件偏移,单位:字节（物体开始出现时,视频帧在原始视频文件中相对于文件起始处的偏移）
        public long             nEndFileOffset;                       //结束时文件偏移,单位:字节（物体消逝时,视频帧在原始视频文件中相对于文件起始处的偏移）
        public byte[]           byColorSimilar = new byte[EM_COLOR_TYPE.NET_COLOR_TYPE_MAX]; //物体颜色相似度,取值范围：0-100,数组下标值代表某种颜色,详见EM_COLOR_TYPE
        public byte[]           byUpperBodyColorSimilar = new byte[EM_COLOR_TYPE.NET_COLOR_TYPE_MAX]; //上半身物体颜色相似度(物体类型为人时有效)
        public byte[]           byLowerBodyColorSimilar = new byte[EM_COLOR_TYPE.NET_COLOR_TYPE_MAX]; //下半身物体颜色相似度(物体类型为人时有效)
        public int              nRelativeID;                          //相关物体ID
        public byte[]           szSubText = new byte[20];             //"ObjectType"为"Vehicle"或者"Logo"时,表示车标下的某一车系,比如奥迪A6L,由于车系较多,SDK实现时透传此字段,设备如实填写。
        public int              nPersonStature;                       //入侵人员身高,单位cm
        public int              emPersonDirection;                    //人员入侵方向, 取值为EM_MSG_OBJ_PERSON_DIRECTION中的值
        public int              rgbaLowerBodyColor;                   //使用方法同rgbaMainColor,物体类型为人时有效
        //视频浓缩额外信息
        public int              nSynopsisSpeed;                       //浓缩速度域值,共分1~10共十个档位,5表示浓缩后只保留5以上速度的物体。是个相对单位
        // 为0时,该字段无效
        public int              nSynopsisSize;                        //浓缩尺寸域值,共分1~10共十个档位,3表示浓缩后只保留3以上大小的物体。是个相对单位
        // 为0时,该字段无效
        public int              bEnableDirection;                     //为True时,对物体运动方向做过滤, 类型为BOOL, 取值0或者1
        // 为False时,不对物体运动方向做过滤,
        public NET_POINT        stuSynopsisStartLocation;             //浓缩运动方向,起始坐标点,点的坐标归一化到[0,8192)区间,bEnableDirection为True时有效
        public NET_POINT        stuSynopsisEndLocation;               //浓缩运动方向,终止坐标点,点的坐标归一化到[0,8192)区间,bEnableDirection为True时有效
        public byte[]           szSerialUUID = new byte[22];          //智能物体全局唯一物体标识, 有效数据位21位，包含’\0’, 前2位%d%d:01-视频片段, 02-图片, 03-文件, 99-其他, 中间14位YYYYMMDDhhmmss:年月日时分秒, 后5位%u%u%u%u%u：物体ID，如00001
        public byte[]           szReserved = new byte[2];             //对齐
        public int              nCategoryType;                        //物体类别的类型, 0:未知, 1:Category, 2:InductiveVehicleType
        public byte[]           szInductiveVehicleType = new byte[32]; //在Category类型基础上对车辆类型归纳后的类型
        public byte[]           szTextDirection = new byte[32];       //移动方向(比如钢包运动检测方向)LeftToRight,从左到右,RightToLeft,从右到左,TopToBottom，从上到下,BottomToTop，从下到上
        public byte[]           byReserved = new byte[1956];          //扩展字节

        public NET_MSG_OBJECT_EX2()
        {
            this.dwSize = this.size();
        }

        protected int getNativeAlignment(Class<?> type, Object value, boolean isFirstElement) {
            int alignment = super.getNativeAlignment(type, value, isFirstElement);
            return Math.min(4, alignment);
        }
    }


    //二维空间点
    public static class NET_POINT extends SdkStructure
    {
        public short            nx;
        public short            ny;

        @Override
        public String toString() {
            return "NET_POINT{" +
                    "nx=" + nx +
                    ", ny=" + ny +
                    '}';
        }

        public NET_POINT() {
        }
    }


    public static class NET_TIME_EX extends SdkStructure
    {
        public int              dwYear;                               // 年
        public int              dwMonth;                              // 月
        public int              dwDay;                                // 日
        public int              dwHour;                               // 时
        public int              dwMinute;                             // 分
        public int              dwSecond;                             // 秒
        public int              dwMillisecond;                        // 毫秒
        public int              dwUTC;                                // utc时间(获取时0表示无效，非0有效   下发无效)
        public int[]            dwReserved = new int[1];              // 保留字段

        public void setTime(int year, int month, int day, int hour, int minute, int second) {
            this.dwYear = year;
            this.dwMonth = month;
            this.dwDay = day;
            this.dwHour = hour;
            this.dwMinute = minute;
            this.dwSecond = second;
            this.dwMillisecond = 0;
        }

        public String toString() {
            return dwYear + "/" + dwMonth + "/" + dwDay + " " + dwHour + ":" + dwMinute + ":" + dwSecond;
        }

        public String toStringTime()
        {
            return  String.format("%02d/%02d/%02d %02d:%02d:%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }

        public String toStringTitle()
        {
            return  String.format("Time_%02d%02d%02d_%02d%02d%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }
    }
    // 停止实时预览--扩展     lRealHandle为CLIENT_RealPlayEx的返回值
    public boolean CLIENT_StopRealPlayEx(LLong lRealHandle);
    public boolean CLIENT_LogClose();

    public LLong CLIENT_RealPlayEx(LLong lLoginID,int nChannelID,Pointer hWnd,int rType);
    // 预览类型,对应CLIENT_RealPlayEx接口
    public static class NET_RealPlayType extends SdkStructure
    {
        public static final int   NET_RType_Realplay = 0;               // 实时预览
        public static final int   NET_RType_Multiplay = 1;              // 多画面预览
        public static final int   NET_RType_Realplay_0 = 2;             // 实时预览-主码流 ,等同于NET_RType_Realplay
        public static final int   NET_RType_Realplay_1 = 3;             // 实时预览-从码流1
        public static final int   NET_RType_Realplay_2 = 4;             // 实时预览-从码流2
        public static final int   NET_RType_Realplay_3 = 5;             // 实时预览-从码流3
        public static final int   NET_RType_Multiplay_1 = 6;            // 多画面预览－1画面
        public static final int   NET_RType_Multiplay_4 = 7;            // 多画面预览－4画面
        public static final int   NET_RType_Multiplay_8 = 8;            // 多画面预览－8画面
        public static final int   NET_RType_Multiplay_9 = 9;            // 多画面预览－9画面
        public static final int   NET_RType_Multiplay_16 = 10;          // 多画面预览－16画面
        public static final int   NET_RType_Multiplay_6 = 11;           // 多画面预览－6画面
        public static final int   NET_RType_Multiplay_12 = 12;          // 多画面预览－12画面
        public static final int   NET_RType_Multiplay_25 = 13;          // 多画面预览－25画面
        public static final int   NET_RType_Multiplay_36 = 14;          // 多画面预览－36画面
        public static final int   NET_RType_Multiplay_64 = 15;          // 多画面预览－64画面
        public static final int   NET_RType_Multiplay_255 = 16;         // 不修改当前预览通道数
        public static final int   NET_RType_Realplay_Audio = 17;        // 只拉音频, 非通用
        public static final int   NET_RType_Realplay_Test = 255;        // 带宽测试码流
    }
    public static class DH_RECT extends SdkStructure
    {
        public NativeLong       left;
        public NativeLong       top;
        public NativeLong       right;
        public NativeLong       bottom;
    }

    //物体对应图片文件信息,对应DH_PIC_INFO
    public static class NET_PIC_INFO extends SdkStructure
    {
        public int              dwOffSet;                             // 文件在二进制数据块中的偏移位置,单位:字节
        public int              dwFileLenth;                          // 文件大小,单位:字节
        public short            wWidth;                               // 图片宽度,单位:像素
        public short            wHeight;                              // 图片高度,单位:像素
        public Pointer          pszFilePath;                          // 鉴于历史原因,该成员只在事件上报时有效， char *
        // 文件路径
        // 用户使用该字段时需要自行申请空间进行拷贝保存
        public byte             bIsDetected;                          // 图片是否算法检测出来的检测过的提交识别服务器时,
        // 则不需要再时检测定位抠图,1:检测过的,0:没有检测过
        public byte[]           bReserved = new byte[2];              // 预留字节数
        public byte             byQulityScore;                        // 人脸抓拍质量分数, 0-100
        public int              nFilePathLen;                         // 文件路径长度 既pszFilePath 用户申请的大小
        public NET_POINT        stuPoint;                             // 小图左上角在大图的位置，使用绝对坐标系
        public	int              nIndexInData;                         // 在上传图片数据中的图片序号

        public NET_PIC_INFO() {
        }
    }


    //颜色类型
    public static class EM_COLOR_TYPE extends SdkStructure
    {
        public static final int   NET_COLOR_TYPE_RED = 0;               //红色
        public static final int   NET_COLOR_TYPE_YELLOW = 1;            //黄色
        public static final int   NET_COLOR_TYPE_GREEN = 2;             //绿色
        public static final int   NET_COLOR_TYPE_CYAN = 3;              //青色
        public static final int   NET_COLOR_TYPE_BLUE = 4;              //蓝色
        public static final int   NET_COLOR_TYPE_PURPLE = 5;            //紫色
        public static final int   NET_COLOR_TYPE_BLACK = 6;             //黑色
        public static final int   NET_COLOR_TYPE_WHITE = 7;             //白色
        public static final int   NET_COLOR_TYPE_MAX = 8;
    }


    public boolean CLIENT_StopLoadPic(LLong lAnalyzerHandle);


    //区域；各边距按整长8192的比例
    public static class NET_RECT extends SdkStructure
    {
        public int              left;
        public int              top;
        public int              right;
        public int              bottom;

        public String toString() {
            return "[" + left + " " + top + " " + right + " " + bottom + "]";
        }
    }



    // 全景广角图
    public static class SCENE_IMAGE_INFO_EX extends SdkStructure
    {
        public int              nOffSet;                              // 在二进制数据块中的偏移
        public int              nLength;                              // 图片大小,单位字节
        public int              nWidth;                               // 图片宽度(像素)
        public int              nHeight;                              // 图片高度(像素)
        public byte[]           szFilePath = new byte[260];           // 全景图片路径
        public int              nIndexInData;                         //在上传图片数据中的图片序号
        public byte[]           szImageID = new byte[42];             //图片ID
        public byte[]           szReserved = new byte[6];             //预留字节
        public NET_TIME_EX      SnapTime = new NET_TIME_EX();         //抓拍时间，标准UTC时间（不带时区夏令时偏差），单位秒,参见结构体定义 {@link com.netsdk.lib.NetSDKLib.NET_TIME_EX}
        public byte[]           byReserved = new byte[424];           // 预留字节
    }



    // 获取配置
    // error 为设备返回的错误码： 0-成功 1-失败 2-数据不合法 3-暂时无法设置 4-没有权限
    public boolean CLIENT_GetNewDevConfig(LLong lLoginID,String szCommand,int nChannelID,byte[] szOutBuffer,int dwOutBufferSize,IntByReference error,int waiitime,Pointer pReserved);

    public int CLIENT_GetLastError();

    public boolean CLIENT_ParseData(String szCommand,byte[] szInBuffer,Pointer lpOutBuffer,int dwOutBufferSize,Pointer pReserved);

    public boolean CLIENT_PacketData(String szCommand,Pointer lpInBuffer,int dwInBufferSize,byte[] szOutBuffer,int dwOutBufferSize);

    public boolean CLIENT_SetNewDevConfig(LLong lLoginID,String szCommand,int nChannelID,byte[] szInBuffer,int dwInBufferSize,IntByReference error,IntByReference restart,int waittime);



    // 文件/目录信息
    public static class SDK_REMOTE_FILE_INFO extends SdkStructure
    {
        public int              dwSize;
        public int              bDirectory;                           // 是否文件夹, BOOL类型
        public byte[]           szPath = new byte[MAX_PATH];          // 路径
        public NET_TIME         stuCreateTime;                        // 创建时间
        public NET_TIME         stuModifyTime;                        // 修改时间
        public long             nFileSize;                            // 文件大小
        public byte[]           szFileType = new byte[NET_FILE_TYPE_LEN]; // 文件类型

        public SDK_REMOTE_FILE_INFO() {
            this.dwSize = this.size();
        }
    }



    // 时间
    public static class NET_TIME extends SdkStructure {
        public int              dwYear;                               // 年
        public int              dwMonth;                              // 月
        public int              dwDay;                                // 日
        public int              dwHour;                               // 时
        public int              dwMinute;                             // 分
        public int              dwSecond;                             // 秒

        public NET_TIME() {
            this.dwYear = 0;
            this.dwMonth = 0;
            this.dwDay = 0;
            this.dwHour = 0;
            this.dwMinute = 0;
            this.dwSecond = 0;
        }

        public void setTime(int year, int month, int day, int hour, int minute, int second) {
            this.dwYear = year;
            this.dwMonth = month;
            this.dwDay = day;
            this.dwHour = hour;
            this.dwMinute = minute;
            this.dwSecond = second;
        }

        public NET_TIME(NET_TIME other) {
            this.dwYear = other.dwYear;
            this.dwMonth = other.dwMonth;
            this.dwDay = other.dwDay;
            this.dwHour = other.dwHour;
            this.dwMinute = other.dwMinute;
            this.dwSecond = other.dwSecond;
        }

        public String toStringTime() {
            return  String.format("%02d/%02d/%02d %02d:%02d:%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }

        public String toStringTimeEx() {
            return  String.format("%02d-%02d-%02d %02d:%02d:%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }

        public String toString() {
            return String.format("%02d%02d%02d%02d%02d%02d", dwYear, dwMonth, dwDay, dwHour, dwMinute, dwSecond);
        }
    }

}