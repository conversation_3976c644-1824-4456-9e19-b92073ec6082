package com.jhdr.operation.utils.videoSb;
public class DemoCommon {
    public void RunTest() {
        System.out.println("Run Test");
        CaseMenu menu = new CaseMenu();
/*
		menu.addItem(new CaseMenu.Item(this , "ResetPasswd" , "ResetPasswd"));
		menu.addItem(new CaseMenu.Item(this , "startListen" , "startListen"));
		menu.addItem(new CaseMenu.Item(this , "stopListen" , "stopListen"));
		menu.addItem(new CaseMenu.Item(this , "addFaceInfo" , "addFaceInfo"));
		menu.addItem(new CaseMenu.Item(this , "updateFaceeInfo" , "updateFaceeInfo"));
		menu.addItem(new CaseMenu.Item(this , "removeFaceInfo" , "removeFaceInfo"));
		menu.addItem(new CaseMenu.Item(this , "getFaceInfo" , "getFaceInfo"));
		menu.addItem(new CaseMenu.Item(this , "SetFaceDetectScene" , "SetFaceDetectScene"));
		menu.addItem(new CaseMenu.Item(this , "SetFaceDetectEnable" , "SetFaceDetectEnable"));
		menu.addItem(new CaseMenu.Item(this , "SetVideoAnalyseModule" , "SetVideoAnalyseModule"));
		menu.addItem(new CaseMenu.Item(this , "flowstateIVSEvent", "flowstateIVSEvent"));
		menu.addItem(new CaseMenu.Item(this , "detachIVSEvent", "detachIVSEvent"));
		menu.addItem(new CaseMenu.Item(this , "TestFunctionWithCallBack" , "TestFunctionWithCallBack"));
		menu.addItem(new CaseMenu.Item(this , "syncParkingInfo" , "syncParkingInfo"));
		menu.addItem(new CaseMenu.Item(this , "queryVideoTalkLog" , "queryVideoTalkLog"));
*/
        menu.addItem(new CaseMenu.Item(this, "GetVTOManagerRelation", "GetVTOManagerRelation"));
        menu.addItem(new CaseMenu.Item(this, "SetVTOManagerRelation", "SetVTOManagerRelation"));
        menu.addItem(new CaseMenu.Item(this, "ptzControlToFocus", "ptzControlToFocus"));
        menu.addItem(new CaseMenu.Item(this, "QueryPTZInfo", "QueryPTZInfo"));
        menu.addItem(new CaseMenu.Item(this , "flowstateIVSEvent", "flowstateIVSEvent"));
        menu.addItem(new CaseMenu.Item(this , "detachIVSEvent", "detachIVSEvent"));
        menu.addItem(new CaseMenu.Item(this, "queryDev_PTZView", "queryDev_PTZView"));
        menu.addItem(new CaseMenu.Item(this, "QueryDeviceStateTest", "QueryDeviceStateTest"));
        menu.addItem(new CaseMenu.Item(this, "QueryDevLogCount", "QueryDevLogCount"));
/*		menu.addItem(new CaseMenu.Item(this , "SetFaceDetectScene", "SetFaceDetectScene"));
		menu.addItem(new CaseMenu.Item(this, "startListen", "startListen"));
		menu.addItem(new CaseMenu.Item(this, "startListen", "startListen"));
		menu.addItem(new CaseMenu.Item(this , "stopListen" , "stopListen"));*/
        menu.addItem(new CaseMenu.Item(this, "QueryDeviceTime", "QueryDeviceTime"));
        menu.addItem(new CaseMenu.Item(this, "SetupDeviceTime", "SetupDeviceTime"));

        menu.run();
    }
}