package com.jhdr.operation.utils;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;

import java.util.HashMap;
import java.util.Map;

public class HikvisionUtils {
    /**
     * @Description: 获取视频播放地址
     * @Param: [cameraCode] 输入监控编码
     * @return: java.lang.String
     **/
    public static String getVideoPlayUrl(String cameraCode){
        if (ObjectUtil.isEmpty(cameraCode)){
            return null;
        }
        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("29702023");  // 秘钥appkey
        config.setAppSecret("YzjDOjGl4h0MeWQcO3ll");// 秘钥appSecret
        final String getCamsApi = "/artemis" + "/api/video/v2/cameras/previewURLs";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        paramMap.put("cameraIndexCode", cameraCode);
        paramMap.put("streamType", "0");
        paramMap.put("protocol", "hls");
        paramMap.put("transmode", "1");
        paramMap.put("expand", "transcode=0");
        paramMap.put("streamform", "ps");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        String json= null;
        try {
            json = ArtemisHttpUtil.doPostStringArtemis(config,path, body, null, null, "application/json");
        } catch (Exception e) {
            throw new RuntimeException("请求海康威视 监控服务器失败");
        }

        JSONObject jsonObject = JSONObject.parseObject(json);
        String data = jsonObject.getString("data");

        JSONObject jsonObject1 = JSONObject.parseObject(data);
        String url = "http://*************"+jsonObject1.getString("url").substring(16);

        return url;
    }


    public static String getBianma() {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost("*************:1443"); // 代理API网关nginx服务器ip端口
        config.setAppKey("29702023");  // 秘钥appkey
        config.setAppSecret("YzjDOjGl4h0MeWQcO3ll");// 秘钥appSecret
        final String getCamsApi = "/artemis" + "/api/resource/v1/cameras";
        Map<String, String> paramMap = new HashMap<String, String>();// post请求Form表单参数
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "1000");
        paramMap.put("regionIndexCode", "271af255-451a-4763-92f0-a1d201a5ccd8");
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };

        String CamerasResult = null;
        try {
            CamerasResult = ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.parseObject(CamerasResult);
        String data = jsonObject.getString("data");
        JSONObject jsonObject1 = JSONObject.parseObject(data);
        String list = jsonObject1.getString("list");

        JSONArray json = JSONArray.parseArray(list);
//		JSONObject jsonObject2 = JSON.parseObject(list);
//        if (json.size() > 0) {
//            for (int i = 0; i < json.size(); i++) {
//                JSONObject job = (JSONObject) json.get(i);
//                String name = job.getString("cameraName");
//                String code = job.getString("cameraIndexCode");
//
//                System.out.println(name + ":" + code);
//            }
//        }

        return jsonObject.toString();
    }
}
