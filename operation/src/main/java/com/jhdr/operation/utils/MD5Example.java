package com.jhdr.operation.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

public class MD5Example {

    public static String getMd5Password(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");

        // 将输入字符串转换为字节数组
        byte[] inputBytes = input.getBytes();

        // 计算MD5摘要
        byte[] hashBytes = md.digest(inputBytes);

        // 将字节数组转换为十六进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : hashBytes) {
            sb.append(String.format("%02x", b));
        }

        String md5Hash = sb.toString();

        return md5Hash;
    }
}
