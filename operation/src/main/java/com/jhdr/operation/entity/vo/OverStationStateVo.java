package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("沿河泵站状态")
@AllArgsConstructor
@NoArgsConstructor
public class OverStationStateVo {

    @ApiModelProperty(value = "泵站名称")
    private String strName;

    @ApiModelProperty(value = "状态")
    private String strState;

}
