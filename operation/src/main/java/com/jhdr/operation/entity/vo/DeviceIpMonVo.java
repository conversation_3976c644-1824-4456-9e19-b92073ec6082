package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 运维监控-网络设备对象 opo_network_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "设备ip监测")
@Accessors(chain = true)
public class DeviceIpMonVo
{

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String deviceName;

    /** ip地址 */
    @ApiModelProperty(value = "ip地址")
    @Excel(name = "ip地址")
    private String ip;

    @ApiModelProperty(value = "位置")
    private String site;





}
