package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维机房设备报警api对象 opo_machine_room_alarm
 *
 * <AUTHOR>
 * @date 2024-07-10
 */

@Data
@ApiModel(description = "运维机房设备报警api")
@Accessors(chain = true)
public class OpoMachineRoomAlarmVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 报警id */
    @ApiModelProperty(value = "报警id")
    @TableId
    private Long id;

    /** 报警编号，如果相同表示为同一个报警，不同表示为新报警； */
    @ApiModelProperty(value = "报警编号，如果相同表示为同一个报警，不同表示为新报警；")
    @Excel(name = "报警编号，如果相同表示为同一个报警，不同表示为新报警；")
    private String alarmid;

    /** 当前设备下不同的监测点（或传感器）编号 */
    @ApiModelProperty(value = "当前设备下不同的监测点（或传感器）编号")
    @Excel(name = "当前设备下不同的监测点", readConverterExp = "或=传感器")
    private Long lscid;

    /** 唯一ID编号 */
    @ApiModelProperty(value = "唯一ID编号")
    @Excel(name = "唯一ID编号")
    private String wyid;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String siteName;

    /** 当前设备下监测点（或传感器）名称 */
    @ApiModelProperty(value = "当前设备下监测点（或传感器）名称")
    @Excel(name = "当前设备下监测点", readConverterExp = "或=传感器")
    private String name;

    /** 未知 */
    @ApiModelProperty(value = "未知")
    @Excel(name = "未知")
    private String isad;

    /** 未知 */
    @ApiModelProperty(value = "未知")
    @Excel(name = "未知")
    private String type;

    /** 当前设备地址及端口号 */
    @ApiModelProperty(value = "当前设备地址及端口号")
    @Excel(name = "当前设备地址及端口号")
    private String ip;

    /** 值 */
    @ApiModelProperty(value = "值")
    @Excel(name = "值")
    private String value;

    /** 单位 */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String dw;

    /** 报警等级，紧急1、重要2、一般3 */
    @ApiModelProperty(value = "报警等级，紧急1、重要2、一般3")
    @Excel(name = "报警等级，紧急1、重要2、一般3")
    private Long itemLevels;

    /** 报警开始时间 */
    @ApiModelProperty(value = "报警开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报警开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ts;

}
