package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("水量表")
@TableName("jh_flowyc_r")
@AllArgsConstructor
@NoArgsConstructor
public class JhFlowycRPo {
    @ApiModelProperty(value = "遥测站编码（设备）")
    private String stcd;

    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date datatm;

    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recvtm;

    @ApiModelProperty(value = "水位")
    private Double z;

    @ApiModelProperty(value = "流量")
    private Double q;

    @ApiModelProperty(value = "断面流速")
    private Double sp;

    @ApiModelProperty(value = "累计水量")
    private Double flowd;

    @ApiModelProperty(value = "时段水量")
    private Double dtrf;

    @ApiModelProperty(value = "编码")
    private String ycbm;








}
