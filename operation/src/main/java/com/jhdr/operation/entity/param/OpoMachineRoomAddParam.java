package com.jhdr.operation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 机房对象 opo_machine_room
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "机房")
@Accessors(chain = true)
public class OpoMachineRoomAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Long id;

    /** 机房名称 */
    @ApiModelProperty(value = "机房名称")
    @Excel(name = "机房名称")
    private String roomName;

    /** 温度 */
    @ApiModelProperty(value = "温度")
    @Excel(name = "温度")
    private String temp;

    /** 湿度 */
    @ApiModelProperty(value = "湿度")
    @Excel(name = "湿度")
    private String humidity;

    /** 空调开关 */
    @ApiModelProperty(value = "空调开关")
    @Excel(name = "空调开关")
    private String airState;

    /** 烟雾感应状态 */
    @ApiModelProperty(value = "烟雾感应状态")
    @Excel(name = "烟雾感应状态")
    private String smogState;

}
