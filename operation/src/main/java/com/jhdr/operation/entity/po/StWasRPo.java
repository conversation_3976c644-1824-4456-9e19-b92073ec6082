package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("上水位表")
@TableName("st_was_r")
@AllArgsConstructor
@NoArgsConstructor
public class StWasRPo {

    @ApiModelProperty(value = "遥测站编码（设备）")
    private String stcd;

    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    @ApiModelProperty(value = "上水位")
    private Double upz;

    @ApiModelProperty(value = "闸下水位")
    private Double dwz;

    @ApiModelProperty(value = "总过闸流量")
    private Double tgtq;

    @ApiModelProperty(value = "闸水特征码")
    private String swchrcd;

    @ApiModelProperty(value = "闸上水势")
    private String supwptn;

    @ApiModelProperty(value = "闸下水势")
    private String sdwwptn;

    @ApiModelProperty(value = "测流方法")
    private String msqmt;








}
