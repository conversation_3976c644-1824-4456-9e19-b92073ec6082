package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;



@Data
@ApiModel(description = "运维总览图")
@Accessors(chain = true)
public class OverviewStateVo {

    @ApiModelProperty(value = "泵站采集状态")
    private String pump ="正常";

    @ApiModelProperty(value = "涵闸状态")
    private String gate ="正常";

    @ApiModelProperty(value = "水位站状态")
    private String water ="正常";

    @ApiModelProperty(value = "流量站状态")
    private String flow ="正常";

    @ApiModelProperty(value = "监控")
    private String video ="正常";


    @ApiModelProperty(value = "感知探针1")
    private String gztz1 ="正常";

    @ApiModelProperty(value = "感知探针2")
    private String gztz2 ="正常";



    @ApiModelProperty(value = "云")
    private String cloud ="正常";

    @ApiModelProperty(value = "隔离网闸1")
    private String glwz1 ="正常";

    @ApiModelProperty(value = "隔离网闸2")
    private String glwz2 ="正常";

    @ApiModelProperty(value = "隔离网闸3")
    private String glwz3 ="正常";


    @ApiModelProperty(value = "waf")
    private String waf ="正常";

    @ApiModelProperty(value = "vpn")
    private String vpn ="正常";




    @ApiModelProperty(value = "数据采集前置系统自动化数据采集")
    private String server1 ="正常";

    @ApiModelProperty(value = "数据采集前置系统水文数据采集")
    private String server2 ="正常";

    @ApiModelProperty(value = "数据采集前置系统共享数据采集")
    private String server3 ="正常";

    @ApiModelProperty(value = "数据库系统")
    private String server4 ="正常";


    @ApiModelProperty(value = "一期业务区48口交换机1")
    private String change1 ="正常";

    @ApiModelProperty(value = "一期业务区48口交换机2")
    private String change2 ="正常";

    @ApiModelProperty(value = "二期业务区48口交换机1")
    private String change3 ="正常";

    @ApiModelProperty(value = "二期业务区48口交换机2")
    private String change4 ="正常";

    @ApiModelProperty(value = "路由器")
    private String luyou ="正常";

    @ApiModelProperty(value = "内网防火墙")
    private String fhq1 ="正常";

    @ApiModelProperty(value = "水利专网防火墙")
    private String fhq2 ="正常";

    @ApiModelProperty(value = "出口防火墙")
    private String fhq3 ="正常";













    @ApiModelProperty(value = "服务器1")
    private String state16 ="正常";

    @ApiModelProperty(value = "服务器2")
    private String state27 ="正常";

    @ApiModelProperty(value = "服务器3")
    private String state28 ="正常";

    @ApiModelProperty(value = "数据")
    private String state17 ="正常";

    @ApiModelProperty(value = "防火墙")
    private String state18 ="正常";

    @ApiModelProperty(value = "短信平台")
    private String state34 ="正常";


    @ApiModelProperty(value = "电信设备1")
    private String state19 ="正常";

    @ApiModelProperty(value = "电信设备2")
    private String state20 ="正常";


}
