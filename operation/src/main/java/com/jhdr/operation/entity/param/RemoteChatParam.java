package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 异地会商对象 remote_chat
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@Data
@ApiModel(description = "异地会商")
@Accessors(chain = true)
public class RemoteChatParam {


    /** 会商主题 */
    @ApiModelProperty(value = "会商主题")
    @Excel(name = "会商主题")
    private String title;


    /** 发起时间 */
    @ApiModelProperty(value = "年份")
    private String tm;

    /** 状态(0进行中;1已结束) */
    @ApiModelProperty(value = "状态(0进行中;1已结束)")
    @Excel(name = "状态(0进行中;1已结束)")
    private Integer status;

}
