package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 报警信息对象 opo_alarm_message
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "报警信息")
@Accessors(chain = true)
public class OpoAlarmMessageParam  {

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String staCode;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** 站点类型 */
    @ApiModelProperty(value = "站点类型")
    @Excel(name = "站点类型")
    private String staType;

    /** 承建单位 */
    @ApiModelProperty(value = "承建单位")
    @Excel(name = "承建单位")
    private String construction;

    /** 故障类型 */
    @ApiModelProperty(value = "故障类型")
    @Excel(name = "故障类型")
    private String alarmType;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "处理步骤")
    private String step;

}
