package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-视频站点对象 opo_video_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-视频站点")
@Accessors(chain = true)
public class OpoHKWSVideoStateVo
{

    /** 站码 */
    @ApiModelProperty(value = "站码")
    private String cd;

    @ApiModelProperty(value = "站名")
    private String nm;

    /** 链接" */
    @ApiModelProperty(value = "视频编码")
    private String cameraCode;






}
