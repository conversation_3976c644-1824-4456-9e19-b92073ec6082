package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("供需水分析")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateFlowAnalyseVo {

    @ApiModelProperty(value = "供水")
    private String flowAccum;

    @ApiModelProperty(value = "需水")
    private String flowPlan;

    @ApiModelProperty(value = "时间")
    private String addTime;







}
