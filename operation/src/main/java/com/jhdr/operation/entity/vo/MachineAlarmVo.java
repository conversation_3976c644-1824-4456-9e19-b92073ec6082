package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 机房对象 opo_machine_room
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "设备故障数据")
@Accessors(chain = true)
public class MachineAlarmVo {


    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    @ApiModelProperty(value = "所在机柜")
    private String cabinetName;

    @ApiModelProperty(value = "异常类型")
    private String alarmType;


}
