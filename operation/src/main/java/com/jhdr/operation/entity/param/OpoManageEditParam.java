package com.jhdr.operation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务管理对象 opo_manage
 *
 * <AUTHOR>
 * @date 2024-07-05
 */

@Data
@ApiModel(description = "业务管理")
@Accessors(chain = true)
public class OpoManageEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "id")
    @TableId
    private Integer id;

    /** 业务名称 */
    @ApiModelProperty(value = "业务名称")
    @Excel(name = "业务名称")
    private String name;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /** 内容 */
    @ApiModelProperty(value = "内容")
    @Excel(name = "内容")
    private String content;

}
