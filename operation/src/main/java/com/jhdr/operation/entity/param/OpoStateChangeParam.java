package com.jhdr.operation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务管理对象 opo_manage
 *
 * <AUTHOR>
 * @date 2024-07-05
 */

@Data
@ApiModel(description = "业务修改状态")
@Accessors(chain = true)
public class OpoStateChangeParam
{

    @ApiModelProperty(value = "业务名称")
    private String name;

    @ApiModelProperty(value = "状态")
    private String status;

}
