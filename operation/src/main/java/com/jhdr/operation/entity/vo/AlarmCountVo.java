package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("故障数量数据")
@AllArgsConstructor
@NoArgsConstructor
public class AlarmCountVo {

    @ApiModelProperty(value = "数量")
    private Integer amount;

    @ApiModelProperty(value = "数据时间")
    private String addTime;
}
