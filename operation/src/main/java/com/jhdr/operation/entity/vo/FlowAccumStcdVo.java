package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("水位数据")
@AllArgsConstructor
@NoArgsConstructor
public class FlowAccumStcdVo {

    @ApiModelProperty(value = "金海编码")
    private String jhCode;

    @ApiModelProperty("南瑞编码")
    private String nrcode;

}
