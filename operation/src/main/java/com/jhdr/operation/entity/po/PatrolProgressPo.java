package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


@Data
@ApiModel(description = "巡检进度")
@Accessors(chain = true)
@TableName(value ="opo_patrol_progress")
@AllArgsConstructor
@NoArgsConstructor
public class PatrolProgressPo implements Serializable
{

    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "进度")
    private String progress;



    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;



}
