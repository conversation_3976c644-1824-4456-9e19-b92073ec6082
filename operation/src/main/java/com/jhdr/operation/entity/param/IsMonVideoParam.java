package com.jhdr.operation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@ApiModel(description = "拆除功能入参")
@Accessors(chain = true)
public class IsMonVideoParam
{
    /** id */
    @ApiModelProperty(value = "cd")
    private String cd;

    @ApiModelProperty(value = "0不监测1监测")
    private Integer isMon;


}
