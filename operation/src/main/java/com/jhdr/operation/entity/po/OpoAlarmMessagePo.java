package com.jhdr.operation.entity.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 报警信息对象 opo_alarm_message
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "报警信息")
@Accessors(chain = true)
@TableName(value ="opo_alarm_message")
@AllArgsConstructor
@NoArgsConstructor
public class OpoAlarmMessagePo {

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 故障编码 */
    @ApiModelProperty(value = "故障编码")
    @Excel(name = "故障编码")
    private String alarmCode;

    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String staName;

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String staCode;

    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;

    /** 机码 */
    @ApiModelProperty(value = "机码")
    @Excel(name = "机码")
    private String deviceCode;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** 站点类型 */
    @ApiModelProperty(value = "站点类型")
    @Excel(name = "站点类型")
    private String staType;

    /** 建设单位 */
    @ApiModelProperty(value = "建设单位")
    @Excel(name = "建设单位")
    private String construction;

    /** 故障等级 */
    @ApiModelProperty(value = "故障等级")
    @Excel(name = "故障等级")
    private String alarmRank;

    /** 故障类型 */
    @ApiModelProperty(value = "故障类型")
    @Excel(name = "故障类型")
    private String alarmType;

    /** 故障时间 */
    @ApiModelProperty(value = "故障时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "故障时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 故障状态 */
    @ApiModelProperty(value = "故障状态")
    @Excel(name = "故障状态")
    private String step;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "处理人")
    @Excel(name = "处理人")
    private String person;

    @ApiModelProperty(value = "处理详情")
    @Excel(name = "处理详情")
    private String result;

    /** 处理时间 */
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "恢复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "恢复时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date regainTime;

}
