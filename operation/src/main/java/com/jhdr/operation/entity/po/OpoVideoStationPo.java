package com.jhdr.operation.entity.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-视频站点对象 opo_video_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-视频站点")
@Accessors(chain = true)
@TableName(value ="opo_video_station")
public class OpoVideoStationPo
{

    /** 站码 */
    @ApiModelProperty(value = "视频编码")
    @Excel(name = "视频编码")
    private String code;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

}
