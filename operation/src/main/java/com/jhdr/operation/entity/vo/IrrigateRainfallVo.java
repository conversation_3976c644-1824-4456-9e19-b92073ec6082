package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("降雨量统计")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateRainfallVo {

    @ApiModelProperty(value = "降雨量")
    private String rainfall;

    @ApiModelProperty(value = "平均降雨量")
    private String avgRain;

    @ApiModelProperty(value = "时间")
    private String addTime;







}
