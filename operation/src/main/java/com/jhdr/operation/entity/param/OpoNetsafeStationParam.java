package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-网络安全设备对象 opo_netsafe_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-网络安全设备")
@Accessors(chain = true)
public class OpoNetsafeStationParam {

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String deviceName;


    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;


    /** 设备状态 */
    @ApiModelProperty(value = "设备状态")
    @Excel(name = "设备状态")
    private String deviceState;

    /** 告警状态 */
    @ApiModelProperty(value = "告警状态")
    @Excel(name = "告警状态")
    private String netState;


}
