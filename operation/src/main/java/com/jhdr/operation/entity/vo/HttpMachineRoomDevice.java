package com.jhdr.operation.entity.vo;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 获取农业四情设备实时数据
 * @date 2024/5/16 18:00
 */
@Data
public class HttpMachineRoomDevice {

    //编号，表达父子节点关系
    private int id;
    //与id对应，表示为id的下一层区域或设备
    private int parentID;
    //当值为 -1 表示当前条记录是机房信息
    private int subClass;
    //设备地址及端口号
    private String ip;
    //区域名称、机房名称或设备名称
    private String name;
    //设备在线状态，1在线，0断线
    private int netStatus;
    //设备类型，类型编码说明：基本采集设备为3,
    // 视频2.3：4，视频3.0：5，中控门禁6，报警设备7，
    // UPS 8，空调 9，配电柜 10，其他 11,温湿度设备 14，
    // 微耕门禁 15，基本采集设备II16，485通用设备17,机柜温湿度 18，
    // 冷通道温湿度 19，热通道温湿度 20，水温压力检测 21。值为1
    // 表示为区域或机房类型，其中机房判断条件为SubClass=-1
    private String subtype;
}
