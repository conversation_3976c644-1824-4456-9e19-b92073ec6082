package com.jhdr.operation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维机房设备信息api对象 opo_machine_room_device
 *
 * <AUTHOR>
 * @date 2024-07-10
 */

@Data
@ApiModel(description = "运维机房设备信息api")
@Accessors(chain = true)
public class OpoMachineRoomDeviceEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 编号，表达父子节点关系 */
    @ApiModelProperty(value = "编号，表达父子节点关系")
    @TableId
    private Long id;

    /** 与id对应，表示为id的下一层区域或设备 */
    @ApiModelProperty(value = "与id对应，表示为id的下一层区域或设备")
    @Excel(name = "与id对应，表示为id的下一层区域或设备")
    private Long parentId;

    /** 当值为 -1 表示当前条记录是机房信息 */
    @ApiModelProperty(value = "当值为 -1 表示当前条记录是机房信息")
    @Excel(name = "当值为 -1 表示当前条记录是机房信息")
    private Long subClass;

    /** 设备地址及端口号 */
    @ApiModelProperty(value = "设备地址及端口号")
    @Excel(name = "设备地址及端口号")
    private String ip;

    /** 区域名称、机房名称或设备名称 */
    @ApiModelProperty(value = "区域名称、机房名称或设备名称")
    @Excel(name = "区域名称、机房名称或设备名称")
    private String name;

    /** 设备在线状态，1在线，0断线 */
    @ApiModelProperty(value = "设备在线状态，1在线，0断线")
    @Excel(name = "设备在线状态，1在线，0断线")
    private Long netStatus;

    /** 设备类型，类型编码说明：基本采集设备为3, 视频2.3：4，视频3.0：5，中控门禁6，报警设备7，UPS 8，空调 9，配电柜 10，其他 11,温湿度设备 14，微耕门禁 15，基本采集设备II16，485通用设备17,机柜温湿度 18，冷通道温湿度 19，热通道温湿度 20，水温压力检测 21。值为1  表示为区域或机房类型，其中机房判断条件为SubClass=-1 */
    @ApiModelProperty(value = "设备类型，类型编码说明：基本采集设备为3, 视频2.3：4，视频3.0：5，中控门禁6，报警设备7，UPS 8，空调 9，配电柜 10，其他 11,温湿度设备 14，微耕门禁 15，基本采集设备II16，485通用设备17,机柜温湿度 18，冷通道温湿度 19，热通道温湿度 20，水温压力检测 21。值为1  表示为区域或机房类型，其中机房判断条件为SubClass=-1")
    @Excel(name = "设备类型，类型编码说明：基本采集设备为3, 视频2.3：4，视频3.0：5，中控门禁6，报警设备7，UPS 8，空调 9，配电柜 10，其他 11,温湿度设备 14，微耕门禁 15，基本采集设备II16，485通用设备17,机柜温湿度 18，冷通道温湿度 19，热通道温湿度 20，水温压力检测 21。值为1  表示为区域或机房类型，其中机房判断条件为SubClass=-1")
    private Long subtype;

}
