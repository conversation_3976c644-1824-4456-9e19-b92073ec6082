package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 机房告警记录对象 opo_alarm_record
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "机房告警记录")
@Accessors(chain = true)
public class OpoAlarmRecordEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Long id;

    /** 故障编码 */
    @ApiModelProperty(value = "故障编码")
    @Excel(name = "故障编码")
    private String alarmCode;

    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;

    /** 机码 */
    @ApiModelProperty(value = "机码")
    @Excel(name = "机码")
    private String deviceCode;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 故障状态 */
    @ApiModelProperty(value = "故障状态")
    @Excel(name = "故障状态")
    private String alarmState;

    /** 告警内容 */
    @ApiModelProperty(value = "告警内容")
    @Excel(name = "告警内容")
    private String content;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Long isDelete;

    /** 位置 */
    @ApiModelProperty(value = "位置")
    @Excel(name = "位置")
    private String site;

    @ApiModelProperty(value = "处理人")
    @Excel(name = "处理人")
    private String person;

    @ApiModelProperty(value = "处理详情")
    @Excel(name = "处理详情")
    private String result;

    /** 处理时间 */
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
