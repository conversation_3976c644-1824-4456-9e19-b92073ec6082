package com.jhdr.operation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;



@Data
@ApiModel(description = "测站（水位，）层级")
@Accessors(chain = true)
public class WaterRegionVo implements Serializable {



    @ApiModelProperty(value = "当前区域编码")
    private String stcd;

    @ApiModelProperty(value = "名称")
    private String stnm;

    @ApiModelProperty(value = "父区域编码")
    private String addvcd;

    @ApiModelProperty("子集菜单")
    private List<WaterRegionVo> children;

}
