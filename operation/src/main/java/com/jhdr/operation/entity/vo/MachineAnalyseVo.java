package com.jhdr.operation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 机房对象 opo_machine_room
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "机房")
@Accessors(chain = true)
public class MachineAnalyseVo{


    /** 温度 */
    @ApiModelProperty(value = "服务器百分比")
    private String fwqPercent;

    /** 湿度 */
    @ApiModelProperty(value = "网络安全设备百分比")
    private String safePercent;

    /** 空调开关 */
    @ApiModelProperty(value = "网络设备百分比")
    private String netPercent;

    /** 温度 */
    @ApiModelProperty(value = "服务器数量")
    private Integer fwqCount;

    /** 湿度 */
    @ApiModelProperty(value = "网络安全设备数量")
    private Integer safeCount;

    /** 空调开关 */
    @ApiModelProperty(value = "网络设备数量")
    private Integer netCount;


}
