package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 异地会商详情对象 remote_chat_detail
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@Data
@ApiModel(description = "异地会商详情")
@Accessors(chain = true)
public class RemoteChatDetailAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Integer id;

    /** 会商id */
    @ApiModelProperty(value = "会商id")
    @Excel(name = "会商id")
    private Integer chatId;

    /** 角色 */
    @ApiModelProperty(value = "角色")
    @Excel(name = "角色")
    private String roleName;

    /** 参与人id */
    @ApiModelProperty(value = "参与人id")
    @Excel(name = "参与人id")
    private Integer personId;

    /** 参与人 */
    @ApiModelProperty(value = "参与人")
    @Excel(name = "参与人")
    private String person;

    @ApiModelProperty(value = "联系方式")
    @Excel(name = "联系方式")
    private String phone;

    /** 发起时间 */
    @ApiModelProperty(value = "发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发起时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 内容 */
    @ApiModelProperty(value = "内容")
    @Excel(name = "内容")
    private String content;

    /** 状态(0开始会商;1结束会商;2会商动态) */
    @ApiModelProperty(value = "状态(0开始会商;1结束会商;2会商动态)")
    @Excel(name = "状态(0开始会商;1结束会商;2会商动态)")
    private Integer status;

    /** 附件路径 */
    @ApiModelProperty(value = "附件路径")
    @Excel(name = "附件路径")
    private String filePath;

    /** 附件名称 */
    @ApiModelProperty(value = "附件名称")
    @Excel(name = "附件名称")
    private String fileName;

}
