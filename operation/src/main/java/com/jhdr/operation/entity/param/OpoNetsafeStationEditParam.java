package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-网络安全设备对象 opo_netsafe_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-网络安全设备")
@Accessors(chain = true)
public class OpoNetsafeStationEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Long id;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String deviceName;

    /** 品牌型号 */
    @ApiModelProperty(value = "品牌型号")
    @Excel(name = "品牌型号")
    private String deviceModel;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** ip地址 */
    @ApiModelProperty(value = "ip地址")
    @Excel(name = "ip地址")
    private String ip;

    @ApiModelProperty(value = "网关")
    @Excel(name = "网关")
    private String gateway;

    /** 子网掩码 */
    @ApiModelProperty(value = "子网掩码")
    @Excel(name = "子网掩码")
    private String mask;

    /** MAC地址 */
    @ApiModelProperty(value = "dns")
    @Excel(name = "dns")
    private String dns;

    /** MAC地址 */
    @ApiModelProperty(value = "MAC地址")
    @Excel(name = "MAC地址")
    private String mac;

    /** 设备位置 */
    @ApiModelProperty(value = "设备位置")
    @Excel(name = "设备位置")
    private String site;

    /** 设备状态 */
    @ApiModelProperty(value = "设备状态")
    @Excel(name = "设备状态")
    private String deviceState;

    /** 告警状态 */
    @ApiModelProperty(value = "告警状态")
    @Excel(name = "告警状态")
    private String netState;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    private String alarmTime;

    /** 最后更新时间 */
    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /** 机柜名称 */
    @ApiModelProperty(value = "机柜名称")
    @Excel(name = "机柜名称")
    private String cabinetName;

    /** 是否监测 */
    @ApiModelProperty(value = "是否监测 0否；1是")
    private Integer isMon;

}
