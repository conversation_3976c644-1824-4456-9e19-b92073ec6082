package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("水量表")
@TableName("jh_flowsp_r")
@AllArgsConstructor
@NoArgsConstructor
public class JhFlowspRPo {
    @ApiModelProperty(value = "遥测站编码（设备）")
    private String stcd;

    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    @ApiModelProperty(value = "类型")

    private String qtype;

    @ApiModelProperty(value = "水量")
    private Double psq;








}
