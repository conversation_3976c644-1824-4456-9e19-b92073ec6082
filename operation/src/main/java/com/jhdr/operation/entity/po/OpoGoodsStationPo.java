package com.jhdr.operation.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 物资点对象 opo_goods_station
 *
 * <AUTHOR>
 * @date 2024-09-24
 */

@Data
@ApiModel(description = "物资点")
@Accessors(chain = true)
@TableName(value ="opo_goods_station")
public class OpoGoodsStationPo implements Serializable
{


    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 名称 */
    @ApiModelProperty(value = "名称")
    @Excel(name = "名称")
    private String name;

    /** 编码 */
    @ApiModelProperty(value = "编码")
    @Excel(name = "编码")
    private String code;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    @Excel(name = "经度")
    private String longitude;

    /** 维度 */
    @ApiModelProperty(value = "维度")
    @Excel(name = "维度")
    private String latitude;

    /** 负责人 */
    @ApiModelProperty(value = "负责人")
    @Excel(name = "负责人")
    private String person;

    /** 电话 */
    @ApiModelProperty(value = "电话")
    @Excel(name = "电话")
    private String phone;

    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String admauth;

    /** 图片 */
    @ApiModelProperty(value = "图片")
    @Excel(name = "图片")
    private String path;

    /** 地址 */
    @ApiModelProperty(value = "地址")
    @Excel(name = "地址")
    private String site;

    /** 简介 */
    @ApiModelProperty(value = "简介")
    @Excel(name = "简介")
    private String content;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

}
