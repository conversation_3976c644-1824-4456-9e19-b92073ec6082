package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("工情状态数据")
@AllArgsConstructor
@NoArgsConstructor
public class MachineStateVo {

    @ApiModelProperty(value = "站码")
    private String strCode;


    @ApiModelProperty(value = "站名")
    private String strName;

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 承建单位 */
    @ApiModelProperty(value = "承建单位")
    @Excel(name = "承建单位")
    private String construction;


    @ApiModelProperty(value = "故障Id")
    private Integer alarmId;




}
