package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 监控基本数据 opo_alarm_message
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "监控基本数据")
@Accessors(chain = true)
@TableName(value ="opo_jhom_vm")
@AllArgsConstructor
@NoArgsConstructor
public class OpoJhomVmPo {

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 故障编码 */
    @ApiModelProperty(value = "监控点唯一标识")
    private String cameraIndexCode;

    /** 站名 */
    @ApiModelProperty(value = "监控点名称")
    private String cameraName;

    /** 站码 */
    @ApiModelProperty(value = "监控点类型")
    private String cameraType;

    /** 机名 */
    @ApiModelProperty(value = "通道编号")
    private String channelNo;

    /** 机码 */
    @ApiModelProperty(value = "通道类型")
    private String channelType;

    /** 设备类型 */
    @ApiModelProperty(value = "所属区域唯一标识")
    private String regionIndexCode;

    /** 站点类型 */
    @ApiModelProperty(value = "所属编码设备唯一标识")
    private String encodeDevIndexCode;

    /** 建设单位 */
    @ApiModelProperty(value = "经度")
    private String longitude;

    /** 故障等级 */
    @ApiModelProperty(value = "纬度")
    private String latitude;





}
