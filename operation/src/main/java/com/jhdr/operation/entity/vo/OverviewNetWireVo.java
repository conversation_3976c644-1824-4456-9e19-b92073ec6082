package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@ApiModel(description = "网络拓扑图")
@Accessors(chain = true)
public class OverviewNetWireVo {

    @ApiModelProperty(value = "泵站控制到电信设备1")
    private String state1a1 ="正常";

    @ApiModelProperty(value = "泵站量测到电信设备1")
    private String state1a2 ="正常";

    @ApiModelProperty(value = "枢纽到电信设备1")
    private String state1a3 ="正常";


    @ApiModelProperty(value = "电信设备1到隔离网闸2")
    private String state2 ="正常";

    @ApiModelProperty(value = "隔离网闸2到48口核心交换机")
    private String state3 ="正常";

    @ApiModelProperty(value = "节制闸控制到隔离网闸1")
    private String state4 ="正常";

    @ApiModelProperty(value = "隔离网闸1到控制区48口交换机1")
    private String state5 ="正常";


    @ApiModelProperty(value = "闸泵站通讯服务器到控制区48口交换机1")
    private String state6a1 ="正常";

    @ApiModelProperty(value = "视频服务平台到控制区48口交换机1")
    private String state6a2 ="正常";

    @ApiModelProperty(value = "通讯前置机到控制区48口交换机1")
    private String state6a3 ="正常";

    @ApiModelProperty(value = "控制区48口交换机2到控制区48口交换机1")
    private String state6a4 ="正常";



    @ApiModelProperty(value = "控制区48口交换机1到48口核心交换机")
    private String state7 ="正常";

    @ApiModelProperty(value = "48口核心交换机到隔离网闸3")
    private String state8 ="正常";

    @ApiModelProperty(value = "控制区48口交换机1到隔离网闸3")
    private String state9 ="正常";



    @ApiModelProperty(value = "摄像头1到16口交换机")
    private String state10a1 ="正常";

    @ApiModelProperty(value = "16口交换机到上桥抽水站水利专用交换机")
    private String state10a2 ="正常";



    @ApiModelProperty(value = "省水利厅到电信设备2")
    private String state11a1 ="正常";

    @ApiModelProperty(value = "电信设备2到华为AR-3200路由")
    private String state11a2 ="正常";

    @ApiModelProperty(value = "华为AR-3200路由到防火墙")
    private String state11a3 ="正常";

    @ApiModelProperty(value = "防火墙到入侵防御")
    private String state11a4 ="正常";

    @ApiModelProperty(value = "入侵防御到防毒墙")
    private String state11a5 ="正常";

    @ApiModelProperty(value = "防毒墙到上桥三楼机房水利专网交换机")
    private String state11a6 ="正常";

    @ApiModelProperty(value = "上桥三楼机房水利专网交换机到业务区48口交换机1")
    private String state11a7 ="正常";


    @ApiModelProperty(value = "感知探针1到业务区48口交换机1")
    private String state12a1 ="正常";

    @ApiModelProperty(value = "waf到业务区48口交换机1")
    private String state12a2 ="正常";



    @ApiModelProperty(value = "业务交换机1到业务交换机2")
    private String state13a1 ="正常";

    @ApiModelProperty(value = "业务交换机1到业务区48口交换机1")
    private String state13a2 ="正常";

    @ApiModelProperty(value = "原业务区服务器到业务交换机1")
    private String state13a3 ="正常";

    @ApiModelProperty(value = "上桥节制闸水情服务器到业务交换机1")
    private String state13a4 ="正常";



    @ApiModelProperty(value = "管理交换机1到管理交换机2")
    private String state14a1 ="正常";

    @ApiModelProperty(value = "管理交换机1到业务区48口交换机1")
    private String state14a2 ="正常";

    @ApiModelProperty(value = "安全管理区到管理交换机1")
    private String state14a3 ="正常";



    @ApiModelProperty(value = "存储交换机1到存储交换机2")
    private String state15a1 ="正常";

    @ApiModelProperty(value = "存储交换机1到业务区48口交换机1")
    private String state15a2 ="正常";

    @ApiModelProperty(value = "新增存储区服务器到存储交换机1")
    private String state15a3 ="正常";



    @ApiModelProperty(value = "新建6路监控到门卫室16口交换机")
    private String state16a1 ="正常";

    @ApiModelProperty(value = "门卫室原有硬盘录像机到门卫室16口交换机")
    private String state16a2 ="正常";

    @ApiModelProperty(value = "门卫室原有硬盘录像机到门卫室16口交换机")
    private String state16a3 ="正常";



    @ApiModelProperty(value = "上桥食堂监控交换机到上桥食堂硬盘录像机")
    private String state17a1 ="正常";

    @ApiModelProperty(value = "上桥食堂硬盘录像机到业务区48口交换机1")
    private String state17a2 ="正常";


    @ApiModelProperty(value = "调度中心水利专网工作站区到业务区48口交换机1")
    private String state18a1 ="正常";

    @ApiModelProperty(value = "业务区48口交换机1到业务区48口交换机2")
    private String state18a2 ="正常";

    @ApiModelProperty(value = "业务区48口交换机1到隔离网闸4")
    private String state18a3 ="正常";



    @ApiModelProperty(value = "隔离网闸4到互联网区48口交换机")
    private String state19 ="正常";



    @ApiModelProperty(value = "泵站采集到云")
    private String state20a1 ="正常";

    @ApiModelProperty(value = "泵站摄像头到云")
    private String state20a2 ="正常";

    @ApiModelProperty(value = "云到电信设备3")
    private String state20a3 ="正常";

    @ApiModelProperty(value = "电信设备3到出口防火墙")
    private String state20a4 ="正常";

    @ApiModelProperty(value = "出口防火墙到互联网区48口交换机")
    private String state20a5 ="正常";



    @ApiModelProperty(value = "感知探针2到互联网区48口交换机")
    private String state21 ="正常";


    @ApiModelProperty(value = "无线接入区到上网行为管理")
    private String state22a1 ="正常";

    @ApiModelProperty(value = "上网行为管理到互联网区48口交换机")
    private String state22a2 ="正常";


    @ApiModelProperty(value = "互联网办公终端区到互联网区48口交换机")
    private String state23 ="正常";


    @ApiModelProperty(value = "上桥食堂互联网到互联网区48口交换机")
    private String state24 ="正常";


    @ApiModelProperty(value = "16口楼层交换机1到互联网区48口交换机")
    private String state25a1 ="正常";

    @ApiModelProperty(value = "16口楼层交换机2到16口楼层交换机1")
    private String state25a2 ="正常";

    @ApiModelProperty(value = "楼层互联网电脑到16口楼层交换机1")
    private String state25a3 ="正常";

    @ApiModelProperty(value = "调度中心互联网电脑到16口楼层交换机1")
    private String state25a4 ="正常";

    @ApiModelProperty(value = "vpn到16口楼层交换机")
    private String state25a5 ="正常";



    @ApiModelProperty(value = "蚌埠基地水利专网交换机到上桥三楼机房水利专网交换机")
    private String state26a1 ="正常";

    @ApiModelProperty(value = "进洪闸水利专网交换机到上桥三楼机房水利专网交换机")
    private String state26a2 ="正常";

    @ApiModelProperty(value = "上桥船闸、上桥节制闸专网交换机到上桥三楼机房水利专网交换机")
    private String state26a3 ="正常";

    @ApiModelProperty(value = "量测水硬盘录像机*2到上桥抽水站水利专用交换机")
    private String state26a4 ="正常";

    @ApiModelProperty(value = "上桥抽水站水利专用交换机到上桥三楼机房水利专网交换机")
    private String state26a5 ="正常";














}
