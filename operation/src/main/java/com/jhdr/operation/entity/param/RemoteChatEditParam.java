package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 异地会商对象 remote_chat
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@Data
@ApiModel(description = "异地会商")
@Accessors(chain = true)
public class RemoteChatEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Integer id;

    /** 会商编号 */
    @ApiModelProperty(value = "会商编号")
    @Excel(name = "会商编号")
    private String chatCode;

    /** 会商主题 */
    @ApiModelProperty(value = "会商主题")
    @Excel(name = "会商主题")
    private String title;

    /** 摘要 */
    @ApiModelProperty(value = "摘要")
    @Excel(name = "摘要")
    private String digest;

    /** 视频路径 */
    @ApiModelProperty(value = "视频路径")
    @Excel(name = "视频路径")
    private String videoCode;

    /** 视频名称（险情地点） */
    @ApiModelProperty(value = "视频名称（险情地点）")
    @Excel(name = "视频名称", readConverterExp = "险=情地点")
    private String nm;

    /** 角色 */
    @ApiModelProperty(value = "角色")
    @Excel(name = "角色")
    private String roleName;

    /** 发起人id */
    @ApiModelProperty(value = "发起人id")
    @Excel(name = "发起人id")
    private Integer personId;

    /** 发起人 */
    @ApiModelProperty(value = "发起人")
    @Excel(name = "发起人")
    private String person;

    /** 发起时间 */
    @ApiModelProperty(value = "发起时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发起时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date creatTime;

    /** 状态(0进行中;1已结束) */
    @ApiModelProperty(value = "状态(0进行中;1已结束)")
    @Excel(name = "状态(0进行中;1已结束)")
    private Integer status;

}
