package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("服务器状态数据")
@AllArgsConstructor
@NoArgsConstructor
public class ServerStateVo {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String deviceName;

    @ApiModelProperty(value = "磁盘使用率")
    private String cpUse;

    @ApiModelProperty(value = "cpu使用率")
    private String cpuUse;

    @ApiModelProperty(value = "nc使用率")
    private String ncUse;

    @ApiModelProperty(value = "位置")
    private String site;

}
