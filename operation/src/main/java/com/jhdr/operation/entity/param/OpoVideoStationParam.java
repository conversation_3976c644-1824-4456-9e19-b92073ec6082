package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-视频站点对象 opo_video_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-视频站点")
@Accessors(chain = true)
public class OpoVideoStationParam implements Serializable {


    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String nm;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /** 站名 */
    @ApiModelProperty(value = "所属区域编码")
    @Excel(name = "所属区域编码")
    private String addvcd;

    @ApiModelProperty(value = "上级编码")
    @Excel(name = "上级编码")
    private String indexCode;




}
