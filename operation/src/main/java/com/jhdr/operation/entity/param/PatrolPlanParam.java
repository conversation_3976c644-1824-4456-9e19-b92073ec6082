package com.jhdr.operation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 巡检计划对象 patrol_plan
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "巡检计划")
@Accessors(chain = true)
public class PatrolPlanParam implements Serializable {

    /** 计划名称 */
    @ApiModelProperty(value = "计划名称")
    @Excel(name = "计划名称")
    private String patrolName;

    /** 巡检类型 */
    @ApiModelProperty(value = "巡检类型")
    @Excel(name = "巡检类型")
    private String patrolType;





}
