package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@ApiModel(description = "查询运维")
@Accessors(chain = true)
public class FindOverStateVo {

    @ApiModelProperty(value = "类型")
    private String type ;

    @ApiModelProperty(value = "状态")
    private String status ;







}
