package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 机房对象 opo_machine_room
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "机房设备数量")
@Accessors(chain = true)
public class MachineDeviceCountVo {


    /** 机柜数量 */
    @ApiModelProperty(value = "机柜数量")
    private Integer jgCount;

    /** 设备数量 */
    @ApiModelProperty(value = "设备数量")
    private Integer deviceCount;

    /** 数量U位 */
    @ApiModelProperty(value = "数量U位")
    private Integer uCount;


}
