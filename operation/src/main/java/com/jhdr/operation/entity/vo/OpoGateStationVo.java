package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 运维监控-工情涵闸站点对象 opo_gq_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-涵闸")
@Accessors(chain = true)
public class OpoGateStationVo {

    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String strName;

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String strCode;



    /** 站码 */
    @ApiModelProperty(value = "开启数量")
//    @Excel(name = "开启数量")
    private String gtopn;

    /** 站码 */
    @ApiModelProperty(value = "闸门数量")
    @Excel(name = "闸门数量")
    private String gtcn;

    /** 位置 */
    @ApiModelProperty(value = "位置")
    @Excel(name = "位置")
    private String address;

    /** 承建单位 */
    @ApiModelProperty(value = "承建单位")
    @Excel(name = "承建单位")
    private String construction;

    /** 功率 */
    @ApiModelProperty(value = "开度")
//    @Excel(name = "开度")
    private Double gtoph;

    /** 站码 */
    @ApiModelProperty(value = "运行状态")
    @Excel(name = "运行状态")
    private String strState;

    /** 状态 */
    @ApiModelProperty(value = "是否拆除")
    private Integer status;


    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;


    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    private String alarmTime;






//    /** 详情 */
//    @ApiModelProperty(value = "详情")
//    @Excel(name = "详情")
//    private List<OpoGateStationVo> children;

}
