package com.jhdr.operation.entity.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-工情站点对象 opo_gq_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-工情站点")
@Accessors(chain = true)
@TableName(value ="opo_gq_station")
public class OpoGqStationPo implements Serializable
{

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String staName;

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String staCode;

    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;

    /** 机码 */
    @ApiModelProperty(value = "机码")
    @Excel(name = "机码")
    private String deviceCode;

    /** 站点类型(泵站，水闸) */
    @ApiModelProperty(value = "站点类型(泵站，水闸)")
    @Excel(name = "站点类型(泵站，水闸)")
    private String staType;

    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String manageUnit;

    /** 通讯方式 */
    @ApiModelProperty(value = "通讯方式")
    @Excel(name = "通讯方式")
    private String phone;

    /** 工程位置 */
    @ApiModelProperty(value = "工程位置")
    @Excel(name = "工程位置")
    private String staSite;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String staState;



    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

}
