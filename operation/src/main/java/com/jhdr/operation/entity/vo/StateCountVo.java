package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StateCountVo {

    @ApiModelProperty(value = "正常数量")
    private Integer useAmount;

    @ApiModelProperty(value = "异常数量")
    private Integer alarmAmount;
}
