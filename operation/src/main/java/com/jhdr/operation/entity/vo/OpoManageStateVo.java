package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 业务管理对象 opo_manage
 *
 * <AUTHOR>
 * @date 2024-07-05
 */

@Data
@ApiModel(description = "业务管理")
@Accessors(chain = true)
public class OpoManageStateVo
{

    @ApiModelProperty(value = "第一根线")
    private String state1;

    @ApiModelProperty(value = "第二根线")
    private String state2;

    @ApiModelProperty(value = "第三根线")
    private String state3;

    @ApiModelProperty(value = "第四根线")
    private String state4;

    @ApiModelProperty(value = "第五根线")
    private String state5;

    @ApiModelProperty(value = "第六根线")
    private String state6;

    @ApiModelProperty(value = "第七根线")
    private String state7;

    @ApiModelProperty(value = "第八根线")
    private String state8;






}
