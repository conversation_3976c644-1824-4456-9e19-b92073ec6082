package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@ApiModel("水位数据")
@AllArgsConstructor
@NoArgsConstructor
public class OpoSnYhVo {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty("子集菜单")
    private List<OpoSnYhVo> children;

}
