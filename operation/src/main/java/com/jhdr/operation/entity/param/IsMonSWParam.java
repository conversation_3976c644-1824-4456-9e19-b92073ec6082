package com.jhdr.operation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@ApiModel(description = "拆除功能入参")
@Accessors(chain = true)
public class IsMonSWParam
{
    /** id */
    @ApiModelProperty(value = "编号")
    private String stcd;

    @ApiModelProperty(value = "0不监测1监测")
    private Integer isMon;


}
