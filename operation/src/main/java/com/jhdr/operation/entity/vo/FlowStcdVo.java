package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("水位数据")
@AllArgsConstructor
@NoArgsConstructor
public class FlowStcdVo {

    @ApiModelProperty(value = "金海编码")
    private String jhCode;

    @ApiModelProperty("南瑞编码")
    private String nrcode;

    @ApiModelProperty("泵站类型")
    private Integer newType=1;

    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "站点名称")
    private String stationName;

}
