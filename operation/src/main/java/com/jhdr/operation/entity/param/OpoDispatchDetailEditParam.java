package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程调度审批流程对象 opo_dispatch_detail
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "工程调度审批流程")
@Accessors(chain = true)
public class OpoDispatchDetailEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Integer id;

    /** 操作人id */
    @ApiModelProperty(value = "操作人id")
    @Excel(name = "操作人id")
    private String personId;

    /** 操作人 */
    @ApiModelProperty(value = "操作人")
    @Excel(name = "操作人")
    private String person;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

    /** 调度id */
    @ApiModelProperty(value = "调度id")
    @Excel(name = "调度id")
    private Integer taskId;

}
