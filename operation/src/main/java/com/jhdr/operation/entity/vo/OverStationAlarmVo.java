package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("站点概况")
@AllArgsConstructor
@NoArgsConstructor
public class OverStationAlarmVo {

    @ApiModelProperty(value = "类型名称")
    private String TypeName;

    @ApiModelProperty(value = "数量")
    private Integer count;

}
