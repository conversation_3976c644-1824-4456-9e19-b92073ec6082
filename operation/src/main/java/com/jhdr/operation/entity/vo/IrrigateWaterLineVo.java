package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("实时水位")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateWaterLineVo {

    @ApiModelProperty(value = "站名")
    private String strName;

    @ApiModelProperty(value = "水位")
    private String waterLine;

    @ApiModelProperty(value = "排序")
    private Integer rank;









}
