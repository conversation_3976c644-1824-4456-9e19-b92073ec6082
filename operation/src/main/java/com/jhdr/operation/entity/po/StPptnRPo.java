package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("雨量表")
@TableName("ST_PPTN_R")
@AllArgsConstructor
@NoArgsConstructor
public class StPptnRPo {

    @ApiModelProperty(value = "测站编码")
    private String stcd;

    @ApiModelProperty(value = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    @ApiModelProperty(value = "时段降水量")
    private String drp;

    @ApiModelProperty(value = "时段长")
    private String intv;

    @ApiModelProperty(value = "降水历时")
    private String pdr;

    @ApiModelProperty(value = "日降水量")
    private String dyp;

    @ApiModelProperty(value = "天气状况")
    private String wth;
}
