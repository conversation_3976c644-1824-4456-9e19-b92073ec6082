package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description = "泵站层级")
@Accessors(chain = true)
public class PustRegionVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "当前区域编码")
    private String stcd;

    @ApiModelProperty(value = "名称")
    private String stnm;

    @ApiModelProperty(value = "父区域编码")
    private String addvcd;




    @ApiModelProperty("子集菜单")
    private List<PustRegionVo> children;

}
