package com.jhdr.operation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 机房对象 opo_machine_room
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "机房")
@Accessors(chain = true)
public class OpoRoomValueVo {


    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /** 数据 */
    @ApiModelProperty(value = "数据")
    private String value;



}
