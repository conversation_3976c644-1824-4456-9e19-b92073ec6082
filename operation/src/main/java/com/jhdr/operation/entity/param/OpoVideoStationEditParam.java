package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-视频站点对象 opo_video_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-视频站点")
@Accessors(chain = true)
public class OpoVideoStationEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Long id;

    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String staName;

    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String staCode;

    /** 监控设备名称 */
    @ApiModelProperty(value = "监控设备名称")
    @Excel(name = "监控设备名称")
    private String deviceName;

    /** ip地址 */
    @ApiModelProperty(value = "ip地址")
    @Excel(name = "ip地址")
    private String ip;

    /** 通道号 */
    @ApiModelProperty(value = "通道号")
    @Excel(name = "通道号")
    private String channelId;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String staState;



    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;

}
