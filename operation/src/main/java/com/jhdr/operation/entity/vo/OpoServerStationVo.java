package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-服务器管理对象 opo_fwq_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-服务器管理")
@Accessors(chain = true)
public class OpoServerStationVo {

    @ApiModelProperty(value = "id")
    @TableId
    private Integer id;

    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String deviceName;

    /** 品牌型号 */
    @ApiModelProperty(value = "品牌型号")
    @Excel(name = "品牌")
    private String deviceModel;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** 操作系统 */
    @ApiModelProperty(value = "操作系统")
    @Excel(name = "操作系统")
    private String osName;

    /** IP地址 */
    @ApiModelProperty(value = "IP地址")
    @Excel(name = "ip地址")
    private String ip;

    /** 网关 */
    @ApiModelProperty(value = "网关")
//    @Excel(name = "网关")
    private String gateway;

    /** 网关 */
    @ApiModelProperty(value = "子网掩码")
//    @Excel(name = "子网掩码")
    private String mask;

    /** 磁盘使用率 */
    @ApiModelProperty(value = "磁盘使用率")
    @Excel(name = "磁盘使用率")
    private String cpUse;

    /** cpu大小 */
    @ApiModelProperty(value = "cpu利用率")
    @Excel(name = "cpu利用率")
    private String cpuUse;

    /** 内存大小 */
    @ApiModelProperty(value = "内存利用率")
    @Excel(name = "内存利用率")
    private String ncUse;

    /** 交换区使用率 */
    @ApiModelProperty(value = "交换区使用率")
    @Excel(name = "交换区使用率")
    private String jhqUse;

    /** 运行状态 */
    @ApiModelProperty(value = "运行状态")
    @Excel(name = "运行状态")
    private String runState;



    /** 更新时间 */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
//    @Excel(name = "告警时间")
    private String alarmTime;


    /** 端口数量 */
    @ApiModelProperty(value = "端口数量")
//    @Excel(name = "端口数量")
    private Integer portSize;

    /** 机柜名称 */
    @ApiModelProperty(value = "机柜名称")
    @Excel(name = "机柜名称")
    private String cabinetName;

    /** 是否监测 */
    @ApiModelProperty(value = "是否监测 0否；1是")
    private Integer isMon;



}
