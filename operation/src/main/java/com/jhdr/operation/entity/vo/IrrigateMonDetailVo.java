package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel("监测信息详情")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateMonDetailVo {

    @ApiModelProperty(value = "泵站编码")
    private String strCode;

    @ApiModelProperty(value = "泵站名称")
    private String strName;

    @ApiModelProperty(value = "站上水位")
    private Double ppupz;

    @ApiModelProperty(value = "实时流量")
    private Double pmpq;

    @ApiModelProperty(value = "年取水量")
    private Double flowAccum;

    @ApiModelProperty(value = "图片路径")
    private String url;

    @ApiModelProperty(value = "泵机状态")
    private List<IrrigatePumpDetailVo> status;





}
