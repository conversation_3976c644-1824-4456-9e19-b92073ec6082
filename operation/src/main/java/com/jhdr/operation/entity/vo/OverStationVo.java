package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("站点概况")
@AllArgsConstructor
@NoArgsConstructor
public class OverStationVo {

    @ApiModelProperty(value = "类型名称")
    private String TypeName;

    @ApiModelProperty(value = "正常数量")
    private Integer useCount;

    @ApiModelProperty(value = "异常数量")
    private Integer alarmCount;

    @ApiModelProperty(value = "总数量")
    private Integer allCount;
}
