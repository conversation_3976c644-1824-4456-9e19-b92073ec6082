package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("泵站闸站历史年报表")
@TableName("st_station_water_year")
@AllArgsConstructor
@NoArgsConstructor
public class StStationWaterYearPo {

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Integer id;



    @ApiModelProperty(value = "站点名称")
    private String stationName;

    @ApiModelProperty(value = "站点编码")
    private String stationCode;



    @ApiModelProperty(value = "站点类型 1泵 2闸")
    private Integer stationType;



    @ApiModelProperty(value = "站点年份")
    private String stationYear;



    @ApiModelProperty(value = "抗旱抽水量m³")
    private Double droughtWaterExtraction;

    @ApiModelProperty(value = "排涝抽水量m³")
    private Double floodWaterExtraction;

    @ApiModelProperty(value = "统计类型 1年度 2月份")
    private Integer statisticType;

    @ApiModelProperty(value = "站下水势")
    private Integer stationMonth;










}
