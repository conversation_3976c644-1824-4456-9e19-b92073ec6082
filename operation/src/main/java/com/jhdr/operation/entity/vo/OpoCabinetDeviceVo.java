package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务管理对象 opo_manage
 *
 * <AUTHOR>
 * @date 2024-07-05
 */

@Data
@ApiModel(description = "机柜设备数据")
@Accessors(chain = true)
public class OpoCabinetDeviceVo {


    /** 设备名称 */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;

    /** 类别 */
    @ApiModelProperty(value = "类别")
    private String deviceType;

    /** 类别 */
    @ApiModelProperty(value = "类别")
    private String deviceState;

    /** 品牌型号 */
    @ApiModelProperty(value = "品牌型号")
    private String deviceModel;

    /** 设备数量 */
    @ApiModelProperty(value = "ip")
    private String ip;

    /** 排序 */
    @ApiModelProperty(value = "排序")
    private String rank;

    /** 排序 */
    @ApiModelProperty(value = "U位")
    private String uSite;



}
