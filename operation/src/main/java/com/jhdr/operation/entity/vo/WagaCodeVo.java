package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("涵闸采集")
@AllArgsConstructor
@NoArgsConstructor
public class WagaCodeVo {

    @ApiModelProperty(value = "金海涵闸编码")
    private String strCode;

    @ApiModelProperty(value = "计算流量")
    private Double calculate;

    @ApiModelProperty(value = "闸门数量")
    private Integer gaorNum;

    @ApiModelProperty(value = "水位南瑞编码")
    private String waterCode;

}
