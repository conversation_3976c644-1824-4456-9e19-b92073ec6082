package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 巡检任务对象 patrol_task
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "巡检结果")
@Accessors(chain = true)
public class PatrolResultVo {

    /** 计划名称 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String staName;

    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;

    /** 内容 */
    @ApiModelProperty(value = "内容")
    @Excel(name = "内容")
    private String content;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status="正常";

}
