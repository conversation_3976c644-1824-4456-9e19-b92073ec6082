package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信平台对象 opo_message_send
 *
 * <AUTHOR>
 * @date 2024-08-30
 */

@Data
@ApiModel(description = "短信平台")
@Accessors(chain = true)
public class OpoNoSendVo {


    /** 发送人 */
    @ApiModelProperty(value = "发送人")
    @Excel(name = "发送人")
    private String people;

    /** 内容 */
    @ApiModelProperty(value = "内容")
    @Excel(name = "内容")
    private String content;

    /** 接收人 */
    @ApiModelProperty(value = "接收人")
    @Excel(name = "接收人")
    private String person;

    /** 接收人号码 */
    @ApiModelProperty(value = "接收人号码")
    @Excel(name = "接收人号码")
    private String phone;



}
