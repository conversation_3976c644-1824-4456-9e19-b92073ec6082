package com.jhdr.operation.entity.param;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 机房告警记录对象 opo_alarm_record
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "机房告警记录")
@Accessors(chain = true)
public class StrAlarmRecordParam implements Serializable {

    /** 告警类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** 告警类型 */
    @ApiModelProperty(value = "设备名称")
    @Excel(name = "设备名称")
    private String deviceName;

    /** 告警时间 */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /** 告警时间 */
    @ApiModelProperty(value = "结束时间")
    private String endTime;






}
