package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 巡检任务对象 patrol_task
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "巡检任务")
@Accessors(chain = true)
public class PatrolTaskVo {

    @TableId
    private Integer id;

    /** 计划编号 */
    @ApiModelProperty(value = "计划id")
    private Integer patrolId;


    /** 计划编号 */
    @ApiModelProperty(value = "计划编号")
    @Excel(name = "计划编号")
    private String patrolCode;

    /** 计划名称 */
    @ApiModelProperty(value = "计划名称")
    @Excel(name = "计划名称")
    private String patrolName;

    /** 巡检类型 */
    @ApiModelProperty(value = "巡检类型")
    @Excel(name = "巡检类型")
    private String patrolType;

    /** 巡检范围 */
    @ApiModelProperty(value = "巡检范围")
    @Excel(name = "巡检范围")
    private String patrolScope;



    /** 巡检频率 */
    @ApiModelProperty(value = "巡检频率")
    @Excel(name = "巡检频率")
    private String cycle;

    /** 巡检人 */
    @ApiModelProperty(value = "巡检人")
    @Excel(name = "巡检人")
    private String person;



    /** 计划巡检日期 */
    @ApiModelProperty(value = "计划巡检日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "计划巡检日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date planTime;

    /** 实际巡检时间 */
    @ApiModelProperty(value = "实际巡检时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际巡检时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date patrolTime;

    /** 巡检内容 */
    @ApiModelProperty(value = "巡检内容")
    @Excel(name = "巡检内容")
    private String content;

    /** 巡检结果 */
    @ApiModelProperty(value = "巡检结果")
//    @Excel(name = "巡检结果")
    private String result;

    /** 巡检状态0未巡检1已巡检 */
    @ApiModelProperty(value = "巡检状态0未巡检1已巡检")
//    @Excel(name = "巡检状态0未巡检1已巡检")
    private Long state;


    @ApiModelProperty(value = "通知对象")
//    @Excel(name = "通知对象")
    private String notify;


    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;


}
