package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 机房对象 opo_machine_room
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "机房设备状态数量")
@Accessors(chain = true)
public class MachineStateCountVo {


    /** 温度 */
    @ApiModelProperty(value = "正常数量")
    private Integer useCount;

    /** 湿度 */
    @ApiModelProperty(value = "异常数量")
    private Integer alarmCount;


}
