package com.jhdr.operation.entity.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 机房告警记录对象 opo_alarm_record
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "机房告警记录")
@Accessors(chain = true)
@TableName(value ="opo_alarm_record")
@AllArgsConstructor
@NoArgsConstructor
public class OpoAlarmRecordPo implements Serializable
{

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 故障编码 */
    @ApiModelProperty(value = "故障编码")
    @Excel(name = "故障编码")
    private String alarmCode;

    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;

    /** 机码 */
    @ApiModelProperty(value = "机码")
    @Excel(name = "机码")
    private String deviceCode;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "告警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 故障状态 */
    @ApiModelProperty(value = "故障状态")
    @Excel(name = "故障状态")
    private String alarmState;

    /** 告警内容 */
    @ApiModelProperty(value = "告警内容")
    @Excel(name = "告警内容")
    private String content;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

    /** 位置 */
    @ApiModelProperty(value = "位置")
    @Excel(name = "位置")
    private String site;


    @ApiModelProperty(value = "处理人")
    @Excel(name = "处理人")
    private String person;

    @ApiModelProperty(value = "处理详情")
    @Excel(name = "处理详情")
    private String result;

    /** 处理时间 */
    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "恢复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "恢复时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date regainTime;


}
