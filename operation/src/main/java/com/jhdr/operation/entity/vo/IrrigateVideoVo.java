package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("视频监控")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateVideoVo {

    @ApiModelProperty(value = "视频名称")
    private String nm;

    @ApiModelProperty(value = "相机编码")
    private String cameraCode;

    @ApiModelProperty(value = "视频路径")
    private String url;

    @ApiModelProperty(value = "视频类型")
    private String siteType;




}
