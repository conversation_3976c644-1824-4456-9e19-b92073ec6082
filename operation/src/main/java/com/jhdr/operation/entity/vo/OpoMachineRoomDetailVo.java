package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维机房设备详细信息api对象 opo_machine_room_detail
 *
 * <AUTHOR>
 * @date 2024-07-10
 */

@Data
@ApiModel(description = "运维机房设备详细信息api")
@Accessors(chain = true)
public class OpoMachineRoomDetailVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 编号，表达父子节点关系 */
    @ApiModelProperty(value = "编号，表达父子节点关系")
    @TableId
    private Long id;

    /** 项目名称 */
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称")
    private String subName;

    /** 唯一ID编号 */
    @ApiModelProperty(value = "唯一ID编号")
    @Excel(name = "唯一ID编号")
    private String wyid;

    /** 设备父级id */
    @ApiModelProperty(value = "设备父级id")
    @Excel(name = "设备父级id")
    private Long deviceId;

    /** 设备地址及端口号 */
    @ApiModelProperty(value = "设备地址及端口号")
    @Excel(name = "设备地址及端口号")
    private String ip;

    /** 值 */
    @ApiModelProperty(value = "值")
    @Excel(name = "值")
    private String value;

    /** 单位 */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String dw;

    /** 小编码 */
    @ApiModelProperty(value = "小编码")
    @Excel(name = "小编码")
    private Long subid;

    /** 获取时间 */
    @ApiModelProperty(value = "获取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "获取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date time;

}
