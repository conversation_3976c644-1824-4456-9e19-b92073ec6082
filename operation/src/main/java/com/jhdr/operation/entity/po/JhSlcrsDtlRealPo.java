package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("闸机数据表")
@TableName("jh_slcrs_dtl_r_real")
@AllArgsConstructor
@NoArgsConstructor
public class JhSlcrsDtlRealPo {

    @ApiModelProperty(value = "站点编码")
    private String prjnmcd;

    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    @ApiModelProperty(value = "闸门编码")
    private String gpcd;

    @ApiModelProperty(value = "闸门开度")
    private Double gtoph;

    @ApiModelProperty(value = "过闸流量")
    private String gtq;

    @ApiModelProperty(value = "闸门上升信号")
    private String gtup;

    @ApiModelProperty(value = "闸门下降信号")
    private String gtdw;

    @ApiModelProperty(value = "闸门全开")
    private String gtaop;

    @ApiModelProperty(value = "闸门全关")
    private String gtaco;









}
