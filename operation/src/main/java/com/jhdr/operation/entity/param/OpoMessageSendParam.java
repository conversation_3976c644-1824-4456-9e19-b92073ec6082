package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 短信平台对象 opo_message_send
 *
 * <AUTHOR>
 * @date 2024-08-30
 */

@Data
@ApiModel(description = "短信平台")
@Accessors(chain = true)
public class OpoMessageSendParam {


    /** 接收人 */
    @ApiModelProperty(value = "接收人")
    @Excel(name = "接收人")
    private String person;

    /** 接收人号码 */
    @ApiModelProperty(value = "接收人号码")
    @Excel(name = "接收人号码")
    private String phone;


}
