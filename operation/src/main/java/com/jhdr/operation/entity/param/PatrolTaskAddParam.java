package com.jhdr.operation.entity.param;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 巡检任务对象 patrol_task
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "巡检任务")
@Accessors(chain = true)
public class PatrolTaskAddParam implements Serializable
{
    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 计划id */
    @ApiModelProperty(value = "计划id")
    @Excel(name = "计划id")
    private String patrolId;



    /** 计划名称 */
    @ApiModelProperty(value = "计划名称")
    @Excel(name = "计划名称")
    private String patrolName;

    /** 巡检内容 */
    @ApiModelProperty(value = "巡检内容")
    @Excel(name = "巡检内容")
    private String content;

    /** 巡检类型 */
    @ApiModelProperty(value = "巡检类型")
    @Excel(name = "巡检类型")
    private String patrolType;

    /** 巡检范围 */
    @ApiModelProperty(value = "巡检范围")
    @Excel(name = "巡检范围")
    private String patrolScope;

    /** 巡检频率 */
    @ApiModelProperty(value = "巡检频率")
    @Excel(name = "巡检频率")
    private String cycle;

    /** 计划巡检日期 */
    @ApiModelProperty(value = "计划巡检日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划巡检日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date planTime;

    /** 巡检结果 */
    @ApiModelProperty(value = "巡检结果")
    @Excel(name = "巡检结果")
    private Long result;

    /** 巡检状态0未巡检1已巡检 */
    @ApiModelProperty(value = "巡检状态0未巡检1已巡检")
    @Excel(name = "巡检状态0未巡检1已巡检")
    private Long state;

    /** 实际巡检时间 */
    @ApiModelProperty(value = "实际巡检时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际巡检时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date patrolTime;

    /** 巡检人 */
    @ApiModelProperty(value = "巡检人")
    @Excel(name = "巡检人")
    private String person;



}
