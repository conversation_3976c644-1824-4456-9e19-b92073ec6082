package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 物资点对象 opo_goods_station
 *
 * <AUTHOR>
 * @date 2024-09-24
 */

@Data
@ApiModel(description = "物资详情")
@Accessors(chain = true)
@TableName(value ="opo_goods_detail")
public class OpoGoodsDetailPo
{

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 名称 */
    @ApiModelProperty(value = "名称")
    @Excel(name = "名称")
    private String name;

    /** 编码 */
    @ApiModelProperty(value = "规格型号")
    @Excel(name = "规格型号")
    private String model;

    /** 经度 */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String unit;

    /** 维度 */
    @ApiModelProperty(value = "总数")
    @Excel(name = "总数")
    private Integer amount;

    /** 负责人 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

    /** 电话 */
    @ApiModelProperty(value = "物资点id")
    @Excel(name = "物资点id")
    private Integer stationId;


}
