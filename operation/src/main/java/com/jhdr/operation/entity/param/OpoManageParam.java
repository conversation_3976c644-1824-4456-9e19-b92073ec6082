package com.jhdr.operation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务管理对象 opo_manage
 *
 * <AUTHOR>
 * @date 2024-07-05
 */

@Data
@ApiModel(description = "业务管理")
@Accessors(chain = true)
public class OpoManageParam implements Serializable
{
    /** 业务名称 */
    @ApiModelProperty(value = "业务名称")
    @Excel(name = "业务名称")
    private String name;


    /** 业务名称 */
    @ApiModelProperty(value = "类型")
    @Excel(name = "类型")
    private String type;

}
