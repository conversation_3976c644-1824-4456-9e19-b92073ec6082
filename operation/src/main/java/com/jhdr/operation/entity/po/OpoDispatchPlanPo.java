package com.jhdr.operation.entity.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 调度预案对象 opo_dispatch_plan
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "调度预案")
@Accessors(chain = true)
@TableName(value ="opo_dispatch_plan")
public class OpoDispatchPlanPo
{

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 预案类型 */
    @ApiModelProperty(value = "预案类型")
    @Excel(name = "预案类型")
    private String planType;

    /** 文件名称 */
    @ApiModelProperty(value = "文件名称")
    @Excel(name = "文件名称")
    private String fileName;

    /** 路径 */
    @ApiModelProperty(value = "路径")
    @Excel(name = "路径")
    private String path;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

}
