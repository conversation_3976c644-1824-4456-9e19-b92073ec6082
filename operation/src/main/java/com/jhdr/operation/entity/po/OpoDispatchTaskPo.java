package com.jhdr.operation.entity.po;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 工程调度对象 opo_dispatch_task
 *
 * <AUTHOR>
 * @date 2024-09-25
 */

@Data
@ApiModel(description = "工程调度")
@Accessors(chain = true)
@TableName(value ="opo_dispatch_task")
public class OpoDispatchTaskPo
{
    @ApiModelProperty(value = "$column.columnComment")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 命令票名称 */
    @ApiModelProperty(value = "命令票名称")
    @Excel(name = "命令票名称")
    private String fileName;

    /** 路径 */
    @ApiModelProperty(value = "路径")
    @Excel(name = "路径")
    private String path;

    /** 发起人id */
    @ApiModelProperty(value = "发起人id")
    @Excel(name = "发起人id")
    private String person1Id;

    /** 发起人 */
    @ApiModelProperty(value = "发起人")
    @Excel(name = "发起人")
    private String person1;

    /** 接收人id */
    @ApiModelProperty(value = "接收人id")
    @Excel(name = "接收人id")
    private String person2Id;

    /** 接收人 */
    @ApiModelProperty(value = "接收人")
    @Excel(name = "接收人")
    private String person2;

    /** 审核人id */
    @ApiModelProperty(value = "审核人id")
    @Excel(name = "审核人id")
    private String person3Id;

    /** 审核人 */
    @ApiModelProperty(value = "审核人")
    @Excel(name = "审核人")
    private String person3;

    /** 签发人id */
    @ApiModelProperty(value = "签发人id")
    @Excel(name = "签发人id")
    private String person4Id;

    /** 签发人 */
    @ApiModelProperty(value = "签发人")
    @Excel(name = "签发人")
    private String person4;

    /** 状态 */
    @ApiModelProperty(value = ".2已审核待签发，3已签发待查收，4已查收")
    @Excel(name = "状态")
    private Integer status;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

}
