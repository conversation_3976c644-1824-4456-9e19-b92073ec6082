package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-工情站点对象 opo_gq_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-工情站点-泵站")
@Accessors(chain = true)
public class OpoGqStationParam implements Serializable {
    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String strName;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /** 所属区域编码 */
    @ApiModelProperty(value = "所属区域编码")
    private String addvcd;

    /** 承建单位 */
    @ApiModelProperty(value = "承建单位")
    private String construction;






}
