package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 短信平台对象 opo_message_send
 *
 * <AUTHOR>
 * @date 2024-08-30
 */

@Data
@ApiModel(description = "短信平台")
@Accessors(chain = true)
public class OpoMessageSendVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Integer id;

    /** 发送人 */
    @ApiModelProperty(value = "发送人")
    @Excel(name = "发送人")
    private String people;

    /** 内容 */
    @ApiModelProperty(value = "内容")
    @Excel(name = "内容")
    private String content;

    /** 接收人 */
    @ApiModelProperty(value = "接收人")
    @Excel(name = "接收人")
    private String person;

    /** 接收人号码 */
    @ApiModelProperty(value = "接收人号码")
    @Excel(name = "接收人号码")
    private String phone;

    /** 发送时间 */
    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    /** 发送状态 */
    @ApiModelProperty(value = "发送状态")
    @Excel(name = "发送状态")
    private Integer status;

}
