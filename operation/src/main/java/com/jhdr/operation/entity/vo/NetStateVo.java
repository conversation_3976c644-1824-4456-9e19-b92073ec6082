package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("网络设备监测")
public class NetStateVo {

    @ApiModelProperty(value = "设备名称")
    private String deviceName;


    @ApiModelProperty(value = "状态")
    private String status;


}
