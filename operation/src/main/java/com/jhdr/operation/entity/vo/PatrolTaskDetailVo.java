package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 巡检任务对象 patrol_task
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "巡检详情")
@Accessors(chain = true)
public class PatrolTaskDetailVo
{

    @ApiModelProperty(value = "id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 计划编号 */
    @ApiModelProperty(value = "计划编号")
    @Excel(name = "计划编号")
    private String taskCode;

    /** 计划名称 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String staName;


    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;

    /** 内容 */
    @ApiModelProperty(value = "内容")
    @Excel(name = "内容")
    private String content;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;



}
