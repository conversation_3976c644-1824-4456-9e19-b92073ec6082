package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@ApiModel(description = "语音结果拆分")
@Accessors(chain = true)
public class VoiceResultVo {
    @ApiModelProperty(value = "id")
    private String id ;

    @ApiModelProperty(value = "站点名称")
    private String strName ;

    @ApiModelProperty(value = "类型")
    private String strType ;

    @ApiModelProperty(value = "开始时间")
    private String startTime ;

    @ApiModelProperty(value = "结束时间")
    private String endTime ;

    @ApiModelProperty(value = "数据id")
    private String guid ;

    @ApiModelProperty(value = "数据编码")
    private String cd ;

    @ApiModelProperty(value = "数据名称")
    private String nm ;

    @ApiModelProperty(value = "gisdata")
    private String gisdata ;

    @ApiModelProperty(value = "gissign")
    private String gissign ;

    @ApiModelProperty(value = "经度")
    private String lgtd ;

    @ApiModelProperty(value = "纬度")
    private String lttd ;










}
