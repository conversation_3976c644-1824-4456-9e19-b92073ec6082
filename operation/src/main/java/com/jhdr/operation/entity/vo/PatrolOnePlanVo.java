package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 巡检计划对象 patrol_plan
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "巡检计划")
@Accessors(chain = true)
public class PatrolOnePlanVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "$column.columnComment")
    @TableId
    private Integer id;

    /** 计划名称 */
    @ApiModelProperty(value = "计划名称")
    @Excel(name = "计划名称")
    private String patrolName;

    /** 巡检类型 */
    @ApiModelProperty(value = "巡检类型")
    @Excel(name = "巡检类型")
    private String patrolType;

    /** 巡检范围 */
    @ApiModelProperty(value = "巡检范围")
    @Excel(name = "巡检范围")
    private String patrolScope;

    /** 巡检内容 */
    @ApiModelProperty(value = "巡检内容")
    @Excel(name = "巡检内容")
    private String content;

    /** 巡检人 */
    @ApiModelProperty(value = "巡检人")
    @Excel(name = "巡检人")
    private String person;

    /** 巡检频率 */
    @ApiModelProperty(value = "巡检频率")
    @Excel(name = "巡检频率")
    private String cycle;

    /** 巡检时间 */
    @ApiModelProperty(value = "巡检时间")
    @Excel(name = "巡检时间")
    private String patrolTime;

    /** 通知时间 */
    @ApiModelProperty(value = "通知时间")
    @Excel(name = "通知时间")
    private String informTime;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

    @ApiModelProperty(value = "通知对象")
    private List<PatrolPlanDetailVo> details;

}
