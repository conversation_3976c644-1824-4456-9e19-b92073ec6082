package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("实时水势")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateWaterTrendVo {

    @ApiModelProperty(value = "站名")
    private String strName;

    @ApiModelProperty(value = "上水势")
    private String upTrend;

    @ApiModelProperty(value = "上水势")
    private String downTrend;











}
