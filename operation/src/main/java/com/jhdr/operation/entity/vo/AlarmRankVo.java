package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("故障数量数据")
@AllArgsConstructor
@NoArgsConstructor
public class AlarmRankVo {

    @ApiModelProperty(value = "站点(设备)名称")
    private String staName;

    @ApiModelProperty(value = "站点(设备)类型")
    private String staType;

    @ApiModelProperty(value = "本年告警数量")
    private Integer count;
}
