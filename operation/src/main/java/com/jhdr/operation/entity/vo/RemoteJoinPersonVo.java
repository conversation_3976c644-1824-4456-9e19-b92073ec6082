package com.jhdr.operation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 异地会商对象 remote_chat
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@Data
@ApiModel(description = "参会人员")
@Accessors(chain = true)
public class RemoteJoinPersonVo {

    /** 角色 */
    @ApiModelProperty(value = "角色")
    @Excel(name = "角色")
    private String roleName;

    /** 发起人id */
    @ApiModelProperty(value = "发起人id")
    @Excel(name = "发起人id")
    private Integer personId;

    /** 发起人id */
    @ApiModelProperty(value = "发起人")
    @Excel(name = "发起人")
    private String person;

    /** 发起人id */
    @ApiModelProperty(value = "联系方式")
    @Excel(name = "联系方式")
    private String phone;




}
