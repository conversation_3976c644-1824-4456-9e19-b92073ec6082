package com.jhdr.operation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 异地会商对象 remote_chat
 *
 * <AUTHOR>
 * @date 2024-11-14
 */

@Data
@ApiModel(description = "险情地点")
@Accessors(chain = true)
public class RemoteVideoVo {

    /** 角色 */
    @ApiModelProperty(value = "cd")
    @Excel(name = "cd")
    private String cd;

    /** 发起人id */
    @ApiModelProperty(value = "视频编号")
    @Excel(name = "视频编号")
    private String nm;








}
