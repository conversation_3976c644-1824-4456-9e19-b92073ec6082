package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-水文站点对象 opo_sw_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-水文站点")
@Accessors(chain = true)
public class OpoSwStationVo {

    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "站名")
    private String stnm;


    /** 站码 */
    @ApiModelProperty(value = "站码")
    @Excel(name = "站码")
    private String stcd;



    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String admauth;

    /** 承建单位 */
    @ApiModelProperty(value = "承建单位")
    @Excel(name = "承建单位")
    private String construction;

    /** 工程位置 */
    @ApiModelProperty(value = "工程位置")
    @Excel(name = "工程位置")
    private String stlc;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    @Excel(name = "状态")
    private String status;

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    private String alarmTime;

    @ApiModelProperty(value = "监测方式")
    private String monType;

    /** 是否监测 */
    @ApiModelProperty(value = "是否监测 0否；1是")
    private Integer isMon;

}
