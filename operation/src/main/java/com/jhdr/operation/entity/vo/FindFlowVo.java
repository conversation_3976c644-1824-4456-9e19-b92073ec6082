package com.jhdr.operation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("水位数据")
@AllArgsConstructor
@NoArgsConstructor
public class FindFlowVo {

    @ApiModelProperty(value = "遥测站编码（设备）")
    private String code;

    @ApiModelProperty(value = "数据时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTime;

    @ApiModelProperty(value = "流量")
    private Double flow;

}
