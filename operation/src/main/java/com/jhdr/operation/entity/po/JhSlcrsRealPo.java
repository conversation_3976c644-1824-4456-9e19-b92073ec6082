package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("闸站数据快照表")
@TableName("jh_slcrs_r_real")
@AllArgsConstructor
@NoArgsConstructor
public class JhSlcrsRealPo {

    @ApiModelProperty(value = "站点编码")
    private String prjnmcd;

    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    @ApiModelProperty(value = "闸上水位")
    private Double slupsz;

    @ApiModelProperty(value = "闸下水位")
    private Double sldsz;

    @ApiModelProperty(value = "过闸流量")
    private Double thrslq;

    @ApiModelProperty(value = "开启孔数")
    private Double gtopn;

    @ApiModelProperty(value = "闸上水势")
    private Double upswtp;

    @ApiModelProperty(value = "闸下水势")
    private Double dswtp;

    @ApiModelProperty(value = "填报单位")
    private String rpdpcd;

    @ApiModelProperty(value = "填报人")
    private String reporter;

    @ApiModelProperty(value = "填报人联系方式")
    private String rpttlnmb;

    @ApiModelProperty(value = "备注")
    private String rmk;

    @ApiModelProperty(value = "闸门数量")
    private Double gtcn;











}
