package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(description = "区县编码")
@Accessors(chain = true)
public class CountyVo  {

    @ApiModelProperty(value = "名称")
    private String addvnm;

    @ApiModelProperty(value = "区域编码")
    private String addvcd;



}
