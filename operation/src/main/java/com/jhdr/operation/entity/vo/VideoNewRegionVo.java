package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 视频监控站点基础信息对象 jhom_vm_b
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "视频区域层级")
@Accessors(chain = true)
public class VideoNewRegionVo
{


    @ApiModelProperty(value = "监控编码")
    private String name;

    @ApiModelProperty(value = "当前区域编码")
    private String parentId;

    @ApiModelProperty("子集菜单")
    private List<VideoNewRegionVo> children;

}
