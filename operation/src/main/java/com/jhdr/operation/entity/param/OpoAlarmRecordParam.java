package com.jhdr.operation.entity.param;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 机房告警记录对象 opo_alarm_record
 *
 * <AUTHOR>
 * @date 2024-06-26
 */

@Data
@ApiModel(description = "机房告警记录")
@Accessors(chain = true)
public class OpoAlarmRecordParam implements Serializable {

    /** 告警类型 */
    @ApiModelProperty(value = "告警类型")
    @Excel(name = "告警类型")
    private String alarmType;

    /** 设备类型 */
    @ApiModelProperty(value = "设备类型")
    @Excel(name = "设备类型")
    private String deviceType;

    /** 机名 */
    @ApiModelProperty(value = "机名")
    @Excel(name = "机名")
    private String deviceName;


    @ApiModelProperty(value = "编码")
    private String deviceCode;


    /** 告警时间 */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /** 告警时间 */
    @ApiModelProperty(value = "结束时间")
    private String endTime;


    /** 告警步骤 */
    @ApiModelProperty(value = "告警步骤")
    private String alarmState;






}
