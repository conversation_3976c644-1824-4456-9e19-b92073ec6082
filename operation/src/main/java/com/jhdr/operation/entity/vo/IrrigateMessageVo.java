package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("灌区信息")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateMessageVo {
    @ApiModelProperty(value = "泵站数量")
    private Integer pumpCount;

    @ApiModelProperty(value = "水闸数量")
    private Integer gateCount;

    @ApiModelProperty(value = "干渠数量")
    private Integer trunkCount;

    @ApiModelProperty(value = "沟渠数量")
    private Integer ditchesCount;



}
