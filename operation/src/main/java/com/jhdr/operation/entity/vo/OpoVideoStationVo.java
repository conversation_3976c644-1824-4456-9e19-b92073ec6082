package com.jhdr.operation.entity.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 运维监控-视频站点对象 opo_video_station
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "运维监控-视频站点")
@Accessors(chain = true)
public class OpoVideoStationVo implements Serializable {

    @ApiModelProperty(value = "设备编号")
    private String cd;

    /** 监控设备名称 */
    @ApiModelProperty(value = "监控设备名称")
    @Excel(name = "站名")
    private String nm;


    /** 站名 */
    @ApiModelProperty(value = "站名")
    @Excel(name = "设备位置")
    private String nvr;



    /** 监控标识编码 */
    @ApiModelProperty(value = "监控标识编码")
    private String cameraCode;




    /** ip地址 */
    @ApiModelProperty(value = "ip地址")
    @Excel(name = "ip地址")
    private String ip;

    /** 通道号 */
    @ApiModelProperty(value = "通道号")
    @Excel(name = "通道号")
    private String cn;

    /** 状态 */
    @ApiModelProperty(value = "设备状态")
    @Excel(name = "设备状态")
    private String status;

    /** 告警时间 */
    @ApiModelProperty(value = "告警时间")
    @Excel(name = "告警时间")
    private String alarmTime;

    @ApiModelProperty(value = "告警内容")
    @Excel(name = "告警内容")
    private String alarmContent;



    /** 状态 */
    @ApiModelProperty(value = "视频识别图片")
    @Excel(name = "视频识别图片")
    private String discern;

    /** 视频识别报警状态 */
    @ApiModelProperty(value = "视频识别报警状态")
    @Excel(name = "视频识别报警状态")
    private String discernState;

    /** 站点类型1一期 2二期 */
    @ApiModelProperty(value = "站点类型1一期 2二期")
    @Excel(name = "站点类型1一期 2二期")
    private String siteType;

    /** 二期站点视频地址*/
    @ApiModelProperty(value = "二期站点视频地址")
    @Excel(name = "二期站点视频地址")
    private String siteUrl;


    @ApiModelProperty(value = "承建单位")
    @Excel(name = "承建单位")
    private String construction;


    /** 是否监测 */
    @ApiModelProperty(value = "是否监测 0否；1是")
    private Integer isMon;




}
