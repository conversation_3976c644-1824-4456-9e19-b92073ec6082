package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("泵机数据快照表")
@TableName("jh_pump_r_real")
@AllArgsConstructor
@NoArgsConstructor
public class JhPumpRealPo {

    @ApiModelProperty(value = "站点编码")
    private String prjnmcd;

    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    @ApiModelProperty(value = "站上水位")
    private Double pmpcd;

    @ApiModelProperty(value = "站下水位")
    private Double ppdwz;

    @ApiModelProperty(value = "开机台数")
    private Double omcn;

    @ApiModelProperty(value = "开机功率")
    private Double ompwr;

    @ApiModelProperty(value = "抽水流量")
    private Double pmpq;

    @ApiModelProperty(value = "水情特征码")
    private Double wchrcd;

    @ApiModelProperty(value = "站上水势")
    private Double ppupwptn;

    @ApiModelProperty(value = "站下水势")
    private Double ppdwwptn;

    @ApiModelProperty(value = "测流方法")
    private Double msqmt;

    @ApiModelProperty(value = "引流特征码")
    private Double pdchcd;

    @ApiModelProperty(value = "累计电能")
    private Double ppee;

    @ApiModelProperty(value = "机组数量")
    private Double ppcn;

    @ApiModelProperty(value = "站内ab相电压")
    private Double ppuab;

    @ApiModelProperty(value = "站内bc相电压")
    private Double ppubc;

    @ApiModelProperty(value = "站内ca相电压")
    private Double ppuca;

    @ApiModelProperty(value = "站内a相电流")
    private Double ppia;

    @ApiModelProperty(value = "站内b相电流")
    private Double ppib;

    @ApiModelProperty(value = "站内c相电流")
    private Double ppic;

    @ApiModelProperty(value = "站内有功功率")
    private Double ppapwr;

    @ApiModelProperty(value = "站内无功功率")
    private Double pprpwr;

    @ApiModelProperty(value = "站内功率因数")
    private Double ppcos;


    @ApiModelProperty(value = "数据状态")
    private String status;









}
