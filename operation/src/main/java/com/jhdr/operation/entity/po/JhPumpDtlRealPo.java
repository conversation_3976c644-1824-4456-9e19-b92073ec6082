package com.jhdr.operation.entity.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@ApiModel("泵机数据快照表")
@TableName("jh_pump_dtl_r_real")
@AllArgsConstructor
@NoArgsConstructor
public class JhPumpDtlRealPo {

    @ApiModelProperty(value = "站点编码")
    private String prjnmcd;

    @ApiModelProperty(value = "采集时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date clltm;

    @ApiModelProperty(value = "机组编码")
    private String pmpcd;

    @ApiModelProperty(value = "运行标识")
    private String om;

    @ApiModelProperty(value = "ab电压")
    private String uab;

    @ApiModelProperty(value = "bc电压")
    private String ubc;

    @ApiModelProperty(value = "ca电压")
    private String uca;

    @ApiModelProperty(value = "a相电流")
    private String ia;

    @ApiModelProperty(value = "b相电流")
    private String ib;

    @ApiModelProperty(value = "c相电流")
    private String ic;

    @ApiModelProperty(value = "有功功率")
    private String apwr;

    @ApiModelProperty(value = "无功功率")
    private String rpwr;

    @ApiModelProperty(value = "功率因数")
    private String cos;

    @ApiModelProperty(value = "累计电能")
    private String ee;

    @ApiModelProperty(value = "排水量")
    private String q;







}
