package com.jhdr.operation.entity.param;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 报警信息对象 opo_alarm_message
 *
 * <AUTHOR>
 * @date 2024-07-01
 */

@Data
@ApiModel(description = "报警信息")
@Accessors(chain = true)
public class StrAlarmMessageParam  {

    /** 站码 */
    @ApiModelProperty(value = "站名")
    private String staName;

    /** 站点类型 */
    @ApiModelProperty(value = "站点类型")
    private String staType;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间")
    private String startTime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间")
    private String endTime;

}
