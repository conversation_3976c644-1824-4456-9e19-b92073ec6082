package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("灌区介绍")
@AllArgsConstructor
@NoArgsConstructor
public class IrrigateIntroduceVo {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "介绍")
    private String profile;

    @ApiModelProperty(value = "灌区面积(㎡)")
    private Double acreage;

    @ApiModelProperty(value = "灌区人口(人)")
    private Double population;

    @ApiModelProperty(value = "灌区田亩(亩)")
    private Double croplandArea;

    @ApiModelProperty(value = "所属行政名称")
    private String adminArea;



}
