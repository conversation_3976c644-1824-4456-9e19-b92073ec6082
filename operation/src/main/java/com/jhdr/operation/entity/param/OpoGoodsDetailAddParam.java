package com.jhdr.operation.entity.param;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 物资详情对象 opo_goods_detail
 *
 * <AUTHOR>
 * @date 2024-09-24
 */

@Data
@ApiModel(description = "物资详情")
@Accessors(chain = true)
public class OpoGoodsDetailAddParam
{


    /** $column.columnComment */
    @ApiModelProperty(value = "id")
    private Integer id;

    /** 名称 */
    @ApiModelProperty(value = "名称")
    @Excel(name = "名称")
    private String name;

    /** 规格型号 */
    @ApiModelProperty(value = "规格型号")
    @Excel(name = "规格型号")
    private String model;

    /** 单位 */
    @ApiModelProperty(value = "单位")
    @Excel(name = "单位")
    private String unit;

    /** 总数 */
    @ApiModelProperty(value = "总数")
    @Excel(name = "总数")
    private Long amount;

    /** 是否删除 */
    @ApiModelProperty(value = "是否删除")
    @Excel(name = "是否删除")
    private Integer isDelete;

    /** 物资点id */
    @ApiModelProperty(value = "物资点id")
    @Excel(name = "物资点id")
    private Integer stationId;

}
