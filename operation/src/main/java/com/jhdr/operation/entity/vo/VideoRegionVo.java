package com.jhdr.operation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 视频监控站点基础信息对象 jhom_vm_b
 *
 * <AUTHOR>
 * @date 2024-06-25
 */

@Data
@ApiModel(description = "视频区域层级")
@Accessors(chain = true)
public class VideoRegionVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "监控编码")
    private String cameraCode;

    @ApiModelProperty(value = "当前区域编码")
    private String indexCode;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父区域编码")
    private String parentIndexCode;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    private BigDecimal lttd;
    /** 设备状态 */
    @ApiModelProperty(value = "设备状态 在线 不在线")
    private String status;


    @ApiModelProperty("子集菜单")
    private List<VideoRegionVo> children;

}
