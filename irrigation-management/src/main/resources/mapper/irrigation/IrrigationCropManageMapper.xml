<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationCropManageMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.IrrigationCropManagePo" id="IrrigationCropManageResult">
        <result property="id" column="id"/>
        <result property="cropId" column="crop_id"/>
        <result property="cropName" column="crop_name"/>
        <result property="plantArea" column="plant_area"/>
        <result property="pumpCode" column="pump_code"/>
        <result property="pumpName" column="pump_name"/>
        <result property="createTime" column="create_time"/>
        <result property="irrigationCode" column="irrigation_code"/>
        <result property="irrigationName" column="irrigation_name"/>
    </resultMap>

    <resultMap type="com.jhdr.irrigation.entity.vo.IrrigationPumpRealVo" id="IrrigationPumpRealVo">
        <result property="pumpCode" column="pumpCode"/>
        <result property="pumpName" column="pumpName"/>
        <result property="runStatus" column="runStatus"/>
        <result property="realWater" column="realWater"/>
        <result property="realFlow" column="realFlow"/>
        <result property="realTime" column="realTime"/>

        <collection property="pumpCondition" ofType="com.jhdr.irrigation.entity.vo.JhPumpDtlRInfoVo">
            <result column="pmpcd" property="pmpcd"/>
            <result column="om" property="om"/>
        </collection>

    </resultMap>

    <sql id="selectIrrigationCropManageVo">
        select id,
               crop_id,
               crop_name,
               plant_area,
               pump_code,
               pump_name,
               create_time,
               irrigation_code,
               irrigation_name
        from irrigation_crop_manage
    </sql>

    <select id="selectIrrigationCropManageList" resultType="com.jhdr.irrigation.entity.vo.IrrigationCropManageVo">
        <include refid="selectIrrigationCropManageVo"/>
        <where>
            <if test="cropId != null ">and crop_id = #{cropId}</if>
            <if test="cropName != null  and cropName != ''">and crop_name like concat('%', #{cropName}, '%')</if>
            <if test="plantArea != null ">and plant_area = #{plantArea}</if>
            <if test="pumpCode != null  and pumpCode != ''">and pump_code = #{pumpCode}</if>
            <if test="pumpName != null  and pumpName != ''">and pump_name like concat('%', #{pumpName}, '%')</if>
            <if test="irrigationCode != null  and irrigationCode != ''">and irrigation_code = #{irrigationCode}</if>
            <if test="irrigationName != null  and irrigationName != ''">and irrigation_name like concat('%',
                #{irrigationName}, '%')
            </if>
        </where>
        order by id
    </select>

    <select id="selectIrrigationCropManageById" resultType="com.jhdr.irrigation.entity.vo.IrrigationCropManageVo">
        <include refid="selectIrrigationCropManageVo"/>
        where id = #{id}
    </select>
    <select id="getBaseCropStatisticsByIrrigationCode"
            resultType="com.jhdr.irrigation.entity.vo.IrrigationCropStatisticsVo">
        select crop_id         as cropId,
               crop_name       as cropName,
               sum(plant_area) as allPlantArea
        from irrigation_crop_manage
        where irrigation_code = #{irrigationCode}
        group by crop_id, crop_name
    </select>
    <select id="getBasePumpStatisticsByIrrigationCode"
            resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpStatisticsVo">
        select pump_code       as pumpCode,
               pump_name       as pumpName,
               sum(plant_area) as allIrrigatedArea
        from irrigation_crop_manage
        where irrigation_code = #{irrigationCode}
        group by pump_code, pump_name
    </select>
    <select id="getWaterDemandCropStatistics"
            resultType="com.jhdr.irrigation.entity.vo.IrrigationCropWaterDemandStatisticsVo">
        select crop_id                                          as cropId,
               crop_name                                        as cropName,
               sum(plant_area)                                  as allPlantArea,
               ROUND(sum(plant_area * 0.063673496957964622), 2) as waterDemand
        from irrigation_crop_manage
        where irrigation_code = #{irrigationCode}
        group by crop_id, crop_name
    </select>
    <select id="getWaterDemandPumpStatistics"
            resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpWaterDemandStatisticsVo">
        select pump_code                                        as pumpCode,
               pump_name                                        as pumpName,
               sum(plant_area)                                  as allIrrigatedArea,
               ROUND(sum(plant_area * 0.063673496957964622), 2) as waterDemand
        from irrigation_crop_manage
        where irrigation_code = #{irrigationCode}

        group by pump_code, pump_name
    </select>
    <select id="getIrrigationBaseInfo" resultType="com.jhdr.irrigation.entity.vo.IrrigationBaseVo">
        select *
        from irrigation_base_info limit 1
    </select>
    <select id="getRegionInfo" resultType="com.jhdr.irrigation.entity.vo.IrrigationRegionVo">
        select *
        from irrigation_region_info
        where irrigation_code = #{irrigationCode} limit 1
    </select>
    <select id="getPumpRealList" resultMap="IrrigationPumpRealVo">
        SELECT jpb.str_code AS pumpCode,
               jpb.str_name AS pumpName,
               jprr.ppupz   as realWater,
               jprr.pmpq    as realFlow,
               jprr.clltm      realTime,
               CASE
                   WHEN jprr.omcn > 0 THEN
                       1
                   ELSE 0
                   END      AS runStatus,
               jprd.pmpcd,
               jprd.om
        FROM jhir_pust_b jpb
                 LEFT JOIN jh_pump_r_real jprr on jpb.str_code = jprr.prjnmcd and jprr.ppupz is not null
                 LEFT JOIN jh_pump_dtl_r_real jprd ON jprd.prjnmcd = jprr.prjnmcd
        WHERE jpb.status = 1
          and jpb.addvcd = #{addvcd};
    </select>
    <select id="getPumpLineList" resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpRealVo">
        SELECT str_code pumpCode,
               upz      realWater,
               tm AS    realTime
        FROM jh_upz_r_real
        WHERE str_type = 'BZ'

    </select>
    <select id="getPumpLineListByTime" resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpLineVo">
        SELECT
        jprr.prjnmcd AS pumpCode,
        jpb.str_name AS pumpName,
        jprr.ppupz realWater,
        jprr.pmpq realFlow,
        jprr.clltm as realTime,
        CASE
        WHEN jprr.omcn > 0 THEN
        1 ELSE 0
        END AS runStatus
        FROM
        jh_pump_r jprr
        left join jhir_pust_b jpb on jpb.str_code = jprr.prjnmcd
        WHERE
        jprr.prjnmcd=#{pumpCode} and jpb.addvcd =#{addvcd}
        <if test="startTime != null">and jprr.clltm between #{startTime} and #{endTime}</if>
        order by jprr.clltm desc
    </select>
    <select id="getPerceptionPumpTabulation" resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpLineVo">
        SELECT
        jprr.prjnmcd AS pumpCode,
        pmpq realFlow,
        clltm as realTime
        FROM
        jh_pump_r jprr
        WHERE
        jprr.prjnmcd in
        <foreach collection="pumpCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and jprr.ppupz is not null
        <if test="startTime != null">and clltm between #{startTime} and #{endTime}</if>
        ORDER BY realTime desc
    </select>
    <select id="getPumpList" resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpPullVo">
        select str_code as pumpCode,
               str_name as pumpName
        from jhir_pust_b
        where addvcd = #{addvcd}
          and status = 1
          and is_river_side = 1
    </select>
    <select id="getWeatherDayList" resultType="java.lang.String">
        select weather_json
        from weather_data
        where region = #{irrigationName}
          and status = 1 limit 1
    </select>

    <select id="getPerceptionPumpAll" resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpAllVo">
        SELECT
        sum(pmpq) realFlow,
        to_char(clltm,'YYYY-MM-DD') as realTime
        FROM
        jh_pump_r jprr
        left join jhir_pust_b jpb on jpb.str_code = jprr.prjnmcd
        WHERE jpb.addvcd =#{addvcd} and
        jprr.ppupz is not null
        <if test="startTime != null">and clltm between #{startTime} and #{endTime}</if>
        GROUP BY realTime
        ORDER BY realTime
    </select>
    <select id="getPumpIrrigationAreas" resultType="com.jhdr.irrigation.entity.vo.IrrigationPumpPullVo">
        select
        str_code as pumpCode,
        ifnull( devise_irr, 0 ) * 10000*666.667 as deviseIrr
        from jhir_pust_b
        where status = 1 and str_code in
        <foreach collection="pumpCodes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>

    </select>
    <select id="getAllWaterDemandCropStatistics"
            resultType="com.jhdr.irrigation.entity.vo.IrrigationCropWaterDemandStatisticsVo">
        SELECT irrigation_code,
               irrigation_name,
               crop_id                                          AS cropId,
               crop_name                                        AS cropName,
               SUM(plant_area)                                  AS allPlantArea,
               ROUND(SUM(plant_area * 0.063673496957964622), 2) AS waterDemand,
               '地面灌溉'                                           AS irrigationType
        FROM irrigation_crop_manage

        GROUP BY irrigation_code,
                 irrigation_name,
                 crop_id,
                 crop_name
        ORDER BY irrigation_code, crop_id
    </select>
    <select id="getCropIrrigatedArea" resultType="com.jhdr.irrigation.entity.vo.WaterCropStatisticsVo">
        SELECT
            irrigation_name,
            GROUP_CONCAT ( DISTINCT crop_name ORDER BY crop_name SEPARATOR ', ' ) AS cropName,
            SUM ( plant_area ) AS plantArea
        FROM
            irrigation_crop_manage
        GROUP BY
            irrigation_name
        ORDER BY
            CASE

                WHEN irrigation_name = '颍泉区' THEN
                    1
                WHEN irrigation_name = '颍东区' THEN
                    2
                WHEN irrigation_name = '利辛县' THEN
                    3
                WHEN irrigation_name = '蒙城县' THEN
                    4
                WHEN irrigation_name = '凤台县' THEN
                    5
                WHEN irrigation_name = '潘集区' THEN
                    6 ELSE 7 END;


    </select>

    <insert id="insertIrrigationCropManage"
            parameterType="com.jhdr.irrigation.entity.param.IrrigationCropManageAddParam">
        insert into irrigation_crop_manage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cropId != null">crop_id,</if>
            <if test="cropName != null">crop_name,</if>
            <if test="plantArea != null">plant_area,</if>
            <if test="pumpCode != null">pump_code,</if>
            <if test="pumpName != null">pump_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="irrigationCode != null">irrigation_code,</if>
            <if test="irrigationName != null">irrigation_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cropId != null">#{cropId},</if>
            <if test="cropName != null">#{cropName},</if>
            <if test="plantArea != null">#{plantArea},</if>
            <if test="pumpCode != null">#{pumpCode},</if>
            <if test="pumpName != null">#{pumpName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="irrigationCode != null">#{irrigationCode},</if>
            <if test="irrigationName != null">#{irrigationName},</if>
        </trim>
    </insert>

    <update id="updateIrrigationCropManage"
            parameterType="com.jhdr.irrigation.entity.param.IrrigationCropManageEditParam">
        update irrigation_crop_manage
        <trim prefix="SET" suffixOverrides=",">
            <if test="cropId != null">crop_id = #{cropId},</if>
            <if test="cropName != null">crop_name = #{cropName},</if>
            <if test="plantArea != null">plant_area = #{plantArea},</if>
            <if test="pumpCode != null">pump_code = #{pumpCode},</if>
            <if test="pumpName != null">pump_name = #{pumpName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="irrigationCode != null">irrigation_code = #{irrigationCode},</if>
            <if test="irrigationName != null">irrigation_name = #{irrigationName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIrrigationCropManageById" parameterType="Long">
        delete
        from irrigation_crop_manage
        where id = #{id}
    </delete>

    <delete id="deleteIrrigationCropManageByIds" parameterType="String">
        delete from irrigation_crop_manage where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
