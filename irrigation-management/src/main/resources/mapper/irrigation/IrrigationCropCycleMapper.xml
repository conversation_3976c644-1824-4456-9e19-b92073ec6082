<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationCropCycleMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.IrrigationCropCyclePo" id="IrrigationCropCycleResult">
        <result property="cycleId"    column="cycle_id"    />
        <result property="cropId"    column="crop_id"    />
        <result property="cropName"    column="crop_name"    />
        <result property="cycleName"    column="cycle_name"    />
        <result property="cycleDescription"    column="cycle_description"    />
        <result property="waterRequirement"    column="water_requirement"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="cycleDay"    column="cycle_day"    />
        <result property="cycleSort"    column="cycle_sort"    />
    </resultMap>

    <sql id="selectIrrigationCropCycleVo">
        select cycle_id, crop_id, crop_name, cycle_name, cycle_description, water_requirement, start_date, end_date, cycle_day, cycle_sort from irrigation_crop_cycle
    </sql>

    <select id="selectIrrigationCropCycleList"  resultType="com.jhdr.irrigation.entity.vo.IrrigationCropCycleVo">
        <include refid="selectIrrigationCropCycleVo"/>
        <where>
            <if test="cropId != null "> and crop_id = #{cropId}</if>
            <if test="cropName != null  and cropName != ''"> and crop_name like concat('%', #{cropName}, '%')</if>
            <if test="cycleName != null  and cycleName != ''"> and cycle_name like concat('%', #{cycleName}, '%')</if>
            <if test="cycleDescription != null  and cycleDescription != ''"> and cycle_description = #{cycleDescription}</if>
            <if test="waterRequirement != null "> and water_requirement = #{waterRequirement}</if>
            <if test="startDate != null  and startDate != ''"> and start_date = #{startDate}</if>
            <if test="endDate != null  and endDate != ''"> and end_date = #{endDate}</if>
            <if test="cycleDay != null  and cycleDay != ''"> and cycle_day = #{cycleDay}</if>
            <if test="cycleSort != null  and cycleSort != ''"> and cycle_sort = #{cycleSort}</if>
        </where>
    </select>

    <select id="selectIrrigationCropCycleByCycleId"  resultType="com.jhdr.irrigation.entity.vo.IrrigationCropCycleVo">
            <include refid="selectIrrigationCropCycleVo"/>
            where cycle_id = #{cycleId}
    </select>

    <insert id="insertIrrigationCropCycle" parameterType="com.jhdr.irrigation.entity.param.IrrigationCropCycleAddParam">
        insert into irrigation_crop_cycle
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="cycleId != null">cycle_id,</if>
                    <if test="cropId != null">crop_id,</if>
                    <if test="cropName != null">crop_name,</if>
                    <if test="cycleName != null">cycle_name,</if>
                    <if test="cycleDescription != null">cycle_description,</if>
                    <if test="waterRequirement != null">water_requirement,</if>
                    <if test="startDate != null">start_date,</if>
                    <if test="endDate != null">end_date,</if>
                    <if test="cycleDay != null">cycle_day,</if>
                    <if test="cycleSort != null">cycle_sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="cycleId != null">#{cycleId},</if>
                    <if test="cropId != null">#{cropId},</if>
                    <if test="cropName != null">#{cropName},</if>
                    <if test="cycleName != null">#{cycleName},</if>
                    <if test="cycleDescription != null">#{cycleDescription},</if>
                    <if test="waterRequirement != null">#{waterRequirement},</if>
                    <if test="startDate != null">#{startDate},</if>
                    <if test="endDate != null">#{endDate},</if>
                    <if test="cycleDay != null">#{cycleDay},</if>
                    <if test="cycleSort != null">#{cycleSort},</if>
        </trim>
    </insert>

    <update id="updateIrrigationCropCycle" parameterType="com.jhdr.irrigation.entity.param.IrrigationCropCycleEditParam">
        update irrigation_crop_cycle
        <trim prefix="SET" suffixOverrides=",">
                    <if test="cropId != null">crop_id = #{cropId},</if>
                    <if test="cropName != null">crop_name = #{cropName},</if>
                    <if test="cycleName != null">cycle_name = #{cycleName},</if>
                    <if test="cycleDescription != null">cycle_description = #{cycleDescription},</if>
                    <if test="waterRequirement != null">water_requirement = #{waterRequirement},</if>
                    <if test="startDate != null">start_date = #{startDate},</if>
                    <if test="endDate != null">end_date = #{endDate},</if>
                    <if test="cycleDay != null">cycle_day = #{cycleDay},</if>
                    <if test="cycleSort != null">cycle_sort = #{cycleSort},</if>
        </trim>
        where cycle_id = #{cycleId}
    </update>

    <delete id="deleteIrrigationCropCycleByCycleId" parameterType="Long">
        delete from irrigation_crop_cycle where cycle_id = #{cycleId}
    </delete>

    <delete id="deleteIrrigationCropCycleByCycleIds" parameterType="String">
        delete from irrigation_crop_cycle where cycle_id in
        <foreach item="cycleId" collection="array" open="(" separator="," close=")">
            #{cycleId}
        </foreach>
    </delete>
</mapper>
