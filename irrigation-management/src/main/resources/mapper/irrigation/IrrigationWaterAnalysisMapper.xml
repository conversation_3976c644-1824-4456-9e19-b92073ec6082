<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationWaterAnalysisMapper">

    <!-- 根据测站编码和水位查询库容 -->
    <select id="getCapacityByWaterLevel" resultType="java.lang.Double">
        SELECT
            W
        FROM
            chxh.st_zvarl_b
        WHERE
            STCD = #{stcd}
            AND RZ &lt;= #{waterLevel}
        ORDER BY
            RZ DESC
        LIMIT 1
    </select>

    <!-- 根据测站编码和库容查询水位 -->
    <select id="getWaterLevelByCapacity" resultType="java.lang.Double">
        SELECT
            RZ
        FROM
            chxh.st_zvarl_b
        WHERE
            STCD = #{stcd}
            AND W &lt;= #{capacity}
        ORDER BY
            W DESC
        LIMIT 1
    </select>

    <!-- 获取库容曲线数据 -->
    <select id="getCapacityCurveData" resultType="java.util.Map">
        SELECT
            RZ as waterLevel,
            W as capacity
        FROM
            chxh.st_zvarl_b
        WHERE
            STCD = #{stcd}
        ORDER BY
            RZ ASC
    </select>

    <!-- 保存降雨数据 -->
    <insert id="saveRainfallData">
        INSERT INTO st_pptn_r (
            STCD,
            TM,
            DRP
        ) VALUES (
            #{stcd},
            #{tm},
            #{drp}
        )
    </insert>

    <!-- 保存河道水位数据 -->
    <insert id="saveWaterLevelData">
        INSERT INTO st_river_r (
            STCD,
            TM,
            Z
        ) VALUES (
            #{stcd},
            #{tm},
            #{z}
        )
    </insert>

    <!-- 保存枢纽流量数据 -->
    <insert id="saveHubFlowData">
        INSERT INTO jh_flowsp_r (
            STCD,
            TM,
            QTYPE,
            PSQ
        ) VALUES (
            #{stcd},
            #{tm},
            'REAL',
            #{q}
        )
    </insert>

</mapper>
