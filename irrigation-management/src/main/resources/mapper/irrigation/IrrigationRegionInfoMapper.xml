<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.IrrigationRegionInfoMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.IrrigationRegionInfoPo" id="IrrigationRegionInfoResult">
        <result property="id"    column="id"    />
        <result property="irrigationCode"    column="irrigation_code"    />
        <result property="irrigationName"    column="irrigation_name"    />
        <result property="irrigationArea"    column="irrigation_area"    />
        <result property="mainCropTypes"    column="main_crop_types"    />
        <result property="irrigationPeriod"    column="irrigation_period"    />
        <result property="mainIrrigationMethods"    column="main_irrigation_methods"    />
        <result property="mainCanalNum"    column="main_canal_num"    />
        <result property="branchCanalNum"    column="branch_canal_num"    />
        <result property="pumpStationNum"    column="pump_station_num"    />
        <result property="gateStationNum"    column="gate_station_num"    />
        <result property="managementUnit"    column="management_unit"    />
        <result property="pond"    column="pond"    />
        <result property="reservoir"    column="reservoir"    />
        <result property="waterSource"    column="water_source"    />
    </resultMap>

    <sql id="selectIrrigationRegionInfoVo">
        select id, irrigation_code, irrigation_name, irrigation_area, main_crop_types, irrigation_period, main_irrigation_methods, main_canal_num, branch_canal_num, pump_station_num, gate_station_num, management_unit, pond, reservoir, water_source from irrigation_region_info
    </sql>

    <select id="selectIrrigationRegionInfoList"  resultType="com.jhdr.irrigation.entity.vo.IrrigationRegionInfoVo">
        <include refid="selectIrrigationRegionInfoVo"/>
        <where>
            <if test="irrigationCode != null  and irrigationCode != ''"> and irrigation_code = #{irrigationCode}</if>
            <if test="irrigationName != null  and irrigationName != ''"> and irrigation_name like concat('%', #{irrigationName}, '%')</if>
            <if test="irrigationArea != null "> and irrigation_area = #{irrigationArea}</if>
            <if test="mainCropTypes != null  and mainCropTypes != ''"> and main_crop_types = #{mainCropTypes}</if>
            <if test="irrigationPeriod != null  and irrigationPeriod != ''"> and irrigation_period = #{irrigationPeriod}</if>
            <if test="mainIrrigationMethods != null  and mainIrrigationMethods != ''"> and main_irrigation_methods = #{mainIrrigationMethods}</if>
            <if test="mainCanalNum != null  and mainCanalNum != ''"> and main_canal_num = #{mainCanalNum}</if>
            <if test="branchCanalNum != null  and branchCanalNum != ''"> and branch_canal_num = #{branchCanalNum}</if>
            <if test="pumpStationNum != null  and pumpStationNum != ''"> and pump_station_num = #{pumpStationNum}</if>
            <if test="gateStationNum != null  and gateStationNum != ''"> and gate_station_num = #{gateStationNum}</if>
            <if test="managementUnit != null  and managementUnit != ''"> and management_unit = #{managementUnit}</if>
            <if test="pond != null  and pond != ''"> and pond = #{pond}</if>
            <if test="reservoir != null  and reservoir != ''"> and reservoir = #{reservoir}</if>
            <if test="waterSource != null  and waterSource != ''"> and water_source = #{waterSource}</if>
        </where>
    </select>

    <select id="selectIrrigationRegionInfoById"  resultType="com.jhdr.irrigation.entity.vo.IrrigationRegionInfoVo">
            <include refid="selectIrrigationRegionInfoVo"/>
            where id = #{id}
    </select>

    <insert id="insertIrrigationRegionInfo" parameterType="com.jhdr.irrigation.entity.param.IrrigationRegionInfoAddParam">
        insert into irrigation_region_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="irrigationCode != null">irrigation_code,</if>
                    <if test="irrigationName != null">irrigation_name,</if>
                    <if test="irrigationArea != null">irrigation_area,</if>
                    <if test="mainCropTypes != null">main_crop_types,</if>
                    <if test="irrigationPeriod != null">irrigation_period,</if>
                    <if test="mainIrrigationMethods != null">main_irrigation_methods,</if>
                    <if test="mainCanalNum != null">main_canal_num,</if>
                    <if test="branchCanalNum != null">branch_canal_num,</if>
                    <if test="pumpStationNum != null">pump_station_num,</if>
                    <if test="gateStationNum != null">gate_station_num,</if>
                    <if test="managementUnit != null">management_unit,</if>
                    <if test="pond != null">pond,</if>
                    <if test="reservoir != null">reservoir,</if>
                    <if test="waterSource != null">water_source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="irrigationCode != null">#{irrigationCode},</if>
                    <if test="irrigationName != null">#{irrigationName},</if>
                    <if test="irrigationArea != null">#{irrigationArea},</if>
                    <if test="mainCropTypes != null">#{mainCropTypes},</if>
                    <if test="irrigationPeriod != null">#{irrigationPeriod},</if>
                    <if test="mainIrrigationMethods != null">#{mainIrrigationMethods},</if>
                    <if test="mainCanalNum != null">#{mainCanalNum},</if>
                    <if test="branchCanalNum != null">#{branchCanalNum},</if>
                    <if test="pumpStationNum != null">#{pumpStationNum},</if>
                    <if test="gateStationNum != null">#{gateStationNum},</if>
                    <if test="managementUnit != null">#{managementUnit},</if>
                    <if test="pond != null">#{pond},</if>
                    <if test="reservoir != null">#{reservoir},</if>
                    <if test="waterSource != null">#{waterSource},</if>
        </trim>
    </insert>

    <update id="updateIrrigationRegionInfo" parameterType="com.jhdr.irrigation.entity.param.IrrigationRegionInfoEditParam">
        update irrigation_region_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="irrigationCode != null">irrigation_code = #{irrigationCode},</if>
                    <if test="irrigationName != null">irrigation_name = #{irrigationName},</if>
                    <if test="irrigationArea != null">irrigation_area = #{irrigationArea},</if>
                    <if test="mainCropTypes != null">main_crop_types = #{mainCropTypes},</if>
                    <if test="irrigationPeriod != null">irrigation_period = #{irrigationPeriod},</if>
                    <if test="mainIrrigationMethods != null">main_irrigation_methods = #{mainIrrigationMethods},</if>
                    <if test="mainCanalNum != null">main_canal_num = #{mainCanalNum},</if>
                    <if test="branchCanalNum != null">branch_canal_num = #{branchCanalNum},</if>
                    <if test="pumpStationNum != null">pump_station_num = #{pumpStationNum},</if>
                    <if test="gateStationNum != null">gate_station_num = #{gateStationNum},</if>
                    <if test="managementUnit != null">management_unit = #{managementUnit},</if>
                    <if test="pond != null">pond = #{pond},</if>
                    <if test="reservoir != null">reservoir = #{reservoir},</if>
                    <if test="waterSource != null">water_source = #{waterSource},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIrrigationRegionInfoById" parameterType="Long">
        delete from irrigation_region_info where id = #{id}
    </delete>

    <delete id="deleteIrrigationRegionInfoByIds" parameterType="String">
        delete from irrigation_region_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
