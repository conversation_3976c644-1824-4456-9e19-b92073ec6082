<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.WaterSchedulePlanLevelMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.WaterSchedulePlanLevelPo" id="WaterSchedulePlanLevelResult">
        <result property="pumpId"    column="pump_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="gateStationCode"    column="gate_station_code"    />
        <result property="realWater"    column="real_water"    />
        <result property="realTime"    column="real_time"    />
    </resultMap>

    <sql id="selectWaterSchedulePlanLevelVo">
        select pump_id, plan_id, gate_station_code, real_water, real_time from water_schedule_plan_level
    </sql>

    <select id="selectWaterSchedulePlanLevelList"  resultType="com.jhdr.irrigation.entity.vo.WaterSchedulePlanLevelVo">
        <include refid="selectWaterSchedulePlanLevelVo"/>
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="gateStationCode != null  and gateStationCode != ''"> and gate_station_code = #{gateStationCode}</if>
            <if test="realWater != null  and realWater != ''"> and real_water = #{realWater}</if>
            <if test="realTime != null  and realTime != ''"> and real_time = #{realTime}</if>
        </where>
    </select>

    <select id="selectWaterSchedulePlanLevelByPumpId"  resultType="com.jhdr.irrigation.entity.vo.WaterSchedulePlanLevelVo">
            <include refid="selectWaterSchedulePlanLevelVo"/>
            where pump_id = #{pumpId}
    </select>

    <insert id="insertWaterSchedulePlanLevel" parameterType="com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelAddParam">
        insert into water_schedule_plan_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="pumpId != null">pump_id,</if>
                    <if test="planId != null">plan_id,</if>
                    <if test="gateStationCode != null">gate_station_code,</if>
                    <if test="realWater != null">real_water,</if>
                    <if test="realTime != null">real_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="pumpId != null">#{pumpId},</if>
                    <if test="planId != null">#{planId},</if>
                    <if test="gateStationCode != null">#{gateStationCode},</if>
                    <if test="realWater != null">#{realWater},</if>
                    <if test="realTime != null">#{realTime},</if>
        </trim>
    </insert>

    <update id="updateWaterSchedulePlanLevel" parameterType="com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelEditParam">
        update water_schedule_plan_level
        <trim prefix="SET" suffixOverrides=",">
                    <if test="planId != null">plan_id = #{planId},</if>
                    <if test="gateStationCode != null">gate_station_code = #{gateStationCode},</if>
                    <if test="realWater != null">real_water = #{realWater},</if>
                    <if test="realTime != null">real_time = #{realTime},</if>
        </trim>
        where pump_id = #{pumpId}
    </update>

    <delete id="deleteWaterSchedulePlanLevelByPumpId" parameterType="Long">
        delete from water_schedule_plan_level where pump_id = #{pumpId}
    </delete>

    <delete id="deleteWaterSchedulePlanLevelByPumpIds" parameterType="String">
        delete from water_schedule_plan_level where pump_id in
        <foreach item="pumpId" collection="array" open="(" separator="," close=")">
            #{pumpId}
        </foreach>
    </delete>
</mapper>
