<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jhdr.irrigation.mapper.WaterSchedulePlanPumpMapper">

    <resultMap type="com.jhdr.irrigation.entity.po.WaterSchedulePlanPumpPo" id="WaterSchedulePlanPumpResult">
        <result property="levelId"    column="level_id"    />
        <result property="planId"    column="plan_id"    />
        <result property="pumpCode"    column="pump_code"    />
        <result property="irrigationCode"    column="irrigation_code"    />
        <result property="pumpName"    column="pump_name"    />
        <result property="waterDemand"    column="water_demand"    />
        <result property="runTime"    column="run_time"    />
    </resultMap>

    <sql id="selectWaterSchedulePlanPumpVo">
        select level_id, plan_id, pump_code, irrigation_code, pump_name, water_demand, run_time from water_schedule_plan_pump
    </sql>

    <select id="selectWaterSchedulePlanPumpList"  resultType="com.jhdr.irrigation.entity.vo.WaterSchedulePlanPumpVo">
        <include refid="selectWaterSchedulePlanPumpVo"/>
        <where>
            <if test="planId != null "> and plan_id = #{planId}</if>
            <if test="pumpCode != null  and pumpCode != ''"> and pump_code = #{pumpCode}</if>
            <if test="irrigationCode != null  and irrigationCode != ''"> and irrigation_code = #{irrigationCode}</if>
            <if test="pumpName != null  and pumpName != ''"> and pump_name like concat('%', #{pumpName}, '%')</if>
            <if test="waterDemand != null "> and water_demand = #{waterDemand}</if>
            <if test="runTime != null "> and run_time = #{runTime}</if>
        </where>
    </select>

    <select id="selectWaterSchedulePlanPumpByLevelId"  resultType="com.jhdr.irrigation.entity.vo.WaterSchedulePlanPumpVo">
            <include refid="selectWaterSchedulePlanPumpVo"/>
            where level_id = #{levelId}
    </select>

    <insert id="insertWaterSchedulePlanPump" parameterType="com.jhdr.irrigation.entity.param.WaterSchedulePlanPumpAddParam">
        insert into water_schedule_plan_pump
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="levelId != null">level_id,</if>
                    <if test="planId != null">plan_id,</if>
                    <if test="pumpCode != null">pump_code,</if>
                    <if test="irrigationCode != null">irrigation_code,</if>
                    <if test="pumpName != null">pump_name,</if>
                    <if test="waterDemand != null">water_demand,</if>
                    <if test="runTime != null">run_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="levelId != null">#{levelId},</if>
                    <if test="planId != null">#{planId},</if>
                    <if test="pumpCode != null">#{pumpCode},</if>
                    <if test="irrigationCode != null">#{irrigationCode},</if>
                    <if test="pumpName != null">#{pumpName},</if>
                    <if test="waterDemand != null">#{waterDemand},</if>
                    <if test="runTime != null">#{runTime},</if>
        </trim>
    </insert>

    <update id="updateWaterSchedulePlanPump" parameterType="com.jhdr.irrigation.entity.param.WaterSchedulePlanPumpEditParam">
        update water_schedule_plan_pump
        <trim prefix="SET" suffixOverrides=",">
                    <if test="planId != null">plan_id = #{planId},</if>
                    <if test="pumpCode != null">pump_code = #{pumpCode},</if>
                    <if test="irrigationCode != null">irrigation_code = #{irrigationCode},</if>
                    <if test="pumpName != null">pump_name = #{pumpName},</if>
                    <if test="waterDemand != null">water_demand = #{waterDemand},</if>
                    <if test="runTime != null">run_time = #{runTime},</if>
        </trim>
        where level_id = #{levelId}
    </update>

    <delete id="deleteWaterSchedulePlanPumpByLevelId" parameterType="Long">
        delete from water_schedule_plan_pump where level_id = #{levelId}
    </delete>

    <delete id="deleteWaterSchedulePlanPumpByLevelIds" parameterType="String">
        delete from water_schedule_plan_pump where level_id in
        <foreach item="levelId" collection="array" open="(" separator="," close=")">
            #{levelId}
        </foreach>
    </delete>
</mapper>
