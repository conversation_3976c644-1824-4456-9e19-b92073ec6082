package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.IrrigationPlanPo;
import com.jhdr.irrigation.entity.param.IrrigationPlanParam;
import com.jhdr.irrigation.entity.param.IrrigationPlanAddParam;
import com.jhdr.irrigation.entity.param.IrrigationPlanEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationPlanVo;
import com.jhdr.irrigation.mapper.IrrigationPlanMapper;
import com.jhdr.irrigation.service.IIrrigationPlanService;

import java.util.ArrayList;

import java.util.List;

/**
 * 灌溉计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class IrrigationPlanServiceImpl extends ServiceImpl<IrrigationPlanMapper, IrrigationPlanPo> implements IIrrigationPlanService {

    @Override
    public List<IrrigationPlanVo> queryList(IrrigationPlanPo irrigationPlan) {
        LambdaQueryWrapper<IrrigationPlanPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(irrigationPlan.getPlanName())){
            lqw.like(IrrigationPlanPo::getPlanName ,irrigationPlan.getPlanName());
        }
        if (StringUtils.isNotBlank(irrigationPlan.getPlanWay())){
            lqw.eq(IrrigationPlanPo::getPlanWay ,irrigationPlan.getPlanWay());
        }
        if (StringUtils.isNotBlank(irrigationPlan.getPlanWater())){
            lqw.eq(IrrigationPlanPo::getPlanWater ,irrigationPlan.getPlanWater());
        }
        if (StringUtils.isNotBlank(irrigationPlan.getPlanDate())){
            lqw.eq(IrrigationPlanPo::getPlanDate ,irrigationPlan.getPlanDate());
        }
        List<IrrigationPlanVo> irrigationPlanVos= BeanUtil.copyToList(this.list(lqw), IrrigationPlanVo.class);
        return irrigationPlanVos;
    }
    /**
     * 查询灌溉计划
     *
     * @param id 灌溉计划主键
     * @return 灌溉计划
     */
    @Override
    public IrrigationPlanVo selectIrrigationPlanById(Long id)
    {
        return baseMapper.selectIrrigationPlanById(id);
    }

    /**
     * 查询灌溉计划列表
     *
     * @param irrigationPlan 灌溉计划
     * @return 灌溉计划
     */
    @Override
    public List<IrrigationPlanVo> selectIrrigationPlanList(IrrigationPlanParam irrigationPlan)
    {
        return baseMapper.selectIrrigationPlanList(irrigationPlan);
    }

    /**
     * 新增灌溉计划
     *
     * @param irrigationPlanAddParam 灌溉计划
     * @return 结果
     */
    @Override
    public int insertIrrigationPlan(IrrigationPlanAddParam irrigationPlanAddParam)
    {

        IrrigationPlanPo irrigationPlan=new IrrigationPlanPo();
        BeanUtil.copyProperties(irrigationPlanAddParam,irrigationPlan);
        return baseMapper.insert(irrigationPlan);
    }

    /**
     * 修改灌溉计划
     *
     * @param irrigationPlanEditParam 灌溉计划
     * @return 结果
     */
    @Override
    public int updateIrrigationPlan(IrrigationPlanEditParam irrigationPlanEditParam)
    {
        IrrigationPlanPo irrigationPlan=new IrrigationPlanPo();
        BeanUtil.copyProperties(irrigationPlanEditParam,irrigationPlan);
        return baseMapper.updateById(irrigationPlan);
    }

    /**
     * 批量删除灌溉计划
     *
     * @param ids 需要删除的灌溉计划主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationPlanByIds(Long[] ids)
    {
        return baseMapper.deleteIrrigationPlanByIds(ids);
    }

    /**
     * 删除灌溉计划信息
     *
     * @param id 灌溉计划主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationPlanById(Long id)
    {
        return baseMapper.deleteIrrigationPlanById(id);
    }
}
