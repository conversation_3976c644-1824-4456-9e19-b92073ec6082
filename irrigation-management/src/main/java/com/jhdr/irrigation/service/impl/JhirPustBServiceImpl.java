package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.jhdr.irrigation.entity.vo.JhirPustRegionVo;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.JhirPustBPo;
import com.jhdr.irrigation.entity.param.JhirPustBParam;
import com.jhdr.irrigation.entity.param.JhirPustBAddParam;
import com.jhdr.irrigation.entity.param.JhirPustBEditParam;
import com.jhdr.irrigation.entity.vo.JhirPustBVo;
import com.jhdr.irrigation.mapper.JhirPustBMapper;
import com.jhdr.irrigation.service.IJhirPustBService;

import java.util.ArrayList;

import java.util.List;

/**
 * 泵站基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class JhirPustBServiceImpl extends ServiceImpl<JhirPustBMapper, JhirPustBPo> implements IJhirPustBService {


    /**
     * 查询泵站基本信息
     *
     * @param strCode 泵站基本信息主键
     * @return 泵站基本信息
     */
    @Override
    public JhirPustBVo selectJhirPustBByStrCode(String strCode)
    {
        return baseMapper.selectJhirPustBByStrCode(strCode);
    }

    /**
     * 查询泵站基本信息列表
     *
     * @param jhirPustB 泵站基本信息
     * @return 泵站基本信息
     */
    @Override
    public List<JhirPustBVo> selectJhirPustBList(JhirPustBParam jhirPustB)
    {
        return baseMapper.selectJhirPustBList(jhirPustB);
    }

    /**
     * 新增泵站基本信息
     *
     * @param jhirPustBAddParam 泵站基本信息
     * @return 结果
     */
    @Override
    public int insertJhirPustB(JhirPustBAddParam jhirPustBAddParam)
    {

        JhirPustBPo jhirPustB=new JhirPustBPo();
        BeanUtil.copyProperties(jhirPustBAddParam,jhirPustB);
        return baseMapper.insert(jhirPustB);
    }

    /**
     * 修改泵站基本信息
     *
     * @param jhirPustBEditParam 泵站基本信息
     * @return 结果
     */
    @Override
    public int updateJhirPustB(JhirPustBEditParam jhirPustBEditParam)
    {
        JhirPustBPo jhirPustB=new JhirPustBPo();
        BeanUtil.copyProperties(jhirPustBEditParam,jhirPustB);
        return baseMapper.updateById(jhirPustB);
    }

    /**
     * 批量删除泵站基本信息
     *
     * @param strCodes 需要删除的泵站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirPustBByStrCodes(String[] strCodes)
    {
        return baseMapper.deleteJhirPustBByStrCodes(strCodes);
    }

    /**
     * 删除泵站基本信息信息
     *
     * @param strCode 泵站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteJhirPustBByStrCode(String strCode)
    {
        return baseMapper.deleteJhirPustBByStrCode(strCode);
    }

    /**
     * 查询 pump_num
     *
     * @return 结果
     */
    @Override
    public List<JhirPustRegionVo> selectRegionList() {
        return baseMapper.selectRegionList();
    }
}
