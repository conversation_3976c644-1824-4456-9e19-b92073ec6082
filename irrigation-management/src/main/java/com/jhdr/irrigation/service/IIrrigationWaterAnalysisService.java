package com.jhdr.irrigation.service;

import com.jhdr.irrigation.entity.param.WaterAnalysisParam;
import com.jhdr.irrigation.entity.param.WaterAnalysisSimpleParam;
import com.jhdr.irrigation.entity.vo.AvailableWaterVo;
import com.jhdr.irrigation.entity.vo.RegionWaterProductionVo;
import com.jhdr.irrigation.entity.vo.WaterInflowAnalysisVo;

/**
 * 灌区水情分析服务接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface IIrrigationWaterAnalysisService {

    /**
     * 获取阚疃节制闸断面来水情况分析
     *
     * @param param 水情分析参数
     * @return 阚疃节制闸断面来水情况分析结果
     */
    WaterInflowAnalysisVo getWaterInflowAnalysis(WaterAnalysisParam param);

    /**
     * 获取片区产水量分析
     *
     * @param param 水情分析参数
     * @return 片区产水量分析结果
     */
    RegionWaterProductionVo getRegionWaterProduction(WaterAnalysisParam param);

    /**
     * 获取可供水量分析
     *
     * @param param 水情分析参数
     * @return 可供水量分析结果
     */
    AvailableWaterVo getAvailableWater(WaterAnalysisParam param);

    /**
     * 获取阚疃节制闸断面来水情况分析（简化版）
     *
     * @param param 水情分析简化参数
     * @return 阚疃节制闸断面来水情况分析结果
     */
    WaterInflowAnalysisVo getWaterInflowAnalysisSimple(WaterAnalysisSimpleParam param);

    /**
     * 获取片区产水量分析（简化版）
     *
     * @param param 水情分析简化参数
     * @return 片区产水量分析结果
     */
    RegionWaterProductionVo getRegionWaterProductionSimple(WaterAnalysisSimpleParam param);

    /**
     * 获取可供水量分析（简化版）
     *
     * @param param 水情分析简化参数
     * @return 可供水量分析结果
     */
    AvailableWaterVo getAvailableWaterSimple(WaterAnalysisSimpleParam param);
}
