package com.jhdr.irrigation.service;

import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.vo.CropCycleVo;
import com.jhdr.irrigation.entity.vo.DroughtRiskConfigVo;
import com.jhdr.irrigation.entity.vo.IrrigationConfigTypeVo;
import com.jhdr.irrigation.entity.vo.IrrigationPeriodVo;
import com.jhdr.irrigation.entity.vo.PlantingStructureVo;
import com.jhdr.irrigation.entity.vo.PumpStationVo;
import com.jhdr.irrigation.entity.vo.WaterCoefficientConfigVo;
import com.jhdr.irrigation.entity.vo.WaterLevelGuaranteeVo;
import com.jhdr.irrigation.entity.vo.WaterPriorityConfigVo;
import com.jhdr.irrigation.entity.vo.WaterUsageVo;
import com.jhdr.irrigation.entity.vo.WeatherDataConfigVo;

import java.util.List;

/**
 * 灌区配置类型 服务接口
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
public interface IIrrigationConfigTypeService {

    /**
     * 查询灌区配置类型列表
     *
     * @param param 查询参数
     * @return 灌区配置类型列表
     */
    List<IrrigationConfigTypeVo> selectIrrigationConfigTypeList(IrrigationConfigTypeParam param);

    /**
     * 根据ID查询灌区配置类型详情
     *
     * @param id 配置类型ID
     * @return 灌区配置类型详情
     */
    IrrigationConfigTypeVo selectIrrigationConfigTypeById(Long id);

    /**
     * 新增灌区配置类型
     *
     * @param param 新增参数
     * @return 结果
     */
    int insertIrrigationConfigType(IrrigationConfigTypeAddParam param);

    /**
     * 修改灌区配置类型
     *
     * @param param 修改参数
     * @return 结果
     */
    int updateIrrigationConfigType(IrrigationConfigTypeEditParam param);

    /**
     * 删除灌区配置类型
     *
     * @param id 配置类型ID
     * @return 结果
     */
    int deleteIrrigationConfigTypeById(Long id);

    /**
     * 批量删除灌区配置类型
     *
     * @param ids 需要删除的配置类型ID数组
     * @return 结果
     */
    int deleteIrrigationConfigTypeByIds(Long[] ids);

    /**
     * 获取气象数据配置
     *
     * @return 气象数据配置
     */
    WeatherDataConfigVo getWeatherDataConfig();

    /**
     * 保存气象数据配置
     *
     * @param param 气象数据配置参数
     * @return 结果
     */
    int saveWeatherDataConfig(WeatherDataConfigParam param);

    /**
     * 获取干旱风险配置
     *
     * @return 干旱风险配置
     */
    DroughtRiskConfigVo getDroughtRiskConfig();

    /**
     * 保存干旱风险配置
     *
     * @param param 干旱风险配置参数
     * @return 结果
     */
    int saveDroughtRiskConfig(DroughtRiskConfigParam param);

    /**
     * 获取水情年设置
     *
     * @return 水情年设置
     */
    WaterCoefficientConfigVo getWaterCoefficientConfig();

    /**
     * 保存水情年设置
     *
     * @param param 水情年设置参数
     * @return 结果
     */
    int saveWaterCoefficientConfig(WaterCoefficientConfigParam param);

    /**
     * 获取种植结构列表
     *
     * @return 种植结构列表
     */
    List<PlantingStructureVo> getPlantingStructureList();

    /**
     * 保存种植结构
     *
     * @param param 种植结构参数
     * @return 结果
     */
    int savePlantingStructure(PlantingStructureParam param);

    /**
     * 批量保存种植结构
     *
     * @param param 种植结构批量参数
     * @return 结果
     */
    int batchSavePlantingStructure(PlantingStructureBatchParam param);

    /**
     * 获取生活用水列表
     *
     * @return 生活用水列表
     */
    List<WaterUsageVo> getWaterUsageList();

    /**
     * 保存生活用水
     *
     * @param param 生活用水参数
     * @return 结果
     */
    int saveWaterUsage(WaterUsageParam param);

    /**
     * 批量保存生活用水
     *
     * @param param 生活用水批量参数
     * @return 结果
     */
    int batchSaveWaterUsage(WaterUsageBatchParam param);

    /**
     * 删除生活用水
     *
     * @param year 年份
     * @return 结果
     */
    int deleteWaterUsage(String year);

    /**
     * 获取水资源配置优先级
     *
     * @return 水资源配置优先级
     */
    WaterPriorityConfigVo getWaterPriorityConfig();

    /**
     * 保存水资源配置优先级
     *
     * @param param 水资源配置优先级参数
     * @return 结果
     */
    int saveWaterPriorityConfig(WaterPriorityConfigParam param);

    /**
     * 获取调度泵站列表
     *
     * @return 调度泵站列表
     */
    List<PumpStationVo> getPumpStationList();

    /**
     * 保存调度泵站
     *
     * @param param 调度泵站参数
     * @return 结果
     */
    int savePumpStation(PumpStationParam param);

    /**
     * 批量保存调度泵站
     *
     * @param param 调度泵站批量参数
     * @return 结果
     */
    int batchSavePumpStation(PumpStationBatchParam param);

    /**
     * 获取灌溉期时间范围
     *
     * @return 灌溉期时间范围
     */
    IrrigationPeriodVo getIrrigationPeriod();

    /**
     * 保存灌溉期时间范围
     *
     * @param param 灌溉期时间范围参数
     * @return 结果
     */
    int saveIrrigationPeriod(IrrigationPeriodParam param);

    /**
     * 获取水位保证
     *
     * @return 水位保证
     */
    WaterLevelGuaranteeVo getWaterLevelGuarantee();

    /**
     * 保存水位保证
     *
     * @param param 水位保证参数
     * @return 结果
     */
    int saveWaterLevelGuarantee(WaterLevelGuaranteeParam param);

    /**
     * 获取农作物周期信息列表
     *
     * @param cropId 作物ID，可为null，为null时查询所有作物
     * @return 农作物周期信息列表
     */
    List<CropCycleVo> getCropCycleList(Long cropId);

    /**
     * 保存农作物周期信息
     *
     * @param param 农作物周期信息参数
     * @return 结果
     */
    int saveCropCycle(CropCycleParam param);

    /**
     * 批量保存农作物周期信息
     *
     * @param param 农作物周期信息批量参数
     * @return 结果
     */
    int batchSaveCropCycle(CropCycleBatchParam param);

    /**
     * 删除农作物周期信息
     *
     * @param cycleId 周期ID
     * @return 结果
     */
    int deleteCropCycle(Long cycleId);
}
