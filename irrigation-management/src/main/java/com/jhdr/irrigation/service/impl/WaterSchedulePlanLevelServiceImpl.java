package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.WaterSchedulePlanLevelPo;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelAddParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelEditParam;
import com.jhdr.irrigation.entity.vo.WaterSchedulePlanLevelVo;
import com.jhdr.irrigation.mapper.WaterSchedulePlanLevelMapper;
import com.jhdr.irrigation.service.IWaterSchedulePlanLevelService;

import java.util.ArrayList;

import java.util.List;

/**
 * 灌区管理-调度方案水位历史数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Service
public class WaterSchedulePlanLevelServiceImpl extends ServiceImpl<WaterSchedulePlanLevelMapper, WaterSchedulePlanLevelPo> implements IWaterSchedulePlanLevelService {

    @Override
    public List<WaterSchedulePlanLevelVo> queryList(WaterSchedulePlanLevelPo waterSchedulePlanLevel) {
        LambdaQueryWrapper<WaterSchedulePlanLevelPo> lqw = Wrappers.lambdaQuery();
        if (waterSchedulePlanLevel.getPlanId() != null){
            lqw.eq(WaterSchedulePlanLevelPo::getPlanId ,waterSchedulePlanLevel.getPlanId());
        }
        if (StringUtils.isNotBlank(waterSchedulePlanLevel.getGateStationCode())){
            lqw.eq(WaterSchedulePlanLevelPo::getGateStationCode ,waterSchedulePlanLevel.getGateStationCode());
        }
        if (StringUtils.isNotBlank(waterSchedulePlanLevel.getRealWater())){
            lqw.eq(WaterSchedulePlanLevelPo::getRealWater ,waterSchedulePlanLevel.getRealWater());
        }
        if (StringUtils.isNotBlank(waterSchedulePlanLevel.getRealTime())){
            lqw.eq(WaterSchedulePlanLevelPo::getRealTime ,waterSchedulePlanLevel.getRealTime());
        }
        List<WaterSchedulePlanLevelVo> waterSchedulePlanLevelVos= BeanUtil.copyToList(this.list(lqw), WaterSchedulePlanLevelVo.class);
        return waterSchedulePlanLevelVos;
    }
    /**
     * 查询灌区管理-调度方案水位历史数据
     *
     * @param pumpId 灌区管理-调度方案水位历史数据主键
     * @return 灌区管理-调度方案水位历史数据
     */
    @Override
    public WaterSchedulePlanLevelVo selectWaterSchedulePlanLevelByPumpId(Long pumpId)
    {
        return baseMapper.selectWaterSchedulePlanLevelByPumpId(pumpId);
    }

    /**
     * 查询灌区管理-调度方案水位历史数据列表
     *
     * @param waterSchedulePlanLevel 灌区管理-调度方案水位历史数据
     * @return 灌区管理-调度方案水位历史数据
     */
    @Override
    public List<WaterSchedulePlanLevelVo> selectWaterSchedulePlanLevelList(WaterSchedulePlanLevelParam waterSchedulePlanLevel)
    {
        return baseMapper.selectWaterSchedulePlanLevelList(waterSchedulePlanLevel);
    }

    /**
     * 新增灌区管理-调度方案水位历史数据
     *
     * @param waterSchedulePlanLevelAddParam 灌区管理-调度方案水位历史数据
     * @return 结果
     */
    @Override
    public int insertWaterSchedulePlanLevel(WaterSchedulePlanLevelAddParam waterSchedulePlanLevelAddParam)
    {

        WaterSchedulePlanLevelPo waterSchedulePlanLevel=new WaterSchedulePlanLevelPo();
        BeanUtil.copyProperties(waterSchedulePlanLevelAddParam,waterSchedulePlanLevel);
        return baseMapper.insert(waterSchedulePlanLevel);
    }

    /**
     * 修改灌区管理-调度方案水位历史数据
     *
     * @param waterSchedulePlanLevelEditParam 灌区管理-调度方案水位历史数据
     * @return 结果
     */
    @Override
    public int updateWaterSchedulePlanLevel(WaterSchedulePlanLevelEditParam waterSchedulePlanLevelEditParam)
    {
        WaterSchedulePlanLevelPo waterSchedulePlanLevel=new WaterSchedulePlanLevelPo();
        BeanUtil.copyProperties(waterSchedulePlanLevelEditParam,waterSchedulePlanLevel);
        return baseMapper.updateById(waterSchedulePlanLevel);
    }

    /**
     * 批量删除灌区管理-调度方案水位历史数据
     *
     * @param pumpIds 需要删除的灌区管理-调度方案水位历史数据主键
     * @return 结果
     */
    @Override
    public int deleteWaterSchedulePlanLevelByPumpIds(Long[] pumpIds)
    {
        return baseMapper.deleteWaterSchedulePlanLevelByPumpIds(pumpIds);
    }

    /**
     * 删除灌区管理-调度方案水位历史数据信息
     *
     * @param pumpId 灌区管理-调度方案水位历史数据主键
     * @return 结果
     */
    @Override
    public int deleteWaterSchedulePlanLevelByPumpId(Long pumpId)
    {
        return baseMapper.deleteWaterSchedulePlanLevelByPumpId(pumpId);
    }
}
