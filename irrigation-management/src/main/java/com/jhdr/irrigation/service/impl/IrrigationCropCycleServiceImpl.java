package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.IrrigationCropCyclePo;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleParam;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleAddParam;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationCropCycleVo;
import com.jhdr.irrigation.mapper.IrrigationCropCycleMapper;
import com.jhdr.irrigation.service.IIrrigationCropCycleService;

import java.util.ArrayList;

import java.util.List;

/**
 * 灌区作物周期Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
@Service
public class IrrigationCropCycleServiceImpl extends ServiceImpl<IrrigationCropCycleMapper, IrrigationCropCyclePo> implements IIrrigationCropCycleService {

    @Override
    public List<IrrigationCropCycleVo> queryList(IrrigationCropCyclePo irrigationCropCycle) {
        LambdaQueryWrapper<IrrigationCropCyclePo> lqw = Wrappers.lambdaQuery();
        if (irrigationCropCycle.getCropId() != null){
            lqw.eq(IrrigationCropCyclePo::getCropId ,irrigationCropCycle.getCropId());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getCropName())){
            lqw.like(IrrigationCropCyclePo::getCropName ,irrigationCropCycle.getCropName());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getCycleName())){
            lqw.like(IrrigationCropCyclePo::getCycleName ,irrigationCropCycle.getCycleName());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getCycleDescription())){
            lqw.eq(IrrigationCropCyclePo::getCycleDescription ,irrigationCropCycle.getCycleDescription());
        }
        if (irrigationCropCycle.getWaterRequirement() != null){
            lqw.eq(IrrigationCropCyclePo::getWaterRequirement ,irrigationCropCycle.getWaterRequirement());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getStartDate())){
            lqw.eq(IrrigationCropCyclePo::getStartDate ,irrigationCropCycle.getStartDate());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getEndDate())){
            lqw.eq(IrrigationCropCyclePo::getEndDate ,irrigationCropCycle.getEndDate());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getCycleDay())){
            lqw.eq(IrrigationCropCyclePo::getCycleDay ,irrigationCropCycle.getCycleDay());
        }
        if (StringUtils.isNotBlank(irrigationCropCycle.getCycleSort())){
            lqw.eq(IrrigationCropCyclePo::getCycleSort ,irrigationCropCycle.getCycleSort());
        }
        List<IrrigationCropCycleVo> irrigationCropCycleVos= BeanUtil.copyToList(this.list(lqw), IrrigationCropCycleVo.class);
        return irrigationCropCycleVos;
    }
    /**
     * 查询灌区作物周期
     *
     * @param cycleId 灌区作物周期主键
     * @return 灌区作物周期
     */
    @Override
    public IrrigationCropCycleVo selectIrrigationCropCycleByCycleId(Long cycleId)
    {
        return baseMapper.selectIrrigationCropCycleByCycleId(cycleId);
    }

    /**
     * 查询灌区作物周期列表
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 灌区作物周期
     */
    @Override
    public List<IrrigationCropCycleVo> selectIrrigationCropCycleList(IrrigationCropCycleParam irrigationCropCycle)
    {
        return baseMapper.selectIrrigationCropCycleList(irrigationCropCycle);
    }

    /**
     * 新增灌区作物周期
     *
     * @param irrigationCropCycleAddParam 灌区作物周期
     * @return 结果
     */
    @Override
    public int insertIrrigationCropCycle(IrrigationCropCycleAddParam irrigationCropCycleAddParam)
    {

        IrrigationCropCyclePo irrigationCropCycle=new IrrigationCropCyclePo();
        BeanUtil.copyProperties(irrigationCropCycleAddParam,irrigationCropCycle);
        return baseMapper.insert(irrigationCropCycle);
    }

    /**
     * 修改灌区作物周期
     *
     * @param irrigationCropCycleEditParam 灌区作物周期
     * @return 结果
     */
    @Override
    public int updateIrrigationCropCycle(IrrigationCropCycleEditParam irrigationCropCycleEditParam)
    {
        IrrigationCropCyclePo irrigationCropCycle=new IrrigationCropCyclePo();
        BeanUtil.copyProperties(irrigationCropCycleEditParam,irrigationCropCycle);
        return baseMapper.updateById(irrigationCropCycle);
    }

    /**
     * 批量删除灌区作物周期
     *
     * @param cycleIds 需要删除的灌区作物周期主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationCropCycleByCycleIds(Long[] cycleIds)
    {
        return baseMapper.deleteIrrigationCropCycleByCycleIds(cycleIds);
    }

    /**
     * 删除灌区作物周期信息
     *
     * @param cycleId 灌区作物周期主键
     * @return 结果
     */
    @Override
    public int deleteIrrigationCropCycleByCycleId(Long cycleId)
    {
        return baseMapper.deleteIrrigationCropCycleByCycleId(cycleId);
    }
}
