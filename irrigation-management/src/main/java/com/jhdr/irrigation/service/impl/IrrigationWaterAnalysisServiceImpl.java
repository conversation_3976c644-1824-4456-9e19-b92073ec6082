package com.jhdr.irrigation.service.impl;

import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.irrigation.entity.param.WaterAnalysisParam;
import com.jhdr.irrigation.entity.param.WaterAnalysisSimpleParam;
import com.jhdr.irrigation.entity.vo.AvailableWaterVo;
import com.jhdr.irrigation.entity.vo.RegionWaterProductionVo;
import com.jhdr.irrigation.entity.vo.WaterInflowAnalysisVo;
import com.jhdr.irrigation.mapper.IrrigationWaterAnalysisMapper;
import com.jhdr.irrigation.service.IIrrigationWaterAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 灌区水情分析服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Service
public class IrrigationWaterAnalysisServiceImpl implements IIrrigationWaterAnalysisService {

    @Autowired
    private IrrigationWaterAnalysisMapper irrigationWaterAnalysisMapper;

    // 区域面积（平方公里）
    private static final double HUAIYUAN_AREA = 2500.0;
    private static final double PANJI_AREA = 1800.0;
    private static final double FENGTAI_AREA = 2200.0;
    private static final double MENGCHENG_AREA = 2000.0;

    // 折损率
    private static final double RAINFALL_LOSS_RATE = 0.8; // 降雨折损率
    private static final double WATER_PRODUCTION_RATE = 0.8; // 产水折损率
    private static final double RIVER_INFLOW_RATE = 0.01; // 入河流量比例

    /**
     * 获取阚疃节制闸断面来水情况分析
     *
     * @param param 水情分析参数
     * @return 阚疃节制闸断面来水情况分析结果
     */
    @Override
    public WaterInflowAnalysisVo getWaterInflowAnalysis(WaterAnalysisParam param) {
        WaterInflowAnalysisVo vo = new WaterInflowAnalysisVo();

        // 从参数中获取数据
        List<String> dateList = getDateListFromParam(param);
        List<Double> kantunGateFlow = param.getKantunGateFlow();
        List<Double> shangqiaoGateFlow = param.getShangqiaoGateFlow();

        // 计算阚疃闸累计过闸流量(万m³)
        List<Double> kantunAccumulatedFlow = calculateAccumulatedFlow(kantunGateFlow);

        // 计算上桥闸累计过闸流量(万m³)
        List<Double> shangqiaoAccumulatedFlow = calculateAccumulatedFlow(shangqiaoGateFlow);

        // 计算阚疃到上桥净流入流量(万m³)
        List<Double> netInflowVolume = calculateNetInflowVolume(kantunAccumulatedFlow, shangqiaoAccumulatedFlow);

        // 计算阚疃闸到上桥库容变化(万m³)
        List<Double> reservoirCapacityChange = calculateReservoirCapacityChange(netInflowVolume);

        // 计算阚疃闸下水位变化(m)和上桥闸上水位变化(m)
        List<Double> kantunDownWaterLevelChange = calculateWaterLevelChange(reservoirCapacityChange, true);
        List<Double> shangqiaoUpWaterLevelChange = calculateWaterLevelChange(reservoirCapacityChange, false);

        vo.setDateList(dateList);
        vo.setKantunAccumulatedFlow(kantunAccumulatedFlow);
        vo.setShangqiaoAccumulatedFlow(shangqiaoAccumulatedFlow);
        vo.setNetInflowVolume(netInflowVolume);
        vo.setKantunDownWaterLevelChange(kantunDownWaterLevelChange);
        vo.setShangqiaoUpWaterLevelChange(shangqiaoUpWaterLevelChange);
        vo.setReservoirCapacityChange(reservoirCapacityChange);

        return vo;
    }

    /**
     * 获取片区产水量分析
     *
     * @param param 水情分析参数
     * @return 片区产水量分析结果
     */
    @Override
    public RegionWaterProductionVo getRegionWaterProduction(WaterAnalysisParam param) {
        RegionWaterProductionVo vo = new RegionWaterProductionVo();

        // 从参数中获取数据
        List<String> dateList = getDateListFromParam(param);
        List<Double> huaiyuanRainfall = param.getHuaiyuanRainfall();
        List<Double> panjiRainfall = param.getPanjiRainfall();
        List<Double> fengtaiRainfall = param.getFengtaiRainfall();
        List<Double> mengchengRainfall = param.getMengchengRainfall();

        // 计算各片区产水量(万m³)
        List<Double> huaiyuanWaterProduction = calculateWaterProduction(huaiyuanRainfall, HUAIYUAN_AREA);
        List<Double> panjiWaterProduction = calculateWaterProduction(panjiRainfall, PANJI_AREA);
        List<Double> fengtaiWaterProduction = calculateWaterProduction(fengtaiRainfall, FENGTAI_AREA);
        List<Double> mengchengWaterProduction = calculateWaterProduction(mengchengRainfall, MENGCHENG_AREA);

        // 计算总产水量(万m³)
        List<Double> totalWaterProduction = calculateTotalWaterProduction(
                huaiyuanWaterProduction, panjiWaterProduction, fengtaiWaterProduction, mengchengWaterProduction);

        // 计算入河流量(万m³)
        List<Double> riverInflowVolume = calculateRiverInflowVolume(totalWaterProduction);

        vo.setDateList(dateList);
        vo.setHuaiyuanWaterProduction(huaiyuanWaterProduction);
        vo.setPanjiWaterProduction(panjiWaterProduction);
        vo.setFengtaiWaterProduction(fengtaiWaterProduction);
        vo.setMengchengWaterProduction(mengchengWaterProduction);
        vo.setTotalWaterProduction(totalWaterProduction);
        vo.setRiverInflowVolume(riverInflowVolume);

        return vo;
    }

    /**
     * 获取可供水量分析
     *
     * @param param 水情分析参数
     * @return 可供水量分析结果
     */
    @Override
    public AvailableWaterVo getAvailableWater(WaterAnalysisParam param) {
        AvailableWaterVo vo = new AvailableWaterVo();

        // 获取阚疃节制闸断面来水情况分析和片区产水量分析
        WaterInflowAnalysisVo waterInflowAnalysis = getWaterInflowAnalysis(param);
        RegionWaterProductionVo regionWaterProduction = getRegionWaterProduction(param);

        List<String> dateList = waterInflowAnalysis.getDateList();
        List<Double> netInflowVolume = waterInflowAnalysis.getNetInflowVolume();
        List<Double> regionWaterProductionList = regionWaterProduction.getTotalWaterProduction();

        // 计算可供水量(万m³)
        List<Double> availableWaterVolume = calculateAvailableWaterVolume(netInflowVolume, regionWaterProductionList);

        // 计算累计可供水量(万m³)
        List<Double> accumulatedAvailableWaterVolume = calculateAccumulatedAvailableWaterVolume(availableWaterVolume);

        vo.setDateList(dateList);
        vo.setNetInflowVolume(netInflowVolume);
        vo.setRegionWaterProduction(regionWaterProductionList);
        vo.setAvailableWaterVolume(availableWaterVolume);
        vo.setAccumulatedAvailableWaterVolume(accumulatedAvailableWaterVolume);

        return vo;
    }

    /**
     * 从参数中获取日期列表
     */
    private List<String> getDateListFromParam(WaterAnalysisParam param) {
        List<String> dateList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 从开始日期计算7天的日期列表
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(param.getStartDate());

        for (int i = 0; i < 7; i++) {
            dateList.add(sdf.format(calendar.getTime()));
            calendar.add(Calendar.DAY_OF_MONTH, 1);
        }

        return dateList;
    }

    /**
     * 计算累计过闸流量(万m³)
     * 将流量(m³/s)转换为日累计水量(万m³)
     */
    private List<Double> calculateAccumulatedFlow(List<Double> flowList) {
        List<Double> accumulatedFlow = new ArrayList<>();
        for (Double flow : flowList) {
            // 流量(m³/s) * 86400秒/天 / 10000 = 日累计水量(万m³)
            double dailyVolume = flow * 86400 / 10000;
            // 保留两位小数
            dailyVolume = new BigDecimal(dailyVolume).setScale(2, RoundingMode.HALF_UP).doubleValue();
            accumulatedFlow.add(dailyVolume);
        }
        return accumulatedFlow;
    }

    /**
     * 计算阚疃到上桥净流入流量(万m³)
     */
    private List<Double> calculateNetInflowVolume(List<Double> kantunAccumulatedFlow, List<Double> shangqiaoAccumulatedFlow) {
        List<Double> netInflowVolume = new ArrayList<>();
        for (int i = 0; i < kantunAccumulatedFlow.size(); i++) {
            double netVolume = kantunAccumulatedFlow.get(i) - shangqiaoAccumulatedFlow.get(i);
            // 保留两位小数
            netVolume = new BigDecimal(netVolume).setScale(2, RoundingMode.HALF_UP).doubleValue();
            netInflowVolume.add(netVolume);
        }
        return netInflowVolume;
    }

    /**
     * 计算阚疃闸到上桥库容变化(万m³)
     */
    private List<Double> calculateReservoirCapacityChange(List<Double> netInflowVolume) {
        List<Double> reservoirCapacityChange = new ArrayList<>();
        double capacity = 0.0; // 初始库容变化为0

        for (Double netVolume : netInflowVolume) {
            capacity += netVolume;
            // 保留两位小数
            double roundedCapacity = new BigDecimal(capacity).setScale(2, RoundingMode.HALF_UP).doubleValue();
            reservoirCapacityChange.add(roundedCapacity);
        }

        return reservoirCapacityChange;
    }

    /**
     * 计算水位变化(m)
     * 根据库容变化计算水位变化，使用库容曲线
     * @param isKantun 是否为阚疃闸
     */
    private List<Double> calculateWaterLevelChange(List<Double> reservoirCapacityChange, boolean isKantun) {
        List<Double> waterLevelChange = new ArrayList<>();

        // 获取库容曲线数据
        Map<Double, Double> capacityToWaterLevelMap = getCapacityToWaterLevelMap(isKantun);

        for (Double capacity : reservoirCapacityChange) {
            // 根据库容查找对应的水位
            double waterLevel = getWaterLevelByCapacity(capacity, capacityToWaterLevelMap);
            // 保留两位小数
            waterLevel = new BigDecimal(waterLevel).setScale(2, RoundingMode.HALF_UP).doubleValue();
            waterLevelChange.add(waterLevel);
        }

        return waterLevelChange;
    }

    /**
     * 获取库容-水位映射表
     * 实际应该从数据库中获取st_zvarl_b表的数据
     */
    private Map<Double, Double> getCapacityToWaterLevelMap(boolean isKantun) {
        Map<Double, Double> map = new HashMap<>();

        // 这里使用模拟数据，实际应该从数据库中获取
        if (isKantun) {
            // 阚疃闸的库容-水位关系
            map.put(0.0, 15.5);
            map.put(100.0, 16.0);
            map.put(200.0, 16.5);
            map.put(300.0, 17.0);
            map.put(400.0, 17.5);
            map.put(500.0, 18.0);
        } else {
            // 上桥闸的库容-水位关系
            map.put(0.0, 16.2);
            map.put(100.0, 16.7);
            map.put(200.0, 17.2);
            map.put(300.0, 17.7);
            map.put(400.0, 18.2);
            map.put(500.0, 18.7);
        }

        return map;
    }

    /**
     * 根据库容查找对应的水位
     * 使用线性插值计算
     */
    private double getWaterLevelByCapacity(double capacity, Map<Double, Double> capacityToWaterLevelMap) {
        // 找到最接近的两个库容点
        Double lowerCapacity = null;
        Double upperCapacity = null;

        for (Double cap : capacityToWaterLevelMap.keySet()) {
            if (cap <= capacity && (lowerCapacity == null || cap > lowerCapacity)) {
                lowerCapacity = cap;
            }
            if (cap >= capacity && (upperCapacity == null || cap < upperCapacity)) {
                upperCapacity = cap;
            }
        }

        // 如果库容小于最小库容点
        if (lowerCapacity == null) {
            return capacityToWaterLevelMap.get(upperCapacity);
        }

        // 如果库容大于最大库容点
        if (upperCapacity == null) {
            return capacityToWaterLevelMap.get(lowerCapacity);
        }

        // 如果库容正好等于某个库容点
        if (lowerCapacity.equals(upperCapacity)) {
            return capacityToWaterLevelMap.get(lowerCapacity);
        }

        // 线性插值计算水位
        double lowerWaterLevel = capacityToWaterLevelMap.get(lowerCapacity);
        double upperWaterLevel = capacityToWaterLevelMap.get(upperCapacity);

        return lowerWaterLevel + (upperWaterLevel - lowerWaterLevel) * (capacity - lowerCapacity) / (upperCapacity - lowerCapacity);
    }

    /**
     * 计算片区产水量(万m³)
     */
    private List<Double> calculateWaterProduction(List<Double> rainfallList, double area) {
        List<Double> waterProduction = new ArrayList<>();

        for (Double rainfall : rainfallList) {
            // 水量(m³) = 面积(km²) * 面雨量(mm) * 10³ * 折损率
            // 水量(万m³) = 面积(km²) * 面雨量(mm) * 10³ * 折损率 / 10⁴
            // 水量(万m³) = 面积(km²) * 面雨量(mm) * 折损率 / 10
            double production = area * rainfall * RAINFALL_LOSS_RATE * WATER_PRODUCTION_RATE / 10;
            // 保留两位小数
            production = new BigDecimal(production).setScale(2, RoundingMode.HALF_UP).doubleValue();
            waterProduction.add(production);
        }

        return waterProduction;
    }

    /**
     * 计算总产水量(万m³)
     */
    private List<Double> calculateTotalWaterProduction(
            List<Double> huaiyuanWaterProduction,
            List<Double> panjiWaterProduction,
            List<Double> fengtaiWaterProduction,
            List<Double> mengchengWaterProduction) {

        List<Double> totalWaterProduction = new ArrayList<>();

        for (int i = 0; i < huaiyuanWaterProduction.size(); i++) {
            double total = huaiyuanWaterProduction.get(i) +
                    panjiWaterProduction.get(i) +
                    fengtaiWaterProduction.get(i) +
                    mengchengWaterProduction.get(i);
            // 保留两位小数
            total = new BigDecimal(total).setScale(2, RoundingMode.HALF_UP).doubleValue();
            totalWaterProduction.add(total);
        }

        return totalWaterProduction;
    }

    /**
     * 计算入河流量(万m³)
     */
    private List<Double> calculateRiverInflowVolume(List<Double> totalWaterProduction) {
        List<Double> riverInflowVolume = new ArrayList<>();

        for (Double production : totalWaterProduction) {
            double inflow = production * RIVER_INFLOW_RATE;
            // 保留两位小数
            inflow = new BigDecimal(inflow).setScale(2, RoundingMode.HALF_UP).doubleValue();
            riverInflowVolume.add(inflow);
        }

        return riverInflowVolume;
    }

    /**
     * 计算可供水量(万m³)
     */
    private List<Double> calculateAvailableWaterVolume(List<Double> netInflowVolume, List<Double> regionWaterProduction) {
        List<Double> availableWaterVolume = new ArrayList<>();

        for (int i = 0; i < netInflowVolume.size(); i++) {
            double available = netInflowVolume.get(i) + regionWaterProduction.get(i);
            // 保留两位小数
            available = new BigDecimal(available).setScale(2, RoundingMode.HALF_UP).doubleValue();
            availableWaterVolume.add(available);
        }

        return availableWaterVolume;
    }

    /**
     * 计算累计可供水量(万m³)
     */
    private List<Double> calculateAccumulatedAvailableWaterVolume(List<Double> availableWaterVolume) {
        List<Double> accumulatedAvailableWaterVolume = new ArrayList<>();
        double accumulated = 0.0;

        for (Double available : availableWaterVolume) {
            accumulated += available;
            // 保留两位小数
            double roundedAccumulated = new BigDecimal(accumulated).setScale(2, RoundingMode.HALF_UP).doubleValue();
            accumulatedAvailableWaterVolume.add(roundedAccumulated);
        }

        return accumulatedAvailableWaterVolume;
    }

    /**
     * 获取阚疃节制闸断面来水情况分析（简化版）
     *
     * @param param 水情分析简化参数
     * @return 阚疃节制闸断面来水情况分析结果
     */
    @Override
    public WaterInflowAnalysisVo getWaterInflowAnalysisSimple(WaterAnalysisSimpleParam param) {
        // 将简化参数转换为完整参数
        WaterAnalysisParam fullParam = convertToFullParam(param);
        return getWaterInflowAnalysis(fullParam);
    }

    /**
     * 获取片区产水量分析（简化版）
     *
     * @param param 水情分析简化参数
     * @return 片区产水量分析结果
     */
    @Override
    public RegionWaterProductionVo getRegionWaterProductionSimple(WaterAnalysisSimpleParam param) {
        // 将简化参数转换为完整参数
        WaterAnalysisParam fullParam = convertToFullParam(param);
        return getRegionWaterProduction(fullParam);
    }

    /**
     * 获取可供水量分析（简化版）
     *
     * @param param 水情分析简化参数
     * @return 可供水量分析结果
     */
    @Override
    public AvailableWaterVo getAvailableWaterSimple(WaterAnalysisSimpleParam param) {
        // 将简化参数转换为完整参数
        WaterAnalysisParam fullParam = convertToFullParam(param);
        return getAvailableWater(fullParam);
    }

    /**
     * 将简化参数转换为完整参数
     * 将当前水位和流量扩展为7天的数据
     */
    private WaterAnalysisParam convertToFullParam(WaterAnalysisSimpleParam simpleParam) {
        WaterAnalysisParam fullParam = new WaterAnalysisParam();

        // 复制基本信息
        fullParam.setStartDate(simpleParam.getStartDate());
        fullParam.setEndDate(simpleParam.getEndDate());
        fullParam.setHuaiyuanRainfall(simpleParam.getHuaiyuanRainfall());
        fullParam.setPanjiRainfall(simpleParam.getPanjiRainfall());
        fullParam.setFengtaiRainfall(simpleParam.getFengtaiRainfall());
        fullParam.setMengchengRainfall(simpleParam.getMengchengRainfall());

        // 将当前水位和流量扩展为7天的数据（假设7天内保持不变）
        List<Double> kantunDownWaterLevels = new ArrayList<>();
        List<Double> shangqiaoUpWaterLevels = new ArrayList<>();
        List<Double> kantunGateFlows = new ArrayList<>();
        List<Double> shangqiaoGateFlows = new ArrayList<>();

        for (int i = 0; i < 7; i++) {
            kantunDownWaterLevels.add(simpleParam.getKantunDownWaterLevel());
            shangqiaoUpWaterLevels.add(simpleParam.getShangqiaoUpWaterLevel());
            kantunGateFlows.add(simpleParam.getKantunGateFlow());
            shangqiaoGateFlows.add(simpleParam.getShangqiaoGateFlow());
        }

        fullParam.setKantunDownWaterLevel(kantunDownWaterLevels);
        fullParam.setShangqiaoUpWaterLevel(shangqiaoUpWaterLevels);
        fullParam.setKantunGateFlow(kantunGateFlows);
        fullParam.setShangqiaoGateFlow(shangqiaoGateFlows);

        return fullParam;
    }
}
