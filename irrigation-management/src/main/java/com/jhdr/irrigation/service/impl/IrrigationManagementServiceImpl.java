package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jhdr.common.core.exception.ServiceException;
import com.jhdr.common.redis.service.RedisService;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.IrrigationCropCyclePo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.IIrrigationCropCycleService;
import com.jhdr.irrigation.service.IIrrigationCropManageService;
import com.jhdr.irrigation.service.IWaterSchedulePlanService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 灌区管理
 */
@Component
public class IrrigationManagementServiceImpl {

    @Resource
    private RedisService redisService;

    @Resource
    private ModelProcessUtils modelProcessUtils;
    @Autowired
    private IIrrigationCropManageService irrigationCropManageService;

    @Autowired
    private IIrrigationCropCycleService irrigationCropCycleService;

    @Autowired
    private IWaterSchedulePlanService waterSchedulePlanService;

    /**
     * 获取灌区基本信息
     *
     * @return
     */
    public IrrigationBaseVo getBaseInfo() {
        //获取灌区基础信息
        IrrigationBaseVo baseInfo = irrigationCropManageService.getIrrigationBaseInfo();
        return baseInfo;
    }

    /**
     * 获取灌区区域信息
     *
     * @param irrigationCode 灌区编码
     * @return
     */
    public IrrigationRegionVo getRegionInfo(String irrigationCode) {

        //获取灌区区域信息
        IrrigationRegionVo regionInfo = irrigationCropManageService.getRegionInfo(irrigationCode);
        return regionInfo;

    }

    /**
     * 获取灌区感知-泵站实时数据
     *
     * @param irrigationCode
     * @return
     */
    public List<IrrigationPumpRealVo> getPerceptionPumpReal(String irrigationCode) {

        List<IrrigationPumpRealVo> pumpRealList = new ArrayList<>();

        pumpRealList = getPumpRealList(irrigationCode);
        return pumpRealList;


    }


    /**
     * 获取灌区感知-七天天气
     *
     * @param irrigationCode
     * @return
     */
    public List<IrrigationWeatherDayVo> getPerceptionWeather(String irrigationCode) {

        List<IrrigationWeatherDayVo> weatherDayList = new ArrayList<>();

        weatherDayList = getWeatherDayList(irrigationCode);
        return weatherDayList;

    }

    /**
     * 获取灌区感知-泵站折线
     *
     * @param pumpLineParam
     * @return
     */
    public List<IrrigationPumpLineVo> getPerceptionPumpLine(IrrigationPumpLineParam pumpLineParam) {
        pumpLineParam.setAddvcd(setAddvcd(pumpLineParam.getIrrigationCode()));
        List<IrrigationPumpLineVo> pumpLineList = irrigationCropManageService.getPumpLineListByTime(pumpLineParam);
        return pumpLineList;
    }


    //获取灌区感知-泵站流量汇总
    public List<IrrigationPumpAllVo> getPerceptionPumpAll(IrrigationPumpAllParam pumpAllParam) {
        pumpAllParam.setAddvcd(setAddvcd(pumpAllParam.getIrrigationCode()));
        if (ObjectUtil.isNotEmpty(pumpAllParam)) {
            if (ObjectUtil.isEmpty(pumpAllParam.getStartTime())) {
                throw new ServiceException("请选择开始时间");
            } else {
                pumpAllParam.setStartTime(pumpAllParam.getStartTime().substring(0, 10) + " 00:00:01");
            }
            if (ObjectUtil.isEmpty(pumpAllParam.getEndTime())) {
                throw new ServiceException("请选择结束时间");
            } else {
                pumpAllParam.setEndTime(pumpAllParam.getEndTime().substring(0, 10) + " 23:59:59");
            }

        }
        return irrigationCropManageService.getPerceptionPumpAll(pumpAllParam);
    }

    /**
     * 获取灌区感知-泵站列表分页
     *
     * @param pumpLineParam
     * @return
     */
    public List<IrrigationPumpLineVo> getPerceptionPumpTabulation(IrrigationPumpLineParam pumpLineParam) {

        pumpLineParam.setAddvcd(setAddvcd(pumpLineParam.getIrrigationCode()));
        if (ObjectUtil.isNotEmpty(pumpLineParam)) {
            if (ObjectUtil.isEmpty(pumpLineParam.getStartTime())) {
                throw new ServiceException("请选择开始时间");
            } else {
                pumpLineParam.setStartTime(pumpLineParam.getStartTime().substring(0, 10) + " 00:00:01");
            }
            if (ObjectUtil.isEmpty(pumpLineParam.getEndTime())) {
                throw new ServiceException("请选择结束时间");
            } else {
                pumpLineParam.setEndTime(pumpLineParam.getEndTime().substring(0, 10) + " 23:59:59");
            }

        }
        List<IrrigationPumpLineVo> pumpLineList = irrigationCropManageService.getPerceptionPumpTabulation(pumpLineParam);
        return pumpLineList;
    }

    private String setAddvcd(String irrigationCode) {

        String addvcd = null;
        switch (irrigationCode) {
            case "GQ341623":
                //("利辛县");
                addvcd = "341623";
                break;
            case "GQ340421":
                //("凤台区");
                addvcd = "340421";
                break;
            case "GQ340321":
                //("怀远县");
                addvcd = "340321";
                break;
            case "GQ341622":
                //("蒙城县");
                addvcd = "341622";
                break;
            case "GQ340406":
                //("潘集区");
                addvcd = "340406";
                break;
            case "GQ341203":
                //("颍东区");
                addvcd = "341203";
                break;
            case "GQ341204":
                //("颍泉区");
                addvcd = "341204";
                break;
        }
        return addvcd;
    }

    //获取灌区感知-泵站统计分析
    public String getPerceptionPumpStatistics(IrrigationPumpLineParam pumpLineParam) {

        StringBuilder str = new StringBuilder();
        str.append(pumpLineParam.getStartTime() + "起-止" + pumpLineParam.getEndTime() + ",");
        //假数据 这个泵的假数据
        List<IrrigationPumpRealVo> pumpRealList = getPumpRealList(pumpLineParam.getIrrigationCode());
        double sum = 0.0;
        for (String pumpCode : pumpLineParam.getPumpCode().split(",")) {
            String foundPumpName = null;
            for (IrrigationPumpRealVo pump : pumpRealList) {
                if (pump.getPumpCode().equals(pumpCode)) {
                    foundPumpName = pump.getPumpName();
                    str.append(foundPumpName + "的抽水量是" + "0.00" + "万立方米,");
                    sum += Double.parseDouble("0.00");
                    break;
                }
            }
        }
        str.append("总抽水量是" + sum + "万立方米。");

        return str.toString();
    }

    //获取灌区作物-作物类型
    public List<IrrigationCropTypeVo> getBaseCropTypes() {
        List<IrrigationCropTypeVo> perceptionCropTypes = new ArrayList<>();

        perceptionCropTypes.add(new IrrigationCropTypeVo().setCropId("1").setCropName("水稻"));
        perceptionCropTypes.add(new IrrigationCropTypeVo().setCropId("2").setCropName("小麦"));
        perceptionCropTypes.add(new IrrigationCropTypeVo().setCropId("3").setCropName("玉米"));
        return perceptionCropTypes;
    }

    //获取灌区作物-作物统计
    public List<IrrigationCropStatisticsVo> getBaseCropStatistics(String irrigationCode) {

        List<IrrigationCropStatisticsVo> cropStatisticsList =
                irrigationCropManageService.getBaseCropStatisticsByIrrigationCode(irrigationCode);

        return cropStatisticsList;
    }

    //获取灌区作物-获取泵站统计
    public List<IrrigationPumpStatisticsVo> getBasePumpStatistics(String irrigationCode) {
        List<IrrigationPumpStatisticsVo> pumpStatisticsList =
                irrigationCropManageService.getBasePumpStatisticsByIrrigationCode(irrigationCode);

        return pumpStatisticsList;
    }

    //获取灌区感知-灌区作物-泵站灌溉面积
    public String getBaseCropIrrigatedStatistics(String irrigationCode) {

        List<IrrigationPumpStatisticsVo> pumpStatisticsList =
                irrigationCropManageService.getBasePumpStatisticsByIrrigationCode(irrigationCode);

        BigDecimal allIrrigatedArea = new BigDecimal("0");

        for (IrrigationPumpStatisticsVo irrigationPumpStatisticsVo : pumpStatisticsList) {
            if (ObjectUtil.isNotEmpty(irrigationPumpStatisticsVo.getAllIrrigatedArea())) {
                allIrrigatedArea = allIrrigatedArea.add(irrigationPumpStatisticsVo.getAllIrrigatedArea());
            }

        }
        return allIrrigatedArea + "";
    }

    //获取灌区感知-作物需水量统计
    public List<IrrigationCropWaterDemandStatisticsVo> getWaterDemandCropStatistics(IrrigationWaterParam waterParam) {
        List<IrrigationCropWaterDemandStatisticsVo> cropWaterDemandStatisticsList = new ArrayList<>();


        cropWaterDemandStatisticsList = irrigationCropManageService.getWaterDemandCropStatistics(waterParam);
        String areaPoint = setAreaPoint(waterParam.getIrrigationCode());
        WaterCropIrrigationVo cropIrrigationVo = redisService.getCacheObject(areaPoint + "cropDemand");

        if (ObjectUtil.isNotEmpty(cropIrrigationVo) && ObjectUtil.isNotEmpty(cropIrrigationVo.getValue())) {
            List<String> value = cropIrrigationVo.getValue();
            BigDecimal cornWater = new BigDecimal(0);
            BigDecimal wheatWater = new BigDecimal(0);
            BigDecimal paddyWater = new BigDecimal(0);

            String yue1 = "0";
            String yue2 = "0";
            String yue3 = "0";
            String yue4 = "0";
            String yue5 = "0";
            String yue6 = "0";
            String yue7 = "0";
            String yue8 = "0";
            String yue9 = "0";
            String yue10 = "0";
            String yue11 = "0";
            String yue12 = "0";
            if (value.size()>0){
                yue1 = value.get(0);
            }
            if (value.size()>1) {
                yue2 = value.get(1);
            }
            if (value.size()>2) {
                yue3 = value.get(2);
            }
            if (value.size()>3) {
                yue4 = value.get(3);
            }
            if (value.size()>4) {
                yue5 = value.get(4);
            }
            if (value.size()>5) {
                yue6 = value.get(5);
            }
            if (value.size()>6) {
                yue7 = value.get(6);
            }
            if (value.size()>7) {
                yue8 = value.get(7);
            }
            if (value.size()>8) {
                yue9 = value.get(8);
            }
            if (value.size()>9) {
                yue10 = value.get(9);
            }
            if (value.size()>10) {
                yue11 = value.get(10);
            }
            if (value.size()>11) {
                yue12 = value.get(11);
            }

//            颍泉、颍东、利辛：
//            玉米：统计6-9月份需水量
//            小麦：统计10-5月份需水量
            if ("YingQuan".equals(areaPoint) || "YingDong".equals(areaPoint) || "LiXin".equals(areaPoint)) {
                cornWater = new BigDecimal(yue6).add(new BigDecimal(yue7)).add(new BigDecimal(yue8)).add(new BigDecimal(yue9));
                wheatWater = new BigDecimal(yue10).add(new BigDecimal(yue11)).add(new BigDecimal(yue12))
                        .add(new BigDecimal(yue1)).add(new BigDecimal(yue2)).add(new BigDecimal(yue3))
                        .add(new BigDecimal(yue4)).add(new BigDecimal(yue5));
            }
//            蒙城、凤台、潘集、怀远：
//            水稻：统计6-10月份需水量
//            小麦：统计11-5月份需水量
            if ("MengCheng".equals(areaPoint) || "FengTai".equals(areaPoint) || "PanJi".equals(areaPoint) || "HuaiYuan".equals(areaPoint)) {
                paddyWater = new BigDecimal(yue6).add(new BigDecimal(yue7)).add(new BigDecimal(yue8))
                        .add(new BigDecimal(yue9)).add(new BigDecimal(yue10));

                wheatWater = new BigDecimal(yue11).add(new BigDecimal(yue12)).add(new BigDecimal(yue1))
                        .add(new BigDecimal(yue2)).add(new BigDecimal(yue3)).add(new BigDecimal(yue4))
                        .add(new BigDecimal(yue5));
            }
            for (IrrigationCropWaterDemandStatisticsVo irrigationCropWaterDemandStatisticsVo : cropWaterDemandStatisticsList) {
                if (irrigationCropWaterDemandStatisticsVo.getCropId() == 3) {
                    irrigationCropWaterDemandStatisticsVo.setWaterDemand(cornWater);
                }
                if (irrigationCropWaterDemandStatisticsVo.getCropId() == 2) {
                    irrigationCropWaterDemandStatisticsVo.setWaterDemand(wheatWater);
                }
                if (irrigationCropWaterDemandStatisticsVo.getCropId() == 1) {
                    irrigationCropWaterDemandStatisticsVo.setWaterDemand(paddyWater);
                }
            }
        }
        return cropWaterDemandStatisticsList;
    }


    //获取泵站用水量统计
    public List<IrrigationPumpWaterDemandStatisticsVo> getWaterDemandPumpStatistics(IrrigationWaterParam waterParam) {
        List<IrrigationPumpWaterDemandStatisticsVo> pumpWaterDemandStatisticsList = new ArrayList<>();

        pumpWaterDemandStatisticsList = irrigationCropManageService.getWaterDemandPumpStatistics(waterParam);

        String areaPoint = setAreaPoint(waterParam.getIrrigationCode());
        WaterCropIrrigationVo cropIrrigationVo = redisService.getCacheObject(areaPoint + "cropDemand");
        if (ObjectUtil.isNotEmpty(cropIrrigationVo) && ObjectUtil.isNotEmpty(cropIrrigationVo.getValue())) {
            BigDecimal allCorpWater = new BigDecimal(0);
            BigDecimal pumpWaterDemand = new BigDecimal(0);
            List<String> value = cropIrrigationVo.getValue();
            for (String yue : value) {
                allCorpWater = allCorpWater.add(new BigDecimal(yue));
            }

            for (IrrigationPumpWaterDemandStatisticsVo pumpWaterDemandStatisticsVo : pumpWaterDemandStatisticsList) {
                pumpWaterDemand = pumpWaterDemand.add(pumpWaterDemandStatisticsVo.getAllIrrigatedArea());
            }
            if (allCorpWater.compareTo(new BigDecimal(0)) == 0) {
                return pumpWaterDemandStatisticsList;
            }
            BigDecimal divide = allCorpWater.divide(pumpWaterDemand, 8, BigDecimal.ROUND_HALF_UP);
            for (IrrigationPumpWaterDemandStatisticsVo pumpWaterDemandStatisticsVo : pumpWaterDemandStatisticsList) {
                pumpWaterDemandStatisticsVo.setWaterDemand(pumpWaterDemandStatisticsVo.getAllIrrigatedArea().multiply(divide).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }

        return pumpWaterDemandStatisticsList;
    }


    private String setAreaPoint(String irrigationCode) {
        String areaPoint = null;
        switch (irrigationCode) {
            case "GQ341623":
                //("利辛县");
                areaPoint = "LiXin";
                break;
            case "GQ340421":
                //("凤台区");
                areaPoint = "FengTai";
                break;
            case "GQ340321":
                //("怀远县");
                areaPoint = "HuaiYuan";
                break;
            case "GQ341622":
                //("蒙城县");
                areaPoint = "MengCheng";
                break;
            case "GQ340406":
                //("潘集区");
                areaPoint = "PanJi";
                break;
            case "GQ341203":
                //("颍东区");
                areaPoint = "YingDong";
                break;
            case "GQ341204":
                //("颍泉区");
                areaPoint = "YingQuan";
                break;
        }
        return areaPoint;
    }

    //获取泵站实时数据
    public List<IrrigationPumpRealVo> getPumpRealList(String irrigationCode) {

        List<IrrigationPumpRealVo> pumpRealList = irrigationCropManageService.getPumpRealList(irrigationCode);
        List<IrrigationPumpRealVo> pumpLineList = irrigationCropManageService.getPumpLineList(irrigationCode);
        List<IrrigationPumpRealVo> pumpList = new ArrayList<>();
        for (IrrigationPumpRealVo voA : pumpRealList) {
            if (voA.getRealWater() != null && !voA.getRealWater().isEmpty()) {
                // 如果 a 中的记录已经有实时水位信息，则直接添加到 c 中
                pumpList.add(voA);
            } else {
                // 尝试从 b 中查找具有相同泵站编码的记录
                Optional<IrrigationPumpRealVo> voBOptional = pumpLineList.stream()
                        .filter(voB -> voB.getPumpCode().equals(voA.getPumpCode()))
                        .findFirst();

                if (voBOptional.isPresent()) {
                    // 如果找到了，则使用 b 中的数据填充 a 中的记录，并添加到 c 中
                    IrrigationPumpRealVo voB = voBOptional.get();
                    voA.setRealWater(voB.getRealWater());
                    voA.setRealTime(voB.getRealTime());
                    pumpList.add(voA);
                }
                // 如果在 b 中没有找到对应的记录，则不添加到 c 中
            }
        }


        return pumpList;

    }


    //获取灌区感知-七天天气
    private List<IrrigationWeatherDayVo> getWeatherDayList(String irrigationCode) {
        List<IrrigationWeatherDayVo> weatherDayList = irrigationCropManageService.getWeatherDayList(irrigationCode);

        return weatherDayList;

    }


    public List<IrrigationPumpPullVo> getPumpList(String irrigationCode) {

        String addvcd = setAddvcd(irrigationCode);
        List<IrrigationPumpPullVo> pumpPullVos = irrigationCropManageService.getPumpList(addvcd);
        return pumpPullVos;
    }


    public IrrigationPopupAllVo getWaterDemandPopup(String irrigationCode) {
        IrrigationPopupAllVo popupAllVo = new IrrigationPopupAllVo();

        List<IrrigationCropStatisticsVo> cropStatisticsList =
                irrigationCropManageService.getBaseCropStatisticsByIrrigationCode(irrigationCode);
        if (ObjectUtil.isNotEmpty(cropStatisticsList)) {
            for (IrrigationCropStatisticsVo cropStatisticsVo : cropStatisticsList) {
                if (cropStatisticsVo.getCropId() == 1) {
                    IrrigationPopupVo shuidaoCropVo = new IrrigationPopupVo();
                    shuidaoCropVo.setCropId(cropStatisticsVo.getCropId());
                    shuidaoCropVo.setCropName(cropStatisticsVo.getCropName());
                    shuidaoCropVo.setAllPlantArea(cropStatisticsVo.getAllPlantArea());

                    List<IrrigationCropCyclePo> list = irrigationCropCycleService.list(new QueryWrapper<IrrigationCropCyclePo>().eq("crop_id", 1).orderByAsc("cycle_sort"));
                    List<IrrigationCropCycleVo> shuidao = BeanUtil.copyToList(list, IrrigationCropCycleVo.class);
                    if (ObjectUtil.isNotEmpty(shuidao)) {
                        shuidao.forEach(yum -> {
                            if (isInRange(yum.getStartDate(), yum.getEndDate())) {
                                yum.setNowStatus(1);
                            } else {
                                yum.setNowStatus(0);
                            }
                        });
                    }
                    shuidaoCropVo.setCropCycleVos(shuidao);
                    popupAllVo.setShuidaoCropVo(shuidaoCropVo);
                } else if (cropStatisticsVo.getCropId() == 2) {
                    IrrigationPopupVo xiaomaiCropVo = new IrrigationPopupVo();
                    xiaomaiCropVo.setCropId(cropStatisticsVo.getCropId());
                    xiaomaiCropVo.setCropName(cropStatisticsVo.getCropName());
                    xiaomaiCropVo.setAllPlantArea(cropStatisticsVo.getAllPlantArea());

                    List<IrrigationCropCyclePo> list = irrigationCropCycleService.list(new QueryWrapper<IrrigationCropCyclePo>().eq("crop_id", 2).orderByAsc("cycle_sort"));
                    List<IrrigationCropCycleVo> xiaomai = BeanUtil.copyToList(list, IrrigationCropCycleVo.class);
                    if (ObjectUtil.isNotEmpty(xiaomai)) {
                        xiaomai.forEach(yum -> {
                            if (isInRange(yum.getStartDate(), yum.getEndDate())) {
                                yum.setNowStatus(1);
                            } else {
                                yum.setNowStatus(0);
                            }
                        });
                    }
                    xiaomaiCropVo.setCropCycleVos(xiaomai);
                    popupAllVo.setShuidaoCropVo(xiaomaiCropVo);
                } else if (cropStatisticsVo.getCropId() == 3) {
                    IrrigationPopupVo yumiCropVo = new IrrigationPopupVo();
                    yumiCropVo.setCropId(cropStatisticsVo.getCropId());
                    yumiCropVo.setCropName(cropStatisticsVo.getCropName());
                    yumiCropVo.setAllPlantArea(cropStatisticsVo.getAllPlantArea());

                    List<IrrigationCropCyclePo> list = irrigationCropCycleService.list(new QueryWrapper<IrrigationCropCyclePo>().eq("crop_id", 3).orderByAsc("cycle_sort"));
                    List<IrrigationCropCycleVo> yumi = BeanUtil.copyToList(list, IrrigationCropCycleVo.class);
                    if (ObjectUtil.isNotEmpty(yumi)) {
                        yumi.forEach(yum -> {
                            if (isInRange(yum.getStartDate(), yum.getEndDate())) {
                                yum.setNowStatus(1);
                            } else {
                                yum.setNowStatus(0);
                            }
                        });
                    }
                    yumiCropVo.setCropCycleVos(yumi);
                    popupAllVo.setShuidaoCropVo(yumiCropVo);
                }
            }
            return popupAllVo;

        } else {
            return null;
        }
    }

    //判断日期是否在范围内
    private boolean isInRange(String startStr, String endStr) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();

        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM月dd日");

        // 获取当前年份
        int currentYear = currentDate.getYear();

        // 将字符串转换为LocalDate，并指定当前年份
        LocalDate startDate = LocalDate.parse(currentYear + "-" + startStr.replace("月", "-").replace("日", ""), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(currentYear + "-" + endStr.replace("月", "-").replace("日", ""), DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        // 判断当前日期是否在范围内
        boolean isInRange = !currentDate.isBefore(startDate) && !currentDate.isAfter(endDate);
        return isInRange;
    }

    // 获取所有作物的需水量统计
    public List<IrrigationCropWaterDemandStatisticsVo> getAllWaterDemandCropStatistics() {

        return irrigationCropManageService.getAllWaterDemandCropStatistics();

    }

    public IrrigationModelDataVo modelCompute(IrrigationPlanModelParam modelParam) {

        //初始化模型输入参数 ；参数一致直接返回redis中存储得结果 ；否则返回空，等待下次请求
        IrrigationModelDataVo irrigationPlanModelParam = modelProcessUtils.initialRainModel(modelParam);

        return irrigationPlanModelParam;
    }

    public IrrigationModelYearTypeVo modelComputeYear(IrrigationPlanModelYearParam irrigationPlan) {

        String rainYear = modelProcessUtils.checkYear(irrigationPlan.getRain().toString());
        if (ObjectUtil.isNotEmpty(rainYear)) {


            IrrigationModelYearTypeVo modelYearTypeVo = new IrrigationModelYearTypeVo();

            IrrigationPlanModelParam rainModelParam = redisService.getCacheObject("rainModelParam");
            IrrigationModelDataVo irrigationPlanModelParam = modelProcessUtils.initialRainModel(rainModelParam);
            modelYearTypeVo.setTypicalYear(irrigationPlanModelParam.getTypicalYear());
//            modelYearTypeVo.setWaterDemand(irrigationPlanModelParam.getWaterDemand());
            modelYearTypeVo.setRainImg(irrigationPlanModelParam.getRainImg());
            String situation = "2";
            if ("wet".equals(rainYear)) {
                situation = "1";
            } else if ("dry".equals(rainYear)) {
                situation = "3";
            }
            redisService.setCacheObject("yearType", situation);
            modelYearTypeVo.setSituation(situation);
            return modelYearTypeVo;
        }
        return null;
    }

    public IrrigationModelDispatchVo modelComputeDispatch(IrrigationPlanModelDispatchParam dispatchParam) {

        IrrigationModelDispatchVo irrigationPlanModelParam = new IrrigationModelDispatchVo();
        Integer yue = 1;
        if (ObjectUtil.isNotEmpty(dispatchParam.getYue())) {
            yue = dispatchParam.getYue();
        }
        //枯水年
        WaterSchedulePlanDispatchVo dispatchGenerate = waterSchedulePlanService.dispatchGenerate(yue);

        IrrigationModelDataLineVo shangQiaoLineVo = new IrrigationModelDataLineVo();

        double realWaterDemandSQ = 62.61 * Math.pow(dispatchParam.getShangQiaoWaterLine(), 2) - 1053 * dispatchParam.getShangQiaoWaterLine() + 2696;
        double wareWaterDemandSQ = 62.61 * Math.pow(dispatchParam.getShangQiaoWaterWarnLine(), 2) - 1053 * dispatchParam.getShangQiaoWaterWarnLine() + 2696;
        double shangQiaoWaterDemand = dispatchGenerate.getShangQiaoWaterDemand().doubleValue();
        double waterDemandSQ = shangQiaoWaterDemand - (realWaterDemandSQ - wareWaterDemandSQ);
        // y = 62.61x2 – 1053x + 2696

        shangQiaoLineVo.setRealWaterDemand(NumberUtil.round(realWaterDemandSQ, 2).doubleValue());
        shangQiaoLineVo.setWareWaterDemand(NumberUtil.round(wareWaterDemandSQ, 2).doubleValue());
        shangQiaoLineVo.setFutureWaterDemand(NumberUtil.round(shangQiaoWaterDemand, 2).doubleValue());

        if (waterDemandSQ < 0) {
            shangQiaoLineVo.setWaterDemand(0);
        } else {
            shangQiaoLineVo.setWaterDemand(NumberUtil.round(waterDemandSQ, 2).doubleValue());
        }


        double sq = realWaterDemandSQ - shangQiaoWaterDemand;
        //y = -2.427e-08 x2 + 0.001073 x + 13.92
        double futureLineSQ = -2.427e-08 * Math.pow(sq, 2) + 0.001073 * sq + 13.92;
        double shangQiaoWaterEndDemand = realWaterDemandSQ + waterDemandSQ;
        double shangQiaoWaterEndLine = -2.427e-08 * Math.pow(shangQiaoWaterEndDemand, 2) + 0.001073 * shangQiaoWaterEndDemand + 13.92;


        shangQiaoLineVo.setRealLine(dispatchParam.getShangQiaoWaterLine());
        shangQiaoLineVo.setWareLine(dispatchParam.getShangQiaoWaterWarnLine());
        if (futureLineSQ < 19.5) {
            shangQiaoLineVo.setFutureLine(NumberUtil.round(19.5, 2).doubleValue());
        } else {
            shangQiaoLineVo.setFutureLine(NumberUtil.round(futureLineSQ, 2).doubleValue());
        }

        irrigationPlanModelParam.setShangQiaoList(shangQiaoLineVo);


        IrrigationModelDataLineVo kanTuanLineVo = new IrrigationModelDataLineVo();

        double realWaterDemandKT = 14.82 * Math.pow(dispatchParam.getKanTuanWaterLine(), 2) - 346.7 * dispatchParam.getKanTuanWaterLine() + 1316;
        double wareWaterDemandKT = 14.82 * Math.pow(dispatchParam.getKanTuanWaterWarnLine(), 2) - 346.7 * dispatchParam.getKanTuanWaterWarnLine() + 1316;
        double kanTuanWaterDemand = dispatchGenerate.getKanTuanWaterDemand().doubleValue();
        double waterDemandKT = kanTuanWaterDemand - (realWaterDemandKT - wareWaterDemandKT);


        // y = 14.82 x2 - 346.7 x + 1316
        kanTuanLineVo.setRealWaterDemand(NumberUtil.round(realWaterDemandKT, 2).doubleValue());
        kanTuanLineVo.setWareWaterDemand(NumberUtil.round(wareWaterDemandKT, 2).doubleValue());
        kanTuanLineVo.setFutureWaterDemand(NumberUtil.round(kanTuanWaterDemand, 2).doubleValue());
        if (waterDemandKT < 0) {
            kanTuanLineVo.setWaterDemand(0);
        } else {
            kanTuanLineVo.setWaterDemand(NumberUtil.round(waterDemandKT, 2).doubleValue());
        }


        double kt = realWaterDemandKT - kanTuanWaterDemand;
        //y = -5.942e-07 x2 + 0.004383 x + 18.67
        double futureLineKT = -5.942e-07 * Math.pow(kt, 2) + 0.004383 * kt + 18.67;

        double kanTuanWaterEndDemand = realWaterDemandKT + waterDemandKT;
        double kanTuanWaterEndLine = -5.942e-07 * Math.pow(kanTuanWaterEndDemand, 2) + 0.004383 * kanTuanWaterEndDemand + 18.67;

        kanTuanLineVo.setRealLine(dispatchParam.getKanTuanWaterLine());
        kanTuanLineVo.setWareLine(dispatchParam.getKanTuanWaterWarnLine());
        kanTuanLineVo.setFutureLine(NumberUtil.round(futureLineKT, 2).doubleValue());


        irrigationPlanModelParam.setKanTuanList(kanTuanLineVo);


        IrrigationModelDataLineVo chaHuaLineVo = new IrrigationModelDataLineVo();

        double realWaterDemandCH = 15.12 * Math.pow(dispatchParam.getChaHuaWaterLine(), 2) - 460.3 * dispatchParam.getChaHuaWaterLine() + 2964;
        double wareWaterDemandCH = 15.12 * Math.pow(dispatchParam.getChaHuaWaterWarnLine(), 2) - 460.3 * dispatchParam.getChaHuaWaterWarnLine() + 2964;
        double chaHuaWaterDemand = dispatchGenerate.getChaHuaWaterDemand().doubleValue();
        double waterDemandCH = chaHuaWaterDemand - (realWaterDemandCH - wareWaterDemandCH);
        // y = 15.12 x2 – 460.3 x + 2964
        chaHuaLineVo.setRealWaterDemand(NumberUtil.round(realWaterDemandCH, 2).doubleValue());
        chaHuaLineVo.setWareWaterDemand(NumberUtil.round(wareWaterDemandCH, 2).doubleValue());
        chaHuaLineVo.setFutureWaterDemand(NumberUtil.round(chaHuaWaterDemand, 2).doubleValue());
        if (waterDemandCH < 0) {
            chaHuaLineVo.setWaterDemand(0);
        } else {
            chaHuaLineVo.setWaterDemand(NumberUtil.round(waterDemandCH, 2).doubleValue());
        }


        double CH = realWaterDemandCH - chaHuaWaterDemand;
        //y = -8.814e-07 x2 + 0.004976 x + 21.19
        double futureLineCH = -8.814e-07 * Math.pow(CH, 2) + 0.004976 * CH + 21.19;

        double chaHuaWaterEndDemand = realWaterDemandCH + waterDemandCH;
        double chaHuaWaterEndLine = -8.814e-07 * Math.pow(chaHuaWaterEndDemand, 2) + 0.004976 * chaHuaWaterEndDemand + 21.19;

        chaHuaLineVo.setRealLine(dispatchParam.getChaHuaWaterLine());
        chaHuaLineVo.setWareLine(dispatchParam.getChaHuaWaterWarnLine());
        chaHuaLineVo.setFutureLine(NumberUtil.round(futureLineCH, 2).doubleValue());

        irrigationPlanModelParam.setChaHuaList(chaHuaLineVo);

        WaterSchedulePlanSimulationVo waterSchedulePlanSimulationVo = BeanUtil.copyProperties(dispatchGenerate, WaterSchedulePlanSimulationVo.class);

        waterSchedulePlanSimulationVo.setShangQiaoWaterLine(dispatchParam.getShangQiaoWaterLine());
        waterSchedulePlanSimulationVo.setShangQiaoWaterEndLine(NumberUtil.round(shangQiaoWaterEndLine, 2).doubleValue());

        waterSchedulePlanSimulationVo.setKanTuanWaterEndLine(NumberUtil.round(kanTuanWaterEndLine, 2).doubleValue());
        waterSchedulePlanSimulationVo.setKanTuanWaterLine(dispatchParam.getKanTuanWaterLine());

        waterSchedulePlanSimulationVo.setChaHuaWaterLine(dispatchParam.getChaHuaWaterLine());
        waterSchedulePlanSimulationVo.setChaHuaWaterEndLine(NumberUtil.round(chaHuaWaterEndLine, 2).doubleValue());
        if (dispatchGenerate != null && dispatchGenerate.getDispatchScheme() == 1) {

            redisService.setCacheObject("dispatchGenerate01", waterSchedulePlanSimulationVo);
        } else if (dispatchGenerate != null && dispatchGenerate.getDispatchScheme() == 2) {

            redisService.setCacheObject("dispatchGenerate02", waterSchedulePlanSimulationVo);
        }


        return irrigationPlanModelParam;
    }

    public String modelComputeYearGet() {
        return redisService.getCacheObject("yearType");
    }


    //计算区域需水量
    public IrrigationModelAreaTypeVo modelComputeArea(IrrigationPlanModelComputeAreaParam commissionArea) {
        IrrigationModelAreaTypeVo irrigationModelAreaTypeVo = new IrrigationModelAreaTypeVo();
        irrigationModelAreaTypeVo = modelProcessUtils.modelComputeArea(commissionArea);


        return irrigationModelAreaTypeVo;
    }


    public WaterSchedulePlanSimulationVo getWaterDemandScheme(String scheme) {
        if (scheme != null && "1".equals(scheme)) {

            return redisService.getCacheObject("dispatchGenerate01");
        } else if (scheme != null && "2".equals(scheme)) {

            return redisService.getCacheObject("dispatchGenerate02");
        }
        return null;
    }

    public WaterCropIrrigatedAreaVo getCropIrrigatedArea() {
        return irrigationCropManageService.getCropIrrigatedArea();
    }

    public int waterModelRun(WaterModelRunParam modelRunParam) {
        modelProcessUtils.waterModelRun(modelRunParam);

        return 1;
    }

    public WaterInflowForecastVo inflowForecast() {

        //        "GQ341204\t颍泉区\n" +
        //        "GQ341203\t颍东区\n" +
        //        "GQ341622\t蒙城县\n" +
        //        "GQ340406\t潘集区\n" +
        //        "GQ340321\t怀远县\n" +
        //        "GQ341623\t利辛县\n" +
        //        "GQ340421\t凤台县  ")
        WaterInflowForecastParam infoParam = new WaterInflowForecastParam();
        infoParam.setYingQuan(irrigationCropManageService.getWeatherDayList("GQ341204"));
        infoParam.setYingDong(irrigationCropManageService.getWeatherDayList("GQ341203"));
        infoParam.setMengCheng(irrigationCropManageService.getWeatherDayList("GQ341622"));
        infoParam.setPanJi(irrigationCropManageService.getWeatherDayList("GQ340406"));
        infoParam.setHuaiYuan(irrigationCropManageService.getWeatherDayList("GQ340321"));
        infoParam.setLiXin(irrigationCropManageService.getWeatherDayList("GQ341623"));
        infoParam.setFengTai(irrigationCropManageService.getWeatherDayList("GQ340421"));

        return modelProcessUtils.inflowForecast(infoParam);
    }

    public WaterCropIrrigationVo cropDemand(String irrigation) {

        return modelProcessUtils.cropDemand(irrigation);

    }

    public WaterCropIrrigationVo areaProduction(String irrigation) {
        return modelProcessUtils.areaProduction(irrigation);
    }

    public WaterThreeLifeDemandVo threeLifeDemand(String irrigation) {
        return modelProcessUtils.threeLifeDemand(irrigation);
    }

    public WaterSupplyDemandAnalyseVo supplyDemandAnalyse(String irrigation) {
        WaterSupplyDemandAnalyseVo waterSupplyYear = new WaterSupplyDemandAnalyseVo();
        String waterSupplyMonth = modelProcessUtils.waterSupplyMonth(irrigation);
        WaterSupplyDemandAnalyseVo waterSupplyYear001 = modelProcessUtils.waterSupplyYear(irrigation);
        if (waterSupplyYear001 != null) {
            List<String> water_supply = waterSupplyYear001.getWater_supply();
            waterSupplyYear.setWater_supply(water_supply);
//           List<String> waterActual = new ArrayList<>();
//           if (ObjectUtil.isNotEmpty(water_supply)){
//               for (String s : water_supply){
//                   waterActual.add(Double.parseDouble(s)*RandomUtil.randomDouble(50, 80)*0.01+"");
//               }
//           }

            waterSupplyYear.setWaterActual(getWaterActual(irrigation));
            waterSupplyYear.setWater_demand(waterSupplyYear001.getWater_demand());
            waterSupplyYear.setRain(waterSupplyYear001.getRain());
            waterSupplyYear.setTime(waterSupplyYear001.getTime());
        }

        waterSupplyYear.setMonthIrrigate(waterSupplyMonth == null ? "0" : waterSupplyMonth);
        //颍泉YingQuan、颍东YingDong、蒙城MengCheng、潘集PanJi、怀远HuaiYuan、利辛 LiXin、凤台 FengTai
        //颍泉：42%；颍东：45%；利辛：43%；蒙城：50%；凤台：55%；潘集：60%；怀远：57%
        if ("YingQuan".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 42 / 100));

        }
        if ("YingDong".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 45 / 100));
        }
        if ("LiXin".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 43 / 100));
        }
        if ("MengCheng".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 50 / 100));
        }
        if ("PanJi".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 60 / 100));
        }
        if ("HuaiYuan".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 57 / 100));
        }
        if ("FengTai".equals(irrigation)) {
            waterSupplyYear.setAlreadyIrrigate("" + (Double.parseDouble(waterSupplyYear.getMonthIrrigate()) * 55 / 100));
        }


        return waterSupplyYear;
    }

    private List<String> getWaterActual(String irrigation) {
        List<String> waterActual = new ArrayList<>();
        if ("YingQuan".equals(irrigation)) {
            Collections.addAll(waterActual, "24.50", "0", "0", "176.99", "457.17", "0", "0", "0", "68.98", "141.02", "80.64", "0");
        }
        if ("YingDong".equals(irrigation)) {
            Collections.addAll(waterActual, "40.05", "0", "0", "463.82", "1322.44", "0", "0", "0", "446.52", "353.91", "332.09", "0");
        }
        if ("LiXin".equals(irrigation)) {
            Collections.addAll(waterActual, "75.87", "0", "0", "609.87", "2226.75", "0", "0", "0", "41.92", "119.93", "332.09", "0");
        }
        if ("MengCheng".equals(irrigation)) {
            Collections.addAll(waterActual, "134.23", "0", "0", "611.22", "1946.09", "881", "0", "0", "45.84", "436.73", "744.28", "0");
        }
        if ("PanJi".equals(irrigation)) {

            Collections.addAll(waterActual, "0", "0", "0", "135.57", "345.70", "775", "205", "666", "430.19", "337.51", "38.67", "0");
        }
        if ("HuaiYuan".equals(irrigation)) {
            Collections.addAll(waterActual, "0", "0", "0", "877.47", "2507.16", "5897", "1378", "4608", "2739.78", "2418.13", "179.54", "0");
        }
        if ("FengTai".equals(irrigation)) {
            Collections.addAll(waterActual, "0", "0", "0", "227.32", "1207.11", "2202", "0", "0", "1080.27", "1294.99", "139.51", "0");
        }
        return waterActual;
    }

    public JSONObject irrigationPlanDevelop() {
        return modelProcessUtils.irrigationPlanDevelop();
    }

    public List<WaterUseEfficiencyVo> efficiencyManage(WaterEfficiencyManageParam manageParam) {
        return modelProcessUtils.efficiencyManage(manageParam);
    }

    public WaterYearIrrigationDemandVo yearIrrigationDemand() {
        WaterYearIrrigationDemandVo waterYearIrrigationDemandVo = new WaterYearIrrigationDemandVo();
        WaterSchedulePlanDispatchVo dispatchGenerate = dispatchGenerate();
        BeanUtil.copyProperties(dispatchGenerate, waterYearIrrigationDemandVo);

        return waterYearIrrigationDemandVo;
    }

    public WaterSchedulePlanDispatchVo dispatchGenerate() {

        WaterSchedulePlanDispatchVo waterSchedulePlanDispatchVo = new WaterSchedulePlanDispatchVo();
        BigDecimal yingQuanWaterDemand = getWaterDemand("GQ341204");
        //("颍泉区");
        waterSchedulePlanDispatchVo.setYingQuanWaterDemand(yingQuanWaterDemand);
        BigDecimal yingDongWaterDemand = getWaterDemand("GQ341203");
        //("颍东区");
        waterSchedulePlanDispatchVo.setYingDongWaterDemand(yingDongWaterDemand);

        BigDecimal liXinWaterDemand = getWaterDemand("GQ341623");
        //("利辛县");
        waterSchedulePlanDispatchVo.setLiXinWaterDemand(liXinWaterDemand);

        BigDecimal mengChengWaterDemand = getWaterDemand("GQ341622");
        //("蒙城县");
        waterSchedulePlanDispatchVo.setMengChengWaterDemand(mengChengWaterDemand);
        BigDecimal fengTaiWaterDemand = getWaterDemand("GQ340421");
        //("凤台区");
        waterSchedulePlanDispatchVo.setFengTaiWaterDemand(fengTaiWaterDemand);
        BigDecimal panJiWaterDemand = getWaterDemand("GQ340406");
        //("潘集区");
        waterSchedulePlanDispatchVo.setPanJiWaterDemand(panJiWaterDemand);
        BigDecimal huaiYuanWaterDemand = getWaterDemand("GQ340321");
        //("怀远县");
        waterSchedulePlanDispatchVo.setHuaiYuanWaterDemand(huaiYuanWaterDemand);

        return waterSchedulePlanDispatchVo;


    }

    private BigDecimal getWaterDemand(String irrigationCode) {

        String areaPoint = setAreaPoint(irrigationCode);

        WaterThreeLifeDemandVo waterThreeLifeDemandVo = redisService.getCacheObject(areaPoint + "threeLifeDemand");
        BigDecimal allCropWater = new BigDecimal(0);
        if (ObjectUtil.isNotEmpty(waterThreeLifeDemandVo) && ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getProduction())
                && ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getDomestic()) && ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getEcological())) {

            List<String> productions = waterThreeLifeDemandVo.getProduction();
            List<String> domes = waterThreeLifeDemandVo.getDomestic();
            List<String> ecologics = waterThreeLifeDemandVo.getEcological();
            for (String produce : productions) {
                allCropWater = allCropWater.add(new BigDecimal(produce));
            }
            for (String dom : domes) {
                allCropWater = allCropWater.add(new BigDecimal(dom));
            }
            for (String eco : ecologics) {
                allCropWater = allCropWater.add(new BigDecimal(eco));
            }
        }
        if (allCropWater.compareTo(new BigDecimal("0")) > 0) {
            return allCropWater.divide(new BigDecimal("10000")).setScale(2, RoundingMode.HALF_UP);
        }
        return new BigDecimal("0");
    }

    public WaterYearIrrigationDemandYueVo yueIrrigationDemand(IrrigationPlanModelDispatchYueParam dispatchParam) {
        WaterYearIrrigationDemandYueVo waterYearIrrigationDemandYueVo = new WaterYearIrrigationDemandYueVo();

        WaterThreeLifeDemandVo waterThreeLifeDemandVo = redisService.getCacheObject(dispatchParam.getIrrigation() + "threeLifeDemand");

        waterYearIrrigationDemandYueVo.setOneMonth(waterModelRun(waterThreeLifeDemandVo, 1));
        waterYearIrrigationDemandYueVo.setTwoMonth(waterModelRun(waterThreeLifeDemandVo, 2));
        waterYearIrrigationDemandYueVo.setThreeMonth(waterModelRun(waterThreeLifeDemandVo, 3));
        waterYearIrrigationDemandYueVo.setFourMonth(waterModelRun(waterThreeLifeDemandVo, 4));
        waterYearIrrigationDemandYueVo.setFiveMonth(waterModelRun(waterThreeLifeDemandVo, 5));
        waterYearIrrigationDemandYueVo.setSixMonth(waterModelRun(waterThreeLifeDemandVo, 6));
        waterYearIrrigationDemandYueVo.setSevenMonth(waterModelRun(waterThreeLifeDemandVo, 7));
        waterYearIrrigationDemandYueVo.setEightMonth(waterModelRun(waterThreeLifeDemandVo, 8));
        waterYearIrrigationDemandYueVo.setNineMonth(waterModelRun(waterThreeLifeDemandVo, 9));
        waterYearIrrigationDemandYueVo.setTenMonth(waterModelRun(waterThreeLifeDemandVo, 10));
        waterYearIrrigationDemandYueVo.setElevenMonth(waterModelRun(waterThreeLifeDemandVo, 11));
        waterYearIrrigationDemandYueVo.setTwelveMonth(waterModelRun(waterThreeLifeDemandVo, 12));

        return waterYearIrrigationDemandYueVo;
    }

    public String waterModelRun(WaterThreeLifeDemandVo waterThreeLifeDemandVo, int yue) {
        BigDecimal oneMonth = new BigDecimal(0);
        if (ObjectUtil.isNotEmpty(waterThreeLifeDemandVo) && ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getProduction())
                && ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getDomestic()) && ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getEcological())) {

            List<String> productions = waterThreeLifeDemandVo.getProduction();
            List<String> domes = waterThreeLifeDemandVo.getDomestic();
            List<String> ecologics = waterThreeLifeDemandVo.getEcological();
            if (productions.size() >= yue) {
                oneMonth = oneMonth.add(new BigDecimal(productions.get(yue - 1)));
                oneMonth = oneMonth.add(new BigDecimal(domes.get(yue - 1)));
                oneMonth = oneMonth.add(new BigDecimal(ecologics.get(yue - 1)));
            }

        }
        if (oneMonth.compareTo(new BigDecimal("0")) > 0) {
            oneMonth.setScale(2, RoundingMode.HALF_UP);
        }
        return oneMonth.toString();
    }

}
