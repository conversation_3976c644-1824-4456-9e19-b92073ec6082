package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationCropCyclePo;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleParam;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleAddParam;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationCropCycleVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 灌区作物周期Service接口
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
public interface IIrrigationCropCycleService extends IService<IrrigationCropCyclePo>
{

    /**
     * 查询灌区作物周期列表
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 灌区作物周期集合
     */
    public List<IrrigationCropCycleVo> queryList(IrrigationCropCyclePo irrigationCropCycle);

    /**
     * 查询灌区作物周期
     *
     * @param cycleId 灌区作物周期主键
     * @return 灌区作物周期
     */
    public IrrigationCropCycleVo selectIrrigationCropCycleByCycleId(Long cycleId);

    /**
     * 查询灌区作物周期列表
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 灌区作物周期集合
     */
    public List<IrrigationCropCycleVo> selectIrrigationCropCycleList(IrrigationCropCycleParam irrigationCropCycle);

    /**
     * 新增灌区作物周期
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 结果
     */
    public int insertIrrigationCropCycle(IrrigationCropCycleAddParam irrigationCropCycle);

    /**
     * 修改灌区作物周期
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 结果
     */
    public int updateIrrigationCropCycle(IrrigationCropCycleEditParam irrigationCropCycle);

    /**
     * 批量删除灌区作物周期
     *
     * @param cycleIds 需要删除的灌区作物周期主键集合
     * @return 结果
     */
    public int deleteIrrigationCropCycleByCycleIds(Long[] cycleIds);

    /**
     * 删除灌区作物周期信息
     *
     * @param cycleId 灌区作物周期主键
     * @return 结果
     */
    public int deleteIrrigationCropCycleByCycleId(Long cycleId);

}
