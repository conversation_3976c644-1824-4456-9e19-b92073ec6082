package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationRegionInfoPo;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoParam;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoAddParam;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationRegionInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 灌区区域信息Service接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface IIrrigationRegionInfoService extends IService<IrrigationRegionInfoPo>
{

    /**
     * 查询灌区区域信息列表
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 灌区区域信息集合
     */
    public List<IrrigationRegionInfoVo> queryList(IrrigationRegionInfoPo irrigationRegionInfo);

    /**
     * 查询灌区区域信息
     *
     * @param id 灌区区域信息主键
     * @return 灌区区域信息
     */
    public IrrigationRegionInfoVo selectIrrigationRegionInfoById(Long id);

    /**
     * 查询灌区区域信息列表
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 灌区区域信息集合
     */
    public List<IrrigationRegionInfoVo> selectIrrigationRegionInfoList(IrrigationRegionInfoParam irrigationRegionInfo);

    /**
     * 新增灌区区域信息
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 结果
     */
    public int insertIrrigationRegionInfo(IrrigationRegionInfoAddParam irrigationRegionInfo);

    /**
     * 修改灌区区域信息
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 结果
     */
    public int updateIrrigationRegionInfo(IrrigationRegionInfoEditParam irrigationRegionInfo);

    /**
     * 批量删除灌区区域信息
     *
     * @param ids 需要删除的灌区区域信息主键集合
     * @return 结果
     */
    public int deleteIrrigationRegionInfoByIds(Long[] ids);

    /**
     * 删除灌区区域信息信息
     *
     * @param id 灌区区域信息主键
     * @return 结果
     */
    public int deleteIrrigationRegionInfoById(Long id);

}
