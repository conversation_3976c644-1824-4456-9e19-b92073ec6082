package com.jhdr.irrigation.service;

import java.util.List;

import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.IrrigationCropGrowPo;
import com.jhdr.irrigation.entity.vo.IrrigationCropGrowVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jhdr.irrigation.entity.vo.StStationWaterMonthPumpVo;
import com.jhdr.irrigation.entity.vo.StStationWaterYearPumpVo;

/**
 * 灌区作物生长期Service接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface IIrrigationCropGrowService extends IService<IrrigationCropGrowPo>
{

    /**
     * 查询灌区作物生长期列表
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 灌区作物生长期集合
     */
    public List<IrrigationCropGrowVo> queryList(IrrigationCropGrowPo irrigationCropGrow);

    /**
     * 查询灌区作物生长期
     *
     * @param id 灌区作物生长期主键
     * @return 灌区作物生长期
     */
    public IrrigationCropGrowVo selectIrrigationCropGrowById(Long id);

    /**
     * 查询灌区作物生长期列表
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 灌区作物生长期集合
     */
    public List<IrrigationCropGrowVo> selectIrrigationCropGrowList(IrrigationCropGrowParam irrigationCropGrow);

    /**
     * 新增灌区作物生长期
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 结果
     */
    public int insertIrrigationCropGrow(IrrigationCropGrowAddParam irrigationCropGrow);

    /**
     * 修改灌区作物生长期
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 结果
     */
    public int updateIrrigationCropGrow(IrrigationCropGrowEditParam irrigationCropGrow);

    /**
     * 批量删除灌区作物生长期
     *
     * @param ids 需要删除的灌区作物生长期主键集合
     * @return 结果
     */
    public int deleteIrrigationCropGrowByIds(Long[] ids);



    // 泵站年统计
    List<StStationWaterYearPumpVo> pumpYearList(StStationWaterYearParam stationWaterYearParam);

    // 闸站年统计
    List<StStationWaterYearPumpVo> floodYearList(StStationWaterYearParam stationWaterYearParam);

    // 泵站月统计
    List<StStationWaterMonthPumpVo> pumpMonthList(StStationWaterMonthParam stationWaterMonthParam);

    // 闸站月统计
    List<StStationWaterMonthPumpVo> floodMonthList(StStationWaterMonthParam stationWaterMonthParam);
}
