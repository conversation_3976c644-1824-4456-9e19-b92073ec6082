package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.IrrigationPlanPo;
import com.jhdr.irrigation.entity.param.IrrigationPlanParam;
import com.jhdr.irrigation.entity.param.IrrigationPlanAddParam;
import com.jhdr.irrigation.entity.param.IrrigationPlanEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationPlanVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 灌溉计划Service接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface IIrrigationPlanService extends IService<IrrigationPlanPo>
{

    /**
     * 查询灌溉计划列表
     *
     * @param irrigationPlan 灌溉计划
     * @return 灌溉计划集合
     */
    public List<IrrigationPlanVo> queryList(IrrigationPlanPo irrigationPlan);

    /**
     * 查询灌溉计划
     *
     * @param id 灌溉计划主键
     * @return 灌溉计划
     */
    public IrrigationPlanVo selectIrrigationPlanById(Long id);

    /**
     * 查询灌溉计划列表
     *
     * @param irrigationPlan 灌溉计划
     * @return 灌溉计划集合
     */
    public List<IrrigationPlanVo> selectIrrigationPlanList(IrrigationPlanParam irrigationPlan);

    /**
     * 新增灌溉计划
     *
     * @param irrigationPlan 灌溉计划
     * @return 结果
     */
    public int insertIrrigationPlan(IrrigationPlanAddParam irrigationPlan);

    /**
     * 修改灌溉计划
     *
     * @param irrigationPlan 灌溉计划
     * @return 结果
     */
    public int updateIrrigationPlan(IrrigationPlanEditParam irrigationPlan);

    /**
     * 批量删除灌溉计划
     *
     * @param ids 需要删除的灌溉计划主键集合
     * @return 结果
     */
    public int deleteIrrigationPlanByIds(Long[] ids);

    /**
     * 删除灌溉计划信息
     *
     * @param id 灌溉计划主键
     * @return 结果
     */
    public int deleteIrrigationPlanById(Long id);

}
