package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.MD5Utils;
import com.jhdr.common.redis.service.RedisService;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.vo.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 模型处理工具类
 * @date 2024/10/18 17:14
 */
@Component
public class ModelProcessUtils {

    @Resource
    private RedisService redisService;
    //本地
        //  private String baseUrl = "http://www.plakeco.cn:8025";
        //管理局
//        private String baseUrl = "http://***********:8025";
        //水利厅
        private String baseUrl = "http://**********:8025";
        //
    //请求初始化降雨量模型接口
    private String initialRainModelUrl = baseUrl + "/api/v1/WaterDemandStatistics/runModel";
    //获取降雨量模型图片base64
    private String getRainModelPicUrl = baseUrl + "/api/v1/WaterDemandStatistics/image";
    //蓄水量模型接口
    private String initialWaterModelUrl = baseUrl + "/api/v1/WaterDemandStatistics/waterStorage";
    //获取典型年份接口
    private String getTypicalYearUrl = baseUrl + "/api/v1/WaterDemandStatistics/typicalYear";
    //丰平枯年份判断
    private String checkYearUrl = baseUrl + "/api/v1/WaterDemandStatistics/hydroclassification";
    //区域需水量模型计算接口
    private String modelComputeAreaUrl = baseUrl + "/api/v1/AreaDemand/runModel";
    //获取模型水深图片数据
    private String getModelAreaDepth = baseUrl + "/api/v1/AreaDemand/depth";
    //获取区域灌溉需水量
    private String getAreaWaterModelUrl = baseUrl + "/api/v1/AreaDemand/waterDemand";
    //运行需水分析 茨淮新河整体模型运行
    private String waterModelRunUrl = baseUrl + "/api/v1/WaterForecast/RunModel";
    //运行 茨淮新河来水预报
    private String inflowForecastUrl = baseUrl + "/api/v1/WaterForecast/WaterArrival";
    // 获取茨淮新河 - 作物需水
    private String cropDemandUrl = baseUrl + "/api/v1/WaterForecast/CropWaterDemand?irrigation=";
    // 获取茨淮新河 - 区内产水
    private String areaProductionUrl = baseUrl + "/api/v1/WaterForecast/WaterProduction?irrigation=";
    // 获取茨淮新河 - 三生需水
    private String threeLifeDemandUrl = baseUrl + "/api/v1/WaterForecast/LifeWaterDemand?irrigation=";

    // 供水分析-模型输出-当月灌溉量
    private String waterSupplyMonthUrl = baseUrl + "/api/v1/WaterForecast/WaterSupply/Month?irrigation=";
    //供水分析-模型输出-供需水图表
    private String waterSupplyYearUrl = baseUrl + "/api/v1/WaterForecast/WaterSupply/Year?irrigation=";
    //供用水分析-灌溉计划制定
    private String irrigationPlanDevelopUrl = baseUrl + "/api/v1/WaterSourceScheduling/IrrigationPlan";
    //供用水分析-用水效率
    private String efficiencyManageUrl = baseUrl + "/api/v1/WaterSourceScheduling/WaterUseEfficiency";


    //初始化降雨模型
    public IrrigationModelDataVo initialRainModel(IrrigationPlanModelParam modelParam) {

        redisService.setCacheObject("newRainModelParam"+modelParam.getTimeType(), modelParam);
        List<IrrigationPlanModelRainParam> rains = modelParam.getRains();
        // 使用流 API 将 List 转换为 Map
        String rawData = MD5Utils.md5Hex(rains.toString(), "UTF-8");
        String initialRainModelMd5 = redisService.getCacheObject("newRainModelInput"+modelParam.getTimeType());
        if (rawData.equals(initialRainModelMd5)) {

            return modelComputeResult(modelParam);
        } else {
//            Map<String, Double> rainMap = rains.stream()
//                    .collect(Collectors.toMap(
//                            IrrigationPlanModelRainParam::getYear,  // 键为年份
//                            IrrigationPlanModelRainParam::getRain   // 值为降雨量
//                    ));
            String post = HttpUtil.post(initialRainModelUrl, JSONUtil.toJsonStr(rains));
            redisService.setCacheObject("newRainModelInput"+modelParam.getTimeType(), rawData);
            redisService.setCacheObject("newGetRainModelPic"+modelParam.getTimeType(), null);
            redisService.setCacheObject("newGetTypicalYear"+modelParam.getTimeType(), null);
            redisService.setCacheObject("newWaterModelInput"+modelParam.getTimeType(), null);
            redisService.setCacheObject("newWaterModelOutput"+modelParam.getTimeType(), null);

        }
        return null;
    }

    //丰平枯年份判断 1 23
    public String checkYear(String rain) {
        String checkYear = HttpUtil.get(checkYearUrl + "?rainfall=" + rain);
        String checkYearResponse = processPostResponse(checkYear);
        if (ObjectUtil.isNotEmpty(checkYearResponse)) {
            return checkYearResponse;
        }
        return null;
    }

    //区域需水量模型计算
    public IrrigationModelAreaTypeVo modelComputeArea(IrrigationPlanModelComputeAreaParam commissionArea) {

        String inputMd5 = MD5Utils.md5Hex(commissionArea.toString(), "UTF-8");
        IrrigationPlanModelComputeAreaParam areaParam = redisService.getCacheObject(inputMd5 + "input");
        if (ObjectUtil.isNotEmpty(areaParam)) {
            IrrigationModelAreaTypeVo modelComputeAreaOutput = redisService.getCacheObject(inputMd5 + "output");
            if (ObjectUtil.isNotEmpty(modelComputeAreaOutput)) {
//                modelComputeAreaOutput.setCornLine(redisService.getCacheObject(inputMd5+"cornLine"));
//                modelComputeAreaOutput.setPaddyLine(redisService.getCacheObject(inputMd5+"paddyLine"));
//                modelComputeAreaOutput.setWheatLine(redisService.getCacheObject(inputMd5+"wheatLine"));
                return modelComputeAreaOutput;
            } else {
                IrrigationPlanModelAreaDepthParam commissionAreaDepthParam = new IrrigationPlanModelAreaDepthParam();

                String areaYear = redisService.getCacheObject(inputMd5 + "areaYear");
                String areaPoint = redisService.getCacheObject(inputMd5 + "areaPoint");
                List<String> areaCrop = redisService.getCacheObject(inputMd5 + "areaCrop");
                commissionAreaDepthParam.setArea(areaPoint);
                commissionAreaDepthParam.setYear(areaYear);
                commissionAreaDepthParam.setCropList(areaCrop);
                IrrigationModelAreaTypeVo irrigationModelAreaTypeVo = modelAreaDepth(commissionAreaDepthParam);
                if (ObjectUtil.isNotEmpty(irrigationModelAreaTypeVo)) {
                    irrigationModelAreaTypeVo.setCornLine(redisService.getCacheObject(inputMd5 + "cornLine"));
                    irrigationModelAreaTypeVo.setPaddyLine(redisService.getCacheObject(inputMd5 + "paddyLine"));
                    irrigationModelAreaTypeVo.setWheatLine(redisService.getCacheObject(inputMd5 + "wheatLine"));
                    redisService.setCacheObject(inputMd5 + "output", irrigationModelAreaTypeVo);
                    return irrigationModelAreaTypeVo;
                }
            }
        } else {
            List<AreaDataParam> corn = commissionArea.getCorn();
            String areaYear = "2022";
            String areaPoint = "Point1";
            List<String> crops = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(corn)) {
                areaPoint = corn.get(0).getArea();
                redisService.setCacheObject(inputMd5 + "cornLine", corn.get(0).getMaxFieldDepth());
                areaYear = corn.get(0).getYear();
                crops.add("corn");
            }
            List<AreaDataParam> paddy = commissionArea.getPaddy();
            if (ObjectUtil.isNotEmpty(paddy)) {
                areaPoint = paddy.get(0).getArea();
                redisService.setCacheObject(inputMd5 + "paddyLine", paddy.get(0).getMaxFieldDepth());
                areaYear = paddy.get(0).getYear();
                crops.add("paddy");
            }
            List<AreaDataParam> wheat = commissionArea.getWheat();
            if (ObjectUtil.isNotEmpty(wheat)) {
                areaPoint = wheat.get(0).getArea();
                redisService.setCacheObject(inputMd5 + "wheatLine", wheat.get(0).getMaxFieldDepth());
                areaYear = wheat.get(0).getYear();
                crops.add("wheat");
            }
            redisService.setCacheObject(inputMd5 + "areaYear", areaYear);
            redisService.setCacheObject(inputMd5 + "areaPoint", areaPoint);
            redisService.setCacheObject(inputMd5 + "areaCrop", crops);
            redisService.setCacheObject(inputMd5 + "input", commissionArea);

            HttpUtil.post(modelComputeAreaUrl, JSONUtil.toJsonStr(commissionArea));

            redisService.setCacheObject(inputMd5 + "input", commissionArea);

            IrrigationModelCropDataVo areaWaterModel = getAreaWaterModel(commissionArea);
            redisService.setCacheObject(areaPoint + "WaterOutput", areaWaterModel);
        }
        return null;
    }

    //降雨模型计算结果封装
    private IrrigationModelDataVo modelComputeResult(IrrigationPlanModelParam modelParam) {
        String rainModelPic = redisService.getCacheObject("newGetRainModelPic"+modelParam.getTimeType());
        if (ObjectUtil.isEmpty(rainModelPic)) {
            getRainModelPic(modelParam.getTimeType());
            initialWaterModel(modelParam);
            return null;
        } else {
            IrrigationModelDataVo irrigationModelDataVo = new IrrigationModelDataVo();
            irrigationModelDataVo.setWaterDemand(initialWaterModel(modelParam));
            irrigationModelDataVo.setTypicalYear(getTypicalYear(modelParam.getTimeType()));
            irrigationModelDataVo.setRainImg(rainModelPic);

            return irrigationModelDataVo;
        }

    }

    //获取典型年份
    public List<IrrigationModelDataYearVo> getTypicalYear(String timeType) {
        List<IrrigationModelDataYearVo> rainList = new ArrayList<>();

        List<IrrigationModelDataYearVo> getTypicalYear = redisService.getCacheObject("getTypicalYear"+timeType);
        if (ObjectUtil.isNotEmpty(getTypicalYear)) {
            return getTypicalYear;
        }
        String rainYear = HttpUtil.get(getTypicalYearUrl+"?time="+timeType);

        String rainYearResponse = processPostResponse(rainYear);

        if (ObjectUtil.isNotEmpty(rainYearResponse)) {

            WaterModelData data = JSONUtil.parseObj(rainYearResponse).toBean(WaterModelData.class);
            //todo 典型年份下灌溉需水量定

            //丰水年
            IrrigationModelDataYearVo dataWaterYear = new IrrigationModelDataYearVo().
                    setYear(data.getWet().getYear()).setRain(data.getWet().getValue()).setYearType("丰水年25%");
            //平水年
            IrrigationModelDataYearVo dataWaterYear1 = new IrrigationModelDataYearVo().
                    setYear(data.getNormal().getYear()).setRain(data.getNormal().getValue()).setYearType("平水年50%");
            //枯水年
            IrrigationModelDataYearVo dataWaterYear2 = new IrrigationModelDataYearVo().
                    setYear(data.getDry().getYear()).setRain(data.getDry().getValue()).setYearType("枯水年75%");
            rainList.add(dataWaterYear);
            rainList.add(dataWaterYear1);
            rainList.add(dataWaterYear2);

            redisService.setCacheObject("newGetTypicalYear"+timeType, rainList);
            return rainList;
        }
        return null;
    }

    //获取蓄水量模型
    public List<IrrigationModelDataWaterVo> initialWaterModel(IrrigationPlanModelParam modelParam) {

        List<IrrigationModelDataWaterVo> waterDemand = new ArrayList<>();
        List<IrrigationPlanModelWaterDemandParam> waveModels = modelParam.getWaterDemand();
        if (ObjectUtil.isNotEmpty(waveModels)) {
            // 使用流 API 将 List 转换为 Map
            String waterData = MD5Utils.md5Hex(waveModels.toString(), "UTF-8");
            Map<String, Double> waterMap = waveModels.stream()
                    .collect(Collectors.toMap(
                            IrrigationPlanModelWaterDemandParam::getYear,  // 键为年份
                            IrrigationPlanModelWaterDemandParam::getWaterDemand   // 值为蓄水量
                    ));
            String initialWaterModelMd5 = redisService.getCacheObject(modelParam.getTimeType()+"newWaterModelInput");
            if (waterData.equals(initialWaterModelMd5)) {

                List<IrrigationModelDataWaterVo> waterModelOutput = redisService.getCacheObject(modelParam.getTimeType()+"newWaterModelOutput");
                if (ObjectUtil.isNotEmpty(waterModelOutput)) {
                    return waterModelOutput;
                }
            }
            redisService.setCacheObject(modelParam.getTimeType()+"newWaterModelInput", waterData);
            String waterPost = HttpUtil.post(initialWaterModelUrl, JSONUtil.toJsonStr(waterMap));

            String waterPostResponse = processPostResponse(waterPost);

            if (ObjectUtil.isNotEmpty(waterPostResponse)) {

                WaterModelData data = JSONUtil.parseObj(waterPostResponse).toBean(WaterModelData.class);
                //todo 典型年份下灌溉需水量定

                //丰水年
                IrrigationModelDataWaterVo dataWaterYear = new IrrigationModelDataWaterVo().
                        setYear(data.getWet().getYear()).setWaterDemand(data.getWet().getValue()).setYearType("丰水年25%");
                //平水年
                IrrigationModelDataWaterVo dataWaterYear1 = new IrrigationModelDataWaterVo().
                        setYear(data.getNormal().getYear()).setWaterDemand(data.getNormal().getValue()).setYearType("平水年50%");
                //枯水年
                IrrigationModelDataWaterVo dataWaterYear2 = new IrrigationModelDataWaterVo().
                        setYear(data.getDry().getYear()).setWaterDemand(data.getDry().getValue()).setYearType("枯水年75%");
                waterDemand.add(dataWaterYear);
                waterDemand.add(dataWaterYear1);
                waterDemand.add(dataWaterYear2);

                redisService.setCacheObject("newWaterModelOutput", waterDemand);
                return waterDemand;
            }
        }

        return null;
    }

    //获取模型图片
    public void getRainModelPic(String timeType) {

        String response = HttpUtil.get(getRainModelPicUrl + "?time=" + timeType);
        String dataBase64 = processPostResponse(response);
        if (ObjectUtil.isNotEmpty(dataBase64)) {
            redisService.setCacheObject("newGetRainModelPic"+timeType, dataBase64);
        }
    }


    //获取模型结果水深
    public IrrigationModelAreaTypeVo modelAreaDepth(IrrigationPlanModelAreaDepthParam depthParam) {
        String post = HttpUtil.post(getModelAreaDepth, JSONUtil.toJsonStr(depthParam));

        String dataBase64 = processPostResponse(post);
        if (ObjectUtil.isNotEmpty(dataBase64)) {
            return JSONUtil.parseObj(dataBase64).toBean(IrrigationModelAreaTypeVo.class);
        }
        return null;
    }


    //获取区域灌溉需水量
    public IrrigationModelCropDataVo getAreaWaterModel(IrrigationPlanModelComputeAreaParam commissionArea) {

        String post = HttpUtil.post(getAreaWaterModelUrl, JSONUtil.toJsonStr(commissionArea));

        String dataBase64 = processPostResponse(post);
        if (ObjectUtil.isNotEmpty(dataBase64)) {
            return JSONUtil.parseObj(dataBase64).toBean(IrrigationModelCropDataVo.class);
        }
        return null;
    }

    //处理模型返回数据
    public String processPostResponse(String response) {
        if (StrUtil.isNotBlank(response)) {
            // 解析JSON响应
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            // 检查code字段，确保请求成功
            int code = jsonResponse.getInt("code");
            if (code == 200) {
                // 提取数据
                String data = jsonResponse.getStr("data");

                return data;
            } else {
                return null;
            }
        }
        return null;
    }


    public void waterModelRun(WaterModelRunParam modelRunParam) {
        modelRunParam.setSimulateWeather("2024");

        String post = HttpUtil.post(waterModelRunUrl, JSONUtil.toJsonStr(modelRunParam));
    }

    public WaterInflowForecastVo inflowForecast(WaterInflowForecastParam infoParam) {

        String post = HttpUtil.post(inflowForecastUrl, JSONUtil.toJsonStr(infoParam));
        String postData = processPostResponse(post);
        if (ObjectUtil.isNotEmpty(postData)) {
            return JSONUtil.parseObj(postData).toBean(WaterInflowForecastVo.class);
        }
        return null;
    }

    public WaterCropIrrigationVo cropDemand(String irrigation) {
        String get = HttpUtil.get(cropDemandUrl + irrigation);
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {

            WaterCropIrrigationVo irrigationVo = JSONUtil.parseObj(getData).toBean(WaterCropIrrigationVo.class);
            redisService.setCacheObject(irrigation + "cropDemand", irrigationVo);
            return irrigationVo;
        }
        return null;

    }

    public WaterCropIrrigationVo areaProduction(String irrigation) {
        String get = HttpUtil.get(areaProductionUrl + irrigation);
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {
            return JSONUtil.parseObj(getData).toBean(WaterCropIrrigationVo.class);
        }
        return null;
    }

    public WaterThreeLifeDemandVo threeLifeDemand(String irrigation) {

        String get = HttpUtil.get(threeLifeDemandUrl + irrigation);
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {
            WaterThreeLifeDemandVo waterThreeLifeDemandVo = JSONUtil.parseObj(getData).toBean(WaterThreeLifeDemandVo.class);
            redisService.setCacheObject(irrigation + "threeLifeDemand", waterThreeLifeDemandVo);
            return waterThreeLifeDemandVo;
        }
        return null;
    }

    public String waterSupplyMonth(String irrigation) {
        String get = HttpUtil.get(waterSupplyMonthUrl + irrigation);
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {

            return getData;
        }
        return null;
    }

    public WaterSupplyDemandAnalyseVo waterSupplyYear(String irrigation) {
        String get = HttpUtil.get(waterSupplyYearUrl + irrigation);
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {
            WaterSupplyDemandAnalyseVo demandAnalyseVo = JSONUtil.parseObj(getData).toBean(WaterSupplyDemandAnalyseVo.class);
            redisService.setCacheObject(irrigation + "waterSupplyYear", demandAnalyseVo);
            return demandAnalyseVo;
        }
        return null;
    }

    public JSONObject irrigationPlanDevelop() {
        String get = HttpUtil.get(irrigationPlanDevelopUrl);
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {
            JSONObject jsonResponse = JSONUtil.parseObj(getData);
            return jsonResponse;
        }
        return null;
    }

    public List<WaterUseEfficiencyVo> efficiencyManage(WaterEfficiencyManageParam manageParam) {

        Map<String, Object> manageParamMap = new HashMap<>();
        manageParamMap.put("YingQuan", manageParam.getYingQuan());
        manageParamMap.put("YingDong", manageParam.getYingDong());
        manageParamMap.put("FengTai", manageParam.getFengTai());
        manageParamMap.put("HuaiYuan", manageParam.getHuaiYuan());
        manageParamMap.put("LiXin", manageParam.getLiXin());
        manageParamMap.put("MengCheng", manageParam.getMengCheng());
        manageParamMap.put("PanJi", manageParam.getPanJi());

        String get = HttpUtil.post(efficiencyManageUrl + "/" + manageParam.getYear(), JSONUtil.toJsonStr(manageParamMap));
        String getData = processPostResponse(get);
        if (ObjectUtil.isNotEmpty(getData)) {
            List<WaterUseEfficiencyVo> demandAnalyseVo = JSONUtil.toList(getData, WaterUseEfficiencyVo.class);

            return demandAnalyseVo;
        }
        return null;
    }
}
