package com.jhdr.irrigation.service;

import java.util.List;

import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.IrrigationCropManagePo;
import com.jhdr.irrigation.entity.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 灌区作物管理Service接口
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
public interface IIrrigationCropManageService extends IService<IrrigationCropManagePo>
{



    /**
     * 查询灌区作物管理
     *
     * @param id 灌区作物管理主键
     * @return 灌区作物管理
     */
    public IrrigationCropManageVo selectIrrigationCropManageById(Long id);

    /**
     * 查询灌区作物管理列表
     *
     * @param irrigationCropManage 灌区作物管理
     * @return 灌区作物管理集合
     */
    public List<IrrigationCropManageVo> selectIrrigationCropManageList(IrrigationCropManageParam irrigationCropManage);

    /**
     * 新增灌区作物管理
     *
     * @param irrigationCropManage 灌区作物管理
     * @return 结果
     */
    public int insertIrrigationCropManage(IrrigationCropManageAddParam irrigationCropManage);

    /**
     * 修改灌区作物管理
     *
     * @param irrigationCropManage 灌区作物管理
     * @return 结果
     */
    public int updateIrrigationCropManage(IrrigationCropManageEditParam irrigationCropManage);

    /**
     * 批量删除灌区作物管理
     *
     * @param ids 需要删除的灌区作物管理主键集合
     * @return 结果
     */
    public int deleteIrrigationCropManageByIds(Long[] ids);

    /**
     * 删除灌区作物管理信息
     *
     * @param id 灌区作物管理主键
     * @return 结果
     */
    public int deleteIrrigationCropManageById(Long id);

    /**
     * 获取灌区作物统计
     * @param irrigationCode
     * @return
     */
    List<IrrigationCropStatisticsVo> getBaseCropStatisticsByIrrigationCode(String irrigationCode);

    /**
     * 获取灌区泵站统计
     * @param irrigationCode
     * @return
     */
    List<IrrigationPumpStatisticsVo> getBasePumpStatisticsByIrrigationCode(String irrigationCode);

    // 农作物需水量
    List<IrrigationCropWaterDemandStatisticsVo> getWaterDemandCropStatistics(IrrigationWaterParam waterParam);
    // 泵站需水量
    List<IrrigationPumpWaterDemandStatisticsVo> getWaterDemandPumpStatistics(IrrigationWaterParam waterParam);


    //获取灌区基础信息
    IrrigationBaseVo getIrrigationBaseInfo();
    //获取灌区区域信息
    IrrigationRegionVo getRegionInfo(String irrigationCode);
    //获取泵站实时信息
    List<IrrigationPumpRealVo> getPumpRealList(String irrigationCode);
    //获取泵站水位信息
    List<IrrigationPumpRealVo> getPumpLineList(String irrigationCode);
    //获取泵站 历史水位和流量信息
    List<IrrigationPumpLineVo> getPumpLineListByTime(IrrigationPumpLineParam pumpLineParam);
    //获取泵站 感知信息
    List<IrrigationPumpLineVo> getPerceptionPumpTabulation(IrrigationPumpLineParam pumpLineParam);
    //获取泵站下拉列表
    List<IrrigationPumpPullVo> getPumpList(String addvcd);
    //获取七天天气信息
    List<IrrigationWeatherDayVo> getWeatherDayList(String irrigationCode);
    //获取泵站流量汇总
    List<IrrigationPumpAllVo> getPerceptionPumpAll(IrrigationPumpAllParam pumpAllParam);
    //检查灌区作物 中泵站灌溉面积要大于种植面积
    String allCheck(List<IrrigationCropManageAddParam> irrigationCropManages);

    List<IrrigationCropWaterDemandStatisticsVo> getAllWaterDemandCropStatistics();

    WaterCropIrrigatedAreaVo getCropIrrigatedArea();
}
