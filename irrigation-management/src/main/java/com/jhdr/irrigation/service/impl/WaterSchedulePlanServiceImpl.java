package com.jhdr.irrigation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import com.jhdr.common.core.utils.DateUtils;
import com.jhdr.common.redis.service.RedisService;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.WaterSchedulePlanLevelPo;
import com.jhdr.irrigation.entity.po.WaterSchedulePlanPumpPo;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.IIrrigationCropManageService;
import com.jhdr.irrigation.service.IWaterSchedulePlanLevelService;
import com.jhdr.irrigation.service.IWaterSchedulePlanPumpService;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.StringUtils;
import com.jhdr.irrigation.entity.po.WaterSchedulePlanPo;
import com.jhdr.irrigation.mapper.WaterSchedulePlanMapper;
import com.jhdr.irrigation.service.IWaterSchedulePlanService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 灌区管理-调度方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Service
public class WaterSchedulePlanServiceImpl extends ServiceImpl<WaterSchedulePlanMapper, WaterSchedulePlanPo> implements IWaterSchedulePlanService {

    @Resource
    IWaterSchedulePlanLevelService waterSchedulePlanLevelService;

    @Resource
    private IWaterSchedulePlanPumpService waterSchedulePlanPumpService;

    @Autowired
    private IIrrigationCropManageService irrigationCropManageService;



    @Override
    public List<WaterSchedulePlanVo> queryList(WaterSchedulePlanPo waterSchedulePlan) {
        LambdaQueryWrapper<WaterSchedulePlanPo> lqw = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(waterSchedulePlan.getPlanName())) {
            lqw.like(WaterSchedulePlanPo::getPlanName, waterSchedulePlan.getPlanName());
        }
        if (StringUtils.isNotBlank(waterSchedulePlan.getWeatherParameter())) {
            lqw.eq(WaterSchedulePlanPo::getWeatherParameter, waterSchedulePlan.getWeatherParameter());
        }
        if (StringUtils.isNotBlank(waterSchedulePlan.getStartTime())) {
            lqw.eq(WaterSchedulePlanPo::getStartTime, waterSchedulePlan.getStartTime());
        }
        if (StringUtils.isNotBlank(waterSchedulePlan.getEndTime())) {
            lqw.eq(WaterSchedulePlanPo::getEndTime, waterSchedulePlan.getEndTime());
        }
        List<WaterSchedulePlanVo> waterSchedulePlanVos = BeanUtil.copyToList(this.list(lqw), WaterSchedulePlanVo.class);
        return waterSchedulePlanVos;
    }

    /**
     * 查询灌区管理-调度方案
     *
     * @param planId 灌区管理-调度方案主键
     * @return 灌区管理-调度方案
     */
    @Override
    public WaterSchedulePlanVo selectWaterSchedulePlanByPlanId(Long planId) {
        return baseMapper.selectWaterSchedulePlanByPlanId(planId);
    }

    /**
     * 查询灌区管理-调度方案列表
     *
     * @param waterSchedulePlan 灌区管理-调度方案
     * @return 灌区管理-调度方案
     */
    @Override
    public List<WaterSchedulePlanVo> selectWaterSchedulePlanList(WaterSchedulePlanParam waterSchedulePlan) {
        return baseMapper.selectWaterSchedulePlanList(waterSchedulePlan);
    }

    /**
     * 新增灌区管理-调度方案
     *
     * @param waterSchedulePlanAddParam 灌区管理-调度方案
     * @return 结果
     */
    @Override
    public int insertWaterSchedulePlan(WaterSchedulePlanAddParam waterSchedulePlanAddParam) {
        WaterSchedulePlanPo waterSchedulePlan = new WaterSchedulePlanPo();
        BeanUtil.copyProperties(waterSchedulePlanAddParam, waterSchedulePlan);
        waterSchedulePlan.setCreateTime(DateUtils.getNowDate());
        baseMapper.insert(waterSchedulePlan);

        //处理泵站 抽水数据 开机时长
        if (ObjectUtil.isNotEmpty(waterSchedulePlanAddParam.getPlanPumpList())) {
            WaterSchedulePlanPumpListVo planPumpList = waterSchedulePlanAddParam.getPlanPumpList();

            List<WaterSchedulePlanPumpPo> planPumpPos = new ArrayList<>();

            //GQ341623利辛灌区 泵站数据
            List<WaterSchedulePumpWaterVo> liXinPumpList = planPumpList.getLiXinPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, liXinPumpList, "GQ341623");

            //GQ340421 凤台灌区
            List<WaterSchedulePumpWaterVo> fengTaiPumpList = planPumpList.getFengTaiPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, fengTaiPumpList, "GQ340421");

            //GQ340321 怀远灌区
            List<WaterSchedulePumpWaterVo> huaiYuanPumpList = planPumpList.getHuaiYuanPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, huaiYuanPumpList, "GQ340321");
            //GQ341622 蒙城灌区
            List<WaterSchedulePumpWaterVo> mengChengPumpList = planPumpList.getMengChengPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, mengChengPumpList, "GQ341622");
            //GQ340406潘集灌区
            List<WaterSchedulePumpWaterVo> panJiPumpList = planPumpList.getPanJiPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, panJiPumpList, "GQ340406");
            //GQ341203 颍东区
            List<WaterSchedulePumpWaterVo> yingDongPumpList = planPumpList.getYingDongPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, yingDongPumpList, "GQ341203");
            //GQ341204 颍泉区
            List<WaterSchedulePumpWaterVo> yingQuanPumpList = planPumpList.getYingQuanPumpList();
            addPlanPumpDataCopy(waterSchedulePlan.getPlanId(), planPumpPos, yingQuanPumpList, "GQ341204");

            waterSchedulePlanPumpService.saveBatch(planPumpPos);
        }

        //处理水位 历史数据
        if (ObjectUtil.isNotEmpty(waterSchedulePlanAddParam.getPlanLineList())) {
            WaterSchedulePlanLineListVo planLineList = waterSchedulePlanAddParam.getPlanLineList();

            List<WaterSchedulePlanLevelPo> planLinePos = new ArrayList<>();

            Map<String, String> gateStationCodes = new HashMap<>();
            gateStationCodes.put("chaHua", "341203000022");
            gateStationCodes.put("kanTuan", "341623000096");
            gateStationCodes.put("shangQiao", "108131001");

            for (Map.Entry<String, String> entry : gateStationCodes.entrySet()) {
                List<?> sourceList = null;
                switch (entry.getKey()) {
                    case "chaHua":
                        //插花 涵闸 341203000022 上水位
                        sourceList = planLineList.getChaHuaLineList();
                        break;
                    case "kanTuan":
                        //阚疃  341623000096 上水位
                        sourceList = planLineList.getKanTuanLineList();
                        break;
                    case "shangQiao":
                        //上桥 涵闸 108131001 上水位
                        sourceList = planLineList.getShangQiaoLineList();
                        break;
                }

                if (ObjectUtil.isNotEmpty(sourceList)) {
                    List<WaterSchedulePlanLevelPo> targetList = BeanUtil.copyToList(sourceList, WaterSchedulePlanLevelPo.class);
                    targetList.forEach(item -> {
                        item.setPlanId(waterSchedulePlan.getPlanId());
                        item.setGateStationCode(entry.getValue());
                    });
                    planLinePos.addAll(targetList);
                }
            }

            waterSchedulePlanLevelService.saveBatch(planLinePos);
        }
        return 1;
    }

    private void addPlanPumpDataCopy(Long planId, List<WaterSchedulePlanPumpPo> planPumpPos, List<WaterSchedulePumpWaterVo> pumpWaterVos, String irrigationCode) {
        if (ObjectUtil.isNotEmpty(pumpWaterVos)) {
            List<WaterSchedulePlanPumpPo> pumpPos = BeanUtil.copyToList(pumpWaterVos, WaterSchedulePlanPumpPo.class);
            pumpPos.forEach(pumpPo -> {
                pumpPo.setPlanId(planId);
                pumpPo.setIrrigationCode(irrigationCode);
            });
            planPumpPos.addAll(pumpPos);
        }
    }

    /**
     * 修改灌区管理-调度方案
     *
     * @param waterSchedulePlanEditParam 灌区管理-调度方案
     * @return 结果
     */
    @Override
    public int updateWaterSchedulePlan(WaterSchedulePlanEditParam waterSchedulePlanEditParam) {
        WaterSchedulePlanPo waterSchedulePlan = new WaterSchedulePlanPo();
        BeanUtil.copyProperties(waterSchedulePlanEditParam, waterSchedulePlan);
        return baseMapper.updateById(waterSchedulePlan);
    }

    /**
     * 批量删除灌区管理-调度方案
     *
     * @param planIds 需要删除的灌区管理-调度方案主键
     * @return 结果
     */
    @Override
    public int deleteWaterSchedulePlanByPlanIds(Long[] planIds) {
        return baseMapper.deleteWaterSchedulePlanByPlanIds(planIds);
    }

    /**
     * 删除灌区管理-调度方案信息
     *
     * @param planId 灌区管理-调度方案主键
     * @return 结果
     */
    @Override
    public int deleteWaterSchedulePlanByPlanId(Long planId) {
        return baseMapper.deleteWaterSchedulePlanByPlanId(planId);
    }

    @Override
    public WaterSchedulePlanPumpListVo schedulePlanPump(WaterSchedulePlanGenerateParam waterSchedulePlanParam) {
        WaterSchedulePlanPumpListVo waterSchedulePlanPumpListVo = new WaterSchedulePlanPumpListVo();
        //凤台区
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ340421"));
        List<WaterSchedulePumpWaterVo> fengTaiPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setFengTaiPumpList(fengTaiPumpList);
        //怀远县
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ340321"));
        List<WaterSchedulePumpWaterVo> huaiYuanPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setHuaiYuanPumpList(huaiYuanPumpList);
        //蒙城县
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ341623"));
        List<WaterSchedulePumpWaterVo> liXinPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setLiXinPumpList(liXinPumpList);
        //潘集区
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ341622"));
        List<WaterSchedulePumpWaterVo> mengChengPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setMengChengPumpList(mengChengPumpList);
        //潘集区
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ340406"));
        List<WaterSchedulePumpWaterVo> panJiPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setPanJiPumpList(panJiPumpList);
        //颍东区
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ341203"));
        List<WaterSchedulePumpWaterVo> yingDongPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setYingDongPumpList(yingDongPumpList);
        //颍泉区
        waterSchedulePlanParam.setAddvcd(setAddvcd("GQ341204"));
        List<WaterSchedulePumpWaterVo> yingQuanPumpList = baseMapper.schedulePlanPump(waterSchedulePlanParam);
        waterSchedulePlanPumpListVo.setYingQuanPumpList(yingQuanPumpList);
        return waterSchedulePlanPumpListVo;
    }

    private String setAddvcd(String irrigationCode) {

        String addvcd = null;
        switch (irrigationCode) {
            case "GQ341623":
                //("利辛县");
                addvcd = "341623";
                break;
            case "GQ340421":
                //("凤台区");
                addvcd = "340421";
                break;
            case "GQ340321":
                //("怀远县");
                addvcd = "340321";
                break;
            case "GQ341622":
                //("蒙城县");
                addvcd = "341622";
                break;
            case "GQ340406":
                //("潘集区");
                addvcd = "340406";
                break;
            case "GQ341203":
                //("颍东区");
                addvcd = "341203";
                break;
            case "GQ341204":
                //("颍泉区");
                addvcd = "341204";
                break;
        }
        return addvcd;
    }

    @Override
    public WaterSchedulePlanLineListVo schedulePlanLevel(WaterSchedulePlanGenerateParam planParam) {
        WaterSchedulePlanLineListVo waterSchedulePlanLineListVo = new WaterSchedulePlanLineListVo();
        //取插花上水位
        List<WaterSchedulePlanLineVo> chaHuaLineList = new ArrayList<>();
        //取阚疃上水位
        List<WaterSchedulePlanLineVo> kanTuanLineList = new ArrayList<>();
        //取上桥上水位
        List<WaterSchedulePlanLineVo> shangQiaoLineList = new ArrayList<>();
        //日雨量查询
        if (ObjectUtil.isNotEmpty(planParam) && "2".equals(planParam.getWeatherParameter())) {
            chaHuaLineList = addPlanPumpDataCopy(planParam);
            kanTuanLineList = addPlanPumpDataCopy(planParam);
            shangQiaoLineList = addPlanPumpDataCopy(planParam);

        } else if (ObjectUtil.isNotEmpty(planParam) && "1".equals(planParam.getWeatherParameter())) {

            // 获取当前日期
            String currentDate = DateUtil.format(DateUtil.date(), "yyyy-MM-dd");
            planParam.setStartTime(currentDate);

            // 计算七天后的日期
            String futureDate = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), 7), "yyyy-MM-dd");
            planParam.setEndTime(futureDate);
            chaHuaLineList = addPlanPumpDataCopy(planParam);
            kanTuanLineList = addPlanPumpDataCopy(planParam);
            shangQiaoLineList = addPlanPumpDataCopy(planParam);
        }

        waterSchedulePlanLineListVo.setChaHuaLineList(chaHuaLineList);
        waterSchedulePlanLineListVo.setKanTuanLineList(kanTuanLineList);
        waterSchedulePlanLineListVo.setShangQiaoLineList(shangQiaoLineList);
        return waterSchedulePlanLineListVo;
    }

    @Override
    public WaterSchedulePlanLineNowListVo newSchedulePlanLevel(Integer dispatchScheme) {

        WaterSchedulePlanLineNowListVo waterSchedulePlanLineNowListVo = new WaterSchedulePlanLineNowListVo();
        //取插花上水位
        BigDecimal chahuaWaterLine = baseMapper.getWaterLine("341203000022");
        //取阚疃上水位
        BigDecimal kantuanWaterLine = baseMapper.getWaterLine("341623000096");
        //取上桥上水位
        BigDecimal shangqiaoWaterLine = baseMapper.getWaterLine("108131001");

        //取插花上水位
        List<WaterSchedulePlanLineVo> chaHuaLineList = getDateList();
        chaHuaLineList.get(0).setRealWater(chahuaWaterLine.toString());

        //取阚疃上水位
        List<WaterSchedulePlanLineVo> kanTuanLineList = getDateList();
        kanTuanLineList.get(0).setRealWater(kantuanWaterLine.toString());
        //取上桥上水位
        List<WaterSchedulePlanLineVo> shangQiaoLineList = getDateList();
        shangQiaoLineList.get(0).setRealWater(shangqiaoWaterLine.toString());

        //取插花上水位
        List<WaterSchedulePlanLineVo> chaHuaLineNextList = getDateList();
        chaHuaLineNextList.get(0).setRealWater(chahuaWaterLine.toString());
        //取阚疃上水位
        List<WaterSchedulePlanLineVo> kanTuanLineNextList = getDateList();
        kanTuanLineNextList.get(0).setRealWater(kantuanWaterLine.toString());
        //取上桥上水位
        List<WaterSchedulePlanLineVo> shangQiaoLineNextList = getDateList();
        shangQiaoLineNextList.get(0).setRealWater(shangqiaoWaterLine.toString());

        for (int i = 1; i < chaHuaLineList.size(); i++) {
            chaHuaLineList.get(i).setRealWater(-1.1+getBaseDemand(-0.1,0.1)-(0.1*i)+chahuaWaterLine.doubleValue()+"");
        }
        for (int i = 1; i < kanTuanLineList.size(); i++) {
            kanTuanLineList.get(i).setRealWater(-1.2+getBaseDemand(-0.1,0.1)-(0.1*i)+kantuanWaterLine.doubleValue()+"");
        }
        for (int i = 1; i < shangQiaoLineList.size(); i++) {
            shangQiaoLineList.get(i).setRealWater(-1+getBaseDemand(-0.2,0.2)-(0.1*i)+shangqiaoWaterLine.doubleValue()+"");
        }

        //方案1
        if (dispatchScheme == 1) {
            chaHuaLineNextList.get(1).setRealWater(-0.1-(0.1*1)+chahuaWaterLine.doubleValue()+"");
            chaHuaLineNextList.get(2).setRealWater(0.1-(0.1*2)+chahuaWaterLine.doubleValue()+"");
            for (int i = 3; i < chaHuaLineNextList.size(); i++) {
                chaHuaLineNextList.get(i).setRealWater(-0.05-(0.1*i)+chahuaWaterLine.doubleValue()+"");
            }
            kanTuanLineNextList.get(1).setRealWater(-0.2-(0.1*1)+kantuanWaterLine.doubleValue()+"");
            kanTuanLineNextList.get(2).setRealWater(0.2-(0.1*2)+kantuanWaterLine.doubleValue()+"");
            for (int i = 3; i < kanTuanLineNextList.size(); i++) {
                kanTuanLineNextList.get(i).setRealWater(-0.1+(0.1*i)+kantuanWaterLine.doubleValue()+"");
            }
            shangQiaoLineNextList.get(1).setRealWater(-0.1-(0.1*1)+shangqiaoWaterLine.doubleValue()+"");
            shangQiaoLineNextList.get(2).setRealWater(-0.15-(0.1*2)+shangqiaoWaterLine.doubleValue()+"");
            shangQiaoLineNextList.get(3).setRealWater(-0.17-(0.1*3)+shangqiaoWaterLine.doubleValue()+"");
            for (int i = 4; i < shangQiaoLineNextList.size(); i++) {
                shangQiaoLineNextList.get(i).setRealWater(-0.1+(0.1*i)+shangqiaoWaterLine.doubleValue()+"");
            }


        } else
        //方案 2
        {
            chaHuaLineNextList.get(1).setRealWater(-0.1-(0.1*1)+chahuaWaterLine.doubleValue()+"");
            chaHuaLineNextList.get(2).setRealWater(0.1-(0.1*2)+chahuaWaterLine.doubleValue()+"");
            for (int i = 3; i < chaHuaLineNextList.size(); i++) {
                chaHuaLineNextList.get(i).setRealWater(-0.05-(0.1*i)+chahuaWaterLine.doubleValue()+"");
            }
            kanTuanLineNextList.get(1).setRealWater(-0.2-(0.1*1)+kantuanWaterLine.doubleValue()+"");
            kanTuanLineNextList.get(2).setRealWater(0.2-(0.1*2)+kantuanWaterLine.doubleValue()+"");
            for (int i = 3; i < kanTuanLineNextList.size(); i++) {
                kanTuanLineNextList.get(i).setRealWater(-0.1+(0.1*i)+kantuanWaterLine.doubleValue()+"");
            }

            shangQiaoLineNextList.get(1).setRealWater(-0.1-(0.1*1)+shangqiaoWaterLine.doubleValue()+"");
            shangQiaoLineNextList.get(2).setRealWater(0.15+(0.1*2)+shangqiaoWaterLine.doubleValue()+"");

            for (int i = 3; i < shangQiaoLineNextList.size(); i++) {
                shangQiaoLineNextList.get(i).setRealWater(0.15+(0.1*2)-(0.05*i)+shangqiaoWaterLine.doubleValue()+"");
            }

        }


        waterSchedulePlanLineVo(chaHuaLineList);
        waterSchedulePlanLineVo(kanTuanLineList);
        waterSchedulePlanLineVo(shangQiaoLineList);
        waterSchedulePlanLineVo(shangQiaoLineNextList);
        waterSchedulePlanLineVo(kanTuanLineNextList);
        waterSchedulePlanLineVo(chaHuaLineNextList);




        waterSchedulePlanLineNowListVo.setChaHuaLineList(chaHuaLineList);
        waterSchedulePlanLineNowListVo.setKanTuanLineList(kanTuanLineList);
        waterSchedulePlanLineNowListVo.setShangQiaoLineList(shangQiaoLineList);
        waterSchedulePlanLineNowListVo.setChaHuaLineNextList(chaHuaLineNextList);
        waterSchedulePlanLineNowListVo.setKanTuanLineNextList(kanTuanLineNextList);
        waterSchedulePlanLineNowListVo.setShangQiaoLineNextList(shangQiaoLineNextList);

        return waterSchedulePlanLineNowListVo;
    }


    private void waterSchedulePlanLineVo(List<WaterSchedulePlanLineVo> lineList){
        lineList.forEach(item ->{
            double value = Double.parseDouble(item.getRealWater());
            String formattedValue = String.format("%.2f", value);
            item.setRealWater(formattedValue);
        });
    }

    //获取随机基数
    private double getBaseDemand(double min,double max) {

        // 创建Random对象
        Random random = new Random();

        // 生成[min, max]之间的随机浮点数
        return min + (max - min) * random.nextDouble();
    }

    //填充三十天的时间，每三天填充一个数据
    private List<WaterSchedulePlanLineVo> getDateList() {
        // 创建一个列表来存储日期
        List<WaterSchedulePlanLineVo> dates = new ArrayList<>();

        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        LocalDate endDate = currentDate.plusDays(30); // 三十天后的日期

        // 循环生成每三天的日期
        while (currentDate.isBefore(endDate)) {
            // 格式化日期
            String formattedDate = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 添加到列表
            dates.add(new WaterSchedulePlanLineVo().setRealTime(formattedDate));

            // 增加三天
            currentDate = currentDate.plusDays(3);
        }

        return dates;
    }

    private List<WaterSchedulePlanLineVo> addPlanPumpDataCopy(WaterSchedulePlanGenerateParam planParam) {
        List<WaterSchedulePlanLineVo> planLineVos = new ArrayList<>();
        String startDateStr = planParam.getStartTime();
        String endDateStr = planParam.getEndTime();
        if (ObjectUtil.isNotEmpty(planParam.getStartTime()) && ObjectUtil.isNotEmpty(planParam.getEndTime())) {

            startDateStr = updateQueryData(startDateStr);
            endDateStr = updateQueryData(endDateStr);
            planParam.setStartTime(startDateStr);
            planParam.setEndTime(endDateStr);

        } else {
            throw new RuntimeException("请输入查询时间");
        }
        Map<LocalDateTime, BigDecimal> rainfallData = new TreeMap<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime startDate = LocalDateTime.parse(startDateStr, formatter);
        LocalDateTime endDate = LocalDateTime.parse(endDateStr, formatter);

        while (!startDate.isAfter(endDate)) {
            rainfallData.putIfAbsent(startDate, new BigDecimal(0.0));
            startDate = startDate.plusDays(1);
        }

        // 生成第一个随机数
        double firstRandomNumber = RandomUtil.randomDouble(18.00, 22.00);
        double lastRandomNumber;

        // 循环生成随机数序列
        do {
            planLineVos.clear(); // 清空列表以重新生成

            // 生成第一个WaterSchedulePlanLineVo
            WaterSchedulePlanLineVo firstRainfall = new WaterSchedulePlanLineVo();
            LocalDateTime firstKey = rainfallData.keySet().iterator().next();
            firstRainfall.setRealTime(firstKey.minusDays(1).format(formatter));
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            String formattedNumber = decimalFormat.format(firstRandomNumber);
            firstRainfall.setRealWater(formattedNumber);
            planLineVos.add(firstRainfall);

            // 生成其他随机数
            for (Map.Entry<LocalDateTime, BigDecimal> entry : rainfallData.entrySet()) {
                if (entry.equals(rainfallData.entrySet().iterator().next())) {
                    continue; // 跳过第一个元素
                }

                // 生成数据
                double randomNumber = RandomUtil.randomDouble(18.00, 22.00);
                WaterSchedulePlanLineVo rainfall = new WaterSchedulePlanLineVo();
                rainfall.setRealTime(entry.getKey().minusDays(1).format(formatter));
                String formattedNumberNew = decimalFormat.format(randomNumber);
                rainfall.setRealWater(formattedNumberNew);
                planLineVos.add(rainfall);
            }

            // 获取最后一个随机数
            lastRandomNumber = Double.parseDouble(planLineVos.get(planLineVos.size() - 1).getRealWater());
        } while (lastRandomNumber >= firstRandomNumber); // 如果最后一个随机数不小于第一个随机数，则重新生成

        return planLineVos;
    }

    private String updateQueryData(String dateStr) {

        // 截取日期部分
        String part = dateStr.substring(0, 10);
        // 拼接午夜的时间部分
        String time = " 00:00:00";
        // 将日期部分和午夜时间部分拼接起来
        dateStr = part + time;

        // 创建一个日期时间格式器，用于解析输入的日期时间字符串
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析输入的日期时间字符串
        LocalDateTime dateTime = LocalDateTime.parse(dateStr, inputFormatter);
        LocalDateTime addDateTime = dateTime.plusDays(1);
        // 清除时间部分，保留日期，设置时间为午夜
        LocalDateTime midnight = addDateTime.withHour(0).withMinute(0).withSecond(0);

        // 创建一个新的日期时间格式器，用于输出午夜的日期时间字符串
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 将午夜的日期时间格式化为字符串
        String midnightStr = midnight.format(outputFormatter);

        // 输出：2024-05-17 00:00:00
        return midnightStr;
    }

    @Override
    public WaterSchedulePlanInfoVo getPlanInfo(Long planId) {
        WaterSchedulePlanInfoVo waterSchedulePlanInfoVo = new WaterSchedulePlanInfoVo();
        WaterSchedulePlanPo waterSchedulePlanPo = baseMapper.selectById(planId);
        waterSchedulePlanInfoVo.setPlanName(waterSchedulePlanPo.getPlanName());
        /** 方案历史水位信息 */
        WaterSchedulePlanLineListVo planLineList = new WaterSchedulePlanLineListVo();
        List<WaterSchedulePlanLevelVo> waterSchedulePlanLevelVos = waterSchedulePlanLevelService.selectWaterSchedulePlanLevelList(new WaterSchedulePlanLevelParam().setPlanId(planId));
        // 根据闸站编码分组
        Map<String, List<WaterSchedulePlanLevelVo>> groupedByGateStationCode = waterSchedulePlanLevelVos.stream()
                .collect(Collectors.groupingBy(WaterSchedulePlanLevelVo::getGateStationCode));

        //插花 341203000022
        planLineList.setChaHuaLineList(BeanUtil.copyToList(groupedByGateStationCode.get("341203000022"), WaterSchedulePlanLineVo.class));
        // 阚疃   341623000096
        planLineList.setKanTuanLineList(BeanUtil.copyToList(groupedByGateStationCode.get("341623000096"), WaterSchedulePlanLineVo.class));
        //上桥 108131001
        planLineList.setShangQiaoLineList(BeanUtil.copyToList(groupedByGateStationCode.get("108131001"), WaterSchedulePlanLineVo.class));
        /** 方案历史泵站信息 */
        WaterSchedulePlanPumpListVo planPumpList = new WaterSchedulePlanPumpListVo();
        List<WaterSchedulePlanPumpVo> planPumpVos = waterSchedulePlanPumpService.selectWaterSchedulePlanPumpList(new WaterSchedulePlanPumpParam().setPlanId(planId));
        // 根据泵站编码分组
        Map<String, List<WaterSchedulePlanPumpVo>> planPumpLevelVos = planPumpVos.stream()
                .collect(Collectors.groupingBy(WaterSchedulePlanPumpVo::getIrrigationCode));

        //        GQ341623\t利辛县\n" +
        planPumpList.setLiXinPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ341623"), WaterSchedulePumpWaterVo.class));
        //        GQ340406\t潘集区\n" +
        planPumpList.setPanJiPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ340406"), WaterSchedulePumpWaterVo.class));
        //        GQ340321\t怀远县\n" +
        planPumpList.setHuaiYuanPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ340321"), WaterSchedulePumpWaterVo.class));
        //        GQ341204\t颍泉区\n" +
        planPumpList.setFengTaiPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ341204"), WaterSchedulePumpWaterVo.class));
        //        GQ341203\t颍东区\n" +
        planPumpList.setYingDongPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ341203"), WaterSchedulePumpWaterVo.class));
        //        GQ341622\t蒙城县\n" +
        planPumpList.setYingQuanPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ341622"), WaterSchedulePumpWaterVo.class));
        //       "GQ340421\t凤台县
        planPumpList.setMengChengPumpList(BeanUtil.copyToList(planPumpLevelVos.get("GQ340421"), WaterSchedulePumpWaterVo.class));
        waterSchedulePlanInfoVo.setPlanPumpList(planPumpList);
        waterSchedulePlanInfoVo.setPlanLineList(planLineList);
        return waterSchedulePlanInfoVo;
    }

    @Override
    public WaterSchedulePlanContrastListVo contrastPumpList(WaterSchedulePlanContrastParam waterSchedulePlanParam) {
        WaterSchedulePlanContrastListVo waterSchedulePlanContrastListVo = new WaterSchedulePlanContrastListVo();
        List<WaterSchedulePumpContrastVo> pumpContrastVos = baseMapper.contrastPumpList(waterSchedulePlanParam);
//        @ApiModelProperty(value = "颍泉区 泵站调度")
        List<WaterSchedulePumpContrastVo> yingQuanPumpList = new ArrayList<>();
//        @ApiModelProperty(value = "颍东区 泵站调度")
        List<WaterSchedulePumpContrastVo> yingDongPumpList = new ArrayList<>();
//        @ApiModelProperty(value = "蒙城县 泵站调度")
        List<WaterSchedulePumpContrastVo> mengChengPumpList = new ArrayList<>();
//        @ApiModelProperty(value = "潘集区 泵站调度")
        List<WaterSchedulePumpContrastVo> panJiPumpList = new ArrayList<>();
//        @ApiModelProperty(value = "怀远县 泵站调度")
        List<WaterSchedulePumpContrastVo> huaiYuanPumpList = new ArrayList<>();
//        @ApiModelProperty(value = "利辛县 泵站调度")
        List<WaterSchedulePumpContrastVo> liXinPumpList = new ArrayList<>();
//        @ApiModelProperty(value = "凤台区 泵站调度")
        List<WaterSchedulePumpContrastVo> fengTaiPumpList = new ArrayList<>();


        BigDecimal waterDemandAStatistics = new BigDecimal("0");
        BigDecimal waterDemandBStatistics = new BigDecimal("0");

        for (WaterSchedulePumpContrastVo pumpContrastVo : pumpContrastVos) {
            //GQ341623 利辛县 灌区 泵站数据
            //GQ340421 凤台区 灌区
            //GQ340321 怀远县 灌区
            //GQ341622  蒙城县 灌区
            //GQ340406 潘集区 灌区
            //GQ341203  颍东区
            //GQ341204  颍泉区
            switch (pumpContrastVo.getIrrigationCode()) {
                case "GQ341623":
                    pumpContrastVo.setIrrigationName("利辛县");
                    liXinPumpList.add(pumpContrastVo);
                    break;
                case "GQ340421":
                    pumpContrastVo.setIrrigationName("凤台区");
                    fengTaiPumpList.add(pumpContrastVo);
                    break;
                case "GQ340321":
                    pumpContrastVo.setIrrigationName("怀远县");
                    huaiYuanPumpList.add(pumpContrastVo);
                    break;
                case "GQ341622":
                    pumpContrastVo.setIrrigationName("蒙城县");
                    mengChengPumpList.add(pumpContrastVo);
                    break;
                case "GQ340406":
                    pumpContrastVo.setIrrigationName("潘集区");
                    panJiPumpList.add(pumpContrastVo);
                    break;
                case "GQ341203":
                    pumpContrastVo.setIrrigationName("颍东区");
                    yingDongPumpList.add(pumpContrastVo);
                    break;
                case "GQ341204":
                    pumpContrastVo.setIrrigationName("颍泉区");
                    yingQuanPumpList.add(pumpContrastVo);
            }

            if (ObjectUtil.isNotEmpty(pumpContrastVo.getWaterDemandA())) {
                waterDemandAStatistics = waterDemandAStatistics.add(pumpContrastVo.getWaterDemandA());
            }
            if (ObjectUtil.isNotEmpty(pumpContrastVo.getWaterDemandB())) {
                waterDemandBStatistics = waterDemandBStatistics.add(pumpContrastVo.getWaterDemandB());
            }
        }
        waterSchedulePlanContrastListVo.setLiXinPumpList(liXinPumpList);
        waterSchedulePlanContrastListVo.setFengTaiPumpList(fengTaiPumpList);
        waterSchedulePlanContrastListVo.setHuaiYuanPumpList(huaiYuanPumpList);
        waterSchedulePlanContrastListVo.setMengChengPumpList(mengChengPumpList);
        waterSchedulePlanContrastListVo.setPanJiPumpList(panJiPumpList);
        waterSchedulePlanContrastListVo.setYingDongPumpList(yingDongPumpList);
        waterSchedulePlanContrastListVo.setYingQuanPumpList(yingQuanPumpList);
        String statisticsContrastData = "方案A：" + waterSchedulePlanParam.getPlanNameA() + " 总抽水量：" + waterDemandAStatistics + "m³;  " +
                "方案B：" + waterSchedulePlanParam.getPlanNameB() + "总抽水量：" + waterDemandBStatistics + "m³";
        waterSchedulePlanContrastListVo.setStatisticsContrastData(statisticsContrastData);
        return waterSchedulePlanContrastListVo;
    }

    @Override
    public WaterSchedulePlanDispatchVo dispatchGenerate(Integer yue) {

        //丰水年：方案1   平水年方案1或者2 枯水年：方案2
        Integer dispatchScheme = 2;

        WaterSchedulePlanDispatchVo waterSchedulePlanDispatchVo = new WaterSchedulePlanDispatchVo();
        BigDecimal yingQuanWaterDemand = getWaterDemand("GQ341204",yue);
        //("颍泉区");
        waterSchedulePlanDispatchVo.setYingQuanWaterDemand(yingQuanWaterDemand);
        BigDecimal yingDongWaterDemand = getWaterDemand("GQ341203",yue);
        //("颍东区");
        waterSchedulePlanDispatchVo.setYingDongWaterDemand(yingDongWaterDemand);

        BigDecimal liXinWaterDemand = getWaterDemand("GQ341623",yue);
        //("利辛县");
        waterSchedulePlanDispatchVo.setLiXinWaterDemand(liXinWaterDemand);

        BigDecimal mengChengWaterDemand = getWaterDemand("GQ341622",yue);
        //("蒙城县");
        waterSchedulePlanDispatchVo.setMengChengWaterDemand(mengChengWaterDemand);
        BigDecimal fengTaiWaterDemand = getWaterDemand("GQ340421",yue);
        //("凤台区");
        waterSchedulePlanDispatchVo.setFengTaiWaterDemand(fengTaiWaterDemand);
        BigDecimal panJiWaterDemand = getWaterDemand("GQ340406",yue);
        //("潘集区");
        waterSchedulePlanDispatchVo.setPanJiWaterDemand(panJiWaterDemand);
        BigDecimal huaiYuanWaterDemand = getWaterDemand("GQ340321",yue);
        //("怀远县");
        waterSchedulePlanDispatchVo.setHuaiYuanWaterDemand(huaiYuanWaterDemand);
        //计算总
        BigDecimal totalWaterDemand = yingQuanWaterDemand.add(yingDongWaterDemand)
                .add(liXinWaterDemand).add(mengChengWaterDemand).
                add(fengTaiWaterDemand).add(panJiWaterDemand).add(huaiYuanWaterDemand);
        waterSchedulePlanDispatchVo.setTotalWaterDemand(totalWaterDemand);


//        @ApiModelProperty(value = "茨河铺至插花灌溉需水万m³")//取插花上水位
        BigDecimal chaHuaWaterDemand = yingQuanWaterDemand.add(yingDongWaterDemand);
//        @ApiModelProperty(value = "插花至阚疃灌溉需水万m³")//取阚疃上水位
        BigDecimal kanTuanWaterDemand = liXinWaterDemand;
//        @ApiModelProperty(value = "阚疃至上桥灌溉需水万m³")//取上桥上水位
        BigDecimal shangQiaoWaterDemand = mengChengWaterDemand.add(fengTaiWaterDemand).add(panJiWaterDemand).add(huaiYuanWaterDemand);

        waterSchedulePlanDispatchVo.setChaHuaWaterDemand(chaHuaWaterDemand);
        waterSchedulePlanDispatchVo.setKanTuanWaterDemand(kanTuanWaterDemand);
        waterSchedulePlanDispatchVo.setShangQiaoWaterDemand(shangQiaoWaterDemand);
        waterSchedulePlanDispatchVo.setTotalDispatchWaterDemand(totalWaterDemand);
        //方案
        waterSchedulePlanDispatchVo.setDispatchScheme(dispatchScheme);


        waterSchedulePlanDispatchVo.setChaHuaDispatchWaterDemand(chaHuaWaterDemand.add(kanTuanWaterDemand));
        waterSchedulePlanDispatchVo.setKanTuanDispatchWaterDemand(kanTuanWaterDemand);
        waterSchedulePlanDispatchVo.setShangQiaoDispatchWaterDemand(shangQiaoWaterDemand);


        return waterSchedulePlanDispatchVo;


    }
    @Resource
    private RedisService redisService;

    private BigDecimal getWaterDemand(String irrigationCode,int yue) {

        String areaPoint = setAreaPoint(irrigationCode);

        WaterThreeLifeDemandVo waterThreeLifeDemandVo = redisService.getCacheObject(areaPoint + "threeLifeDemand");
        BigDecimal allCropWater=new BigDecimal(0);
        if (ObjectUtil.isNotEmpty(waterThreeLifeDemandVo)&&ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getProduction())
                &&ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getDomestic())&&ObjectUtil.isNotEmpty(waterThreeLifeDemandVo.getEcological())){

            List<String> productions = waterThreeLifeDemandVo.getProduction();
            List<String> domes = waterThreeLifeDemandVo.getDomestic();
            List<String> ecologics = waterThreeLifeDemandVo.getEcological();
            allCropWater=allCropWater.add(new BigDecimal(productions.get(yue-1)));
            allCropWater=allCropWater.add(new BigDecimal(domes.get(yue-1)));
            allCropWater=allCropWater.add(new BigDecimal(ecologics.get(yue-1)));

        }
        if (allCropWater.compareTo(new BigDecimal("0"))>0){
            return allCropWater.setScale(2, RoundingMode.HALF_UP);
        }
        return new BigDecimal("0");
    }
    private String setAreaPoint( String irrigationCode){
        String areaPoint = null;
        switch (irrigationCode) {
            case "GQ341623":
                //("利辛县");
                areaPoint="LiXin";
                break;
            case "GQ340421":
                //("凤台区");
                areaPoint="FengTai";
                break;
            case "GQ340321":
                //("怀远县");
                areaPoint="HuaiYuan";
                break;
            case "GQ341622":
                //("蒙城县");
                areaPoint="MengCheng";
                break;
            case "GQ340406":
                //("潘集区");
                areaPoint="PanJi";
                break;
            case "GQ341203":
                //("颍东区");
                areaPoint="YingDong";
                break;
            case "GQ341204":
                //("颍泉区");
                areaPoint="YingQuan";
                break;
        }
        return areaPoint;
    }
    private String setAreaPointOld( String irrigationCode){
        String areaPoint = null;
        switch (irrigationCode) {
            case "GQ341623":
                //("利辛县");
                areaPoint="Point5";
                break;
            case "GQ340421":
                //("凤台区");
                areaPoint="Point8";
                break;
            case "GQ340321":
                //("怀远县");
                areaPoint="Point11";
                break;
            case "GQ341622":
                //("蒙城县");
                areaPoint="Point7";
                break;
            case "GQ340406":
                //("潘集区");
                areaPoint="Point9";
                break;
            case "GQ341203":
                //("颍东区");
                areaPoint="Point4";
                break;
            case "GQ341204":
                //("颍泉区");
                areaPoint="Point1";
                break;
        }
        return areaPoint;
    }




    @Override
    public WaterSchedulePlanRainVo rainAll() {

        WaterSchedulePlanRainVo waterSchedulePlanRainVo =new WaterSchedulePlanRainVo();
        waterSchedulePlanRainVo.setJanuaryRainfall("619.0");
        waterSchedulePlanRainVo.setFebruaryRainfall("2599.0");
        waterSchedulePlanRainVo.setMarchRainfall("2068.0");
        waterSchedulePlanRainVo.setAprilRainfall("1233.5");
        waterSchedulePlanRainVo.setMayRainfall("364.6");
        waterSchedulePlanRainVo.setJuneRainfall("1970.6");
        waterSchedulePlanRainVo.setJulyRainfall("5389.6");
        waterSchedulePlanRainVo.setAugustRainfall("2252.3");
        waterSchedulePlanRainVo.setSeptemberRainfall("334.5");
//        List<String> strings = baseMapper.rainAll();
//
////        @ApiModelProperty(value = "1月份降雨量")
//        waterSchedulePlanRainVo.setJanuaryRainfall(strings.get(0)!=null?strings.get(0):"");
//
////        @ApiModelProperty(value = "2月份降雨量")
//        waterSchedulePlanRainVo.setFebruaryRainfall(strings.get(1)!=null?strings.get(1):"");
//
////        @ApiModelProperty(value = "3月份降雨量")
//        waterSchedulePlanRainVo.setMarchRainfall(strings.get(2)!=null?strings.get(2):"");
//
////        @ApiModelProperty(value = "4月份降雨量")
//        waterSchedulePlanRainVo.setAprilRainfall(strings.get(3)!=null?strings.get(3):"");
//
////        @ApiModelProperty(value = "5月份降雨量")
//        waterSchedulePlanRainVo.setMayRainfall(strings.get(4)!=null?strings.get(4):"");
//
////        @ApiModelProperty(value = "6月份降雨量")
//        waterSchedulePlanRainVo.setJuneRainfall(strings.get(5)!=null?strings.get(5):"");
//
////        @ApiModelProperty(value = "7月份降雨量")
//        waterSchedulePlanRainVo.setJulyRainfall(strings.get(6)!=null?strings.get(6):"");
//
////        @ApiModelProperty(value = "8月份降雨量")
//        waterSchedulePlanRainVo.setAugustRainfall(strings.get(7)!=null?strings.get(7):"");
//
////        @ApiModelProperty(value = "9月份降雨量")
//        waterSchedulePlanRainVo.setSeptemberRainfall(strings.get(8)!=null?strings.get(8):"");
//
////        @ApiModelProperty(value = "10月份降雨量")
//        waterSchedulePlanRainVo.setOctoberRainfall(strings.get(9)!=null?strings.get(9):"");
//
////        @ApiModelProperty(value = "11月份降雨量")
//        waterSchedulePlanRainVo.setNovemberRainfall(strings.get(10)!=null?strings.get(10):"");
//
////        @ApiModelProperty(value = "12月份降雨量")
//        waterSchedulePlanRainVo.setDecemberRainfall(strings.get(11)!=null?strings.get(11):"");

        return waterSchedulePlanRainVo;
    }
}
