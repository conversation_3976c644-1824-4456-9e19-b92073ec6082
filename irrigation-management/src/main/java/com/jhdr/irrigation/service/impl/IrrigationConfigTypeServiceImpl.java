package com.jhdr.irrigation.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jhdr.common.core.utils.StringUtils;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.security.utils.SecurityUtils;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.IrrigationConfigCropCyclePo;
import com.jhdr.irrigation.entity.po.IrrigationConfigTypePo;
import com.jhdr.irrigation.entity.po.IrrigationConfigValuePo;
import com.jhdr.irrigation.entity.vo.CropCycleVo;
import com.jhdr.irrigation.entity.vo.DroughtRiskConfigVo;
import com.jhdr.irrigation.entity.vo.IrrigationConfigTypeVo;
import com.jhdr.irrigation.entity.vo.IrrigationPeriodVo;
import com.jhdr.irrigation.entity.vo.PlantingStructureVo;
import com.jhdr.irrigation.entity.vo.PumpStationVo;
import com.jhdr.irrigation.entity.vo.WaterCoefficientConfigVo;
import com.jhdr.irrigation.entity.vo.WaterLevelGuaranteeVo;
import com.jhdr.irrigation.entity.vo.WaterPriorityConfigVo;
import com.jhdr.irrigation.entity.vo.WaterUsageVo;
import com.jhdr.irrigation.entity.vo.WeatherDataConfigVo;
import com.jhdr.irrigation.mapper.IrrigationConfigCropCycleMapper;
import com.jhdr.irrigation.mapper.IrrigationConfigTypeMapper;
import com.jhdr.irrigation.mapper.IrrigationConfigValueMapper;
import com.jhdr.irrigation.service.IIrrigationConfigTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 灌区配置类型 服务实现类
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class IrrigationConfigTypeServiceImpl extends ServiceImpl<IrrigationConfigTypeMapper, IrrigationConfigTypePo> implements IIrrigationConfigTypeService {

    @Autowired
    private IrrigationConfigValueMapper irrigationConfigValueMapper;

    @Autowired
    private IrrigationConfigCropCycleMapper irrigationConfigCropCycleMapper;

    /**
     * 查询灌区配置类型列表
     *
     * @param param 查询参数
     * @return 灌区配置类型列表
     */
    @Override
    public List<IrrigationConfigTypeVo> selectIrrigationConfigTypeList(IrrigationConfigTypeParam param) {
        LambdaQueryWrapper<IrrigationConfigTypePo> queryWrapper = new LambdaQueryWrapper<>();

        // 构建查询条件
        if (param != null) {
            // 配置名称模糊查询
            if (StringUtils.isNotEmpty(param.getConfigName())) {
                queryWrapper.like(IrrigationConfigTypePo::getConfigName, param.getConfigName());
            }

            // 所属模型类型精确查询
            if (StringUtils.isNotEmpty(param.getModelType())) {
                queryWrapper.eq(IrrigationConfigTypePo::getModelType, param.getModelType());
            }

            // 是否启用精确查询
            if (ObjectUtil.isNotNull(param.getIsEnabled())) {
                queryWrapper.eq(IrrigationConfigTypePo::getIsEnabled, param.getIsEnabled());
            }
        }

        // 按排序号升序排列
        queryWrapper.orderByAsc(IrrigationConfigTypePo::getSortOrder);

        // 查询数据
        List<IrrigationConfigTypePo> list = this.list(queryWrapper);

        // 转换为VO对象
        List<IrrigationConfigTypeVo> voList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(list)) {
            voList = list.stream().map(po -> {
                IrrigationConfigTypeVo vo = new IrrigationConfigTypeVo();
                BeanUtils.copyProperties(po, vo);

                // 手动处理时间字段转换
                if (po.getCreateTime() != null) {
                    vo.setCreateTime(java.sql.Timestamp.valueOf(po.getCreateTime()));
                }
                if (po.getUpdateTime() != null) {
                    vo.setUpdateTime(java.sql.Timestamp.valueOf(po.getUpdateTime()));
                }

                return vo;
            }).collect(Collectors.toList());
        }

        return voList;
    }

    /**
     * 根据ID查询灌区配置类型详情
     *
     * @param id 配置类型ID
     * @return 灌区配置类型详情
     */
    @Override
    public IrrigationConfigTypeVo selectIrrigationConfigTypeById(Long id) {
        IrrigationConfigTypePo po = this.getById(id);
        if (ObjectUtil.isNull(po)) {
            return null;
        }

        IrrigationConfigTypeVo vo = new IrrigationConfigTypeVo();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 新增灌区配置类型
     *
     * @param param 新增参数
     * @return 结果
     */
    @Override
    public int insertIrrigationConfigType(IrrigationConfigTypeAddParam param) {
        IrrigationConfigTypePo po = new IrrigationConfigTypePo();
        BeanUtils.copyProperties(param, po);

        // 设置默认值
        if (ObjectUtil.isNull(po.getSortOrder())) {
            po.setSortOrder(0);
        }
        if (ObjectUtil.isNull(po.getIsEnabled())) {
            po.setIsEnabled(1);
        }

        // 设置创建信息
        po.setCreateTime(LocalDateTime.now());
        po.setCreateBy(SecurityUtils.getUsername());

        return this.save(po) ? 1 : 0;
    }

    /**
     * 修改灌区配置类型
     *
     * @param param 修改参数
     * @return 结果
     */
    @Override
    public int updateIrrigationConfigType(IrrigationConfigTypeEditParam param) {
        // 检查记录是否存在
        IrrigationConfigTypePo existPo = this.getById(param.getId());
        if (ObjectUtil.isNull(existPo)) {
            return 0;
        }

        IrrigationConfigTypePo po = new IrrigationConfigTypePo();
        BeanUtils.copyProperties(param, po);

        // 设置更新信息
        po.setUpdateTime(LocalDateTime.now());
        po.setUpdateBy(SecurityUtils.getUsername());

        return this.updateById(po) ? 1 : 0;
    }

    /**
     * 删除灌区配置类型
     *
     * @param id 配置类型ID
     * @return 结果
     */
    @Override
    public int deleteIrrigationConfigTypeById(Long id) {
        return this.removeById(id) ? 1 : 0;
    }

    /**
     * 批量删除灌区配置类型
     *
     * @param ids 需要删除的配置类型ID数组
     * @return 结果
     */
    @Override
    public int deleteIrrigationConfigTypeByIds(Long[] ids) {
        return this.removeByIds(Arrays.asList(ids)) ? ids.length : 0;
    }

    /**
     * 获取气象数据配置
     *
     * @return 气象数据配置
     */
    @Override
    public WeatherDataConfigVo getWeatherDataConfig() {
        WeatherDataConfigVo vo = new WeatherDataConfigVo();

        // 查询config_type_id为1的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 1);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                // 根据config_code设置对应的值
                if ("forecast_days".equals(config.getConfigCode())) {
                    vo.setForecastDays(Integer.parseInt(config.getConfigValue()));
                } else if ("rainfall_history".equals(config.getConfigCode())) {
                    vo.setRainfallHistory(config.getConfigValue());
                } else if ("flow_rate".equals(config.getConfigCode())) {
                    vo.setFlowRate(config.getConfigValue());
                }
            }
        }

        return vo;
    }

    /**
     * 保存气象数据配置
     *
     * @param param 气象数据配置参数
     * @return 结果
     */
    @Override
    public int saveWeatherDataConfig(WeatherDataConfigParam param) {
        int result = 0;

        // 查询config_type_id为1的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 1);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        // 将配置项转换为Map，方便查找
        Map<String, IrrigationConfigValuePo> configMap = new HashMap<>();
        for (IrrigationConfigValuePo config : configList) {
            configMap.put(config.getConfigCode(), config);
        }

        // 更新预报天数
        if (ObjectUtil.isNotNull(param.getForecastDays())) {
            IrrigationConfigValuePo forecastDaysConfig = configMap.get("forecast_days");
            if (ObjectUtil.isNotNull(forecastDaysConfig)) {
                forecastDaysConfig.setConfigValue(String.valueOf(param.getForecastDays()));
                forecastDaysConfig.setUpdateTime(LocalDateTime.now());
                forecastDaysConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(forecastDaysConfig);
            }
        }

        // 更新历史平均降雨量
        if (StringUtils.isNotEmpty(param.getRainfallHistory())) {
            IrrigationConfigValuePo rainfallHistoryConfig = configMap.get("rainfall_history");
            if (ObjectUtil.isNotNull(rainfallHistoryConfig)) {
                rainfallHistoryConfig.setConfigValue(param.getRainfallHistory());
                rainfallHistoryConfig.setUpdateTime(LocalDateTime.now());
                rainfallHistoryConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(rainfallHistoryConfig);
            }
        }

        // 更新径流深度
        if (StringUtils.isNotEmpty(param.getFlowRate())) {
            IrrigationConfigValuePo flowRateConfig = configMap.get("flow_rate");
            if (ObjectUtil.isNotNull(flowRateConfig)) {
                flowRateConfig.setConfigValue(param.getFlowRate());
                flowRateConfig.setUpdateTime(LocalDateTime.now());
                flowRateConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(flowRateConfig);
            }
        }

        return result;
    }

    /**
     * 获取干旱风险配置
     *
     * @return 干旱风险配置
     */
    @Override
    public DroughtRiskConfigVo getDroughtRiskConfig() {
        DroughtRiskConfigVo vo = new DroughtRiskConfigVo();

        // 查询config_type_id为2的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 2);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                // 根据config_code设置对应的值
                if ("drought_threshold".equals(config.getConfigCode())) {
                    vo.setDroughtThreshold(Integer.parseInt(config.getConfigValue()));
                }
            }
        }

        return vo;
    }

    /**
     * 保存干旱风险配置
     *
     * @param param 干旱风险配置参数
     * @return 结果
     */
    @Override
    public int saveDroughtRiskConfig(DroughtRiskConfigParam param) {
        int result = 0;

        // 查询config_type_id为2的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 2);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        // 将配置项转换为Map，方便查找
        Map<String, IrrigationConfigValuePo> configMap = new HashMap<>();
        for (IrrigationConfigValuePo config : configList) {
            configMap.put(config.getConfigCode(), config);
        }

        // 更新干旱预警天数
        if (ObjectUtil.isNotNull(param.getDroughtThreshold())) {
            IrrigationConfigValuePo droughtThresholdConfig = configMap.get("drought_threshold");
            if (ObjectUtil.isNotNull(droughtThresholdConfig)) {
                droughtThresholdConfig.setConfigValue(String.valueOf(param.getDroughtThreshold()));
                droughtThresholdConfig.setUpdateTime(LocalDateTime.now());
                droughtThresholdConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(droughtThresholdConfig);
            }
        }

        return result;
    }

    /**
     * 获取水情年设置
     *
     * @return 水情年设置
     */
    @Override
    public WaterCoefficientConfigVo getWaterCoefficientConfig() {
        WaterCoefficientConfigVo vo = new WaterCoefficientConfigVo();

        // 查询config_type_id为3的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 3);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                // 根据config_code设置对应的值
                if ("water_coefficient_wet".equals(config.getConfigCode())) {
                    vo.setWaterCoefficientWet(config.getConfigValue());
                } else if ("water_coefficient_normal".equals(config.getConfigCode())) {
                    vo.setWaterCoefficientNormal(config.getConfigValue());
                } else if ("water_coefficient_dry".equals(config.getConfigCode())) {
                    vo.setWaterCoefficientDry(config.getConfigValue());
                }
            }
        }

        return vo;
    }

    /**
     * 保存水情年设置
     *
     * @param param 水情年设置参数
     * @return 结果
     */
    @Override
    public int saveWaterCoefficientConfig(WaterCoefficientConfigParam param) {
        int result = 0;

        // 查询config_type_id为3的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 3);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        // 将配置项转换为Map，方便查找
        Map<String, IrrigationConfigValuePo> configMap = new HashMap<>();
        for (IrrigationConfigValuePo config : configList) {
            configMap.put(config.getConfigCode(), config);
        }

        // 更新丰水年需水系数
        if (StringUtils.isNotEmpty(param.getWaterCoefficientWet())) {
            IrrigationConfigValuePo wetConfig = configMap.get("water_coefficient_wet");
            if (ObjectUtil.isNotNull(wetConfig)) {
                wetConfig.setConfigValue(param.getWaterCoefficientWet());
                wetConfig.setUpdateTime(LocalDateTime.now());
                wetConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(wetConfig);
            }
        }

        // 更新平水年需水系数
        if (StringUtils.isNotEmpty(param.getWaterCoefficientNormal())) {
            IrrigationConfigValuePo normalConfig = configMap.get("water_coefficient_normal");
            if (ObjectUtil.isNotNull(normalConfig)) {
                normalConfig.setConfigValue(param.getWaterCoefficientNormal());
                normalConfig.setUpdateTime(LocalDateTime.now());
                normalConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(normalConfig);
            }
        }

        // 更新枯水年需水系数
        if (StringUtils.isNotEmpty(param.getWaterCoefficientDry())) {
            IrrigationConfigValuePo dryConfig = configMap.get("water_coefficient_dry");
            if (ObjectUtil.isNotNull(dryConfig)) {
                dryConfig.setConfigValue(param.getWaterCoefficientDry());
                dryConfig.setUpdateTime(LocalDateTime.now());
                dryConfig.setUpdateBy(SecurityUtils.getUsername());
                result += irrigationConfigValueMapper.updateById(dryConfig);
            }
        }

        return result;
    }

    /**
     * 获取种植结构列表
     *
     * @return 种植结构列表
     */
    @Override
    public List<PlantingStructureVo> getPlantingStructureList() {
        List<PlantingStructureVo> voList = new ArrayList<>();

        // 查询config_type_id为5的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 5);
        queryWrapper.isNotNull(IrrigationConfigValuePo::getIrrigationCode);
        queryWrapper.isNotNull(IrrigationConfigValuePo::getIrrigationName);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                PlantingStructureVo vo = new PlantingStructureVo();
                vo.setIrrigationCode(config.getIrrigationCode());
                vo.setIrrigationName(config.getIrrigationName());
                vo.setCropCode(config.getConfigCode());

                // 根据作物编码设置作物名称
                if ("1".equals(config.getConfigCode())) {
                    vo.setCropName("水稽");
                } else if ("2".equals(config.getConfigCode())) {
                    vo.setCropName("小麦");
                } else if ("3".equals(config.getConfigCode())) {
                    vo.setCropName("玉米");
                } else {
                    vo.setCropName("其他");
                }

                vo.setPlantingArea(config.getConfigValue());
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 保存种植结构
     *
     * @param param 种植结构参数
     * @return 结果
     */
    @Override
    public int savePlantingStructure(PlantingStructureParam param) {
        // 查询是否已存在相同的灌区和作物
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 5);
        queryWrapper.eq(IrrigationConfigValuePo::getIrrigationCode, param.getIrrigationCode());
        queryWrapper.eq(IrrigationConfigValuePo::getConfigCode, param.getCropCode());
        IrrigationConfigValuePo existConfig = irrigationConfigValueMapper.selectOne(queryWrapper);

        if (ObjectUtil.isNotNull(existConfig)) {
            // 更新已存在的记录
            existConfig.setConfigValue(param.getPlantingArea());
            existConfig.setUpdateTime(LocalDateTime.now());
            existConfig.setUpdateBy(SecurityUtils.getUsername());
            return irrigationConfigValueMapper.updateById(existConfig);
        } else {
            // 新增记录
            IrrigationConfigValuePo newConfig = new IrrigationConfigValuePo();
            newConfig.setConfigTypeId(5L);
            newConfig.setConfigCode(param.getCropCode());
            newConfig.setConfigValue(param.getPlantingArea());
            newConfig.setIrrigationCode(param.getIrrigationCode());
            newConfig.setIrrigationName(param.getIrrigationName());
            newConfig.setIsEnabled(1);
            newConfig.setCreateTime(LocalDateTime.now());
            newConfig.setCreateBy(SecurityUtils.getUsername());

            // 设置备注
            String cropName = "";
            if ("1".equals(param.getCropCode())) {
                cropName = "水稻";
            } else if ("2".equals(param.getCropCode())) {
                cropName = "小麦";
            } else if ("3".equals(param.getCropCode())) {
                cropName = "玉米";
            } else {
                cropName = "其他";
            }
            newConfig.setRemark(cropName + "种植面积(亩)");

            return irrigationConfigValueMapper.insert(newConfig);
        }
    }

    /**
     * 批量保存种植结构
     *
     * @param param 种植结构批量参数
     * @return 结果
     */
    @Override
    public int batchSavePlantingStructure(PlantingStructureBatchParam param) {
        int result = 0;

        if (ObjectUtil.isNotEmpty(param.getPlantingStructures())) {
            for (PlantingStructureParam plantingStructure : param.getPlantingStructures()) {
                result += savePlantingStructure(plantingStructure);
            }
        }

        return result;
    }

    /**
     * 获取生活用水列表
     *
     * @return 生活用水列表
     */
    @Override
    public List<WaterUsageVo> getWaterUsageList() {
        List<WaterUsageVo> voList = new ArrayList<>();

        // 查询config_type_id为6的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 6);
        queryWrapper.orderByDesc(IrrigationConfigValuePo::getConfigCode); // 按年份降序排序
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                WaterUsageVo vo = new WaterUsageVo();
                vo.setYear(config.getConfigCode());
                vo.setWaterUsage(config.getConfigValue());
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 保存生活用水
     *
     * @param param 生活用水参数
     * @return 结果
     */
    @Override
    public int saveWaterUsage(WaterUsageParam param) {
        // 查询是否已存在相同的年份
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 6);
        queryWrapper.eq(IrrigationConfigValuePo::getConfigCode, param.getYear());
        IrrigationConfigValuePo existConfig = irrigationConfigValueMapper.selectOne(queryWrapper);

        if (ObjectUtil.isNotNull(existConfig)) {
            // 更新已存在的记录
            existConfig.setConfigValue(param.getWaterUsage());
            existConfig.setUpdateTime(LocalDateTime.now());
            existConfig.setUpdateBy(SecurityUtils.getUsername());
            return irrigationConfigValueMapper.updateById(existConfig);
        } else {
            // 新增记录
            IrrigationConfigValuePo newConfig = new IrrigationConfigValuePo();
            newConfig.setConfigTypeId(6L);
            newConfig.setConfigCode(param.getYear());
            newConfig.setConfigValue(param.getWaterUsage());
            newConfig.setIsEnabled(1);
            newConfig.setCreateTime(LocalDateTime.now());
            newConfig.setCreateBy(SecurityUtils.getUsername());
            newConfig.setRemark("生活用水量(m³)");

            return irrigationConfigValueMapper.insert(newConfig);
        }
    }

    /**
     * 批量保存生活用水
     *
     * @param param 生活用水批量参数
     * @return 结果
     */
    @Override
    public int batchSaveWaterUsage(WaterUsageBatchParam param) {
        int result = 0;

        if (ObjectUtil.isNotEmpty(param.getWaterUsages())) {
            for (WaterUsageParam waterUsage : param.getWaterUsages()) {
                result += saveWaterUsage(waterUsage);
            }
        }

        return result;
    }

    /**
     * 删除生活用水
     *
     * @param year 年份
     * @return 结果
     */
    @Override
    public int deleteWaterUsage(String year) {
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 6);
        queryWrapper.eq(IrrigationConfigValuePo::getConfigCode, year);
        return irrigationConfigValueMapper.delete(queryWrapper);
    }

    /**
     * 获取水资源配置优先级
     *
     * @return 水资源配置优先级
     */
    @Override
    public WaterPriorityConfigVo getWaterPriorityConfig() {
        WaterPriorityConfigVo vo = new WaterPriorityConfigVo();

        // 查询config_type_id为7的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 7);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                // 根据config_code设置对应的值
                if ("water_unit_first_level".equals(config.getConfigCode())) {
                    vo.setWaterUnitFirstLevel(config.getConfigValue());
                } else if ("water_unit_first_level_per".equals(config.getConfigCode())) {
                    vo.setWaterUnitFirstLevelPer(config.getConfigValue());
                } else if ("water_unit_second_level".equals(config.getConfigCode())) {
                    vo.setWaterUnitSecondLevel(config.getConfigValue());
                } else if ("water_unit_second_level_per".equals(config.getConfigCode())) {
                    vo.setWaterUnitSecondLevelPer(config.getConfigValue());
                } else if ("water_area_first_level".equals(config.getConfigCode())) {
                    vo.setWaterAreaFirstLevel(config.getConfigValue());
                } else if ("water_area_second_level".equals(config.getConfigCode())) {
                    vo.setWaterAreaSecondLevel(config.getConfigValue());
                }
            }
        }

        return vo;
    }

    /**
     * 保存水资源配置优先级
     *
     * @param param 水资源配置优先级参数
     * @return 结果
     */
    @Override
    public int saveWaterPriorityConfig(WaterPriorityConfigParam param) {
        int result = 0;

        // 查询config_type_id为7的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 7);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        // 将配置项转换为Map，方便查找
        Map<String, IrrigationConfigValuePo> configMap = new HashMap<>();
        for (IrrigationConfigValuePo config : configList) {
            configMap.put(config.getConfigCode(), config);
        }

        // 更新用水单元一级保灌优先级
        if (StringUtils.isNotEmpty(param.getWaterUnitFirstLevel())) {
            result += updateOrInsertConfig(configMap, "water_unit_first_level", param.getWaterUnitFirstLevel(), "用水单元一级保灌优先级：生活用水");
        }

        // 更新用水单元一级保灌优先级百分比
        if (StringUtils.isNotEmpty(param.getWaterUnitFirstLevelPer())) {
            result += updateOrInsertConfig(configMap, "water_unit_first_level_per", param.getWaterUnitFirstLevelPer(), "用水单元一级保灌优先级：百分比");
        }

        // 更新用水单元二级保灌优先级
        if (StringUtils.isNotEmpty(param.getWaterUnitSecondLevel())) {
            result += updateOrInsertConfig(configMap, "water_unit_second_level", param.getWaterUnitSecondLevel(), "用水单元二级保灌优先级：农业用水");
        }

        // 更新用水单元二级保灌优先级百分比
        if (StringUtils.isNotEmpty(param.getWaterUnitSecondLevelPer())) {
            result += updateOrInsertConfig(configMap, "water_unit_second_level_per", param.getWaterUnitSecondLevelPer(), "用水单元二级保灌优先级：百分比");
        }

        // 更新用水片区一级保灌优先级
        if (StringUtils.isNotEmpty(param.getWaterAreaFirstLevel())) {
            result += updateOrInsertConfig(configMap, "water_area_first_level", param.getWaterAreaFirstLevel(), "用水片区一级保灌优先级：怀远县编码");
        }

        // 更新用水片区二级保灌优先级
        if (StringUtils.isNotEmpty(param.getWaterAreaSecondLevel())) {
            result += updateOrInsertConfig(configMap, "water_area_second_level", param.getWaterAreaSecondLevel(), "用水片区二级保灌优先级：凤台县编码");
        }

        return result;
    }

    /**
     * 更新或插入配置项
     *
     * @param configMap 配置项Map
     * @param configCode 配置编码
     * @param configValue 配置值
     * @param remark 备注
     * @return 结果
     */
    private int updateOrInsertConfig(Map<String, IrrigationConfigValuePo> configMap, String configCode, String configValue, String remark) {
        IrrigationConfigValuePo config = configMap.get(configCode);
        if (ObjectUtil.isNotNull(config)) {
            // 更新已存在的记录
            config.setConfigValue(configValue);
            config.setUpdateTime(LocalDateTime.now());
            config.setUpdateBy(SecurityUtils.getUsername());
            return irrigationConfigValueMapper.updateById(config);
        } else {
            // 新增记录
            IrrigationConfigValuePo newConfig = new IrrigationConfigValuePo();
            newConfig.setConfigTypeId(7L);
            newConfig.setConfigCode(configCode);
            newConfig.setConfigValue(configValue);
            newConfig.setIsEnabled(1);
            newConfig.setCreateTime(LocalDateTime.now());
            newConfig.setCreateBy(SecurityUtils.getUsername());
            newConfig.setRemark(remark);

            return irrigationConfigValueMapper.insert(newConfig);
        }
    }

    /**
     * 获取调度泵站列表
     *
     * @return 调度泵站列表
     */
    @Override
    public List<PumpStationVo> getPumpStationList() {
        List<PumpStationVo> voList = new ArrayList<>();

        // 查询config_type_id为8的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 8);
        queryWrapper.orderByAsc(IrrigationConfigValuePo::getConfigCode); // 按泵机编号升序排序
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                PumpStationVo vo = new PumpStationVo();
                vo.setPumpNumber(config.getConfigCode());
                vo.setFlowRate(config.getConfigValue());
                vo.setIsEnabled(config.getIsEnabled());
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 保存调度泵站
     *
     * @param param 调度泵站参数
     * @return 结果
     */
    @Override
    public int savePumpStation(PumpStationParam param) {
        // 查询是否已存在相同的泵机编号
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 8);
        queryWrapper.eq(IrrigationConfigValuePo::getConfigCode, param.getPumpNumber());
        IrrigationConfigValuePo existConfig = irrigationConfigValueMapper.selectOne(queryWrapper);

        if (ObjectUtil.isNotNull(existConfig)) {
            // 更新已存在的记录
            existConfig.setConfigValue(param.getFlowRate());
            existConfig.setIsEnabled(param.getIsEnabled());
            existConfig.setUpdateTime(LocalDateTime.now());
            existConfig.setUpdateBy(SecurityUtils.getUsername());
            return irrigationConfigValueMapper.updateById(existConfig);
        } else {
            // 新增记录
            IrrigationConfigValuePo newConfig = new IrrigationConfigValuePo();
            newConfig.setConfigTypeId(8L);
            newConfig.setConfigCode(param.getPumpNumber());
            newConfig.setConfigValue(param.getFlowRate());
            newConfig.setIsEnabled(param.getIsEnabled());
            newConfig.setCreateTime(LocalDateTime.now());
            newConfig.setCreateBy(SecurityUtils.getUsername());
            newConfig.setRemark("泵机" + param.getPumpNumber() + "的瞬时流量(m³/s)");

            return irrigationConfigValueMapper.insert(newConfig);
        }
    }

    /**
     * 批量保存调度泵站
     *
     * @param param 调度泵站批量参数
     * @return 结果
     */
    @Override
    public int batchSavePumpStation(PumpStationBatchParam param) {
        int result = 0;

        if (ObjectUtil.isNotEmpty(param.getPumpStations())) {
            for (PumpStationParam pumpStation : param.getPumpStations()) {
                result += savePumpStation(pumpStation);
            }
        }

        return result;
    }

    /**
     * 获取灌溉期时间范围
     *
     * @return 灌溉期时间范围
     */
    @Override
    public IrrigationPeriodVo getIrrigationPeriod() {
        IrrigationPeriodVo vo = new IrrigationPeriodVo();

        // 查询config_type_id为9的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 9);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                // 根据config_code设置对应的值
                if ("irrigation_period_start".equals(config.getConfigCode())) {
                    vo.setIrrigationPeriodStart(config.getConfigValue());
                } else if ("irrigation_period_end".equals(config.getConfigCode())) {
                    vo.setIrrigationPeriodEnd(config.getConfigValue());
                } else if ("non_irrigation_period_start".equals(config.getConfigCode())) {
                    vo.setNonIrrigationPeriodStart(config.getConfigValue());
                } else if ("non_irrigation_period_end".equals(config.getConfigCode())) {
                    vo.setNonIrrigationPeriodEnd(config.getConfigValue());
                }
            }
        }

        return vo;
    }

    /**
     * 保存灌溉期时间范围
     *
     * @param param 灌溉期时间范围参数
     * @return 结果
     */
    @Override
    public int saveIrrigationPeriod(IrrigationPeriodParam param) {
        int result = 0;

        // 查询config_type_id为9的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 9);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        // 将配置项转换为Map，方便查找
        Map<String, IrrigationConfigValuePo> configMap = new HashMap<>();
        for (IrrigationConfigValuePo config : configList) {
            configMap.put(config.getConfigCode(), config);
        }

        // 更新灌溉期开始时间
        if (StringUtils.isNotEmpty(param.getIrrigationPeriodStart())) {
            result += updateOrInsertPeriodConfig(configMap, "irrigation_period_start", param.getIrrigationPeriodStart(), "灌溉期开始时间（月-日）");
        }

        // 更新灌溉期结束时间
        if (StringUtils.isNotEmpty(param.getIrrigationPeriodEnd())) {
            result += updateOrInsertPeriodConfig(configMap, "irrigation_period_end", param.getIrrigationPeriodEnd(), "灌溉期结束时间（月-日）");
        }

        // 更新非灌溉期开始时间
        if (StringUtils.isNotEmpty(param.getNonIrrigationPeriodStart())) {
            result += updateOrInsertPeriodConfig(configMap, "non_irrigation_period_start", param.getNonIrrigationPeriodStart(), "非灌溉期开始时间（月-日）");
        }

        // 更新非灌溉期结束时间
        if (StringUtils.isNotEmpty(param.getNonIrrigationPeriodEnd())) {
            result += updateOrInsertPeriodConfig(configMap, "non_irrigation_period_end", param.getNonIrrigationPeriodEnd(), "非灌溉期结束时间（月-日）");
        }

        return result;
    }

    /**
     * 更新或插入时间范围配置项
     *
     * @param configMap 配置项Map
     * @param configCode 配置编码
     * @param configValue 配置值
     * @param remark 备注
     * @return 结果
     */
    private int updateOrInsertPeriodConfig(Map<String, IrrigationConfigValuePo> configMap, String configCode, String configValue, String remark) {
        IrrigationConfigValuePo config = configMap.get(configCode);
        if (ObjectUtil.isNotNull(config)) {
            // 更新已存在的记录
            config.setConfigValue(configValue);
            config.setUpdateTime(LocalDateTime.now());
            config.setUpdateBy(SecurityUtils.getUsername());
            return irrigationConfigValueMapper.updateById(config);
        } else {
            // 新增记录
            IrrigationConfigValuePo newConfig = new IrrigationConfigValuePo();
            newConfig.setConfigTypeId(9L);
            newConfig.setConfigCode(configCode);
            newConfig.setConfigValue(configValue);
            newConfig.setIsEnabled(1);
            newConfig.setCreateTime(LocalDateTime.now());
            newConfig.setCreateBy(SecurityUtils.getUsername());
            newConfig.setRemark(remark);

            return irrigationConfigValueMapper.insert(newConfig);
        }
    }

    /**
     * 获取水位保证
     *
     * @return 水位保证
     */
    @Override
    public WaterLevelGuaranteeVo getWaterLevelGuarantee() {
        WaterLevelGuaranteeVo vo = new WaterLevelGuaranteeVo();

        // 查询config_type_id为10的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 10);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(configList)) {
            for (IrrigationConfigValuePo config : configList) {
                // 根据config_code设置对应的值
                if ("navigation_water_level".equals(config.getConfigCode())) {
                    vo.setNavigationWaterLevel(config.getConfigValue());
                } else if ("huaihe_min_water_level".equals(config.getConfigCode())) {
                    vo.setHuaiheMinWaterLevel(config.getConfigValue());
                } else if ("nanwan_gate_water_level".equals(config.getConfigCode())) {
                    vo.setNanwanGateWaterLevel(config.getConfigValue());
                }
            }
        }

        return vo;
    }

    /**
     * 保存水位保证
     *
     * @param param 水位保证参数
     * @return 结果
     */
    @Override
    public int saveWaterLevelGuarantee(WaterLevelGuaranteeParam param) {
        int result = 0;

        // 查询config_type_id为10的所有配置项
        LambdaQueryWrapper<IrrigationConfigValuePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigValuePo::getConfigTypeId, 10);
        List<IrrigationConfigValuePo> configList = irrigationConfigValueMapper.selectList(queryWrapper);

        // 将配置项转换为Map，方便查找
        Map<String, IrrigationConfigValuePo> configMap = new HashMap<>();
        for (IrrigationConfigValuePo config : configList) {
            configMap.put(config.getConfigCode(), config);
        }

        // 更新通航保证水位
        if (StringUtils.isNotEmpty(param.getNavigationWaterLevel())) {
            result += updateOrInsertWaterLevelConfig(configMap, "navigation_water_level", param.getNavigationWaterLevel(), "通航保证水位(m)");
        }

        // 更新淮河最低限制水位
        if (StringUtils.isNotEmpty(param.getHuaiheMinWaterLevel())) {
            result += updateOrInsertWaterLevelConfig(configMap, "huaihe_min_water_level", param.getHuaiheMinWaterLevel(), "淮河最低限制水位(m)");
        }

        // 更新南湾节制闸水位
        if (StringUtils.isNotEmpty(param.getNanwanGateWaterLevel())) {
            result += updateOrInsertWaterLevelConfig(configMap, "nanwan_gate_water_level", param.getNanwanGateWaterLevel(), "南湾节制闸水位(m)");
        }

        return result;
    }

    /**
     * 更新或插入水位配置项
     *
     * @param configMap 配置项Map
     * @param configCode 配置编码
     * @param configValue 配置值
     * @param remark 备注
     * @return 结果
     */
    private int updateOrInsertWaterLevelConfig(Map<String, IrrigationConfigValuePo> configMap, String configCode, String configValue, String remark) {
        IrrigationConfigValuePo config = configMap.get(configCode);
        if (ObjectUtil.isNotNull(config)) {
            // 更新已存在的记录
            config.setConfigValue(configValue);
            config.setUpdateTime(LocalDateTime.now());
            config.setUpdateBy(SecurityUtils.getUsername());
            return irrigationConfigValueMapper.updateById(config);
        } else {
            // 新增记录
            IrrigationConfigValuePo newConfig = new IrrigationConfigValuePo();
            newConfig.setConfigTypeId(10L);
            newConfig.setConfigCode(configCode);
            newConfig.setConfigValue(configValue);
            newConfig.setIsEnabled(1);
            newConfig.setCreateTime(LocalDateTime.now());
            newConfig.setCreateBy(SecurityUtils.getUsername());
            newConfig.setRemark(remark);

            return irrigationConfigValueMapper.insert(newConfig);
        }
    }

    /**
     * 获取农作物周期信息列表
     *
     * @param cropId 作物ID，可为null，为null时查询所有作物
     * @return 农作物周期信息列表
     */
    @Override
    public List<CropCycleVo> getCropCycleList(Long cropId) {
        List<CropCycleVo> voList = new ArrayList<>();

        // 查询config_type_id为4的所有配置项
        LambdaQueryWrapper<IrrigationConfigCropCyclePo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IrrigationConfigCropCyclePo::getConfigTypeId, 4);
        // 如果cropId不为空，则按cropId过滤
        if (ObjectUtil.isNotNull(cropId)) {
            queryWrapper.eq(IrrigationConfigCropCyclePo::getCropId, cropId);
        }
        queryWrapper.orderByAsc(IrrigationConfigCropCyclePo::getCropId)
                .orderByAsc(IrrigationConfigCropCyclePo::getCycleSort);
        List<IrrigationConfigCropCyclePo> cycleList = irrigationConfigCropCycleMapper.selectList(queryWrapper);

        if (ObjectUtil.isNotEmpty(cycleList)) {
            for (IrrigationConfigCropCyclePo cycle : cycleList) {
                CropCycleVo vo = new CropCycleVo();
                BeanUtils.copyProperties(cycle, vo);
                voList.add(vo);
            }
        }

        return voList;
    }

    /**
     * 保存农作物周期信息
     *
     * @param param 农作物周期信息参数
     * @return 结果
     */
    @Override
    public int saveCropCycle(CropCycleParam param) {
        IrrigationConfigCropCyclePo cyclePo = new IrrigationConfigCropCyclePo();
        BeanUtils.copyProperties(param, cyclePo);

        // 设置配置类型ID为4
        cyclePo.setConfigTypeId(4L);

        if (ObjectUtil.isNotNull(param.getCycleId())) {
            // 更新
            return irrigationConfigCropCycleMapper.updateById(cyclePo);
        } else {
            // 新增
            return irrigationConfigCropCycleMapper.insert(cyclePo);
        }
    }

    /**
     * 批量保存农作物周期信息
     *
     * @param param 农作物周期信息批量参数
     * @return 结果
     */
    @Override
    public int batchSaveCropCycle(CropCycleBatchParam param) {
        int result = 0;

        if (ObjectUtil.isNotEmpty(param.getCropCycles())) {
            for (CropCycleParam cropCycle : param.getCropCycles()) {
                result += saveCropCycle(cropCycle);
            }
        }

        return result;
    }

    /**
     * 删除农作物周期信息
     *
     * @param cycleId 周期ID
     * @return 结果
     */
    @Override
    public int deleteCropCycle(Long cycleId) {
        return irrigationConfigCropCycleMapper.deleteById(cycleId);
    }
}
