package com.jhdr.irrigation.service;

import java.util.List;
import com.jhdr.irrigation.entity.po.WaterSchedulePlanLevelPo;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelAddParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanLevelEditParam;
import com.jhdr.irrigation.entity.vo.WaterSchedulePlanLevelVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 灌区管理-调度方案水位历史数据Service接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface IWaterSchedulePlanLevelService extends IService<WaterSchedulePlanLevelPo>
{

    /**
     * 查询灌区管理-调度方案水位历史数据列表
     *
     * @param waterSchedulePlanLevel 灌区管理-调度方案水位历史数据
     * @return 灌区管理-调度方案水位历史数据集合
     */
    public List<WaterSchedulePlanLevelVo> queryList(WaterSchedulePlanLevelPo waterSchedulePlanLevel);

    /**
     * 查询灌区管理-调度方案水位历史数据
     *
     * @param pumpId 灌区管理-调度方案水位历史数据主键
     * @return 灌区管理-调度方案水位历史数据
     */
    public WaterSchedulePlanLevelVo selectWaterSchedulePlanLevelByPumpId(Long pumpId);

    /**
     * 查询灌区管理-调度方案水位历史数据列表
     *
     * @param waterSchedulePlanLevel 灌区管理-调度方案水位历史数据
     * @return 灌区管理-调度方案水位历史数据集合
     */
    public List<WaterSchedulePlanLevelVo> selectWaterSchedulePlanLevelList(WaterSchedulePlanLevelParam waterSchedulePlanLevel);

    /**
     * 新增灌区管理-调度方案水位历史数据
     *
     * @param waterSchedulePlanLevel 灌区管理-调度方案水位历史数据
     * @return 结果
     */
    public int insertWaterSchedulePlanLevel(WaterSchedulePlanLevelAddParam waterSchedulePlanLevel);

    /**
     * 修改灌区管理-调度方案水位历史数据
     *
     * @param waterSchedulePlanLevel 灌区管理-调度方案水位历史数据
     * @return 结果
     */
    public int updateWaterSchedulePlanLevel(WaterSchedulePlanLevelEditParam waterSchedulePlanLevel);

    /**
     * 批量删除灌区管理-调度方案水位历史数据
     *
     * @param pumpIds 需要删除的灌区管理-调度方案水位历史数据主键集合
     * @return 结果
     */
    public int deleteWaterSchedulePlanLevelByPumpIds(Long[] pumpIds);

    /**
     * 删除灌区管理-调度方案水位历史数据信息
     *
     * @param pumpId 灌区管理-调度方案水位历史数据主键
     * @return 结果
     */
    public int deleteWaterSchedulePlanLevelByPumpId(Long pumpId);

}
