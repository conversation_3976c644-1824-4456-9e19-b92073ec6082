package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.IrrigationCropCyclePo;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleParam;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleAddParam;
import com.jhdr.irrigation.entity.param.IrrigationCropCycleEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationCropCycleVo;


/**
 * 灌区作物周期Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-04
 */
public interface IrrigationCropCycleMapper extends BaseMapper<IrrigationCropCyclePo>
{
    /**
     * 查询灌区作物周期
     *
     * @param cycleId 灌区作物周期主键
     * @return 灌区作物周期
     */
    public IrrigationCropCycleVo selectIrrigationCropCycleByCycleId(Long cycleId);

    /**
     * 查询灌区作物周期列表
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 灌区作物周期集合
     */
    public List<IrrigationCropCycleVo> selectIrrigationCropCycleList(IrrigationCropCycleParam irrigationCropCycle);

    /**
     * 新增灌区作物周期
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 结果
     */
    public int insertIrrigationCropCycle(IrrigationCropCycleAddParam irrigationCropCycle);

    /**
     * 修改灌区作物周期
     *
     * @param irrigationCropCycle 灌区作物周期
     * @return 结果
     */
    public int updateIrrigationCropCycle(IrrigationCropCycleEditParam irrigationCropCycle);

    /**
     * 删除灌区作物周期
     *
     * @param cycleId 灌区作物周期主键
     * @return 结果
     */
    public int deleteIrrigationCropCycleByCycleId(Long cycleId);

    /**
     * 批量删除灌区作物周期
     *
     * @param cycleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationCropCycleByCycleIds(Long[] cycleIds);
}
