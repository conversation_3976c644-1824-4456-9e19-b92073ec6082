package com.jhdr.irrigation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 灌区水情分析 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Mapper
public interface IrrigationWaterAnalysisMapper {

    /**
     * 根据测站编码和水位查询库容
     *
     * @param stcd 测站编码
     * @param waterLevel 水位
     * @return 库容
     */
    Double getCapacityByWaterLevel(@Param("stcd") String stcd, @Param("waterLevel") Double waterLevel);

    /**
     * 根据测站编码和库容查询水位
     *
     * @param stcd 测站编码
     * @param capacity 库容
     * @return 水位
     */
    Double getWaterLevelByCapacity(@Param("stcd") String stcd, @Param("capacity") Double capacity);

    /**
     * 获取库容曲线数据
     *
     * @param stcd 测站编码
     * @return 库容曲线数据，key为水位，value为库容
     */
    List<Map<String, Object>> getCapacityCurveData(@Param("stcd") String stcd);

    /**
     * 保存降雨数据
     *
     * @param stcd 测站编码
     * @param tm 时间
     * @param drp 降雨量
     * @return 结果
     */
    int saveRainfallData(@Param("stcd") String stcd, @Param("tm") Date tm, @Param("drp") Double drp);

    /**
     * 保存河道水位数据
     *
     * @param stcd 测站编码
     * @param tm 时间
     * @param z 水位
     * @return 结果
     */
    int saveWaterLevelData(@Param("stcd") String stcd, @Param("tm") Date tm, @Param("z") Double z);

    /**
     * 保存枢纽流量数据
     *
     * @param stcd 测站编码
     * @param tm 时间
     * @param q 流量
     * @return 结果
     */
    int saveHubFlowData(@Param("stcd") String stcd, @Param("tm") Date tm, @Param("q") Double q);
}
