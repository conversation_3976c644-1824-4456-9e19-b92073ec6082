package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.po.IrrigationCropGrowPo;
import com.jhdr.irrigation.entity.vo.IrrigationCropGrowVo;
import com.jhdr.irrigation.entity.vo.StStationWaterMonthPumpVo;
import com.jhdr.irrigation.entity.vo.StStationWaterYearPumpVo;


/**
 * 灌区作物生长期Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface IrrigationCropGrowMapper extends BaseMapper<IrrigationCropGrowPo>
{
    /**
     * 查询灌区作物生长期
     *
     * @param id 灌区作物生长期主键
     * @return 灌区作物生长期
     */
    public IrrigationCropGrowVo selectIrrigationCropGrowById(Long id);

    /**
     * 查询灌区作物生长期列表
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 灌区作物生长期集合
     */
    public List<IrrigationCropGrowVo> selectIrrigationCropGrowList(IrrigationCropGrowParam irrigationCropGrow);

    /**
     * 新增灌区作物生长期
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 结果
     */
    public int insertIrrigationCropGrow(IrrigationCropGrowAddParam irrigationCropGrow);

    /**
     * 修改灌区作物生长期
     *
     * @param irrigationCropGrow 灌区作物生长期
     * @return 结果
     */
    public int updateIrrigationCropGrow(IrrigationCropGrowEditParam irrigationCropGrow);



    /**
     * 批量删除灌区作物生长期
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationCropGrowByIds(Long[] ids);

    List<StStationWaterYearPumpVo> pumpYearList(StStationWaterYearParam stationWaterYearParam);

    List<StStationWaterYearPumpVo> floodYearList(StStationWaterYearParam stationWaterYearParam);

    List<StStationWaterMonthPumpVo> pumpMonthList(StStationWaterMonthParam stationWaterMonthParam);

    List<StStationWaterMonthPumpVo> floodMonthList(StStationWaterMonthParam stationWaterMonthParam);
}
