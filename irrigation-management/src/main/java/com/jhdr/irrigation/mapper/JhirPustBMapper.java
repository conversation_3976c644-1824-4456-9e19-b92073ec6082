package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.JhirPustBPo;
import com.jhdr.irrigation.entity.param.JhirPustBParam;
import com.jhdr.irrigation.entity.param.JhirPustBAddParam;
import com.jhdr.irrigation.entity.param.JhirPustBEditParam;
import com.jhdr.irrigation.entity.vo.JhirPustBVo;
import com.jhdr.irrigation.entity.vo.JhirPustRegionVo;


/**
 * 泵站基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface JhirPustBMapper extends BaseMapper<JhirPustBPo>
{
    /**
     * 查询泵站基本信息
     *
     * @param strCode 泵站基本信息主键
     * @return 泵站基本信息
     */
    public JhirPustBVo selectJhirPustBByStrCode(String strCode);

    /**
     * 查询泵站基本信息列表
     *
     * @param jhirPustB 泵站基本信息
     * @return 泵站基本信息集合
     */
    public List<JhirPustBVo> selectJhirPustBList(JhirPustBParam jhirPustB);

    /**
     * 新增泵站基本信息
     *
     * @param jhirPustB 泵站基本信息
     * @return 结果
     */
    public int insertJhirPustB(JhirPustBAddParam jhirPustB);

    /**
     * 修改泵站基本信息
     *
     * @param jhirPustB 泵站基本信息
     * @return 结果
     */
    public int updateJhirPustB(JhirPustBEditParam jhirPustB);

    /**
     * 删除泵站基本信息
     *
     * @param strCode 泵站基本信息主键
     * @return 结果
     */
    public int deleteJhirPustBByStrCode(String strCode);

    /**
     * 批量删除泵站基本信息
     *
     * @param strCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJhirPustBByStrCodes(String[] strCodes);

    List<JhirPustRegionVo> selectRegionList();
}
