package com.jhdr.irrigation.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jhdr.irrigation.entity.po.IrrigationRegionInfoPo;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoParam;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoAddParam;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationRegionInfoVo;


/**
 * 灌区区域信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface IrrigationRegionInfoMapper extends BaseMapper<IrrigationRegionInfoPo>
{
    /**
     * 查询灌区区域信息
     *
     * @param id 灌区区域信息主键
     * @return 灌区区域信息
     */
    public IrrigationRegionInfoVo selectIrrigationRegionInfoById(Long id);

    /**
     * 查询灌区区域信息列表
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 灌区区域信息集合
     */
    public List<IrrigationRegionInfoVo> selectIrrigationRegionInfoList(IrrigationRegionInfoParam irrigationRegionInfo);

    /**
     * 新增灌区区域信息
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 结果
     */
    public int insertIrrigationRegionInfo(IrrigationRegionInfoAddParam irrigationRegionInfo);

    /**
     * 修改灌区区域信息
     *
     * @param irrigationRegionInfo 灌区区域信息
     * @return 结果
     */
    public int updateIrrigationRegionInfo(IrrigationRegionInfoEditParam irrigationRegionInfo);

    /**
     * 删除灌区区域信息
     *
     * @param id 灌区区域信息主键
     * @return 结果
     */
    public int deleteIrrigationRegionInfoById(Long id);

    /**
     * 批量删除灌区区域信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIrrigationRegionInfoByIds(Long[] ids);
}
