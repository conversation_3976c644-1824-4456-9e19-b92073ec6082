package com.jhdr.irrigation.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.web.page.TableDataInfo;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.vo.IrrigationCropGrowVo;
import com.jhdr.irrigation.entity.vo.JhirPustRegionVo;
import com.jhdr.irrigation.entity.vo.StStationWaterMonthPumpVo;
import com.jhdr.irrigation.entity.vo.StStationWaterYearPumpVo;
import com.jhdr.irrigation.service.IIrrigationCropGrowService;
import com.jhdr.irrigation.service.IJhirPustBService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 灌区作物生长期
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Api(tags = "泵闸站统计分析")
@RestController
@RequestMapping("/statistic")
public class StStationWaterYearController extends BaseController
{
    @Autowired
    private IIrrigationCropGrowService irrigationCropGrowService;
    @Autowired
    private IJhirPustBService jhirPustBService;

    /**
     * 查询泵站基本信息列表
     */
    @ApiOperation(value = "查询区域列表")
    @GetMapping("/region")
    public R<List<JhirPustRegionVo>> region()
    {
        List<JhirPustRegionVo> list = jhirPustBService.selectRegionList();
        return R.ok(list);
    }


    @ApiOperation(value = "泵站年统计")
    @GetMapping("/pump/year/list")
    public R<List<StStationWaterYearPumpVo>> pumpYearList(StStationWaterYearParam stationWaterYearParam)
    {

        List<StStationWaterYearPumpVo> list = irrigationCropGrowService.pumpYearList(stationWaterYearParam);
        return R.ok(list);
    }


    @ApiOperation(value = "闸站年统计")
    @GetMapping("/flood/year/list")
    public R<List<StStationWaterYearPumpVo>> floodYearList(StStationWaterYearParam stationWaterYearParam)
    {
        List<StStationWaterYearPumpVo> list = irrigationCropGrowService.floodYearList(stationWaterYearParam);
        return R.ok(list);
    }

    @ApiOperation(value = "泵站月统计")
    @GetMapping("/pump/month/list")
    public R<List<StStationWaterMonthPumpVo>> pumpMonthList(StStationWaterMonthParam stationWaterMonthParam)
    {
        List<StStationWaterMonthPumpVo> list = irrigationCropGrowService.pumpMonthList(stationWaterMonthParam);
       if (list.size()>0){
           for (StStationWaterMonthPumpVo stStationWaterMonthPumpVo : list) {
               stStationWaterMonthPumpVo.setStationYear(stationWaterMonthParam.getStationYear()+"");
           }
       }
        return R.ok(list);
    }


    @ApiOperation(value = "闸站月统计")
    @GetMapping("/flood/month/list")
    public R<List<StStationWaterMonthPumpVo>> floodMonthList(StStationWaterMonthParam stationWaterMonthParam)
    {
        List<StStationWaterMonthPumpVo> list = irrigationCropGrowService.floodMonthList(stationWaterMonthParam);
        if (list.size()>0){
            for (StStationWaterMonthPumpVo stStationWaterMonthPumpVo : list) {
                stStationWaterMonthPumpVo.setStationYear(stationWaterMonthParam.getStationYear()+"");
            }
        }
        return R.ok(list);
    }



}
