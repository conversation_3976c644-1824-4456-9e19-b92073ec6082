package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.IrrigationRegionInfoPo;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoParam;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoAddParam;
import com.jhdr.irrigation.entity.param.IrrigationRegionInfoEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationRegionInfoVo;
import com.jhdr.irrigation.service.IIrrigationRegionInfoService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 灌区区域信息
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Api(tags = "供需水分析-供水分析")
@RestController
@RequestMapping("/region")
public class IrrigationRegionInfoController extends BaseController
{
    @Autowired
    private IIrrigationRegionInfoService irrigationRegionInfoService;

    /**
     * 查询灌区区域信息列表
     */
    @ApiOperation(value = "查询灌区区域信息列表",notes="irrigation:region:list")
    @RequiresPermissions("irrigation:region:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationRegionInfoVo>> list(IrrigationRegionInfoParam irrigationRegionInfoParam)
    {
        startPage();
        List<IrrigationRegionInfoVo> list = irrigationRegionInfoService.selectIrrigationRegionInfoList(irrigationRegionInfoParam);
        return getDataTable(list);
    }

    /**
     * 修改灌区区域信息
     */
    @ApiOperation(value = "修改灌区区域信息",notes="irrigation:region:edit")
    @RequiresPermissions("irrigation:region:edit")
    @Log(title = "灌区区域信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationRegionInfoEditParam irrigationRegionInfo)
    {
        return toAjaxR(irrigationRegionInfoService.updateIrrigationRegionInfo(irrigationRegionInfo),"修改");
    }


}
