package com.jhdr.irrigation.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanAddParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanContrastParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanGenerateParam;
import com.jhdr.irrigation.entity.param.WaterSchedulePlanParam;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.IWaterSchedulePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 灌区管理-调度方案
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Api(tags = "灌区管理-调度方案")
@RestController
@RequestMapping("/plan")
public class WaterSchedulePlanController extends BaseController
{
    @Autowired
    private IWaterSchedulePlanService waterSchedulePlanService;

    /**
     * 查询灌区管理-调度方案列表
     */
    @ApiOperation(value = "新获取12个月的降雨量")
    @GetMapping("/rain/all")
    public R<WaterSchedulePlanRainVo> rainAll()
    {
        WaterSchedulePlanRainVo rainVo= waterSchedulePlanService.rainAll();
        return R.ok(rainVo);
    }



    @ApiOperation(value = "新调度方案生成 月份 1-12月")
    @GetMapping("/new/dispatch/generate/{yue}")
    public R<WaterSchedulePlanDispatchVo> dispatchGenerate(@PathVariable("yue") Integer yue)
    {
        WaterSchedulePlanDispatchVo dispatchGenerate = waterSchedulePlanService.dispatchGenerate(yue);
        return R.ok(dispatchGenerate);
    }


    @ApiOperation(value = "新灌区水位调度方案生成 传递方案1 或者 2")
    @GetMapping("/new/generate/level/{dispatchScheme}")
    public R<WaterSchedulePlanLineNowListVo> newSchedulePlanLevel(@PathVariable("dispatchScheme") Integer dispatchScheme)
    {
        WaterSchedulePlanLineNowListVo lineListVo = waterSchedulePlanService.newSchedulePlanLevel(dispatchScheme);
        return R.ok(lineListVo);
    }


    @ApiOperation(value = "灌区泵站调度方案生成")
    @GetMapping("/generate/pump")
    public R<WaterSchedulePlanPumpListVo> schedulePlanPump(WaterSchedulePlanGenerateParam waterSchedulePlanParam)
    {
        WaterSchedulePlanPumpListVo planPump = waterSchedulePlanService.schedulePlanPump(waterSchedulePlanParam);
        return R.ok(planPump);
    }

    @ApiOperation(value = "灌区水位调度方案生成")
    @GetMapping("/generate/level")
    public R<WaterSchedulePlanLineListVo> schedulePlanLevel(WaterSchedulePlanGenerateParam waterSchedulePlanParam)
    {
        WaterSchedulePlanLineListVo lineListVo = waterSchedulePlanService.schedulePlanLevel(waterSchedulePlanParam);
        return R.ok(lineListVo);
    }

    /**
     * 保存灌区管理-调度方案
     */
    @ApiOperation(value = "保存灌区管理-调度方案",notes="irrigation:plan:add")
    @RequiresPermissions("irrigation:plan:add")
    @Log(title = "灌区管理-调度方案", businessType = BusinessType.INSERT)
    @PostMapping("save")
    public R add(@RequestBody WaterSchedulePlanAddParam waterSchedulePlan)
    {
        return toAjaxR(waterSchedulePlanService.insertWaterSchedulePlan(waterSchedulePlan),"新增");
    }


    /**
     * 查询灌区管理-调度方案列表
     */
    @ApiOperation(value = "查询灌区管理-调度方案列表或对比下拉列表",notes="irrigation:plan:list")
    @RequiresPermissions("irrigation:plan:list")
    @GetMapping("/list")
    public R<List<WaterSchedulePlanVo>> list(WaterSchedulePlanParam waterSchedulePlanParam)
    {
        List<WaterSchedulePlanVo> list = waterSchedulePlanService.selectWaterSchedulePlanList(waterSchedulePlanParam);
        return R.ok(list);
    }


    /**
     * 获取灌区管理-调度方案详细信息
     */
    @ApiOperation(value = "获取灌区管理-调度方案详细信息",notes="irrigation:plan:query")
    @RequiresPermissions("irrigation:plan:query")
    @GetMapping(value = "/{planId}")
    public R<WaterSchedulePlanInfoVo> getPlanInfo(@PathVariable("planId") Long planId)
    {
        WaterSchedulePlanInfoVo planInfo =  waterSchedulePlanService.getPlanInfo(planId);
        return R.ok(planInfo);
    }



    /**
     * 删除灌区管理-调度方案
     */
    @ApiOperation(value = "删除灌区管理-调度方案",notes="irrigation:plan:remove")
    @RequiresPermissions("irrigation:plan:remove")
    @Log(title = "灌区管理-调度方案", businessType = BusinessType.DELETE)
	@DeleteMapping("/{planIds}")
    public R remove(@PathVariable Long[] planIds)
    {
        return toAjaxR(waterSchedulePlanService.deleteWaterSchedulePlanByPlanIds(planIds),"删除");
    }



    @ApiOperation(value = "调度方案对比--泵站数据")
    @GetMapping("/contrast/pump/list")
    public R<WaterSchedulePlanContrastListVo> contrastPumpList(WaterSchedulePlanContrastParam waterSchedulePlanParam)
    {
        WaterSchedulePlanContrastListVo pumpList = waterSchedulePlanService.contrastPumpList(waterSchedulePlanParam);
        return R.ok(pumpList);
    }

}
