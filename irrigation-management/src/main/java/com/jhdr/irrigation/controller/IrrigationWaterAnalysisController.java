package com.jhdr.irrigation.controller;

import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.WaterAnalysisParam;
import com.jhdr.irrigation.entity.param.WaterAnalysisSimpleParam;
import com.jhdr.irrigation.entity.vo.AvailableWaterVo;
import com.jhdr.irrigation.entity.vo.RegionWaterProductionVo;
import com.jhdr.irrigation.entity.vo.WaterInflowAnalysisVo;
import com.jhdr.irrigation.service.IIrrigationWaterAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 灌区水情分析控制器
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Api(tags = "灌区水情分析")
@RestController
@RequestMapping("/irrigation/water/analysis")
public class IrrigationWaterAnalysisController extends BaseController {

    @Autowired
    private IIrrigationWaterAnalysisService irrigationWaterAnalysisService;

    /**
     * 获取阚疃节制闸断面来水情况分析
     */
    @ApiOperation(value = "获取阚疃节制闸断面来水情况分析", notes = "irrigation:water:analysis:inflow:query")
    @RequiresPermissions("irrigation:water:analysis:inflow:query")
    @PostMapping("/inflow")
    public R<WaterInflowAnalysisVo> getWaterInflowAnalysis(@Valid @RequestBody WaterAnalysisParam param) {
        WaterInflowAnalysisVo vo = irrigationWaterAnalysisService.getWaterInflowAnalysis(param);
        return R.ok(vo);
    }

    /**
     * 获取片区产水量分析
     */
    @ApiOperation(value = "获取片区产水量分析", notes = "irrigation:water:analysis:production:query")
    @RequiresPermissions("irrigation:water:analysis:production:query")
    @PostMapping("/production")
    public R<RegionWaterProductionVo> getRegionWaterProduction(@Valid @RequestBody WaterAnalysisParam param) {
        RegionWaterProductionVo vo = irrigationWaterAnalysisService.getRegionWaterProduction(param);
        return R.ok(vo);
    }

    /**
     * 获取可供水量分析
     */
    @ApiOperation(value = "获取可供水量分析", notes = "irrigation:water:analysis:available:query")
    @RequiresPermissions("irrigation:water:analysis:available:query")
    @PostMapping("/available")
    public R<AvailableWaterVo> getAvailableWater(@Valid @RequestBody WaterAnalysisParam param) {
        AvailableWaterVo vo = irrigationWaterAnalysisService.getAvailableWater(param);
        return R.ok(vo);
    }
}
