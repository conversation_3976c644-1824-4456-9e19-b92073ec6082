package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import cn.hutool.core.bean.BeanUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.IrrigationCropManagePo;
import com.jhdr.irrigation.entity.param.IrrigationCropManageParam;
import com.jhdr.irrigation.entity.param.IrrigationCropManageAddParam;
import com.jhdr.irrigation.entity.param.IrrigationCropManageEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationCropManageVo;
import com.jhdr.irrigation.service.IIrrigationCropManageService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 灌区作物管理
 *
 * <AUTHOR>
 * @date 2024-08-09
 */
@Api(tags = "灌区作物管理")
@RestController
@RequestMapping("/crop")
public class IrrigationCropManageController extends BaseController
{
    @Autowired
    private IIrrigationCropManageService irrigationCropManageService;

    /**
     * 查询灌区作物管理列表
     */
    @ApiOperation(value = "查询灌区作物管理列表",notes="irrigation:crop:list")
    @RequiresPermissions("irrigation:crop:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationCropManageVo>> list(IrrigationCropManageParam irrigationCropManageParam)
    {
        startPage();
        List<IrrigationCropManageVo> list = irrigationCropManageService.selectIrrigationCropManageList(irrigationCropManageParam);
        return getDataTable(list);
    }


    /**
     * 获取灌区作物管理详细信息
     */
    @ApiOperation(value = "获取灌区作物管理详细信息",notes="irrigation:crop:query")
    @RequiresPermissions("irrigation:crop:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationCropManageVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(irrigationCropManageService.selectIrrigationCropManageById(id));
    }

    /**
     * 新增灌区作物管理
     */
    @ApiOperation(value = "新增灌区作物管理",notes="irrigation:crop:add")
    @RequiresPermissions("irrigation:crop:add")
    @Log(title = "灌区作物管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationCropManageAddParam irrigationCropManage)
    {
        return toAjaxR(irrigationCropManageService.insertIrrigationCropManage(irrigationCropManage),"新增");
    }


    @ApiOperation(value = "保存灌区作物管理",notes="irrigation:crop:save")
    @RequiresPermissions("irrigation:crop:save")
    @Log(title = "灌区作物管理", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/all/save")
    public R allAdd(@RequestBody List<IrrigationCropManageAddParam> irrigationCropManages)
    {
        List<IrrigationCropManagePo> irrigationCropManagePos = BeanUtil.copyToList(irrigationCropManages, IrrigationCropManagePo.class);
        return toAjaxR(irrigationCropManageService.saveOrUpdateBatch(irrigationCropManagePos),"新增");
    }

    @ApiOperation(value = "检查灌区作物管理 返回问题泵站,分隔")
    @PostMapping(value = "/all/check")
    public R<String> allCheck(@RequestBody List<IrrigationCropManageAddParam> irrigationCropManages)
    {

        return R.ok(irrigationCropManageService.allCheck(irrigationCropManages));
    }

    /**
     * 修改灌区作物管理
     */
    @ApiOperation(value = "修改灌区作物管理",notes="irrigation:crop:edit")
    @RequiresPermissions("irrigation:crop:edit")
    @Log(title = "灌区作物管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationCropManageEditParam irrigationCropManage)
    {
        return toAjaxR(irrigationCropManageService.updateIrrigationCropManage(irrigationCropManage),"修改");
    }

    /**
     * 删除灌区作物管理
     */
    @ApiOperation(value = "删除灌区作物管理",notes="irrigation:crop:remove")
    @RequiresPermissions("irrigation:crop:remove")
    @Log(title = "灌区作物管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationCropManageService.deleteIrrigationCropManageByIds(ids),"删除");
    }
}
