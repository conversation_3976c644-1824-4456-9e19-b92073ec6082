package com.jhdr.irrigation.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.core.utils.bean.BeanUtils;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.po.IrrigationPlanPo;
import com.jhdr.irrigation.entity.param.IrrigationPlanParam;
import com.jhdr.irrigation.entity.param.IrrigationPlanAddParam;
import com.jhdr.irrigation.entity.param.IrrigationPlanEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationPlanVo;
import com.jhdr.irrigation.service.IIrrigationPlanService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.utils.poi.ExcelUtil;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 灌溉计划
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Api(tags = "供用水管理-灌溉计划制定")
@RestController
@RequestMapping("/irrigate/plan")
public class IrrigationPlanController extends BaseController
{
    @Autowired
    private IIrrigationPlanService irrigationPlanService;

    /**
     * 查询灌溉计划列表
     */
    @ApiOperation(value = "查询灌溉计划列表",notes="irrigation:plan:list")
    @RequiresPermissions("irrigation:plan:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationPlanVo>> list(IrrigationPlanParam irrigationPlanParam)
    {
        startPage();
        List<IrrigationPlanVo> list = irrigationPlanService.selectIrrigationPlanList(irrigationPlanParam);
        return getDataTable(list);
    }



    /**
     * 获取灌溉计划详细信息
     */
    @ApiOperation(value = "获取灌溉计划详细信息",notes="irrigation:plan:query")
    @RequiresPermissions("irrigation:plan:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationPlanVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(irrigationPlanService.selectIrrigationPlanById(id));
    }

    /**
     * 新增灌溉计划
     */
    @ApiOperation(value = "新增灌溉计划",notes="irrigation:plan:add")
    @RequiresPermissions("irrigation:plan:add")
    @Log(title = "灌溉计划", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationPlanAddParam irrigationPlan)
    {
        return toAjaxR(irrigationPlanService.insertIrrigationPlan(irrigationPlan),"新增");
    }

    /**
     * 修改灌溉计划
     */
    @ApiOperation(value = "修改灌溉计划",notes="irrigation:plan:edit")
    @RequiresPermissions("irrigation:plan:edit")
    @Log(title = "灌溉计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationPlanEditParam irrigationPlan)
    {
        return toAjaxR(irrigationPlanService.updateIrrigationPlan(irrigationPlan),"修改");
    }

    /**
     * 删除灌溉计划
     */
    @ApiOperation(value = "删除灌溉计划",notes="irrigation:plan:remove")
    @RequiresPermissions("irrigation:plan:remove")
    @Log(title = "灌溉计划", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationPlanService.deleteIrrigationPlanByIds(ids),"删除");
    }
}
