package com.jhdr.irrigation.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.irrigation.entity.param.*;
import com.jhdr.irrigation.entity.vo.*;
import com.jhdr.irrigation.service.impl.IrrigationManagementServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "灌区管理")
@RestController
@RequestMapping("/irrigation")
public class IrrigationManagementController extends BaseController {


    @Resource
    private IrrigationManagementServiceImpl irrigationManagementService;

    @ApiOperation(value = "1.灌区信息-概览信息")
    @GetMapping(value = "/base/info")
    public R<IrrigationBaseVo> baseInfo() {
        IrrigationBaseVo irrigationBaseVo = irrigationManagementService.getBaseInfo();
        return R.ok(irrigationBaseVo);
    }

    /**
     * 获取灌区基本信息
     */
    @ApiOperation(value = "2.灌区信息-获取某灌区基本信息 " +
            "GQ341204\t颍泉区\n" +
            "GQ341203\t颍东区\n" +
            "GQ341622\t蒙城县\n" +
            "GQ340406\t潘集区\n" +
            "GQ340321\t怀远县\n" +
            "GQ341623\t利辛县\n" +
            "GQ340421\t凤台县  ")
    @GetMapping(value = "/{irrigationCode}")
    public R<IrrigationRegionVo> getRegionInfo(@PathVariable("irrigationCode") String irrigationCode) {
        IrrigationRegionVo irrigationDistrictInfoVo = irrigationManagementService.getRegionInfo(irrigationCode);
        return R.ok(irrigationDistrictInfoVo);
    }

    /**
     * 灌区感知-泵站实时数据
     */
    @ApiOperation(value = "3.灌区感知-泵站实时数据 " +
            "GQ341204\t颍泉区\n" +
            "GQ341203\t颍东区\n" +
            "GQ341622\t蒙城县\n" +
            "GQ340406\t潘集区\n" +
            "GQ340321\t怀远县\n" +
            "GQ341623\t利辛县\n" +
            "GQ340421\t凤台县  ")
    @GetMapping(value = "/perception/pump/real/{irrigationCode}")
    public R<List<IrrigationPumpRealVo>> getPerceptionPumpReal(@PathVariable("irrigationCode") String irrigationCode) {
        List<IrrigationPumpRealVo> pumpRealVos = irrigationManagementService.getPerceptionPumpReal(irrigationCode);
        return R.ok(pumpRealVos);
    }

    /**
     * 灌区感知-七天天气
     */
    @ApiOperation(value = "4.灌区感知-七天天气 " +
            "GQ341204\t颍泉区\n" +
            "GQ341203\t颍东区\n" +
            "GQ341622\t蒙城县\n" +
            "GQ340406\t潘集区\n" +
            "GQ340321\t怀远县\n" +
            "GQ341623\t利辛县\n" +
            "GQ340421\t凤台县  ")
    @GetMapping(value = "/perception/weather/{irrigationCode}")
    public R<List<IrrigationWeatherDayVo>> getPerceptionWeather(@PathVariable("irrigationCode") String irrigationCode) {
        List<IrrigationWeatherDayVo> weatherDayVos = irrigationManagementService.getPerceptionWeather(irrigationCode);
        return R.ok(weatherDayVos);
    }

    @ApiOperation(value = "5.灌区感知-泵站下拉列表 ")
    @GetMapping(value = "/perception/pump/pull/{irrigationCode}")
    public R<List<IrrigationPumpPullVo>> getPerceptionPumpPull(@PathVariable("irrigationCode") String irrigationCode) {
        List<IrrigationPumpPullVo> pumpRealVos = irrigationManagementService.getPumpList(irrigationCode);
        return R.ok(pumpRealVos);
    }

    @ApiOperation(value = "6.灌区感知-泵站列表分页     默认传这俩分页参数 Integer pageNum  Integer pageSize  ")
    @GetMapping(value = "/perception/pump/tabulation")
    public R<List<IrrigationPumpLineVo>> getPerceptionPumpTabulation(IrrigationPumpLineParam pumpLineParam) {


        List<IrrigationPumpLineVo> pumpLineVos = irrigationManagementService.getPerceptionPumpTabulation(pumpLineParam);

        return R.ok(pumpLineVos);
    }

    @ApiOperation(value = "7.灌区感知-泵站折线水位和流量 ")
    @GetMapping(value = "/perception/pump/line")
    public R<List<IrrigationPumpLineVo>> getPerceptionPumpLine(IrrigationPumpLineParam pumpLineParam) {

        List<IrrigationPumpLineVo> pumpLineVos = irrigationManagementService.getPerceptionPumpLine(pumpLineParam);

        return R.ok(pumpLineVos);
    }

    @ApiOperation(value = "7.灌区感知-泵站流量汇总 ")
    @GetMapping(value = "/perception/pump/all")
    public R<List<IrrigationPumpAllVo>> getPerceptionPumpAll(IrrigationPumpAllParam pumpAllParam) {

        List<IrrigationPumpAllVo> pumpAllVos = irrigationManagementService.getPerceptionPumpAll(pumpAllParam);

        return R.ok(pumpAllVos);
    }

    @ApiOperation(value = "8.灌区感知-泵站统计分析 ")
    @GetMapping(value = "/perception/pump/statistics")
    public R<String> getPerceptionPumpStatistics(IrrigationPumpLineParam pumpLineParam) {

        String statistics = irrigationManagementService.getPerceptionPumpStatistics(pumpLineParam);

        return R.ok(statistics);
    }

    @ApiOperation(value = "9.灌区信息-灌区作物-作物下拉列表 ")
    @GetMapping(value = "/base/crop/types")
    public R<List<IrrigationCropTypeVo>> getBaseCropTypes() {

        List<IrrigationCropTypeVo> cropTypes = irrigationManagementService.getBaseCropTypes();

        return R.ok(cropTypes);
    }

    @ApiOperation(value = "10.灌区信息-灌区作物-作物统计列表 ")
    @GetMapping(value = "/base/crop/statistics/{irrigationCode}")
    public R<List<IrrigationCropStatisticsVo>> getBaseCropStatistics(@PathVariable("irrigationCode") String irrigationCode) {

        List<IrrigationCropStatisticsVo> cropStatistics = irrigationManagementService.getBaseCropStatistics(irrigationCode);

        return R.ok(cropStatistics);
    }

    @ApiOperation(value = "11.灌区信息-灌区作物-泵站统计列表 ")
    @GetMapping(value = "/base/pump/statistics/{irrigationCode}")
    public R<List<IrrigationPumpStatisticsVo>> getBasePumpStatistics(@PathVariable("irrigationCode") String irrigationCode) {

        List<IrrigationPumpStatisticsVo> pumpStatisticsVos = irrigationManagementService.getBasePumpStatistics(irrigationCode);

        return R.ok(pumpStatisticsVos);
    }

    @ApiOperation(value = "12.灌区信息-灌区作物-统计 总灌溉面积 ")
    @GetMapping(value = "/base/crop/irrigated/statistics/{irrigationCode}")
    public R<String> getBaseCropIrrigatedStatistics(@PathVariable("irrigationCode") String irrigationCode) {

        String allIrrigatedArea = irrigationManagementService.getBaseCropIrrigatedStatistics(irrigationCode);

        return R.ok(allIrrigatedArea);
    }

    @ApiOperation(value = "13.需水预测-作物统计需水列表 ")
    @GetMapping(value = "/water/demand/crop/statistics")
    public R<List<IrrigationCropWaterDemandStatisticsVo>> getWaterDemandCropStatistics(IrrigationWaterParam waterParam) {

        List<IrrigationCropWaterDemandStatisticsVo> cropStatistics =
                irrigationManagementService.getWaterDemandCropStatistics(waterParam);

        return R.ok(cropStatistics);
    }

    @ApiOperation(value = "14.需水预测-泵站统计需水列表 ")
    @GetMapping(value = "/water/demand/pump/statistics")
    public R<List<IrrigationPumpWaterDemandStatisticsVo>> getWaterDemandPumpStatistics(IrrigationWaterParam waterParam) {

        List<IrrigationPumpWaterDemandStatisticsVo> pumpStatistics =
                irrigationManagementService.getWaterDemandPumpStatistics(waterParam);

        return R.ok(pumpStatistics);
    }

    @ApiOperation(value = "15.需水预测-弹框 传递灌区编码")
    @GetMapping(value = "/water/demand/popup/{irrigationCode}")
    public R<IrrigationPopupAllVo> getWaterDemandPopup(@PathVariable("irrigationCode") String irrigationCode) {

        IrrigationPopupAllVo popupAllVo = irrigationManagementService.getWaterDemandPopup(irrigationCode);


        return R.ok(popupAllVo);
    }


    @ApiOperation(value = "16.模型计算   初始化模型输入参数 ；参数一致直接返回结果 ；否则返回空，等待下次请求 大概5秒")
    @PostMapping("/model/compute")
    public R<IrrigationModelDataVo> modelCompute(@RequestBody IrrigationPlanModelParam modelParam) {
        return R.ok(irrigationManagementService.modelCompute(modelParam), "模型计算");
    }

    @ApiOperation(value = "17.需水分析-茨淮新河计算 1丰水年 2平水年 3枯水年")
    @PostMapping("/model/year/type")
    public R<IrrigationModelYearTypeVo> modelComputeYear(@RequestBody IrrigationPlanModelYearParam irrigationPlan) {
        return R.ok(irrigationManagementService.modelComputeYear(irrigationPlan), "模型计算");
    }

    @ApiOperation(value = "18.需水分析-灌溉片区计算")
    @PostMapping("/model/area")
    public R<IrrigationModelAreaTypeVo> modelComputeArea(@RequestBody IrrigationPlanModelComputeAreaParam commissionArea) {
        return R.ok(irrigationManagementService.modelComputeArea(commissionArea), "模型计算地区数据");
    }

    @ApiOperation(value = "19.需水分析-水资源调度")
    @PostMapping("/model/dispatch")
    public R<IrrigationModelDispatchVo> modelComputeDispatch(@RequestBody IrrigationPlanModelDispatchParam dispatchParam) {
        return R.ok(irrigationManagementService.modelComputeDispatch(dispatchParam), "水资源调度");
    }


    @ApiOperation(value = "20.需水预测-确定是什么年 1丰水年 2平水年 3枯水年 ")
    @GetMapping(value = "/model/year/get")
    public R<String> modelComputeYearGet() {

        String yearType = irrigationManagementService.modelComputeYearGet();

        return R.ok(yearType);
    }

    @ApiOperation(value = "21.数字孪生仿真调度---方案1 传参1 ；方案2 传参2")
    @GetMapping(value = "/water/digit/twin/{scheme}")
    public R<WaterSchedulePlanSimulationVo> getWaterDemandScheme(@PathVariable("scheme") String scheme) {

        WaterSchedulePlanSimulationVo simulationVo = irrigationManagementService.getWaterDemandScheme(scheme);


        return R.ok(simulationVo);
    }

    @ApiOperation(value = "22.需水分析-获取作物和灌区面积")
    @GetMapping(value = "/crop/irrigated/area")
    public R<WaterCropIrrigatedAreaVo> cropIrrigatedArea() {

        WaterCropIrrigatedAreaVo cropIrrigatedArea = irrigationManagementService.getCropIrrigatedArea();
        return R.ok(cropIrrigatedArea);
    }

    @ApiOperation(value = "23.需水分析-茨淮新河-模型运行")
    @PostMapping("/water/model/run")
    public R waterModelRun(@RequestBody WaterModelRunParam modelRunParam) {
        return R.ok(irrigationManagementService.waterModelRun(modelRunParam), "需水分析模型运行成功");
    }

    @ApiOperation(value = "24.需水分析-茨淮新河-来水预报")
    @GetMapping("/water/inflow/forecast")
    public R<WaterInflowForecastVo> inflowForecast() {

        WaterInflowForecastVo forecast = irrigationManagementService.inflowForecast();

        return R.ok(forecast);
    }


    @ApiOperation(value = "25.需水分析-茨淮新河-作物需水 灌区数据 颍泉YingQuan、颍东YingDong、蒙城MengCheng、潘集PanJi、怀远HuaiYuan、利辛 LiXin、凤台 FengTai")
    @GetMapping("/water/crop/demand/{irrigation}")
    public R<WaterCropIrrigationVo> cropDemand(@PathVariable String irrigation) {

        WaterCropIrrigationVo irrigationVo = irrigationManagementService.cropDemand(irrigation);

        return R.ok(irrigationVo);
    }

    @ApiOperation(value = "26.需水分析-茨淮新河-区内产水 灌区数据 颍泉YingQuan、颍东YingDong、蒙城MengCheng、潘集PanJi、怀远HuaiYuan、利辛 LiXin、凤台 FengTai")
    @GetMapping("/water/area/production/{irrigation}")
    public R<WaterCropIrrigationVo> areaProduction(@PathVariable String irrigation) {

        WaterCropIrrigationVo irrigationVo = irrigationManagementService.areaProduction(irrigation);

        return R.ok(irrigationVo);
    }

    @ApiOperation(value = "27.需水分析-茨淮新河-三生需水 灌区数据 颍泉YingQuan、颍东YingDong、蒙城MengCheng、潘集PanJi、怀远HuaiYuan、利辛 LiXin、凤台 FengTai")
    @GetMapping("/water/three/life/demand/{irrigation}")
    public R<WaterThreeLifeDemandVo> threeLifeDemand(@PathVariable String irrigation) {

        WaterThreeLifeDemandVo irrigationVo = irrigationManagementService.threeLifeDemand(irrigation);

        return R.ok(irrigationVo);
    }


    @ApiOperation(value = "28.供需水分析-供水分析 灌区数据 颍泉YingQuan、颍东YingDong、蒙城MengCheng、潘集PanJi、怀远HuaiYuan、利辛 LiXin、凤台 FengTai")
    @GetMapping("/water/supply/demand/analyse/{irrigation}")
    public R<WaterSupplyDemandAnalyseVo> supplyDemandAnalyse(@PathVariable String irrigation) {

        WaterSupplyDemandAnalyseVo irrigationVo = irrigationManagementService.supplyDemandAnalyse(irrigation);
        return R.ok(irrigationVo);
    }
    @ApiOperation(value = "29.供用水分析-灌溉计划制定 返回结果参考 模型接口文档12行，data数据描述 ")
    @GetMapping("/water/irrigation/plan/develop")
    public R<JSONObject> irrigationPlanDevelop() {

        JSONObject irrigationVo = irrigationManagementService.irrigationPlanDevelop();

        return R.ok(irrigationVo);
    }
    @ApiOperation(value = "30.供用水分析-用水效率管理 ")
    @GetMapping("/water/efficiency/manage")
    public R<List<WaterUseEfficiencyVo>> efficiencyManage(WaterEfficiencyManageParam manageParam) {
        List<WaterUseEfficiencyVo> irrigationVo = irrigationManagementService.efficiencyManage(manageParam);
        return R.ok(irrigationVo);
    }

    @ApiOperation(value = "31.供用水-水资源调度-年灌区需水")
    @GetMapping("/model/year/irrigation/demand")
    public R<WaterYearIrrigationDemandVo> yearIrrigationDemand() {
        return R.ok(irrigationManagementService.yearIrrigationDemand());
    }
    @ApiOperation(value = "32.供用水-水资源调度-年灌区需水点击获取")
    @GetMapping("/model/yue/irrigation/demand")
    public R<WaterYearIrrigationDemandYueVo> yueIrrigationDemand(IrrigationPlanModelDispatchYueParam dispatchParam) {
        return R.ok(irrigationManagementService.yueIrrigationDemand(dispatchParam));
    }

}
