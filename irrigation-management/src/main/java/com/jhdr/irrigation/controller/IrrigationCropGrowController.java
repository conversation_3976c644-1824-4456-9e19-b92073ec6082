package com.jhdr.irrigation.controller;

import java.util.List;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jhdr.common.log.annotation.Log;
import com.jhdr.common.log.enums.BusinessType;
import com.jhdr.common.security.annotation.RequiresPermissions;
import com.jhdr.irrigation.entity.param.IrrigationCropGrowParam;
import com.jhdr.irrigation.entity.param.IrrigationCropGrowAddParam;
import com.jhdr.irrigation.entity.param.IrrigationCropGrowEditParam;
import com.jhdr.irrigation.entity.vo.IrrigationCropGrowVo;
import com.jhdr.irrigation.service.IIrrigationCropGrowService;
import com.jhdr.common.core.web.controller.BaseController;
import com.jhdr.common.core.domain.R;
import com.jhdr.common.core.web.page.TableDataInfo;

/**
 * 灌区作物生长期
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Api(tags = "供用水管理-作物信息管理")
@RestController
@RequestMapping("/grow")
public class IrrigationCropGrowController extends BaseController
{
    @Autowired
    private IIrrigationCropGrowService irrigationCropGrowService;

    /**
     * 查询灌区作物生长期列表
     */
    @ApiOperation(value = "查询灌区作物生长期列表",notes="irrigation:grow:list")
    @RequiresPermissions("irrigation:grow:list")
    @GetMapping("/list")
    public TableDataInfo<List<IrrigationCropGrowVo>> list(IrrigationCropGrowParam irrigationCropGrowParam)
    {
        startPage();
        List<IrrigationCropGrowVo> list = irrigationCropGrowService.selectIrrigationCropGrowList(irrigationCropGrowParam);
        return getDataTable(list);
    }


    /**
     * 获取灌区作物生长期详细信息
     */
    @ApiOperation(value = "获取灌区作物生长期详细信息",notes="irrigation:grow:query")
    @RequiresPermissions("irrigation:grow:query")
    @GetMapping(value = "/{id}")
    public R<IrrigationCropGrowVo> getInfo(@PathVariable("id") Long id)
    {

        return R.ok(irrigationCropGrowService.selectIrrigationCropGrowById(id));
    }

    /**
     * 新增灌区作物生长期
     */
    @ApiOperation(value = "新增灌区作物生长期",notes="irrigation:grow:add")
    @RequiresPermissions("irrigation:grow:add")
    @Log(title = "灌区作物生长期", businessType = BusinessType.INSERT)
    @PostMapping
    public R add(@RequestBody IrrigationCropGrowAddParam irrigationCropGrow)
    {
        return toAjaxR(irrigationCropGrowService.insertIrrigationCropGrow(irrigationCropGrow),"新增");
    }

    /**
     * 修改灌区作物生长期
     */
    @ApiOperation(value = "修改灌区作物生长期",notes="irrigation:grow:edit")
    @RequiresPermissions("irrigation:grow:edit")
    @Log(title = "灌区作物生长期", businessType = BusinessType.UPDATE)
    @PutMapping
    public R edit(@RequestBody IrrigationCropGrowEditParam irrigationCropGrow)
    {
        return toAjaxR(irrigationCropGrowService.updateIrrigationCropGrow(irrigationCropGrow),"修改");
    }

    /**
     * 删除灌区作物生长期
     */
    @ApiOperation(value = "删除灌区作物生长期",notes="irrigation:grow:remove")
    @RequiresPermissions("irrigation:grow:remove")
    @Log(title = "灌区作物生长期", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R remove(@PathVariable Long[] ids)
    {
        return toAjaxR(irrigationCropGrowService.deleteIrrigationCropGrowByIds(ids),"删除");
    }
}
