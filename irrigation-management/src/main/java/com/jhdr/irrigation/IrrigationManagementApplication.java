package com.jhdr.irrigation;

import com.jhdr.common.security.annotation.EnableCustomConfig;
import com.jhdr.common.security.annotation.EnableRyFeignClients;
import com.jhdr.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 灌区管理模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
//本地调试时候切换这个，取消权限校验 todo
//@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, PreAuthorizeAspect.class})
public class IrrigationManagementApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(IrrigationManagementApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  灌区管理启动成功   ლ(´ڡ`ლ)ﾞ  \n" );
    }
}
