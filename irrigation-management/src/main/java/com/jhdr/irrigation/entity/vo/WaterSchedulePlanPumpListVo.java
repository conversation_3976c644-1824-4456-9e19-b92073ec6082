package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "调度")
@Accessors(chain = true)
@JsonInclude()
public class WaterSchedulePlanPumpListVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "颍泉区 泵站调度")
    private List<WaterSchedulePumpWaterVo> yingQuanPumpList;
    @ApiModelProperty(value = "颍东区 泵站调度")
    private List<WaterSchedulePumpWaterVo> yingDongPumpList;
    @ApiModelProperty(value = "蒙城县 泵站调度")
    private List<WaterSchedulePumpWaterVo> mengChengPumpList;
    @ApiModelProperty(value = "潘集区 泵站调度")
    private List<WaterSchedulePumpWaterVo> panJiPumpList;
    @ApiModelProperty(value = "怀远县 泵站调度")
    private List<WaterSchedulePumpWaterVo> huaiYuanPumpList;
    @ApiModelProperty(value = "利辛县 泵站调度")
    private List<WaterSchedulePumpWaterVo> liXinPumpList;
    @ApiModelProperty(value = "凤台区 泵站调度")
    private List<WaterSchedulePumpWaterVo> fengTaiPumpList;





}
