package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 灌区作物生长期对象 irrigation_crop_grow
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "泵站月统计汇总")
@Accessors(chain = true)
@JsonInclude()
public class StStationWaterMonthPumpVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点名称")
    private String stationName;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划")
    private String addvcdName;

    @ApiModelProperty(value = "年度")
    private String stationYear;

    @ApiModelProperty(value = "月份")
    private List<StStationMonthExtractionVo> monthExtraction;

    @ApiModelProperty(value = "抗旱抽水量万m³")
    private BigDecimal droughtExtraction;

    @ApiModelProperty(value = "排涝抽水量万m³")
    private BigDecimal floodExtraction;

    @ApiModelProperty(value = "总抽水量")
    private BigDecimal allExtraction;


}
