package com.jhdr.irrigation.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 灌区配置值表
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@TableName("chxh.irrigation_config_value")
public class IrrigationConfigValuePo {
    
    /**
     * 配置值ID
     */
    @TableId
    private Long id;
    
    /**
     * 配置类型ID
     */
    @TableField("config_type_id")
    private Long configTypeId;
    
    /**
     * 配置编码
     */
    @TableField("config_code")
    private String configCode;
    
    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;
    
    /**
     * 灌区编码
     */
    @TableField("irrigation_code")
    private String irrigationCode;
    
    /**
     * 灌区名称
     */
    @TableField("irrigation_name")
    private String irrigationName;
    
    /**
     * 是否启用(1:启用 0:禁用)
     */
    @TableField("is_enabled")
    private Integer isEnabled;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
