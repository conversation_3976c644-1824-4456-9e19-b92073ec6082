package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "茨淮新河 -三生需水")
@Accessors(chain = true)
@JsonInclude()
public class WaterThreeLifeDemandVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间")
    private List<String> time;

    @ApiModelProperty(value = "生产 水量 万m³")
    private List<String> production;

    @ApiModelProperty(value = "生活 水量 万m³")
    private List<String> domestic;

    @ApiModelProperty(value = "生态 水量 万m³")
    private List<String> ecological;


}
