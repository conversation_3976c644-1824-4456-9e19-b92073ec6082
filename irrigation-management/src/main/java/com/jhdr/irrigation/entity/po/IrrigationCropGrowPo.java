package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区作物生长期对象 irrigation_crop_grow
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "灌区作物生长期")
@Accessors(chain = true)
@TableName(value ="irrigation_crop_grow")
public class IrrigationCropGrowPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 灌区周期管理id */
    @ApiModelProperty(value = "灌区周期管理id")
    @TableId
    private Long id;

    /** 作物id */
    @ApiModelProperty(value = "作物id")
    @Excel(name = "作物id")
    private Long cropId;

    /** 作物名称 */
    @ApiModelProperty(value = "作物名称")
    @Excel(name = "作物名称")
    private String cropName;

    /** 种植面积 */
    @ApiModelProperty(value = "种植面积")
    @Excel(name = "种植面积")
    private BigDecimal plantArea;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String irrigationName;

    /** 生长期（天） */
    @ApiModelProperty(value = "生长期（天）")
    @Excel(name = "生长期", readConverterExp = "天=")
    private String growthDay;

}
