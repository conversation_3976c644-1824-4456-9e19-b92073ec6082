package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 水情年设置参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "水情年设置参数")
public class WaterCoefficientConfigParam {
    
    @ApiModelProperty(value = "丰水年需水系数", required = true)
    @NotBlank(message = "丰水年需水系数不能为空")
    private String waterCoefficientWet;
    
    @ApiModelProperty(value = "平水年需水系数", required = true)
    @NotBlank(message = "平水年需水系数不能为空")
    private String waterCoefficientNormal;
    
    @ApiModelProperty(value = "枯水年需水系数", required = true)
    @NotBlank(message = "枯水年需水系数不能为空")
    private String waterCoefficientDry;
}
