package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区作物管理对象 irrigation_crop_manage
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "灌区作物管理")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationCropManageVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 灌区作物管理id */
    @ApiModelProperty(value = "灌区作物管理id")
    @TableId
    private Long id;

    /** 作物id */
    @ApiModelProperty(value = "作物id")
    @Excel(name = "作物id")
    private Long cropId;

    /** 作物名称 */
    @ApiModelProperty(value = "作物名称")
    @Excel(name = "作物名称")
    private String cropName;

    /** 种植面积 */
    @ApiModelProperty(value = "种植面积")
    @Excel(name = "种植面积")
    private BigDecimal plantArea;

    /** 泵站编码 */
    @ApiModelProperty(value = "泵站编码")
    @Excel(name = "泵站编码")
    private String pumpCode;

    /** 泵站名称 */
    @ApiModelProperty(value = "泵站名称")
    @Excel(name = "泵站名称")
    private String pumpName;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String irrigationName;

}
