package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 *  调度方案水位信息
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "调度方案水位")
@Accessors(chain = true)
@JsonInclude()
public class WaterSchedulePlanLineNowListVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "茨河铺至插花")//取插花上水位
    private List<WaterSchedulePlanLineVo> chaHuaLineList;
    @ApiModelProperty(value = "插花至阚疃")//取阚疃上水位
    private List<WaterSchedulePlanLineVo> kanTuanLineList;
    @ApiModelProperty(value = "阚疃至上桥")//取上桥上水位
    private List<WaterSchedulePlanLineVo> shangQiaoLineList;


    @ApiModelProperty(value = "茨河铺至插花")//取插花上水位
    private List<WaterSchedulePlanLineVo> chaHuaLineNextList;
    @ApiModelProperty(value = "插花至阚疃")//取阚疃上水位
    private List<WaterSchedulePlanLineVo> kanTuanLineNextList;
    @ApiModelProperty(value = "阚疃至上桥")//取上桥上水位
    private List<WaterSchedulePlanLineVo> shangQiaoLineNextList;




}
