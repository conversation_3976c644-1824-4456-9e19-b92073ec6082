package com.jhdr.irrigation.entity.vo;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌区基础设施概况对象
 *
 * <AUTHOR>
 * @date 2024/06/24
 */
@Data
@ApiModel(description = "灌区基础设施概况")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationFacilitiesOverviewVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "河流数量")
    private Long riverCount;

    @ApiModelProperty(value = "干渠数量")
    private Long trunkCanalCount;

    @ApiModelProperty(value = "支渠数量")
    private Long branchCanalCount;

    @ApiModelProperty(value = "枢纽数量")
    private Long hubCount;

    @ApiModelProperty(value = "灌区数量")
    private Long irrigationAreaCount;

    @ApiModelProperty(value = "泵站数量")
    private Long pumpStationCount;

    @ApiModelProperty(value = "涵闸数量")
    private Long sluiceGateCount;

    @ApiModelProperty(value = "水位站数量")
    private Long waterLevelStationCount;

    @ApiModelProperty(value = "流量站数量")
    private Long flowStationCount;

    @ApiModelProperty(value = "雨量站数量")
    private Long rainfallStationCount;

    @ApiModelProperty(value = "视频站数量")
    private Long videoStationCount;

    @ApiModelProperty(value = "取水口数量")
    private Long intakeCount;
}
