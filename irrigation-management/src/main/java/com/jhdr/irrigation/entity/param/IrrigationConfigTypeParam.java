package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区配置类型查询参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "灌区配置类型查询参数")
public class IrrigationConfigTypeParam {
    
    @ApiModelProperty(value = "配置名称")
    private String configName;
    
    @ApiModelProperty(value = "所属模型类型")
    private String modelType;
    
    @ApiModelProperty(value = "是否启用(1:启用 0:禁用)")
    private Integer isEnabled;
}
