package com.jhdr.irrigation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌区基本信息对象 irrigation_district_info
 *
 * <AUTHOR>
 * @date 2024-06-24
 */

@Data
@ApiModel(description = "灌区汇总信息")
public class IrrigationDistrictSummaryVo
{


    /** 灌区数量 */
    @ApiModelProperty(value = "灌区数量")
    private String num;

    /** 设计灌溉面积 */
    @ApiModelProperty(value = "设计灌溉面积")
    private String designedIrrigationArea;

}
