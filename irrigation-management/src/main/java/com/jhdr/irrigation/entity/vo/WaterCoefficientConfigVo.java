package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水情年设置视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "水情年设置视图对象")
public class WaterCoefficientConfigVo {
    
    @ApiModelProperty(value = "丰水年需水系数")
    private String waterCoefficientWet;
    
    @ApiModelProperty(value = "平水年需水系数")
    private String waterCoefficientNormal;
    
    @ApiModelProperty(value = "枯水年需水系数")
    private String waterCoefficientDry;
}
