package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "用水效率返回")
@Accessors(chain = true)
@JsonInclude()
public class WaterUseEfficiencyVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "灌区 例如 YingQuan ")
    private String irrigation;

    @ApiModelProperty(value = "作物需水量（单位：立方米）")
    private String crop_water_demand;

    @ApiModelProperty(value = "作物实际用水量（单位：立方米）")
    private String actual_crop_water_usage;

    @ApiModelProperty(value = "用水效率（单位：百分比）")
    private String efficiency;

    @ApiModelProperty(value = "备注")
    private String remarks;



}
