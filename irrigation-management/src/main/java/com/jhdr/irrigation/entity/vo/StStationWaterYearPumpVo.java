package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 灌区作物生长期对象 irrigation_crop_grow
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "泵站年统计汇总")
@Accessors(chain = true)
@JsonInclude()
public class StStationWaterYearPumpVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点名称")
    private String stationName;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划")
    private String addvcdName;

    @ApiModelProperty(value = "年份")
    private List<StStationYearExtractionVo> yearExtraction;

    @ApiModelProperty(value = "抗旱抽水量万m³")
    private BigDecimal droughtExtraction;

    @ApiModelProperty(value = "排涝抽水量万m³")
    private BigDecimal floodExtraction;

    @ApiModelProperty(value = "总抽水量")
    private BigDecimal allExtraction;

}
