package com.jhdr.irrigation.entity.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区管理-调度方案水位历史数据对象 water_schedule_plan_level
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "灌区管理-调度方案水位历史数据")
@Accessors(chain = true)
@TableName(value ="water_schedule_plan_level")
public class WaterSchedulePlanLevelPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 历史方案泵站数据id */
    @ApiModelProperty(value = "历史方案泵站数据id")
    @TableId
    private Long pumpId;

    /** 调度方案id */
    @ApiModelProperty(value = "调度方案id")
    @Excel(name = "调度方案id")
    private Long planId;

    /** 闸站编码 */
    @ApiModelProperty(value = "闸站编码")
    @Excel(name = "闸站编码")
    private String gateStationCode;

    /** 水位 */
    @ApiModelProperty(value = "水位")
    @Excel(name = "水位")
    private String realWater;

    /** 时间 */
    @ApiModelProperty(value = "时间")
    @Excel(name = "时间")
    private String realTime;

}
