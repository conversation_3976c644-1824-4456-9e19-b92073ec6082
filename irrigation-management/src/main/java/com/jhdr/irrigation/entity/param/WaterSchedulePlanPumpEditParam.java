package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区管理-调度方案泵站历史数据对象 water_schedule_plan_pump
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "灌区管理-调度方案泵站历史数据")
@Accessors(chain = true)
public class WaterSchedulePlanPumpEditParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 历史方案水位id */
    @ApiModelProperty(value = "历史方案水位id")
    @TableId
    private Long levelId;

    /** 调度方案id */
    @ApiModelProperty(value = "调度方案id")
    @Excel(name = "调度方案id")
    private Long planId;

    /** 泵站编码 */
    @ApiModelProperty(value = "泵站编码")
    @Excel(name = "泵站编码")
    private String pumpCode;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 泵站名称 */
    @ApiModelProperty(value = "泵站名称")
    @Excel(name = "泵站名称")
    private String pumpName;

    /** 抽水量 */
    @ApiModelProperty(value = "抽水量")
    @Excel(name = "抽水量")
    private BigDecimal waterDemand;

    /** 开机时长 */
    @ApiModelProperty(value = "开机时长")
    @Excel(name = "开机时长")
    private BigDecimal runTime;

}
