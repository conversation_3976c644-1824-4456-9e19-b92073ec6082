package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 灌区配置类型编辑参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "灌区配置类型编辑参数")
public class IrrigationConfigTypeEditParam {
    
    @ApiModelProperty(value = "配置类型ID", required = true)
    @NotNull(message = "配置类型ID不能为空")
    private Long id;
    
    @ApiModelProperty(value = "配置名称", required = true)
    @NotBlank(message = "配置名称不能为空")
    private String configName;
    
    @ApiModelProperty(value = "所属模型类型", required = true)
    @NotBlank(message = "所属模型类型不能为空")
    private String modelType;
    
    @ApiModelProperty(value = "配置说明")
    private String description;
    
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;
    
    @ApiModelProperty(value = "是否启用(1:启用 0:禁用)", required = true)
    @NotNull(message = "是否启用不能为空")
    private Integer isEnabled;
    
    @ApiModelProperty(value = "备注")
    private String remark;
}
