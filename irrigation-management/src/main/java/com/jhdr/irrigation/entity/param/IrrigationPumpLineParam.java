package com.jhdr.irrigation.entity.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌区感知-泵站水位和流量折线
 *
 * <AUTHOR>
 * @date 2024-08-07
 */

@Data
@ApiModel(description = "灌区感知-泵站水位和流量折线")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationPumpLineParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "灌区编码")
    private String irrigationCode; // 灌区编码
    @ApiModelProperty(value = "灌区编码 多数据,分割",hidden = true)
    private  String[] pumpCodes;
    @ApiModelProperty(value = "地区",hidden = true)
    private String addvcd; // 地区行政编码
    @ApiModelProperty(value = "泵站编码 多数据,分割")
    private String pumpCode; // 泵站编码
    @ApiModelProperty(value = "开始时间 例如：2024-08-04")
    private String startTime; // 开始时间
    @ApiModelProperty(value = "结束时间 例如：2024-08-07")
    private String endTime; // 结束时间



}
