package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 灌区管理-基本信息
 *
 * <AUTHOR>
 * @date 2024-08-06
 */

@Data
@ApiModel(description = "灌区管理-基本信息")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationBaseVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "灌区面积")
    private BigDecimal irrigationArea; // 灌区面积
    @ApiModelProperty(value = "灌区田亩")
    private BigDecimal fieldAcres; // 灌区田亩
    @ApiModelProperty(value = "建立时间")
    private String establishmentDate; // 成立时间
    @ApiModelProperty(value = "管理单位")
    private String managementUnit; // 管理单位
    @ApiModelProperty(value = "灌区范围")
    private String irrigationZoneScope; // 灌区范围
    @ApiModelProperty(value = "总干渠")
    private String mainCanal; // 总干渠
    @ApiModelProperty(value = "水系渠系")
    private String waterSystemChannels; // 水系渠系
    @ApiModelProperty(value = "主要作物种类")
    private String mainCropTypes; // 主要作物种类
    @ApiModelProperty(value = "灌溉期")
    private String irrigationPeriod; // 灌溉期
    @ApiModelProperty(value = "主要灌溉方式")
    private String mainIrrigationMethods; // 主要灌溉方式
    @ApiModelProperty(value = "灌区简介")
    private String zoneDescription; // 灌区简介


}
