package com.jhdr.irrigation.entity.param;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌区管理-调度方案对象 water_schedule_plan
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "灌区管理-调度方案生成")
@Accessors(chain = true)
public class WaterSchedulePlanGenerateParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 天气参数 1七天气象预报 2历史同期气象数据 */
    @ApiModelProperty(value = "天气参数 1七天气象预报 2历史同期气象数据")
    @Excel(name = "天气参数 1七天气象预报 2历史同期气象数据")
    private String weatherParameter;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间")
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间")
    @Excel(name = "结束时间")
    private String endTime;

    private String addvcd;

}
