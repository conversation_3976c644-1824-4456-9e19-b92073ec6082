package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "调度")
@Accessors(chain = true)
@JsonInclude()
public class WaterCropIrrigatedAreaVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "作物")
    private String crop;

    @ApiModelProperty(value = "灌区名称 和 面积")
    private List<WaterCropManageVo> waterCropManages;


}
