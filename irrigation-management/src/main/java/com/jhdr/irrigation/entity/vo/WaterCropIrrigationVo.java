package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "来水预报结果")
@Accessors(chain = true)
@JsonInclude()
public class WaterCropIrrigationVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "时间")
    private List<String> time;

    @ApiModelProperty(value = "水量 万m³")
    private List<String> value;


}
