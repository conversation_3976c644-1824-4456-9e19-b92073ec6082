package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 可供水量分析结果
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@ApiModel(description = "可供水量分析结果")
@Accessors(chain = true)
@JsonInclude()
public class AvailableWaterVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期列表")
    private List<String> dateList;

    @ApiModelProperty(value = "阚疃闸到上桥净流入流量(万m³)")
    private List<Double> netInflowVolume;

    @ApiModelProperty(value = "片区产水量(万m³)")
    private List<Double> regionWaterProduction;

    @ApiModelProperty(value = "可供水量(万m³)")
    private List<Double> availableWaterVolume;

    @ApiModelProperty(value = "累计可供水量(万m³)")
    private List<Double> accumulatedAvailableWaterVolume;
}
