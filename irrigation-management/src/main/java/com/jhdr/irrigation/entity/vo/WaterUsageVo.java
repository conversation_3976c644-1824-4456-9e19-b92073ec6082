package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 生活用水视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "生活用水视图对象")
public class WaterUsageVo {
    
    @ApiModelProperty(value = "年份")
    private String year;
    
    @ApiModelProperty(value = "用水量(m³)")
    private String waterUsage;
}
