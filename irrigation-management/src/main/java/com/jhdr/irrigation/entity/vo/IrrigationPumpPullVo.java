package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌区感知-泵站下拉列表
 *
 * <AUTHOR>
 * @date 2024-08-07
 */

@Data
@ApiModel(description = "灌区感知-泵站下拉列表")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationPumpPullVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "泵站编码")
    private String pumpCode; // 泵站编码
    @ApiModelProperty(value = "泵站名称")
    private String pumpName; // 泵站名称

    @ApiModelProperty(value = "泵站设计灌溉面积",hidden = true)
    private BigDecimal deviseIrr;
}
