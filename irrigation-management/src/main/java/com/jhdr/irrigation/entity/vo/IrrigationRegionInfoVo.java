package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区区域信息对象 irrigation_region_info
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "灌区区域信息")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationRegionInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 灌区区域信息ID */
    @ApiModelProperty(value = "灌区区域信息ID")
    @TableId
    private Long id;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String irrigationName;

    /** 干渠数量 */
    @ApiModelProperty(value = "干渠数量")
    @Excel(name = "干渠数量")
    private String mainCanalNum;

    /** 支渠数量 */
    @ApiModelProperty(value = "支渠数量")
    @Excel(name = "支渠数量")
    private String branchCanalNum;

    /** 泵站数量 */
    @ApiModelProperty(value = "泵站数量")
    @Excel(name = "泵站数量")
    private String pumpStationNum;

    /** 涵闸数量 */
    @ApiModelProperty(value = "涵闸数量")
    @Excel(name = "涵闸数量")
    private String gateStationNum;

    /** 水塘 */
    @ApiModelProperty(value = "水塘")
    @Excel(name = "水塘")
    private String pond;

    /** 水库 */
    @ApiModelProperty(value = "水库")
    @Excel(name = "水库")
    private String reservoir;

    /** 水源 */
    @ApiModelProperty(value = "水源")
    @Excel(name = "水源")
    private String waterSource;

}
