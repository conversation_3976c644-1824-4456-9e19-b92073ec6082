package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "模型计算输入")
@Accessors(chain = true)
public class IrrigationPlanModelDispatchParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "月份 1-12月 默认1月")
    private Integer yue;

    @ApiModelProperty(value = "茨河铺至插花水位")//取插花上水位
    private double chaHuaWaterLine;

    @ApiModelProperty(value = "茨河铺至插花最低通航水位")//取插花上水位
    private double chaHuaWaterWarnLine;

//    @ApiModelProperty(value = "茨河铺至插花其他用水(m³)：工业用水、生活用水、等")
//    private double chaHuaWaterOtherWater;

    @ApiModelProperty(value = "插花至阚疃水位")//取阚疃上水位
    private double kanTuanWaterLine;

    @ApiModelProperty(value = "插花至阚疃最低通航水位")//取阚疃上水位
    private double kanTuanWaterWarnLine;

//    @ApiModelProperty(value = "插花至阚疃 其他用水(m³)：工业用水、生活用水、等")
//    private double kanTuanWaterOtherWater;

    @ApiModelProperty(value = "阚疃至上桥水位")//取上桥上水位
    private double shangQiaoWaterLine;

    @ApiModelProperty(value = "阚疃至上桥最低通航水位")//取上桥上水位
    private double shangQiaoWaterWarnLine;

//    @ApiModelProperty(value = "阚疃至上桥 其他用水(m³)：工业用水、生活用水、等")
//    private double shangQiaoWaterOtherWater;


}
