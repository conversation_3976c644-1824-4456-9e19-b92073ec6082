package com.jhdr.irrigation.entity.vo;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌区管理-调度方案对象 water_schedule_plan
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "灌区管理-调度方案")
@Accessors(chain = true)
public class WaterSchedulePlanInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 方案名称 */
    @ApiModelProperty(value = "方案名称")
    private String planName;

    /** 方案历史水位信息 */
    @ApiModelProperty(value = "方案历史水位信息")
    private WaterSchedulePlanLineListVo planLineList;

    /** 方案历史泵站信息 */
    @ApiModelProperty(value = "方案历史泵站信息")
    private WaterSchedulePlanPumpListVo planPumpList;

}
