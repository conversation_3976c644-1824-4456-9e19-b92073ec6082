package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 年统计分析查询
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "年统计分析查询")
@Accessors(chain = true)
public class StStationWaterYearParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划编码")
    private String addvcd;

    @ApiModelProperty(value = "年份开始")
    private String yearStart;

    @ApiModelProperty(value = "年份结束")
    private String yearEnd;

    @ApiModelProperty(value = "站点名称")
    private String stationName;

}
