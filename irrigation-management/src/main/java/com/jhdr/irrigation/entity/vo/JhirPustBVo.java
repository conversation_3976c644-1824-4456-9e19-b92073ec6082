package com.jhdr.irrigation.entity.vo;

import java.math.BigDecimal;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 泵站基本信息对象 jhir_pust_b
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "泵站基本信息")
@Accessors(chain = true)
@JsonInclude()
public class JhirPustBVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 建筑物编码 */
    @ApiModelProperty(value = "泵站编码")
    @TableId
    private String strCode;

    /** 测站名称 */
    @ApiModelProperty(value = "泵站名称")
    private String strName;

    /** 水泵数量 */
    @ApiModelProperty(value = "机组数量")
    private Long pumpNum;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划编码")
    private String addvcd;

    /** 行政区划 */
    @ApiModelProperty(value = "行政区划名称")
    private String addvcdName;

    /** 装机功率 */
    @ApiModelProperty(value = "装机功率")
    @Excel(name = "装机功率")
    private BigDecimal insPow;




}
