package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 需水预测-弹框信息
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "模型数据返回结果")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationModelDataVo implements Serializable
{

    @ApiModelProperty(value = "典型年份确定")
    private List<IrrigationModelDataYearVo> typicalYear;

    @ApiModelProperty(value = "典型年份下灌溉需水量定")
    private List<IrrigationModelDataWaterVo> waterDemand;

    @ApiModelProperty(value = "降雨量图片base64格式")
    private String rainImg;


}
