package com.jhdr.irrigation.entity.vo;

/**
 * <AUTHOR>
 * @description: 模型返回蓄水量数据
 * @date 2024/10/20 13:52
 */
public class WaterModelData {

        //丰水年
        private DataWater wet;
        //平水年
        private DataWater normal;
        //枯水年
        private DataWater dry;

    public DataWater getWet() {
        return wet;
    }

    public void setWet(DataWater wet) {
        this.wet = wet;
    }

    public DataWater getNormal() {
        return normal;
    }

    public void setNormal(DataWater normal) {
        this.normal = normal;
    }

    public DataWater getDry() {
        return dry;
    }

    public void setDry(DataWater dry) {
        this.dry = dry;
    }

    public static class DataWater {
            private String value;
            private String year;

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getYear() {
            return year;
        }

        public void setYear(String year) {
            this.year = year;
        }
    }


}
