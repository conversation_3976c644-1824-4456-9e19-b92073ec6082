package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 水资源配置优先级视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "水资源配置优先级视图对象")
public class WaterPriorityConfigVo {
    
    @ApiModelProperty(value = "用水单元一级保灌优先级")
    private String waterUnitFirstLevel;
    
    @ApiModelProperty(value = "用水单元一级保灌优先级百分比")
    private String waterUnitFirstLevelPer;
    
    @ApiModelProperty(value = "用水单元二级保灌优先级")
    private String waterUnitSecondLevel;
    
    @ApiModelProperty(value = "用水单元二级保灌优先级百分比")
    private String waterUnitSecondLevelPer;
    
    @ApiModelProperty(value = "用水片区一级保灌优先级")
    private String waterAreaFirstLevel;
    
    @ApiModelProperty(value = "用水片区二级保灌优先级")
    private String waterAreaSecondLevel;
}
