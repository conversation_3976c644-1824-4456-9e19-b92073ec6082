package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 调度泵站视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调度泵站视图对象")
public class PumpStationVo {
    
    @ApiModelProperty(value = "泵机编号")
    private String pumpNumber;
    
    @ApiModelProperty(value = "瞬时流量(m³/s)")
    private String flowRate;
    
    @ApiModelProperty(value = "是否启用(1:启用 0:禁用)")
    private Integer isEnabled;
}
