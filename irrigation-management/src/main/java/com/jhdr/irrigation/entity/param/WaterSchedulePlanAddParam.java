package com.jhdr.irrigation.entity.param;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.jhdr.common.core.annotation.Excel;
import com.jhdr.irrigation.entity.vo.WaterSchedulePlanLineListVo;
import com.jhdr.irrigation.entity.vo.WaterSchedulePlanPumpListVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区管理-调度方案对象 water_schedule_plan
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "灌区管理-调度方案")
@Accessors(chain = true)
public class WaterSchedulePlanAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 方案名称 */
    @ApiModelProperty(value = "方案名称")
    private String planName;

    /** 方案历史水位信息 */
    @ApiModelProperty(value = "方案历史水位信息")
    private WaterSchedulePlanLineListVo planLineList;

    /** 方案历史泵站信息 */
    @ApiModelProperty(value = "方案历史泵站信息")
    private WaterSchedulePlanPumpListVo planPumpList;

    /** 天气参数 1七天气象预报 2历史同期气象数据 */
    @ApiModelProperty(value = "天气参数 1七天气象预报 2历史同期气象数据")
    @Excel(name = "天气参数 1七天气象预报 2历史同期气象数据")
    private String weatherParameter;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间")
    @Excel(name = "开始时间")
    private String startTime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间")
    @Excel(name = "结束时间")
    private String endTime;

}
