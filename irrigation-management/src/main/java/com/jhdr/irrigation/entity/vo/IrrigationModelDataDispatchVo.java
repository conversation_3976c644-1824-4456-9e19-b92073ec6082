package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 需水预测-弹框信息
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "模型数据返回结果")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationModelDataDispatchVo implements Serializable
{

    @ApiModelProperty(value = "实时水位")
    private double realLine;

    @ApiModelProperty(value = "最低通航水位")
    private double wareLine;

    @ApiModelProperty(value = "预测水位")
    private double futureLine;


}
