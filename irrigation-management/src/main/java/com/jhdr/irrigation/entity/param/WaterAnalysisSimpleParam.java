package com.jhdr.irrigation.entity.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 水情分析简化输入参数
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@ApiModel(description = "水情分析简化输入参数")
@Accessors(chain = true)
public class WaterAnalysisSimpleParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始日期不能为空")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束日期不能为空")
    private Date endDate;

    @ApiModelProperty(value = "怀远县灌区降雨量(mm)，按日期顺序排列")
    @Size(min = 7, max = 7, message = "怀远县灌区降雨量必须为7天数据")
    @NotNull(message = "怀远县灌区降雨量不能为空")
    private List<Double> huaiyuanRainfall;

    @ApiModelProperty(value = "潘集区灌区降雨量(mm)，按日期顺序排列")
    @Size(min = 7, max = 7, message = "潘集区灌区降雨量必须为7天数据")
    @NotNull(message = "潘集区灌区降雨量不能为空")
    private List<Double> panjiRainfall;

    @ApiModelProperty(value = "凤台县灌区降雨量(mm)，按日期顺序排列")
    @Size(min = 7, max = 7, message = "凤台县灌区降雨量必须为7天数据")
    @NotNull(message = "凤台县灌区降雨量不能为空")
    private List<Double> fengtaiRainfall;

    @ApiModelProperty(value = "蒙城县灌区降雨量(mm)，按日期顺序排列")
    @Size(min = 7, max = 7, message = "蒙城县灌区降雨量必须为7天数据")
    @NotNull(message = "蒙城县灌区降雨量不能为空")
    private List<Double> mengchengRainfall;

    @ApiModelProperty(value = "阚疃闸下当前水位(m)")
    @NotNull(message = "阚疃闸下当前水位不能为空")
    private Double kantunDownWaterLevel;

    @ApiModelProperty(value = "上桥闸上当前水位(m)")
    @NotNull(message = "上桥闸上当前水位不能为空")
    private Double shangqiaoUpWaterLevel;

    @ApiModelProperty(value = "阚疃闸当前过闸流量(m³/s)")
    @NotNull(message = "阚疃闸当前过闸流量不能为空")
    private Double kantunGateFlow;

    @ApiModelProperty(value = "上桥闸当前过闸流量(m³/s)")
    @NotNull(message = "上桥闸当前过闸流量不能为空")
    private Double shangqiaoGateFlow;
}
