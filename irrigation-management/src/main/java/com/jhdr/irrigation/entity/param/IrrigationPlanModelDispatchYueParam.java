package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "月度需水量")
@Accessors(chain = true)
public class IrrigationPlanModelDispatchYueParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "月份 1-12月 默认1月")
    private Integer yue;

    @ApiModelProperty(value = "灌区 例如 YingQuan")
    private String irrigation;


}
