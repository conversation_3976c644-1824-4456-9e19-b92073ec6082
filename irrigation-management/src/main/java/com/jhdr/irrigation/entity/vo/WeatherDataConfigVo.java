package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 气象数据配置视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "气象数据配置视图对象")
public class WeatherDataConfigVo {
    
    @ApiModelProperty(value = "预报天数")
    private Integer forecastDays;
    
    @ApiModelProperty(value = "历史平均降雨量")
    private String rainfallHistory;
    
    @ApiModelProperty(value = "径流深度")
    private String flowRate;
}
