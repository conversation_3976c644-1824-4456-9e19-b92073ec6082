package com.jhdr.irrigation.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 灌区配置类型表
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@TableName("chxh.irrigation_config_type")
public class IrrigationConfigTypePo {
    
    /**
     * 配置类型ID
     */
    @TableId
    private Long id;
    
    /**
     * 配置名称
     */
    @TableField("config_name")
    private String configName;
    
    /**
     * 所属模型类型
     */
    @TableField("model_type")
    private String modelType;
    
    /**
     * 配置说明
     */
    @TableField("description")
    private String description;
    
    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;
    
    /**
     * 是否启用(1:启用 0:禁用)
     */
    @TableField("is_enabled")
    private Integer isEnabled;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;
    
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
