package com.jhdr.irrigation.entity.param;

import java.math.BigDecimal;
import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区作物周期对象 irrigation_crop_cycle
 *
 * <AUTHOR>
 * @date 2024-09-04
 */

@Data
@ApiModel(description = "灌区作物周期")
@Accessors(chain = true)
public class IrrigationCropCycleAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 周期id */
    @ApiModelProperty(value = "周期id")
    @TableId
    private Long cycleId;

    /** 作物id */
    @ApiModelProperty(value = "作物id")
    @Excel(name = "作物id")
    private Long cropId;

    /** 作物名称 */
    @ApiModelProperty(value = "作物名称")
    @Excel(name = "作物名称")
    private String cropName;

    /** 周期名称 */
    @ApiModelProperty(value = "周期名称")
    @Excel(name = "周期名称")
    private String cycleName;

    /** 周期介绍 */
    @ApiModelProperty(value = "周期介绍")
    @Excel(name = "周期介绍")
    private String cycleDescription;

    /** 每亩需水量，以立方米(m³)为单位 */
    @ApiModelProperty(value = "每亩需水量，以立方米(m³)为单位")
    @Excel(name = "每亩需水量，以立方米(m³)为单位")
    private BigDecimal waterRequirement;

    /** 开始日期 */
    @ApiModelProperty(value = "开始日期")
    @Excel(name = "开始日期")
    private String startDate;

    /** 结束日期 */
    @ApiModelProperty(value = "结束日期")
    @Excel(name = "结束日期")
    private String endDate;

    /** 周期天数 */
    @ApiModelProperty(value = "周期天数")
    @Excel(name = "周期天数")
    private String cycleDay;

    /** 周期排序 */
    @ApiModelProperty(value = "周期排序")
    @Excel(name = "周期排序")
    private String cycleSort;

}
