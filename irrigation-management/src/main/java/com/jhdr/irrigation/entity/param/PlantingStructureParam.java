package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 种植结构参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "种植结构参数")
public class PlantingStructureParam {
    
    @ApiModelProperty(value = "灌区编码", required = true)
    @NotBlank(message = "灌区编码不能为空")
    private String irrigationCode;
    
    @ApiModelProperty(value = "灌区名称", required = true)
    @NotBlank(message = "灌区名称不能为空")
    private String irrigationName;
    
    @ApiModelProperty(value = "作物编码", required = true)
    @NotBlank(message = "作物编码不能为空")
    private String cropCode;
    
    @ApiModelProperty(value = "种植面积(亩)", required = true)
    @NotBlank(message = "种植面积不能为空")
    private String plantingArea;
}
