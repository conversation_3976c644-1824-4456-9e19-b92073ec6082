package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 灌区配置类型视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "灌区配置类型视图对象")
public class IrrigationConfigTypeVo {

    @ApiModelProperty(value = "配置类型ID")
    private Long id;

    @ApiModelProperty(value = "配置名称")
    private String configName;

    @ApiModelProperty(value = "所属模型类型")
    private String modelType;

    @ApiModelProperty(value = "配置说明")
    private String description;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "是否启用(1:启用 0:禁用)")
    private Integer isEnabled;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remark;
}
