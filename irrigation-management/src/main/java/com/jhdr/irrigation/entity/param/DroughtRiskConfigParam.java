package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 干旱风险配置参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "干旱风险配置参数")
public class DroughtRiskConfigParam {
    
    @ApiModelProperty(value = "干旱预警天数", required = true)
    @NotNull(message = "干旱预警天数不能为空")
    private Integer droughtThreshold;
}
