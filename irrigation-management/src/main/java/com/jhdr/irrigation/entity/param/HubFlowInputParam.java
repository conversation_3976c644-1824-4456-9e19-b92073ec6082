package com.jhdr.irrigation.entity.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 枢纽流量数据输入参数
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@ApiModel(description = "枢纽流量数据输入参数")
@Accessors(chain = true)
public class HubFlowInputParam implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "开始日期不能为空")
    private Date startDate;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "结束日期不能为空")
    private Date endDate;

    @ApiModelProperty(value = "阚疃闸过闸流量(m³/s)，按日期顺序排列")
    @Size(min = 7, max = 7, message = "阚疃闸过闸流量必须为7天数据")
    @NotNull(message = "阚疃闸过闸流量不能为空")
    private List<Double> kantunGateFlow;

    @ApiModelProperty(value = "上桥闸过闸流量(m³/s)，按日期顺序排列")
    @Size(min = 7, max = 7, message = "上桥闸过闸流量必须为7天数据")
    @NotNull(message = "上桥闸过闸流量不能为空")
    private List<Double> shangqiaoGateFlow;
}
