package com.jhdr.irrigation.entity.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 灌区作物管理对象 irrigation_crop_manage
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "灌区作物管理12")
@Accessors(chain = true)
@JsonInclude()
public class WaterCropStatisticsVo implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 作物名称 */
    @ApiModelProperty(value = "作物名称")
    @Excel(name = "作物名称")
    private String cropName;

    /** 种植面积 */
    @ApiModelProperty(value = "种植面积 km²")
    private BigDecimal plantArea;


    /** 种植面积 */
    @ApiModelProperty(value = "种植面积 万亩")
    private BigDecimal plantAreaMu;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String irrigationName;

}
