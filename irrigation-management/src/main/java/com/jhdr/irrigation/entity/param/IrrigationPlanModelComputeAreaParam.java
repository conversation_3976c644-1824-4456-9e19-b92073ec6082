package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/10/20 17:15
 */
@Data
@ApiModel(description = "模型计算输入")
@Accessors(chain = true)
public class IrrigationPlanModelComputeAreaParam {

    @ApiModelProperty(value = "玉米")
    private List<AreaDataParam> corn;

    @ApiModelProperty(value = "水稻")
    private List<AreaDataParam> paddy;

    @ApiModelProperty(value = "小麦")
    private List<AreaDataParam> wheat;
}
