package com.jhdr.irrigation.entity.po;

import java.math.BigDecimal;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌区区域信息对象 irrigation_region_info
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "灌区区域信息")
@Accessors(chain = true)
@TableName(value ="irrigation_region_info")
public class IrrigationRegionInfoPo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 灌区区域信息ID */
    @ApiModelProperty(value = "灌区区域信息ID")
    @TableId
    private Long id;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称")
    @Excel(name = "灌区名称")
    private String irrigationName;

    /** 灌区面积（万亩） */
    @ApiModelProperty(value = "灌区面积（万亩）")
    @Excel(name = "灌区面积", readConverterExp = "万=亩")
    private BigDecimal irrigationArea;

    /** 主要作物种类 */
    @ApiModelProperty(value = "主要作物种类")
    @Excel(name = "主要作物种类")
    private String mainCropTypes;

    /** 灌溉期 */
    @ApiModelProperty(value = "灌溉期")
    @Excel(name = "灌溉期")
    private String irrigationPeriod;

    /** 主要灌溉方式 */
    @ApiModelProperty(value = "主要灌溉方式")
    @Excel(name = "主要灌溉方式")
    private String mainIrrigationMethods;

    /** 干渠数量 */
    @ApiModelProperty(value = "干渠数量")
    @Excel(name = "干渠数量")
    private String mainCanalNum;

    /** 支渠数量 */
    @ApiModelProperty(value = "支渠数量")
    @Excel(name = "支渠数量")
    private String branchCanalNum;

    /** 泵站数量 */
    @ApiModelProperty(value = "泵站数量")
    @Excel(name = "泵站数量")
    private String pumpStationNum;

    /** 涵闸数量 */
    @ApiModelProperty(value = "涵闸数量")
    @Excel(name = "涵闸数量")
    private String gateStationNum;

    /** 管理单位 */
    @ApiModelProperty(value = "管理单位")
    @Excel(name = "管理单位")
    private String managementUnit;

    /** 水塘 */
    @ApiModelProperty(value = "水塘")
    @Excel(name = "水塘")
    private String pond;

    /** 水库 */
    @ApiModelProperty(value = "水库")
    @Excel(name = "水库")
    private String reservoir;

    /** 水源 */
    @ApiModelProperty(value = "水源")
    @Excel(name = "水源")
    private String waterSource;

}
