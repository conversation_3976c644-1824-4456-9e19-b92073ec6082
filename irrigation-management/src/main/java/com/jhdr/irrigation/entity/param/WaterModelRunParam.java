package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "需水模型计算输入")
@Accessors(chain = true)
public class WaterModelRunParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模拟计算条件： 2022 2023 预测：forecast 默认2023")
    private String simulateWeather;

    @ApiModelProperty(value = "水稻作物田间最大蓄水量 (毫米)默认 10.0")
    private double paddyMaxDepth;

    @ApiModelProperty(value = "小麦作物田间最大蓄水量 (毫米)默认 2.0")
    private double wheatMaxDepth;

    @ApiModelProperty(value = "作物生长吸水量 (毫米/天) 默认2.0")
    private double cropUptake;

    @ApiModelProperty(value = "径流系数 无单位 默认0.3")
    private double runoffCoefficient;


}
