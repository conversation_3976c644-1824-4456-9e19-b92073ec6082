package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌溉期时间范围视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "灌溉期时间范围视图对象")
public class IrrigationPeriodVo {
    
    @ApiModelProperty(value = "灌溉期开始时间（月-日）")
    private String irrigationPeriodStart;
    
    @ApiModelProperty(value = "灌溉期结束时间（月-日）")
    private String irrigationPeriodEnd;
    
    @ApiModelProperty(value = "非灌溉期开始时间（月-日）")
    private String nonIrrigationPeriodStart;
    
    @ApiModelProperty(value = "非灌溉期结束时间（月-日）")
    private String nonIrrigationPeriodEnd;
}
