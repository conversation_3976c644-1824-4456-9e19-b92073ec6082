package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌区感知-泵站水位和流量折线
 *
 * <AUTHOR>
 * @date 2024-08-07
 */

@Data
@ApiModel(description = "灌区感知-泵站水位和流量折线")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationPumpLineVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "泵站编码")
    private String pumpCode; // 泵站编码
    @ApiModelProperty(value = "泵站名称")
    private String pumpName; // 泵站名称
    @ApiModelProperty(value = "实时流量")
    private String realFlow; // 实时流量
    @ApiModelProperty(value = "实时水位")
    private String realWater; // 实时水位
    @ApiModelProperty(value = "实时时间")
    private String realTime; // 实时时间

    @ApiModelProperty(value = "日水量")
    private BigDecimal psq;

    @ApiModelProperty(value = "累计日水量")
    private BigDecimal totalPsq;

}
