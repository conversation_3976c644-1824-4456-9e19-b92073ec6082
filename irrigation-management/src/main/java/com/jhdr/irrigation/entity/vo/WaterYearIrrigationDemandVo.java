package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "年数据")
@Accessors(chain = true)
@JsonInclude()
public class WaterYearIrrigationDemandVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "颍泉区1 灌溉需水量万m³")
    private BigDecimal  yingQuanWaterDemand;
    @ApiModelProperty(value = "颍东区2 灌溉需水量万m³")
    private BigDecimal yingDongWaterDemand;
    @ApiModelProperty(value = "蒙城县3 灌溉需水量万m³")
    private BigDecimal mengChengWaterDemand;
    @ApiModelProperty(value = "潘集区4 灌溉需水量万m³")
    private BigDecimal panJiWaterDemand;
    @ApiModelProperty(value = "怀远县5 灌溉需水量万m³")
    private BigDecimal huaiYuanWaterDemand;
    @ApiModelProperty(value = "利辛县6 灌溉需水量万m³")
    private BigDecimal liXinWaterDemand;
    @ApiModelProperty(value = "凤台区7 灌溉需水量万m³")
    private BigDecimal fengTaiWaterDemand;



}
