package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 水位保证参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "水位保证参数")
public class WaterLevelGuaranteeParam {
    
    @ApiModelProperty(value = "通航保证水位(m)", required = true)
    @NotBlank(message = "通航保证水位不能为空")
    @Pattern(regexp = "^\\d+(\\.\\d+)?$", message = "通航保证水位格式不正确，应为数字")
    private String navigationWaterLevel;
    
    @ApiModelProperty(value = "淮河最低限制水位(m)", required = true)
    @NotBlank(message = "淮河最低限制水位不能为空")
    @Pattern(regexp = "^\\d+(\\.\\d+)?$", message = "淮河最低限制水位格式不正确，应为数字")
    private String huaiheMinWaterLevel;
    
    @ApiModelProperty(value = "南湾节制闸水位(m)", required = true)
    @NotBlank(message = "南湾节制闸水位不能为空")
    @Pattern(regexp = "^\\d+(\\.\\d+)?$", message = "南湾节制闸水位格式不正确，应为数字")
    private String nanwanGateWaterLevel;
}
