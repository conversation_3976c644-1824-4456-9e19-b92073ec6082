package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 种植结构视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "种植结构视图对象")
public class PlantingStructureVo {
    
    @ApiModelProperty(value = "灌区编码")
    private String irrigationCode;
    
    @ApiModelProperty(value = "灌区名称")
    private String irrigationName;
    
    @ApiModelProperty(value = "作物编码")
    private String cropCode;
    
    @ApiModelProperty(value = "作物名称")
    private String cropName;
    
    @ApiModelProperty(value = "种植面积(亩)")
    private String plantingArea;
}
