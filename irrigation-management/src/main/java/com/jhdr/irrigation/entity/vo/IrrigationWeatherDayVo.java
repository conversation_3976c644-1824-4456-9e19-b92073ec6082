package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌区感知-七天天气
 *
 * <AUTHOR>
 * @date 2024-08-07
 */

@Data
@ApiModel(description = "灌区感知-七天天气")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationWeatherDayVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预告时间")
    private String fxDate ; // 预告时间
    @ApiModelProperty(value = "天气 文字描述")
    private String weatherCondition ; // 天气状态
    @ApiModelProperty(value = "天气图标代码")//具体含义看 https://dev.qweather.com/docs/resource/icons/
    private String iconDay ;
    @ApiModelProperty(value = "最高温度")
    private String tempMax; // 最高温度
    @ApiModelProperty(value = "最低温度")
    private String tempMin; // 最低温度
    @ApiModelProperty(value = "降雨量mm")
    private String rainfall; // 降雨量

}
