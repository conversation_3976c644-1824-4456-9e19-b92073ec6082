package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "调度")
@Accessors(chain = true)
@JsonInclude()
public class WaterSchedulePlanContrastListVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "颍泉区 泵站调度")
    private List<WaterSchedulePumpContrastVo> yingQuanPumpList;
    @ApiModelProperty(value = "颍东区 泵站调度")
    private List<WaterSchedulePumpContrastVo> yingDongPumpList;
    @ApiModelProperty(value = "蒙城县 泵站调度")
    private List<WaterSchedulePumpContrastVo> mengChengPumpList;
    @ApiModelProperty(value = "潘集区 泵站调度")
    private List<WaterSchedulePumpContrastVo> panJiPumpList;
    @ApiModelProperty(value = "怀远县 泵站调度")
    private List<WaterSchedulePumpContrastVo> huaiYuanPumpList;
    @ApiModelProperty(value = "利辛县 泵站调度")
    private List<WaterSchedulePumpContrastVo> liXinPumpList;
    @ApiModelProperty(value = "凤台区 泵站调度")
    private List<WaterSchedulePumpContrastVo> fengTaiPumpList;

    @ApiModelProperty(value = "对比统计数据")
    private String statisticsContrastData;





}
