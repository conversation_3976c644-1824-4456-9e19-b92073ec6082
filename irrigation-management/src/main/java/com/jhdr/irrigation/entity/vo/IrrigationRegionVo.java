package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌区管理-基本信息
 *
 * <AUTHOR>
 * @date 2024-08-06
 */

@Data
@ApiModel(description = "灌区管理-区域信息")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationRegionVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "灌区名称")
    private String irrigationName;
    @ApiModelProperty(value = "灌区编码")
    private String irrigationCode;
    @ApiModelProperty(value = "灌区面积")
    private BigDecimal irrigationArea; // 灌区面积
    @ApiModelProperty(value = "主要作物种类")
    private String mainCropTypes; // 主要作物种类
    @ApiModelProperty(value = "灌溉期")
    private String irrigationPeriod; // 灌溉期
    @ApiModelProperty(value = "主要灌溉方式")
    private String mainIrrigationMethods; // 主要灌溉方式
    @ApiModelProperty(value = "干渠数量")
    private String mainCanalNum; // 干渠数量
    @ApiModelProperty(value = "支渠数量")
    private String branchCanalNum;//支渠数量
    @ApiModelProperty(value = "泵站数量")
    private String pumpStationNum;//泵站数量
    @ApiModelProperty(value = "涵闸数量")
    private String gateStationNum;//涵闸数量
    @ApiModelProperty(value = "管理单位")
    private String managementUnit; //管理单位


}
