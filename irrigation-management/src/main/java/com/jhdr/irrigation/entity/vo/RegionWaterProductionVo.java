package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 片区产水量分析结果
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
@Data
@ApiModel(description = "片区产水量分析结果")
@Accessors(chain = true)
@JsonInclude()
public class RegionWaterProductionVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期列表")
    private List<String> dateList;

    @ApiModelProperty(value = "怀远县灌区产水量(万m³)")
    private List<Double> huaiyuanWaterProduction;

    @ApiModelProperty(value = "潘集区灌区产水量(万m³)")
    private List<Double> panjiWaterProduction;

    @ApiModelProperty(value = "凤台县灌区产水量(万m³)")
    private List<Double> fengtaiWaterProduction;

    @ApiModelProperty(value = "蒙城县灌区产水量(万m³)")
    private List<Double> mengchengWaterProduction;


}
