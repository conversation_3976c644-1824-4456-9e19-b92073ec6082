package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌区作物生长期对象 irrigation_crop_grow
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "年份数据")
@Accessors(chain = true)
@JsonInclude()
public class StStationYearExtractionVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "抗旱抽水量万m³")
    private BigDecimal droughtExtraction;

    @ApiModelProperty(value = "排涝抽水量万m³")
    private BigDecimal floodExtraction;

    @ApiModelProperty(value = "总量万m³")
    private BigDecimal allExtraction;

}
