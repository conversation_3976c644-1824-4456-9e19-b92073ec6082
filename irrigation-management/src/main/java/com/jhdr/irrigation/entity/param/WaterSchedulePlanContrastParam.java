package com.jhdr.irrigation.entity.param;

import com.baomidou.mybatisplus.annotation.TableId;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 灌区管理-调度方案对象 water_schedule_plan
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "灌区管理-调度方案对比参数")
@Accessors(chain = true)
public class WaterSchedulePlanContrastParam implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "调度方案Aid")
    private Long planIdA;
    @ApiModelProperty(value = "调度方案A名称")
    private String planNameA;
    @ApiModelProperty(value = "调度方案Aid")
    private Long planIdB;
    @ApiModelProperty(value = "调度方案B名称")
    private String planNameB;


}
