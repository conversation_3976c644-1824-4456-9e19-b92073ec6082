package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "模型计算输入")
@Accessors(chain = true)
public class IrrigationPlanModelYearParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "今年降雨量  单位：mm 传数值类型，限定")
    private BigDecimal rain;

}
