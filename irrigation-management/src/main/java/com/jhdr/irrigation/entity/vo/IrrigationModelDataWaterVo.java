package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 需水预测-弹框信息
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "模型数据返回结果")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationModelDataWaterVo implements Serializable
{

    @ApiModelProperty(value = "年份类型数据")
    private String yearType;

    @ApiModelProperty(value = "灌溉需水量定 单位：亿m³")
    private String waterDemand;

    @ApiModelProperty(value = "年份 单位年")
    private String year;


}
