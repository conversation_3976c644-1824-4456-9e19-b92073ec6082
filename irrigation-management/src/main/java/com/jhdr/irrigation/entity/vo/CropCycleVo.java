package com.jhdr.irrigation.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 农作物周期信息视图对象
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "农作物周期信息视图对象")
public class CropCycleVo {
    
    @ApiModelProperty(value = "周期ID")
    private Long cycleId;
    
    @ApiModelProperty(value = "作物ID")
    private Long cropId;
    
    @ApiModelProperty(value = "作物名称")
    private String cropName;
    
    @ApiModelProperty(value = "周期名称")
    private String cycleName;
    
    @ApiModelProperty(value = "周期介绍")
    private String cycleDescription;
    
    @ApiModelProperty(value = "每亩需水量(m³)")
    private BigDecimal waterRequirement;
    
    @ApiModelProperty(value = "开始日期(月-日)")
    private String startDate;
    
    @ApiModelProperty(value = "结束日期(月-日)")
    private String endDate;
    
    @ApiModelProperty(value = "周期天数")
    private String cycleDay;
    
    @ApiModelProperty(value = "周期排序")
    private String cycleSort;
}
