package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 灌区作物泵站统计
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "灌区作物泵站统计")
@Accessors(chain = true)
@JsonInclude()
public class IrrigationPumpWaterDemandStatisticsVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 泵站编码 */
    @ApiModelProperty(value = "泵站编码")
    @Excel(name = "泵站编码")
    private String pumpCode;

    /** 泵站名称 */
    @ApiModelProperty(value = "泵站名称")
    @Excel(name = "泵站名称")
    private String pumpName;

    @ApiModelProperty(value = "总灌溉面积")
    private BigDecimal allIrrigatedArea;

    @ApiModelProperty(value = "需水量")
    private BigDecimal waterDemand;

}
