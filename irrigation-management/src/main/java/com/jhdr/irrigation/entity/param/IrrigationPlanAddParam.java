package com.jhdr.irrigation.entity.param;

import java.io.Serializable;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */

@Data
@ApiModel(description = "灌溉计划")
@Accessors(chain = true)
public class IrrigationPlanAddParam implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 灌区计划id */
    @ApiModelProperty(value = "灌区计划id")
    @TableId
    private Long id;

    /** 灌溉计划 */
    @ApiModelProperty(value = "灌溉计划")
    @Excel(name = "灌溉计划")
    private String planName;

    /** 灌溉方式 */
    @ApiModelProperty(value = "灌溉方式")
    @Excel(name = "灌溉方式")
    private String planWay;

    /** 灌溉水量 */
    @ApiModelProperty(value = "灌溉水量")
    @Excel(name = "灌溉水量")
    private String planWater;

    /** 灌溉时间 */
    @ApiModelProperty(value = "灌溉时间")
    @Excel(name = "灌溉时间")
    private String planDate;

}
