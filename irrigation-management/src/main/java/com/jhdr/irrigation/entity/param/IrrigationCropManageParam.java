package com.jhdr.irrigation.entity.param;

import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 灌区作物管理对象 irrigation_crop_manage
 *
 * <AUTHOR>
 * @date 2024-08-09
 */

@Data
@ApiModel(description = "灌区作物管理")
@Accessors(chain = true)
public class IrrigationCropManageParam implements Serializable
{
    private static final long serialVersionUID = 1L;



    /** 作物id */
    @ApiModelProperty(value = "作物id",hidden = true)
    @Excel(name = "作物id")
    private Long cropId;

    /** 作物名称 */
    @ApiModelProperty(value = "作物名称",hidden = true)
    @Excel(name = "作物名称")
    private String cropName;

    /** 种植面积 */
    @ApiModelProperty(value = "种植面积",hidden = true)
    @Excel(name = "种植面积")
    private BigDecimal plantArea;

    /** 泵站编码 */
    @ApiModelProperty(value = "泵站编码",hidden = true)
    @Excel(name = "泵站编码")
    private String pumpCode;

    /** 泵站名称 */
    @ApiModelProperty(value = "泵站名称",hidden = true)
    @Excel(name = "泵站名称")
    private String pumpName;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间",hidden = true)
    private Date createTime;

    /** 灌区编码 */
    @ApiModelProperty(value = "灌区编码")
    @Excel(name = "灌区编码")
    private String irrigationCode;

    /** 灌区名称 */
    @ApiModelProperty(value = "灌区名称",hidden = true)
    @Excel(name = "灌区名称")
    private String irrigationName;

}
