package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 调度泵站批量参数
 *
 * <AUTHOR>
 * @date 2024-10-25
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调度泵站批量参数")
public class PumpStationBatchParam {
    
    @ApiModelProperty(value = "调度泵站列表", required = true)
    @NotEmpty(message = "调度泵站列表不能为空")
    @Valid
    private List<PumpStationParam> pumpStations;
}
