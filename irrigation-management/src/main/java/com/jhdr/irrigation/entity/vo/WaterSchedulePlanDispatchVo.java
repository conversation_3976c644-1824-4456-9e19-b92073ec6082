package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @date 2024-08-14
 */

@Data
@ApiModel(description = "调度")
@Accessors(chain = true)
@JsonInclude()
public class WaterSchedulePlanDispatchVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "颍泉区1 灌溉需水量万m³")
    private BigDecimal  yingQuanWaterDemand;
    @ApiModelProperty(value = "颍东区2 灌溉需水量万m³")
    private BigDecimal yingDongWaterDemand;
    @ApiModelProperty(value = "蒙城县3 灌溉需水量万m³")
    private BigDecimal mengChengWaterDemand;
    @ApiModelProperty(value = "潘集区4 灌溉需水量万m³")
    private BigDecimal panJiWaterDemand;
    @ApiModelProperty(value = "怀远县5 灌溉需水量万m³")
    private BigDecimal huaiYuanWaterDemand;
    @ApiModelProperty(value = "利辛县6 灌溉需水量万m³")
    private BigDecimal liXinWaterDemand;
    @ApiModelProperty(value = "凤台区7 灌溉需水量万m³")
    private BigDecimal fengTaiWaterDemand;

    @ApiModelProperty(value = "茨淮新河总灌溉需水量万m³")
    private BigDecimal totalWaterDemand;



    @ApiModelProperty(value = "茨河铺至插花灌溉需水万m³")//取插花上水位
    private BigDecimal chaHuaWaterDemand;
    @ApiModelProperty(value = "插花至阚疃灌溉需水万m³")//取阚疃上水位
    private BigDecimal kanTuanWaterDemand;
    @ApiModelProperty(value = "阚疃至上桥灌溉需水万m³")//取上桥上水位
    private BigDecimal shangQiaoWaterDemand;

    @ApiModelProperty(value = "茨淮新河总调度用水万m³")
    private BigDecimal totalDispatchWaterDemand;

    @ApiModelProperty(value = "调度方案 1 或者 2")
    private Integer dispatchScheme;

    @ApiModelProperty(value = "茨河铺至插花调度水万m³")//取插花上水位
    private BigDecimal chaHuaDispatchWaterDemand;
    @ApiModelProperty(value = "插花至阚疃调度水万m³")//取阚疃上水位
    private BigDecimal kanTuanDispatchWaterDemand;
    @ApiModelProperty(value = "阚疃至上桥调度水万m³")//取上桥上水位
    private BigDecimal shangQiaoDispatchWaterDemand;



}
