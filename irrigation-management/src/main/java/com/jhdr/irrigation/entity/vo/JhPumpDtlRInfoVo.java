package com.jhdr.irrigation.entity.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jhdr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 泵站运行明细对象 jh_pump_dtl_r
 *
 * <AUTHOR>
 * @date 2024-06-20
 */

@Data
@ApiModel(description = "泵站运行明细")
@Accessors(chain = true)
@JsonInclude()
public class JhPumpDtlRInfoVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 机组编码 */
    @ApiModelProperty(value = "机组编码")
    @Excel(name = "机组编码")
    private BigDecimal pmpcd;

    /** 运行标识 */
    @ApiModelProperty(value = "运行标识 0停止 1运行")
    @Excel(name = "运行标识")
    private BigDecimal om;

}
