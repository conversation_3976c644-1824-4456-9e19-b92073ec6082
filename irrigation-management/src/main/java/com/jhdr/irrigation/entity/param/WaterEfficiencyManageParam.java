package com.jhdr.irrigation.entity.param;

import com.jhdr.irrigation.entity.vo.IrrigationWeatherDayVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */


@ApiModel(description = "用水效率管理-用水效率输入")
@Accessors(chain = true)
@Data
public class WaterEfficiencyManageParam implements Serializable
{




    @ApiModelProperty(value = "年 2022 2023")
    private String year;

    @ApiModelProperty(value = "颍泉")
    private String YingQuan;

    @ApiModelProperty(value = "颍东")
    private String  YingDong;

    @ApiModelProperty(value = "蒙城")
    private String  MengCheng;

    @ApiModelProperty(value = "潘集")
    private String  PanJi;

    @ApiModelProperty(value = "怀远")
    private String  HuaiYuan;

    @ApiModelProperty(value = "利辛")
    private String  LiXin;

    @ApiModelProperty(value = "凤台")
    private String  FengTai;

}
