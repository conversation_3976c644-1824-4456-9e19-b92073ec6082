package com.jhdr.irrigation.entity.param;

import com.jhdr.irrigation.entity.vo.IrrigationWeatherDayVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 灌溉计划对象 irrigation_plan
 *
 * <AUTHOR>
 * @date 2024-09-11
 */


@ApiModel(description = "来水预测计算输入")
@Accessors(chain = true)
public class WaterInflowForecastParam implements Serializable
{

    @ApiModelProperty(value = "颍泉")
    private List<IrrigationWeatherDayVo> YingQuan;

    @ApiModelProperty(value = "颍东")
    private List<IrrigationWeatherDayVo>  YingDong;

    @ApiModelProperty(value = "蒙城")
    private List<IrrigationWeatherDayVo>  MengCheng;

    @ApiModelProperty(value = "潘集")
    private List<IrrigationWeatherDayVo>  PanJi;

    @ApiModelProperty(value = "怀远")
    private List<IrrigationWeatherDayVo>  HuaiYuan;

    @ApiModelProperty(value = "利辛")
    private List<IrrigationWeatherDayVo>  LiXin;

    @ApiModelProperty(value = "凤台")
    private List<IrrigationWeatherDayVo>  FengTai;

    public List<IrrigationWeatherDayVo> getYingQuan() {
        return YingQuan;
    }

    public void setYingQuan(List<IrrigationWeatherDayVo> yingQuan) {
        YingQuan = yingQuan;
    }

    public List<IrrigationWeatherDayVo> getYingDong() {
        return YingDong;
    }

    public void setYingDong(List<IrrigationWeatherDayVo> yingDong) {
        YingDong = yingDong;
    }

    public List<IrrigationWeatherDayVo> getMengCheng() {
        return MengCheng;
    }

    public void setMengCheng(List<IrrigationWeatherDayVo> mengCheng) {
        MengCheng = mengCheng;
    }

    public List<IrrigationWeatherDayVo> getPanJi() {
        return PanJi;
    }

    public void setPanJi(List<IrrigationWeatherDayVo> panJi) {
        PanJi = panJi;
    }

    public List<IrrigationWeatherDayVo> getHuaiYuan() {
        return HuaiYuan;
    }

    public void setHuaiYuan(List<IrrigationWeatherDayVo> huaiYuan) {
        HuaiYuan = huaiYuan;
    }

    public List<IrrigationWeatherDayVo> getLiXin() {
        return LiXin;
    }

    public void setLiXin(List<IrrigationWeatherDayVo> liXin) {
        LiXin = liXin;
    }

    public List<IrrigationWeatherDayVo> getFengTai() {
        return FengTai;
    }

    public void setFengTai(List<IrrigationWeatherDayVo> fengTai) {
        FengTai = fengTai;
    }
}
