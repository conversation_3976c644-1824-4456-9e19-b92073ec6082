package com.jhdr.irrigation.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/10/20 17:15
 */
@Data
@ApiModel(description = "水深模型计算输入")
@Accessors(chain = true)
public class IrrigationPlanModelAreaDepthParam {

    @ApiModelProperty(value = "天气年份")
    private String year;

    @ApiModelProperty(value = "区域 Point1--Point11")
    private String area;

    @ApiModelProperty(value = "作物")
    private List<String> cropList;
}
